from qdrant_client import QdrantClient
import subprocess
import sys

# Získání kategorií z Qdrantu
client = QdrantClient(host='localhost', port=6333)
results = client.scroll(
    collection_name='real_products_filsonstore',
    limit=1000,
    with_payload=['category']
)

qdrant_categories = set()
qdrant_categories_without_prefix = set()
for point in results[0]:
    category = point.payload.get('category')
    if category:
        qdrant_categories.add(category)
        # Odstraníme prefix "Autodoplňky > " pro porovnání
        if category.startswith("Autodoplňky > "):
            category_without_prefix = category[len("Autodoplňky > "):]
            qdrant_categories_without_prefix.add(category_without_prefix)

print(f"V Qdrantu je celkem {len(qdrant_categories)} kategorií.")
print(f"Po odstranění prefixu 'Autodoplňky > ' máme {len(qdrant_categories_without_prefix)} unikátních hodnot.")

# <PERSON>ísk<PERSON><PERSON> kategorií z Neo4j
neo4j_container = "neo4j_gallitec"
cypher_query = "MATCH (c:Category_filsonstore) RETURN c.name"

try:
    cmd = ["docker", "exec", "-it", neo4j_container, "cypher-shell", "-u", "neo4j", "-p", "Graph2025Secure!", cypher_query]
    result = subprocess.run(cmd, capture_output=True, text=True)
    
    if result.returncode != 0:
        print(f"Chyba při získávání kategorií z Neo4j: {result.stderr}")
        sys.exit(1)
        
    # Parsování výstupu
    neo4j_categories = set()
    lines = result.stdout.split('\n')
    for line in lines:
        line = line.strip()
        if line and '"' in line:
            # Extrahování jména kategorie mezi uvozovkami
            category = line.split('"')[1] if '"' in line else line
            neo4j_categories.add(category)
    
    # Odstranění hlavičky a patičky
    neo4j_categories.discard("c.name")
    neo4j_categories.discard("+-----------------------------------+")
    
    print(f"\nV Neo4j je celkem {len(neo4j_categories)} kategorií.")
    
    # Kategorie, které jsou v Neo4j a odpovídají kategoriím v Qdrantu (bez prefixu)
    matching_categories = neo4j_categories.intersection(qdrant_categories_without_prefix)
    print(f"\nKategorie, které jsou jak v Neo4j, tak v Qdrantu (po odstranění prefixu): {len(matching_categories)}")
    
    # Kategorie, které jsou v Neo4j, ale nebyly nalezeny v Qdrantu
    neo4j_only_categories = neo4j_categories - qdrant_categories_without_prefix
    print(f"\nKategorie, které jsou jen v Neo4j: {len(neo4j_only_categories)}")
    for category in sorted(neo4j_only_categories):
        print(f"- {category}")
    
    # Vytvořit Cypher dotaz pro aktualizaci kategorií
    update_query = """
    MATCH (c:Category_filsonstore)
    WHERE c.name IN [{}]
    SET c.name = "Autodoplňky > " + c.name
    RETURN c.name AS UpdatedCategory
    """.format(", ".join([f'"{c}"' for c in matching_categories]))
    
    print("\nCypher dotaz pro aktualizaci kategorií:")
    print(update_query)
    
    # Zeptat se uživatele, zda chce provést aktualizaci
    confirm = input("\nChcete provést aktualizaci kategorií v Neo4j? (ano/ne): ")
    if confirm.lower() in ["ano", "yes", "y", "a"]:
        print("\nProvádím aktualizaci kategorií v Neo4j...")
        cmd = ["docker", "exec", "-it", neo4j_container, "cypher-shell", "-u", "neo4j", "-p", "Graph2025Secure!", update_query]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode != 0:
            print(f"Chyba při aktualizaci kategorií v Neo4j: {result.stderr}")
            sys.exit(1)
            
        print("Aktualizace kategorií v Neo4j byla úspěšně dokončena.")
        print(result.stdout)
    else:
        print("\nAktualizace kategorií v Neo4j byla zrušena.")
    
except Exception as e:
    print(f"Došlo k chybě: {e}")
    sys.exit(1) 