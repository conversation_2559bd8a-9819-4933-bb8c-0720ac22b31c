import logging
import asyncio
from typing import List, Dict, Any, Optional, <PERSON><PERSON>
from qdrant_client import AsyncQdrantClient
from openai import AsyncOpenAI
import cohere
from qdrant_fulltext import qdrant_fulltext_search
from semantic import search_semantic_products

logger = logging.getLogger(__name__)

async def hybrid_qdrant_search(
    qdrant_client: AsyncQdrantClient,
    tenant_id: str,
    query: str,
    final_limit: int = 10,
    fulltext_limit: Optional[int] = None, 
    semantic_limit: Optional[int] = None,
    fulltext_ratio: float = 0.25,
    semantic_ratio: float = 0.75,
    openai_client: Optional[AsyncOpenAI] = None,
    cohere_client: Optional[cohere.Client] = None,
    enable_reranking: bool = True,
    ensemble_alpha: float = 0.5
) -> List[Dict[str, Any]]:
    """
    Provede hybridní vyhledávání s využitím nativních Qdrant fulltextových indexů 
    a sémantického vyhledávání. Výsledky jsou kombinovány a případně rerankovány.
    
    Args:
        qdrant_client: Asynchronní Qdrant klient
        tenant_id: ID tenanta
        query: Vyhledávací dotaz
        final_limit: Finální počet výsledků po rerankingu
        fulltext_limit: Limit pro fulltext výsledky (pokud None, bude vypočítán z final_limit a fulltext_ratio)
        semantic_limit: Limit pro sémantické výsledky (pokud None, bude vypočítán z final_limit a semantic_ratio)
        fulltext_ratio: Poměr fulltext výsledků pro kombinaci (0.0-1.0)
        semantic_ratio: Poměr sémantických výsledků pro kombinaci (0.0-1.0)
        openai_client: OpenAI klient pro embeddings
        cohere_client: Cohere klient pro reranking
        enable_reranking: Zapnutí/vypnutí rerankingu
        ensemble_alpha: Váha při kombinaci skóre fulltext a semantic výsledků (0.0-1.0)
    
    Returns:
        Seznam výsledků seřazených podle relevance
    """
    # Nastavení limitů
    if not fulltext_limit and not semantic_limit:
        rerank_limit = final_limit * 4  # Výchozí multiplier 4
        fulltext_limit = int(rerank_limit * fulltext_ratio)
        semantic_limit = int(rerank_limit * semantic_ratio)
    
    # Paralelní volání obou typů vyhledávání
    fulltext_task = asyncio.create_task(qdrant_fulltext_search(
        qdrant_client=qdrant_client,
        tenant_id=tenant_id,
        query=query,
        limit=fulltext_limit or 100
    ))
    
    if openai_client:
        semantic_task = asyncio.create_task(search_semantic_products(
            client=qdrant_client,
            collection_name=f"real_products_{tenant_id}",
            query_text=query,
            openai_client=openai_client,
            cohere_client=None,  # Nepoužíváme reranking v jednotlivých vyhledávačích
            limit=semantic_limit or 100,
            enable_reranking=False  # Vypneme reranking v samotném semantic search
        ))
        
        # Počkáme na oba výsledky
        fulltext_results, semantic_results = await asyncio.gather(fulltext_task, semantic_task)
    else:
        # Pokud nemáme OpenAI klienta, pouze fulltext vyhledávání
        fulltext_results = await fulltext_task
        semantic_results = []
    
    logger.info(f"Získáno {len(fulltext_results)} fulltext a {len(semantic_results)} sémantických výsledků")
    
    # Deduplikace výsledků podle product_id
    product_ids_seen = set()
    combined_results = []
    
    # Pomocná funkce pro získání product_id z výsledku
    def get_product_id(result):
        return str(result.get("product_id") or result.get("original_id") or result.get("id"))
    
    # Nejprve přidáme fulltextové výsledky
    for result in fulltext_results:
        product_id = get_product_id(result)
        if product_id and product_id not in product_ids_seen:
            product_ids_seen.add(product_id)
            # Přidáme informaci o zdroji a vynásobíme skóre relativní vahou fulltext
            result["source"] = "fulltext"
            result["fulltext_score"] = result.get("score", 0.0)
            result["semantic_score"] = 0.0
            # Nastavíme kombinované skóre s váhou ensemble_alpha
            result["score"] = result.get("score", 0.0) * ensemble_alpha
            combined_results.append(result)
    
    # Poté přidáme sémantické výsledky
    for result in semantic_results:
        product_id = get_product_id(result)
        if product_id and product_id not in product_ids_seen:
            product_ids_seen.add(product_id)
            # Přidáme informaci o zdroji a vynásobíme skóre relativní vahou semantic
            result["source"] = "semantic"
            result["semantic_score"] = result.get("score", 0.0)
            result["fulltext_score"] = 0.0
            # Nastavíme kombinované skóre s obrácenou váhou ensemble_alpha
            result["score"] = result.get("score", 0.0) * (1 - ensemble_alpha)
            combined_results.append(result)
    
    # Seřadíme výsledky podle skóre sestupně
    combined_results.sort(key=lambda x: x.get("score", 0.0), reverse=True)
    
    # Reranking pomocí Cohere, pokud je k dispozici a zapnutý
    if enable_reranking and cohere_client and combined_results:
        try:
            # Příprava dat pro reranking
            rerank_documents = []
            for result in combined_results:
                # Vytvoříme text dokumentu z relevantních polí
                doc_text = f"{result.get('name', '')} - {result.get('description', '')} - {result.get('product_code', '')} - {result.get('category', '')}"
                rerank_documents.append(doc_text)
            
            # Volání Cohere rerank API
            rerank_results = cohere_client.rerank(
                model="rerank-english-v2.0",  # nebo jiný vhodný model
                query=query,
                documents=rerank_documents,
                top_n=final_limit
            )
            
            # Zpracování výsledků rerankingu
            reranked_results = []
            for idx, rerank_result in enumerate(rerank_results.results):
                original_idx = rerank_result.index
                if 0 <= original_idx < len(combined_results):
                    result = combined_results[original_idx].copy()
                    # Přidáme rerank skóre
                    result["rerank_score"] = rerank_result.relevance_score
                    result["method"] = "hybrid_qdrant_reranked"
                    reranked_results.append(result)
            
            logger.info(f"Reranking dokončen, získáno {len(reranked_results)} výsledků")
            return reranked_results[:final_limit]
            
        except Exception as e:
            logger.error(f"Chyba při rerankingu: {e}")
            # Fallback na seřazené výsledky bez rerankingu
            return combined_results[:final_limit]
    
    # Vrátíme omezený počet výsledků
    return combined_results[:final_limit]
