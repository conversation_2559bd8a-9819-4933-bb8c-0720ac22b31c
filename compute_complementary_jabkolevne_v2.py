#!/usr/bin/env python3
"""
Vylepšená verze skriptu pro výpočet komplementárních produktů jabkolevne.cz
Tato verze používá zdokonalené mapování kategorií založené na skutečné struktuře dat.

Analogie: Jako expert na Apple produkty, který přesně zná umístění každého produktu
v obchodě a dokáže zákazníkovi najít přesně to, co potřebuje.

Autor: <PERSON>
Datum: 2025-01-17
"""

import json
import logging
import os
import time
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict

from dotenv import load_dotenv
from neo4j import GraphDatabase
from qdrant_client import QdrantClient
from qdrant_client.http.models import Filter, FieldCondition, MatchValue

# Nastavení loggingu
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Načtení proměnných prostředí
load_dotenv()

# Konfigurace
TENANT_ID = "jabkolevne"
QDRANT_COLLECTION = f"real_products_{TENANT_ID}"
CATEGORY_LABEL = f"Category_{TENANT_ID}"

# Neo4j konfigurace
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "Graph2025Secure!")

# Qdrant konfigurace
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333

# Vylepšené mapování založené na skutečných kategoriích v Qdrant
# Používáme částečné shody, jelikož kategorie jsou hierarchické s " > "
QDRANT_CATEGORY_PATTERNS = {
    "iPhone": {
        "exact_matches": ["iPhone"],
        "partial_matches": ["iPhone >", "> iPhone"]
    },
    "iPad": {
        "exact_matches": ["iPad"],  
        "partial_matches": ["iPad >", "> iPad"]
    },
    "MacBook": {
        "exact_matches": ["MacBook", "Mac"],
        "partial_matches": ["MacBook >", "> MacBook", "Mac >", "> Mac"]
    },
    "Apple Watch": {
        "exact_matches": ["Watch", "Apple Watch"],
        "partial_matches": ["Watch >", "> Watch", "Apple Watch >"]
    },
    "AirPods": {
        "exact_matches": ["AirPods"],
        "partial_matches": ["AirPods >", "> AirPods", "Sluchátka >"]
    },
    "Nabíječky (Apple)": {
        "exact_matches": ["Nabíječky"],
        "partial_matches": ["Nabíječky >", "> Nabíječky", "nabíječky >"]
    },
    "Kabely (Apple)": {
        "exact_matches": ["kabely", "Kabely", "Kabel"],
        "partial_matches": ["kabely >", "> kabely", "Kabely >", "Kabel >"]
    },
    "Pouzdra pro iPhone": {
        "exact_matches": ["Kryty na mobilní telefony Apple"],
        "partial_matches": ["Kryty na mobilní telefony Apple >", "iPhone >", "> iPhone"]
    },
    "Ochranná skla pro iPhone": {
        "exact_matches": ["Tvrzená skla pro iPhone", "Tvrzená skla"],
        "partial_matches": ["Tvrzená skla >", "skla >", "iPhone >"]
    },
    "Pouzdra pro iPad": {
        "exact_matches": ["Pouzdra", "Kryty"],
        "partial_matches": ["Pouzdra >", "> Pouzdra", "iPad >", "Kryty >"]
    },
    "Ochranná skla pro iPad": {
        "exact_matches": ["Tvrzená skla", "skla"],
        "partial_matches": ["Tvrzená skla >", "skla >", "iPad >"]
    },
    "Řemínky pro Apple Watch": {
        "exact_matches": ["Řemínky"],
        "partial_matches": ["Řemínky >", "> Řemínky", "Watch >"]
    },
    "Bezdrátové nabíječky (Apple)": {
        "exact_matches": ["Bezdrátové nabíječky"],
        "partial_matches": ["Bezdrátové nabíječky >", "bezdrátové >"]
    },
    "Powerbanky (pro Apple)": {
        "exact_matches": ["Powerbanky", "Power"],
        "partial_matches": ["Powerbanky >", "> Powerbanky", "Power >"]
    },
    "Apple Pencil": {
        "exact_matches": ["Apple Pencil", "Pencil"],
        "partial_matches": ["Apple Pencil >", "Pencil >"]
    },
    "AirTag": {
        "exact_matches": ["AirTag"],
        "partial_matches": ["AirTag >", "> AirTag"]
    },
    "Pouzdra pro AirPods": {
        "exact_matches": ["Pouzdra na sluchátka"],
        "partial_matches": ["Pouzdra na sluchátka >", "AirPods >", "sluchátka >"]
    },
    "Sluchátka (Apple ostatní)": {
        "exact_matches": ["Sluchátka", "Audio"],
        "partial_matches": ["Sluchátka >", "> Sluchátka", "Audio >"]
    },
    "Reproduktory (Apple kompatibilní)": {
        "exact_matches": ["Reproduktory"],
        "partial_matches": ["Reproduktory >", "> Reproduktory"]
    },
    "Apple TV": {
        "exact_matches": ["Apple TV"],
        "partial_matches": ["Apple TV >", "> Apple TV"]
    },
    "Příslušenství do auta (Apple)": {
        "exact_matches": ["Příslušenství do auta"],
        "partial_matches": ["Příslušenství do auta >", "> auto"]
    }
}

class ImprovedComplementaryCalculator:
    """
    Vylepšený kalkulátor komplementárních produktů - jako zkušený konzultant,
    který zná každý detail sortimentu a dokáže rychle najít nejlepší kombinace.
    """
    
    def __init__(self):
        self.neo4j_driver = None
        self.qdrant_client = None
        self.category_cache = {}
        
    def connect_to_databases(self) -> bool:
        """Připojí se k databázím s důrazem na stabilitu."""
        try:
            logger.info("🔗 Připojuji se k databázím...")
            
            # Neo4j
            self.neo4j_driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
            with self.neo4j_driver.session() as session:
                result = session.run("RETURN 1 AS test")
                assert result.single()["test"] == 1
            
            # Qdrant
            self.qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
            collections = self.qdrant_client.get_collections().collections
            collection_names = [col.name for col in collections]
            if QDRANT_COLLECTION not in collection_names:
                logger.error(f"❌ Kolekce {QDRANT_COLLECTION} neexistuje")
                return False
                
            logger.info("✅ Připojení úspěšné")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba připojení: {e}")
            return False
    
    def get_complementary_relationships(self) -> Dict[str, List[Tuple[str, float]]]:
        """Načte komplementární vztahy z Neo4j."""
        try:
            with self.neo4j_driver.session() as session:
                query = f"""
                MATCH (source:{CATEGORY_LABEL})-[r:COMPLEMENTARY_TO]->(target:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN source.name as source_category, target.name as target_category, r.weight as weight
                ORDER BY source.name, r.weight DESC
                """
                
                result = session.run(query)
                relationships = defaultdict(list)
                
                for record in result:
                    source = record["source_category"]
                    target = record["target_category"]
                    weight = record["weight"]
                    relationships[source].append((target, weight))
                
                logger.info(f"📊 Načteno {len(relationships)} kategorií s komplementárními vztahy")
                return dict(relationships)
                
        except Exception as e:
            logger.error(f"❌ Chyba při načítání vztahů: {e}")
            return {}
    
    def sample_categories_in_qdrant(self, sample_size: int = 200) -> Dict[str, int]:
        """Provede vzorkování kategorií v Qdrant pro lepší pochopení struktury."""
        try:
            logger.info(f"🔍 Vzorkuji {sample_size} produktů pro analýzu kategorií...")
            
            results = self.qdrant_client.scroll(
                collection_name=QDRANT_COLLECTION,
                limit=sample_size,
                with_payload=True,
                with_vectors=False
            )
            
            points, _ = results
            category_counts = defaultdict(int)
            
            for point in points:
                category = point.payload.get('category', 'Bez kategorie')
                category_counts[category] += 1
            
            # Seřadíme podle četnosti
            sorted_categories = dict(sorted(category_counts.items(), key=lambda x: x[1], reverse=True))
            
            logger.info("📈 Top 10 nejčastějších kategorií:")
            for category, count in list(sorted_categories.items())[:10]:
                logger.info(f"   - {category}: {count} produktů")
                
            return sorted_categories
            
        except Exception as e:
            logger.error(f"❌ Chyba při vzorkování kategorií: {e}")
            return {}
    
    def find_products_by_improved_pattern(self, simplified_category: str, limit: int = 20) -> List[Dict]:
        """
        Vylepšené hledání produktů s využitím cache a chytřejších vzorců.
        """
        # Zkontrolujeme cache
        cache_key = f"{simplified_category}:{limit}"
        if cache_key in self.category_cache:
            return self.category_cache[cache_key]
        
        patterns = QDRANT_CATEGORY_PATTERNS.get(simplified_category, {})
        exact_matches = patterns.get("exact_matches", [])
        partial_matches = patterns.get("partial_matches", [])
        
        all_products = []
        
        try:
            # Nejprve zkusíme přesné shody
            for pattern in exact_matches:
                try:
                    results = self.qdrant_client.scroll(
                        collection_name=QDRANT_COLLECTION,
                        limit=limit // 2,  # Méně pro každý vzorec
                        with_payload=True,
                        with_vectors=False,
                        scroll_filter=Filter(
                            must=[
                                FieldCondition(
                                    key="category",
                                    match=MatchValue(value=pattern)
                                )
                            ]
                        )
                    )
                    
                    points, _ = results
                    for point in points:
                        product_data = {
                            'id': point.id,
                            'name': point.payload.get('name', 'Neznámý produkt'),
                            'category': point.payload.get('category', ''),
                            'price': point.payload.get('price', 0),
                            'url': point.payload.get('url', ''),
                            'availability': point.payload.get('availability', 'Neznámá'),
                            'brand': point.payload.get('brand', ''),
                            'match_type': 'exact'
                        }
                        all_products.append(product_data)
                        
                except Exception as e:
                    logger.debug(f"   ⚠️ Chyba při hledání '{pattern}': {e}")
                    continue
            
            # Pokud nemáme dost produktů, zkusíme částečné shody
            if len(all_products) < limit and partial_matches:
                for pattern in partial_matches:
                    try:
                        # Použijeme obsahuje místo přesné shody (pokud Qdrant podporuje)
                        # Toto je zjednodušená verze - v reálné implementaci by bylo třeba použít text search
                        pass  # Prozatím přeskočíme částečné shody
                        
                    except Exception as e:
                        logger.debug(f"   ⚠️ Chyba při částečném hledání '{pattern}': {e}")
                        continue
            
            # Odstranění duplicit
            unique_products = {}
            for product in all_products:
                unique_products[product['id']] = product
            
            result_products = list(unique_products.values())[:limit]
            
            # Uložení do cache
            self.category_cache[cache_key] = result_products
            
            logger.info(f"   🔍 '{simplified_category}': nalezeno {len(result_products)} produktů")
            return result_products
            
        except Exception as e:
            logger.error(f"❌ Chyba při hledání pro '{simplified_category}': {e}")
            return []
    
    def calculate_for_limited_products(self, max_products: int = 50) -> Dict:
        """
        Vypočítá komplementární produkty pro omezený počet produktů - pro testování.
        """
        logger.info(f"🧪 === TESTOVACÍ VÝPOČET PRO {max_products} PRODUKTŮ ===")
        
        # 1. Načtení vztahů
        relationships = self.get_complementary_relationships()
        if not relationships:
            logger.error("❌ Žádné vztahy nenalezeny")
            return {}
        
        # 2. Vzorkování kategorií
        category_sample = self.sample_categories_in_qdrant(100)
        
        # 3. Načtení omezeného počtu produktů
        logger.info(f"📦 Načítám {max_products} produktů pro test...")
        
        try:
            results = self.qdrant_client.scroll(
                collection_name=QDRANT_COLLECTION,
                limit=max_products,
                with_payload=True,
                with_vectors=False
            )
            
            points, _ = results
            test_products = []
            
            for point in points:
                product_data = {
                    'id': point.id,
                    'name': point.payload.get('name', 'Neznámý produkt'),
                    'category': point.payload.get('category', ''),
                    'price': point.payload.get('price', 0),
                    'url': point.payload.get('url', ''),
                    'brand': point.payload.get('brand', '')
                }
                test_products.append(product_data)
        
        except Exception as e:
            logger.error(f"❌ Chyba při načítání testovacích produktů: {e}")
            return {}
        
        # 4. Výpočet komplementárních produktů
        logger.info("🔄 Počítám komplementární produkty...")
        
        results = {}
        successful_matches = 0
        
        for i, product in enumerate(test_products):
            try:
                complementary_products = self.calculate_complementary_for_product(product, relationships)
                
                if complementary_products:
                    results[product['id']] = {
                        'source_product': product,
                        'complementary_products': complementary_products,
                        'total_recommendations': len(complementary_products)
                    }
                    successful_matches += 1
                
                if (i + 1) % 10 == 0:
                    logger.info(f"   📊 Zpracováno {i + 1}/{len(test_products)} produktů (úspěšných: {successful_matches})")
                    
            except Exception as e:
                logger.error(f"   ❌ Chyba u produktu {product['name']}: {e}")
                continue
        
        logger.info(f"🎉 Test dokončen! {successful_matches} produktů má doporučení")
        return results
    
    def calculate_complementary_for_product(self, product: Dict, relationships: Dict) -> List[Dict]:
        """Vypočítá komplementární produkty pro jeden produkt."""
        product_category = product.get('category', '')
        
        # Najdeme odpovídající zjednodušenou kategorii
        matching_simplified_category = None
        for simplified_cat, patterns in QDRANT_CATEGORY_PATTERNS.items():
            exact_matches = patterns.get("exact_matches", [])
            
            for pattern in exact_matches:
                if pattern.lower() in product_category.lower():
                    matching_simplified_category = simplified_cat
                    break
            
            if matching_simplified_category:
                break
        
        if not matching_simplified_category:
            logger.debug(f"   ⚠️ Produkt '{product['name']}' (kategorie: '{product_category}') nemá odpovídající zjednodušenou kategorii")
            return []
        
        # Získáme komplementární kategorie
        complementary_categories = relationships.get(matching_simplified_category, [])
        
        if not complementary_categories:
            logger.debug(f"   ⚠️ Kategorie '{matching_simplified_category}' nemá komplementární vztahy")
            return []
        
        # Najdeme produkty pro každou komplementární kategorii
        all_complementary_products = []
        for target_category, weight in complementary_categories[:3]:  # Omezíme na top 3 kategorie
            complementary_products = self.find_products_by_improved_pattern(target_category, limit=5)
            
            for comp_product in complementary_products:
                comp_product['complementary_weight'] = weight
                comp_product['complementary_category'] = target_category
                comp_product['source_product_id'] = product['id']
                comp_product['source_product_name'] = product['name']
                comp_product['source_category'] = matching_simplified_category
                all_complementary_products.append(comp_product)
        
        # Seřadíme podle váhy
        all_complementary_products.sort(key=lambda x: x['complementary_weight'], reverse=True)
        
        if all_complementary_products:
            logger.info(f"   ✅ '{product['name']}' ({matching_simplified_category}) -> {len(all_complementary_products)} doporučení")
        
        return all_complementary_products
    
    def save_test_results(self, results: Dict, filename: str = "test_complementary_jabkolevne.json"):
        """Uloží testovací výsledky."""
        try:
            json_data = {
                'metadata': {
                    'tenant': TENANT_ID,
                    'test_run': True,
                    'total_products_with_recommendations': len(results),
                    'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_recommendations': sum(item['total_recommendations'] for item in results.values())
                },
                'sample_recommendations': {}
            }
            
            for product_id, data in results.items():
                json_data['sample_recommendations'][str(product_id)] = {
                    'source_product': data['source_product'],
                    'complementary_products': data['complementary_products'][:5],  # Top 5 doporučení
                    'total_recommendations': data['total_recommendations']
                }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 Testovací výsledky uloženy do: {filename}")
            
        except Exception as e:
            logger.error(f"❌ Chyba při ukládání testovacích výsledků: {e}")
    
    def generate_test_statistics(self, results: Dict):
        """Vygeneruje statistiky pro testovací běh."""
        try:
            total_products = len(results)
            if total_products == 0:
                logger.info("📊 Žádné výsledky pro statistiky")
                return
            
            total_recommendations = sum(item['total_recommendations'] for item in results.values())
            avg_recommendations = total_recommendations / total_products
            
            # Nejčastější zdroje
            source_categories = defaultdict(int)
            target_categories = defaultdict(int)
            
            for data in results.values():
                if data['complementary_products']:
                    source_cat = data['complementary_products'][0].get('source_category', 'Neznámá')
                    source_categories[source_cat] += 1
                    
                    for comp_product in data['complementary_products']:
                        target_categories[comp_product['complementary_category']] += 1
            
            logger.info("📊 === STATISTIKY TESTOVACÍHO BĚHU ===")
            logger.info(f"📦 Produktů s doporučeními: {total_products}")
            logger.info(f"🔗 Celkem doporučení: {total_recommendations}")
            logger.info(f"📈 Průměr doporučení na produkt: {avg_recommendations:.1f}")
            
            logger.info("🎯 Top 3 zdrojové kategorie:")
            for category, count in sorted(source_categories.items(), key=lambda x: x[1], reverse=True)[:3]:
                logger.info(f"   - {category}: {count} produktů")
            
            logger.info("🏆 Top 3 doporučované kategorie:")
            for category, count in sorted(target_categories.items(), key=lambda x: x[1], reverse=True)[:3]:
                logger.info(f"   - {category}: {count} doporučení")
                
        except Exception as e:
            logger.error(f"❌ Chyba při generování statistik: {e}")
    
    def close_connections(self):
        """Uzavře připojení."""
        if self.neo4j_driver:
            self.neo4j_driver.close()
        if self.qdrant_client:
            self.qdrant_client.close()
        logger.info("🔒 Připojení uzavřena")

def main():
    """Hlavní funkce pro testovací běh."""
    calculator = ImprovedComplementaryCalculator()
    
    try:
        # Připojení
        if not calculator.connect_to_databases():
            return False
        
        # Testovací výpočet
        results = calculator.calculate_for_limited_products(max_products=100)
        
        if not results:
            logger.error("❌ Žádné výsledky z testovacího běhu")
            return False
        
        # Uložení a statistiky
        calculator.save_test_results(results)
        calculator.generate_test_statistics(results)
        
        logger.info("🎉 === TESTOVACÍ BĚH ÚSPĚŠNĚ DOKONČEN ===")
        return True
        
    except Exception as e:
        logger.error(f"❌ Neočekávaná chyba: {e}")
        return False
        
    finally:
        calculator.close_connections()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 