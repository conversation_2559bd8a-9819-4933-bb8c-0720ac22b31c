#!/usr/bin/env python3
"""
Debug skript pro analýzu problému s ultra skriptem compute_complementary.py
pro jabkolevne tenant. Zkusí zpracovat jeden produkt a vypíše detailní diagnostiku.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
sys.path.insert(0, project_root)

# Načtení .env
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# Konfigurace logování
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from core.clients import get_qdrant_async_client, get_gemini_client
from core.utils import create_qdrant_id
from neo4j import AsyncGraphDatabase, basic_auth
from qdrant_client.http.models import Filter, FieldCondition, MatchValue

async def get_neo4j_driver():
    """Vytvoří Neo4j driver."""
    uri = os.getenv("NEO4J_URI")
    user = os.getenv("NEO4J_USER")
    password = os.getenv("NEO4J_PASSWORD")
    
    try:
        driver = AsyncGraphDatabase.driver(uri, auth=basic_auth(user, password))
        await driver.verify_connectivity()
        logger.info("✅ Neo4j driver úspěšně připojen")
        return driver
    except Exception as e:
        logger.error(f"❌ Chyba při připojení k Neo4j: {e}")
        raise

async def debug_single_product(tenant_id: str = "jabkolevne", test_limit: int = 1):
    """
    Debug analýza jednoho produktu - najde kde proces selhává.
    
    Analógia: Jako když automechanik kontroluje auto krok za krokem - 
    motor, brzdy, světla... dokud nenajde, kde je problém.
    """
    
    logger.info(f"🔍 === DEBUG ANALÝZA PRO TENANT: {tenant_id} ===")
    
    # Inicializace klientů
    qdrant_client = get_qdrant_async_client()
    gemini_client = get_gemini_client()
    neo4j_driver = await get_neo4j_driver()
    
    try:
        products_collection = f"real_products_{tenant_id}"
        
        # 1. Test Qdrant připojení a kolekce
        logger.info("🔍 Krok 1: Testuju Qdrant připojení...")
        try:
            collections = await qdrant_client.get_collections()
            collection_names = [c.name for c in collections.collections]
            logger.info(f"✅ Dostupné kolekce: {collection_names}")
            
            if products_collection not in collection_names:
                logger.error(f"❌ Kolekce {products_collection} neexistuje!")
                return
            else:
                logger.info(f"✅ Kolekce {products_collection} existuje")
        except Exception as e:
            logger.error(f"❌ Chyba při přístupu k Qdrant: {e}")
            return
        
        # 2. Získání vzorku produktů
        logger.info("🔍 Krok 2: Získávám vzorek produktů...")
        try:
            scroll_result, _ = await qdrant_client.scroll(
                collection_name=products_collection,
                limit=test_limit,
                with_payload=True
            )
            
            if not scroll_result:
                logger.error(f"❌ Žádné produkty v kolekci {products_collection}")
                return
            
            logger.info(f"✅ Nalezeno {len(scroll_result)} produktů")
            
            # Vyberme první produkt pro detailní analýzu
            test_point = scroll_result[0]
            test_product_id = str(test_point.payload.get("product_id") or test_point.id)
            test_category = test_point.payload.get("category", "")
            test_name = test_point.payload.get("name", "")[:50]
            
            logger.info(f"🎯 Testovací produkt:")
            logger.info(f"   ID: {test_product_id}")
            logger.info(f"   Název: {test_name}")
            logger.info(f"   Kategorie: {test_category}")
            
        except Exception as e:
            logger.error(f"❌ Chyba při získávání produktů: {e}")
            return
        
        # 3. Test Neo4j cache
        logger.info("🔍 Krok 3: Testuju Neo4j komplementární kategorie...")
        try:
            # Načtení všech komplementárních kategorií
            session = neo4j_driver.session()
            query = f"""
            MATCH (source:Category_{tenant_id})-[r:RELATED_TO]->(target:Category_{tenant_id})
            RETURN source.name AS source_name, target.name AS target_name, r.weight AS weight
            ORDER BY r.weight DESC
            """
            
            result = await session.run(query)
            records = await result.data()
            await session.close()
            
            logger.info(f"✅ Nalezeno {len(records)} RELATED_TO vztahů")
            
            if len(records) == 0:
                logger.error("❌ Žádné RELATED_TO vztahy v Neo4j!")
                return
            
            # Ukázka prvních vztahů
            logger.info("📋 Vzorky vztahů:")
            for i, record in enumerate(records[:5]):
                logger.info(f"   {record['source_name']} -> {record['target_name']} (váha: {record['weight']})")
            
            # Hledání pro naši testovací kategorii
            matching_relations = [r for r in records if r['source_name'] == test_category]
            logger.info(f"🎯 Pro kategorii '{test_category}' nalezeno {len(matching_relations)} komplementárních vztahů")
            
            if len(matching_relations) == 0:
                # Zkusíme částečnou shodu
                partial_matches = [r for r in records if test_category in r['source_name'] or r['source_name'] in test_category]
                logger.info(f"🔍 Částečné shody: {len(partial_matches)}")
                for match in partial_matches[:3]:
                    logger.info(f"   Částečná shoda: {match['source_name']} -> {match['target_name']}")
            
        except Exception as e:
            logger.error(f"❌ Chyba při testování Neo4j: {e}")
            return
        
        # 4. Test vyhledávání kandidátů
        logger.info("🔍 Krok 4: Testuju vyhledávání kandidátů...")
        try:
            if matching_relations:
                # Použijeme první komplementární kategorii
                target_category = matching_relations[0]['target_name']
                logger.info(f"🎯 Hledám kandidáty v kategorii: {target_category}")
                
                # Scroll pro tuto kategorii
                category_scroll, _ = await qdrant_client.scroll(
                    collection_name=products_collection,
                    limit=10,
                    with_payload=True
                )
                
                candidates_found = 0
                for point in category_scroll:
                    point_category = point.payload.get("category", "")
                    point_id = str(point.payload.get("product_id") or point.id)
                    
                    # Kontrola shody kategorie
                    if (point_category == target_category or 
                        point_category.endswith(f" > {target_category}") or
                        target_category in point_category):
                        candidates_found += 1
                        logger.info(f"   ✅ Kandidát: {point.payload.get('name', '')[:40]} (kategorie: {point_category})")
                
                logger.info(f"✅ Nalezeno {candidates_found} kandidátů v kategorii {target_category}")
                
                if candidates_found == 0:
                    logger.warning(f"⚠️ Žádní kandidáti v kategorii {target_category}")
                    # Zkusme všechny kategorie
                    all_categories = set()
                    for point in category_scroll:
                        all_categories.add(point.payload.get("category", ""))
                    logger.info(f"📊 Dostupné kategorie v databázi (vzorek): {list(all_categories)[:10]}")
            
        except Exception as e:
            logger.error(f"❌ Chyba při testování kandidátů: {e}")
            return
        
        # 5. Test vectorového vyhledávání
        logger.info("🔍 Krok 5: Testuju vectorové vyhledávání...")
        try:
            # Získání vektoru testovacího produktu
            test_points = await qdrant_client.retrieve(
                collection_name=products_collection,
                ids=[create_qdrant_id(test_product_id)],
                with_vectors=True
            )
            
            if test_points and test_points[0].vector:
                vector = test_points[0].vector
                if isinstance(vector, dict):
                    vector = vector.get("combined", [])
                
                logger.info(f"✅ Vektor získán, dimenze: {len(vector) if vector else 0}")
                
                if vector:
                    # Test similarity search
                    search_result = await qdrant_client.search(
                        collection_name=products_collection,
                        query_vector=vector,
                        limit=5,
                        with_payload=True
                    )
                    
                    logger.info(f"✅ Similarity search nalezl {len(search_result)} podobných produktů")
                    for hit in search_result[:3]:
                        logger.info(f"   📍 {hit.payload.get('name', '')[:40]} (score: {hit.score:.3f})")
                else:
                    logger.error("❌ Vektor je prázdný")
            else:
                logger.error("❌ Nepodařilo se získat vektor")
                
        except Exception as e:
            logger.error(f"❌ Chyba při testování vektorů: {e}")
        
        # 6. Celkové shrnutí
        logger.info("🎯 === SHRNUTÍ DEBUG ANALÝZY ===")
        logger.info(f"📊 Kolekce {products_collection}: ✅")
        logger.info(f"📊 Produkty v kolekci: ✅ ({len(scroll_result)} nalezeno)")
        logger.info(f"📊 Neo4j RELATED_TO vztahy: ✅ ({len(records)} nalezeno)")
        logger.info(f"📊 Komplementární vztahy pro testovací kategorii: {'✅' if matching_relations else '❌'}")
        logger.info(f"📊 Kandidáti v cílové kategorii: {'✅' if candidates_found > 0 else '❌'}")
        
        if not matching_relations:
            logger.warning("⚠️ HLAVNÍ PROBLÉM: Testovací kategorie nemá komplementární vztahy!")
            logger.info("💡 Možná řešení:")
            logger.info("   1. Zkontrolovat mapování kategorií v Neo4j")
            logger.info("   2. Upravit kategorie produktů v Qdrant")
            logger.info("   3. Rozšířit komplementární vztahy v Neo4j")
        
        if candidates_found == 0 and matching_relations:
            logger.warning("⚠️ PROBLÉM: Komplementární kategorie nemají produkty!")
            logger.info("💡 Řešení: Zkontrolovat názvy kategorií - možná nesouhlasí mezi Neo4j a Qdrant")
        
    finally:
        await qdrant_client.close()
        await neo4j_driver.close()
        logger.info("🔒 Všechna připojení uzavřena")

if __name__ == "__main__":
    logger.info("🚀 Spouštím DEBUG analýzu ultra skriptu pro jabkolevne...")
    
    try:
        asyncio.run(debug_single_product("jabkolevne", test_limit=5))
    except Exception as e:
        logger.error(f"💥 Kritická chyba v debug analýze: {e}")
        sys.exit(1) 