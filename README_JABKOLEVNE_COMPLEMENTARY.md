# Komplementární kategorie pro jabkolevne.cz

## Přehled

Tento dokument popisuje implementaci komplementárních kategorií a vztahů pro e-shop jabkolevne.cz. Systém umožňuje doporučování doplňkových produktů na základě grafových vztahů v Neo4j databázi.

## Struktura kategorií

### Zjednodušené kategorie

Namísto komplexní hierarchické struktury jsou vytvořeny zjednodušené kategorie, které lépe odpovídají obchodn<PERSON> logice:

```
Hlavní produktové kategorie:
- iPhone
- iPad  
- MacBook
- Apple Watch
- AirPods

Příslušenství:
- Nabíječky (Apple)
- Kabely (Apple)
- Pouzdra pro iPhone
- Ochranná skla pro iPhone
- Pouzdra pro iPad
- Ochranná skla pro iPad
- Řemínky pro Apple Watch
- Bezdrátové na<PERSON>í<PERSON>ky (Apple)
- Powerbanky (pro Apple)
- Apple Pencil
- AirTag
- Pouzdra pro AirPods
- Sluchátka (Apple ostatní)
- Reproduktory (Apple kompatibilní)
- Apple TV
- Příslušenství do auta (Apple)
```

## Komplementární vztahy

### iPhone komplementární produkty
- **Pouzdra pro iPhone** (váha: 1.0) - Nejsilnější doporučení
- **Ochranná skla pro iPhone** (váha: 1.0) - Nejsilnější doporučení
- **Nabíječky (Apple)** (váha: 0.9) - Velmi silné doporučení
- **Kabely (Apple)** (váha: 0.9) - Velmi silné doporučení
- **Bezdrátové nabíječky (Apple)** (váha: 0.8) - Silné doporučení
- **AirPods** (váha: 0.8) - Silné doporučení
- **Powerbanky (pro Apple)** (váha: 0.7) - Střední doporučení
- **Příslušenství do auta (Apple)** (váha: 0.6) - Slabší doporučení

### iPad komplementární produkty
- **Pouzdra pro iPad** (váha: 1.0)
- **Ochranná skla pro iPad** (váha: 1.0)
- **Apple Pencil** (váha: 0.9)
- **Nabíječky (Apple)** (váha: 0.9)
- **Kabely (Apple)** (váha: 0.9)
- **AirPods** (váha: 0.7)

### MacBook komplementární produkty
- **Nabíječky (Apple)** (váha: 0.9)
- **Kabely (Apple)** (váha: 0.8)
- **AirPods** (váha: 0.7)

### Apple Watch komplementární produkty
- **Řemínky pro Apple Watch** (váha: 1.0)
- **Nabíječky (Apple)** (váha: 0.8)
- **AirPods** (váha: 0.7)

### AirPods komplementární produkty
- **Pouzdra pro AirPods** (váha: 0.9)
- **Bezdrátové nabíječky (Apple)** (váha: 0.7)

## Technická implementace

### Soubory a skripty

1. **`create_jabkolevne_complementary_categories.py`**
   - Hlavní skript pro vytvoření kategorií a vztahů
   - Mapuje existující kategorie na zjednodušené
   - Vytváří obousměrné komplementární vztahy s váhami

2. **`test_jabkolevne_complementary.py`**
   - Testovací skript pro ověření funkčnosti
   - Validuje existenci kategorií a vztahů
   - Testuje dotazy na komplementární produkty

3. **`categories_jabkolevne.json`**
   - Zdroj existujících kategorií z Qdrant databáze
   - Slouží jako mapovací zdroj pro zjednodušené kategorie

### Mapování kategorií

Skript používá následující mapování z původních kategorií:

```python
CATEGORY_MAPPING = {
    "iPhone": "iPhone",
    "iPad": "iPad", 
    "MacBook": "MacBook",
    "Mac": "MacBook",
    "Watch": "Apple Watch",
    "AirPods": "AirPods",
    "Kryty na mobilní telefony Apple": "Pouzdra pro iPhone",
    "Tvrzená skla": "Ochranná skla pro iPhone",
    "Nabíječky": "Nabíječky (Apple)",
    "kabely": "Kabely (Apple)",
    # ... další mapování
}
```

### Neo4j databázová struktura

#### Uzly (Nodes)
- **Label**: `Category_jabkolevne`
- **Properties**:
  - `name`: Název kategorie (string)
  - `type`: "simplified" (pro rozlišení od hierarchických kategorií)

#### Vztahy (Relationships)
- **Type**: `COMPLEMENTARY_TO`
- **Properties**:
  - `weight`: Váha vztahu (float, 0.6-1.0)
  - `created_by`: "jabkolevne_script" (pro identifikaci původu)

#### Příklad Cypher dotazu
```cypher
MATCH (source:Category_jabkolevne {name: 'iPhone'})
-[r:COMPLEMENTARY_TO]->
(target:Category_jabkolevne)
WHERE r.created_by = 'jabkolevne_script'
RETURN target.name as category, r.weight as weight
ORDER BY r.weight DESC
```

## Použití

### Spuštění skriptu
```bash
# Vytvoření kategorií a vztahů
python create_jabkolevne_complementary_categories.py

# Testování funkčnosti
python test_jabkolevne_complementary.py
```

### Výstup skriptu
```
🚀 === Vytváření komplementárních kategorií pro jabkolevne.cz ===
✅ Připojení k Neo4j úspěšné
📂 Načteno 200 existujících kategorií z JSON
📊 Mapování kategorií:
   ✅ Mapováno: 197 kategorií
   ❓ Nemapováno: 3 kategorií
🏗️ Vytvářím 21 zjednodušených kategorií...
✅ Vytvořeno/aktualizováno 21 zjednodušených kategorií
🔗 Vytvářím 22 komplementárních vztahů...
✅ Vytvořeno 44 komplementárních vztahů
🎉 === Úspěšně dokončeno vytváření komplementárních kategorií ===
```

## Statistiky

Po implementaci máme:
- **21 kategorií** (zjednodušených)
- **44 komplementárních vztahů** (obousměrné)
- **Největší počet komplementárních produktů**: iPhone (8 produktů)

## Integrace s doporučovacím systémem

Tato struktura může být využita v následujících scénářích:

1. **Doporučování na stránce produktu**
   - Při prohlížení iPhone → doporučit pouzdra, ochranná skla, nabíječky
   - Při prohlížení iPad → doporučit Apple Pencil, pouzdra, ochranná skla

2. **Křížové prodeje v košíku**
   - Při přidání iPhone do košíku → nabídnout nejsilněji komplementární produkty

3. **Personalizované doporučování**
   - Využití vah pro řazení doporučení podle relevance

## Poznámky k implementaci

- **Obousměrné vztahy**: Každý vztah má zpětný protějšek s nižší váhou (80% původní váhy)
- **Váhový systém**: 1.0 = nejsilnější doporučení, 0.6 = nejslabší doporučení
- **Flexibilita**: Lze snadno přidat nové kategorie nebo upravit váhy
- **Testování**: Kompletní testovací sada ověřuje všechny aspekty implementace

## Budoucí rozšíření

1. **Dynamické učení**: Adaptace vah na základě nákupního chování
2. **Sezónní faktory**: Úprava doporučení podle období roku
3. **Personalizace**: Individuální váhy pro různé typy zákazníků
4. **A/B testing**: Experimentování s různými přístupy k doporučování

## Autor

Pavel Kohout - 2025-01-17 