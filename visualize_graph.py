#!/usr/bin/env python3
import json
import networkx as nx
import matplotlib.pyplot as plt
import argparse
from pathlib import Path
import re

def normalize_category_name(name):
    """Zkrátí název kategorie pro lepší zobrazení."""
    # Pokud obsahuje '>', vezme poslední č<PERSON>
    if ' > ' in name:
        return name.split(' > ')[-1]
    return name

def load_category_relationships(file_path):
    """Načte vztahy mezi kategoriemi z JSON souboru."""
    with open(file_path, 'r', encoding='utf-8') as f:
        relationships = json.load(f)
    return relationships

def create_graph(relationships):
    """Vytvoří graf z načtených vztahů."""
    G = nx.DiGraph()  # Orientovaný graf

    # Přidá hrany a uzly
    for rel in relationships:
        source = rel['source_category']
        target = rel['target_category']
        strength = rel.get('strength', 0.5)  # <PERSON>ý<PERSON>zí síla vztahu
        reason = rel.get('reason', '')
        
        # <PERSON><PERSON><PERSON><PERSON> mapování podle síly vztahu (0.6-1.0)
        normalized_strength = (strength - 0.6) / 0.4 if strength >= 0.6 else 0
        
        # Přidá uzly s atributy
        G.add_node(source, type='source')
        G.add_node(target, type='target')
        
        # Přidá hranu s atributy
        G.add_edge(source, target, 
                   weight=strength,
                   reason=reason,
                   width=normalized_strength * 2 + 0.5,  # Šířka 0.5-2.5
                   alpha=normalized_strength * 0.5 + 0.5)  # Průhlednost 0.5-1.0
    
    return G

def visualize_graph(G, output_file=None, show=True):
    """Vizualizuje graf kategorií."""
    plt.figure(figsize=(20, 16))
    
    # Použije algoritmus pro optimální rozmístění uzlů
    # pos = nx.spring_layout(G, k=0.15, iterations=50)
    pos = nx.kamada_kawai_layout(G)
    
    # Extrahuje data pro vizualizaci
    edge_widths = [G[u][v]['width'] for u, v in G.edges()]
    edge_alphas = [G[u][v]['alpha'] for u, v in G.edges()]
    
    # Zkrátí názvy kategorií pro lepší zobrazení
    labels = {node: normalize_category_name(node) for node in G.nodes()}
    
    # Najde hlavní kategorie (ty, které jsou na začátku cesty)
    main_categories = set()
    for node in G.nodes():
        if ' > ' in node:
            main_cat = node.split(' > ')[0]
            main_categories.add(main_cat)
    
    # Mapuje hlavní kategorie na barvy
    color_map = {}
    colors = plt.cm.tab20.colors
    for i, cat in enumerate(main_categories):
        color_map[cat] = colors[i % len(colors)]
    
    # Přiřadí barvy uzlům podle jejich hlavní kategorie
    node_colors = []
    for node in G.nodes():
        if ' > ' in node:
            main_cat = node.split(' > ')[0]
            node_colors.append(color_map.get(main_cat, 'lightgray'))
        else:
            node_colors.append('lightgray')
    
    # Vykreslí hrany
    edges = nx.draw_networkx_edges(
        G, pos,
        width=edge_widths,
        alpha=0.7,
        edge_color='gray',
        arrows=True,
        arrowsize=10,
        connectionstyle='arc3,rad=0.1'  # Zakřivené hrany
    )
    
    # Vykreslí uzly
    nodes = nx.draw_networkx_nodes(
        G, pos,
        node_size=500,
        node_color=node_colors,
        alpha=0.9
    )
    
    # Přidá popisky
    nx.draw_networkx_labels(
        G, pos,
        labels=labels,
        font_size=8,
        font_weight='bold'
    )
    
    # Přidá titulek
    plt.title("Graf propojených kategorií", fontsize=16)
    
    # Přidá legendu pro hlavní kategorie
    legend_elements = [plt.Line2D([0], [0], marker='o', color='w', 
                                   markerfacecolor=color, markersize=10, 
                                   label=cat) 
                       for cat, color in color_map.items()]
    plt.legend(handles=legend_elements, loc='upper right', title="Hlavní kategorie")
    
    # Přidá tooltip při najetí myší
    annot = plt.annotate("", xy=(0,0), xytext=(20,20),
                         textcoords="offset points",
                         bbox=dict(boxstyle="round", fc="w"),
                         arrowprops=dict(arrowstyle="->"))
    annot.set_visible(False)

    def update_annot(ind):
        node = list(G.nodes())[ind["ind"][0]]
        pos_node = pos[node]
        annot.xy = pos_node
        text = f"{node}\n"
        for neighbor in G.neighbors(node):
            text += f"→ {neighbor} ({G[node][neighbor]['weight']:.2f})\n"
            text += f"   {G[node][neighbor]['reason'][:50]}...\n"
        for pred in G.predecessors(node):
            if pred != node:  # Vyhne se self-loops
                text += f"← {pred} ({G[pred][node]['weight']:.2f})\n"
                text += f"   {G[pred][node]['reason'][:50]}...\n"
        annot.set_text(text)

    def hover(event):
        vis = annot.get_visible()
        if event.inaxes == plt.gca():
            cont, ind = nodes.contains(event)
            if cont:
                update_annot(ind)
                annot.set_visible(True)
                plt.draw()
            else:
                if vis:
                    annot.set_visible(False)
                    plt.draw()
    
    plt.gcf().canvas.mpl_connect("motion_notify_event", hover)
    
    plt.axis('off')
    plt.tight_layout()
    
    # Uloží obrázek, pokud je specifikován výstupní soubor
    if output_file:
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        print(f"Graf uložen do: {output_file}")
    
    # Zobrazí interaktivní graf
    if show:
        plt.show()

def main():
    parser = argparse.ArgumentParser(description='Vizualizace grafu kategorických vztahů')
    parser.add_argument('--tenant', required=True, help='ID tenanta')
    parser.add_argument('--output', '-o', help='Výstupní soubor (PNG/PDF)')
    parser.add_argument('--no-show', action='store_true', help='Nezobrazuje interaktivní graf')
    args = parser.parse_args()
    
    # Cesta k souboru vztahů
    file_path = Path(f'data/state/category_relationships/{args.tenant}_relationships.json')
    
    if not file_path.exists():
        print(f"CHYBA: Soubor {file_path} neexistuje!")
        return
    
    # Načte vztahy a vytvoří graf
    relationships = load_category_relationships(file_path)
    print(f"Načteno {len(relationships)} vztahů mezi kategoriemi.")
    
    G = create_graph(relationships)
    print(f"Graf vytvořen s {G.number_of_nodes()} uzly a {G.number_of_edges()} hranami.")
    
    # Vizualizuje graf
    output_file = args.output if args.output else f"category_graph_{args.tenant}.png"
    visualize_graph(G, output_file=output_file, show=not args.no_show)

if __name__ == "__main__":
    main() 