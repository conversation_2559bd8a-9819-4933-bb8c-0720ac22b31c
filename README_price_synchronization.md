# Systém synchronizace a ověření cen produktů

## Úvod

Tento systém zajišťuje konzistenci cen produktů napříč různými úložišti dat v rámci doporučovacího systému Xcell. Zaj<PERSON>š<PERSON>uje, že ceny zobrazované API jsou vždy aktuální a odpovídají cenám v hlavní databázi produktů.

## Problém

Doporučovací systém Xcell používá několik datových úložišť:
1. **PostgreSQL tabulka `products`** - obsahuje hlavní data o produktech včetně aktuálních cen
2. **PostgreSQL tabulka `embeddings`** - obsahuje vektorové embeddingy produktů a metadata včetně cen
3. **API server** - načítá data z tabulky `embeddings` do paměti při startu

Když dojde ke změně ceny v tabulce `products`, je potřeba tuto změnu propagovat do tabulky `embeddings` a do paměti API serveru, aby API vracelo aktuální ceny.

## Řešení

Systém obsahuje dva hlavní skripty:

### 1. Skript pro aktualizaci cen (`update_prices_metadata.py`)

Tento skript aktualizuje ceny v tabulce `embeddings` podle aktuálních cen v tabulce `products`.

**Hlavní funkce:**
- Načte všechny tenanty z databáze
- Pro každého tenanta aktualizuje ceny všech jeho produktů
- Používá SQL dotaz, který efektivně aktualizuje velké množství dat
- Generuje podrobné logy o průběhu aktualizace

**Spouštění:**
```bash
python update_prices_metadata.py [--tenant TENANT_ID] [--db-url DB_URL]
```

**Parametry:**
- `--tenant` - ID tenanta (volitelné, pokud není zadáno, aktualizují se ceny pro všechny tenanty)
- `--db-url` - URL k PostgreSQL databázi (volitelné, výchozí hodnota je v konfiguraci)

### 2. Skript pro ověření cen (`verify_product_prices.py`)

Tento skript kontroluje konzistenci cen mezi tabulkami `products` a `embeddings` a mezi databází a API.

**Hlavní funkce:**
- Načte všechny tenanty z databáze (nebo konkrétního tenanta, pokud je zadán)
- Kontroluje konzistenci cen mezi tabulkami `products` a `embeddings`
- Volá API pro získání cen produktů a porovnává je s cenami v databázi
- Generuje detailní reporty v JSON formátu a log soubory

**Spouštění:**
```bash
python verify_product_prices.py [--tenant TENANT_ID] [--api-key API_KEY] [--sample SAMPLE_SIZE] [--skip-api]
```

**Parametry:**
- `--tenant` - ID tenanta (volitelné, pokud není zadáno, ověřují se ceny pro všechny tenanty)
- `--api-key` - API klíč pro přístup k API (volitelné, klíče lze načíst z proměnných prostředí)
- `--sample` - Počet produktů k ověření (náhodný vzorek, volitelné)
- `--skip-api` - Přeskočit ověření API cen (volitelné)

## Automatické spouštění

Oba skripty jsou nastaveny v cronu, aby se automaticky spouštěly každý den:

1. **Aktualizace cen** - spouští se každý den v 1:00 ráno:
```
0 1 * * * cd /opt/gallitec/xcell && docker exec xcell-recommendation_api-1 python /app/scripts/update_prices_metadata.py && docker restart xcell-recommendation_api-1
```

2. **Ověření cen** - spouští se každý den v 1:30 ráno (po aktualizaci cen a restartu API):
```
30 1 * * * cd /opt/gallitec/xcell/price_verifications && docker exec xcell-recommendation_api-1 python /app/scripts/verify_product_prices.py --sample 50 > /opt/gallitec/xcell/price_verifications/report_$(date +\%Y\%m\%d).log 2>&1
```

## Výstupy a reporty

Skript pro ověření cen generuje dva typy výstupů:

1. **Log soubory** - obsahují podrobné informace o průběhu ověření, včetně informací o tom, které produkty mají nekonzistentní ceny
   - Umístění: `/opt/gallitec/xcell/price_verifications/report_YYYYMMDD.log`

2. **JSON soubory** - obsahují strukturovaná data o výsledcích ověření, včetně seznamu produktů s nekonzistentními cenami
   - Umístění: `/opt/gallitec/xcell/price_verifications/price_verification_*.json`

## Interpretace výsledků

Skript pro ověření cen může identifikovat několik typů nekonzistencí:

1. **Rozdíl mezi tabulkami `products` a `embeddings`** - cena v tabulce `products` se liší od ceny v tabulce `embeddings`
   - To znamená, že aktualizace cen neprobíhá správně, nebo že došlo ke změně ceny po poslední aktualizaci

2. **Rozdíl mezi databází a API** - cena vrácená API se liší od ceny v databázi
   - To může znamenat, že API server nebyl restartován po aktualizaci cen, nebo že cena byla změněna po poslední aktualizaci

3. **Produkt nenalezen v API** - produkt z databáze nebyl nalezen v doporučeních API
   - Toto není skutečná nekonzistence cen, ale znamená, že API vrátí jiný produkt než ten, který byl požadován (typicky variantu nebo podobný produkt)
   - Toto je běžné chování doporučovacího systému

## Technické detaily

Skripty jsou implementovány v Pythonu a používají následující knihovny:
- `sqlalchemy` - pro práci s databází
- `requests` - pro volání API
- `json` - pro práci s JSON formátem
- `logging` - pro generování logů

## Monitoring v Grafaně

Pro lepší sledování stavu synchronizace cen lze implementovat monitoring v Grafaně:

### Návrh implementace

1. **Vytvoření metriky z JSON výstupů** - pomocí skriptu, který by četl JSON výstupy a odesílal metriky do Promethea

2. **Sledované metriky:**
   - Počet produktů s nekonzistentními cenami mezi tabulkami `products` a `embeddings`
   - Počet produktů s nekonzistentními cenami mezi databází a API
   - Celkový počet produktů s nekonzistentními cenami
   - Počet produktů aktualizovaných při poslední aktualizaci
   - Doba trvání aktualizace
   - Doba trvání ověření

3. **Vytvoření dashboardu v Grafaně:**
   - Panel s přehledem stavu synchronizace cen
   - Grafy s trendy nekonzistentních cen v čase
   - Tabulka s detaily posledních nekonzistencí
   - Alerty při překročení prahové hodnoty nekonzistentních cen

### Implementace metrik pro Prometheus

Pro implementaci monitoringu můžeme vytvořit nový skript `price_metrics_exporter.py`, který bude:
1. Číst JSON výstupy z adresáře `/opt/gallitec/xcell/price_verifications/`
2. Zpracovávat metriky
3. Exportovat je ve formátu, který Prometheus umí číst

Tento skript by pak mohl být nastaven jako HTTP server, který Prometheus pravidelně scrape-uje, nebo by mohl zapisovat metriky do souboru, který by Prometheus četl.

## Další kroky

1. **Implementace monitoringu** - vytvoření skriptu pro export metrik a dashboardu v Grafaně
2. **Optimalizace aktualizace cen** - možnost inkrementální aktualizace cen pouze pro změněné produkty
3. **Automatické notifikace** - posílání notifikací (např. e-mailem) při detekci většího počtu nekonzistentních cen 