import logging
import re
from typing import List, Dict, Any, Optional
from qdrant_client import AsyncQdrantClient
from qdrant_client.http import models

logger = logging.getLogger(__name__)

async def create_fulltext_indexes(qdrant_client: AsyncQdrantClient, tenant_id: str):
    """
    Vytvoří fulltextové indexy pro kolekci produktů daného tenanta.
    Qdrant podporuje fulltextové indexy od verze 0.10.0.
    """
    collection_name = f"real_products_{tenant_id}"
    
    # Seznam polí, která chceme indexovat pro fulltextové vyhledávání
    text_fields = [
        "name",
        "description",
        "product_code",  # Pro přesné vyhledávání produktových kódů
        "sku",
        "code",
        "category",
        "brand"
    ]
    
    logger.info(f"Vytvářím fulltextové indexy pro tenanta {tenant_id}")
    
    # Vytvoření fulltextových indexů pro každé pole
    for field in text_fields:
        try:
            # Speciální nastavení pro produktové kódy - použití prefix tokenizeru
            if field in ["product_code", "sku", "code"]:
                await qdrant_client.create_payload_index(
                    collection_name=collection_name,
                    field_name=field,
                    field_schema=models.TextIndexParams(
                        type="text",
                        tokenizer=models.TokenizerType.PREFIX,  # Prefix pro vyhledávání částečných kódů
                        min_token_len=1,  # Malá hodnota pro krátké kódy
                        max_token_len=20,
                        lowercase=True
                    )
                )
                logger.info(f"Vytvořen PREFIX fulltextový index pro pole {field} v kolekci {collection_name}")
            else:
                # Standardní nastavení pro ostatní textová pole
                await qdrant_client.create_payload_index(
                    collection_name=collection_name,
                    field_name=field,
                    field_schema=models.TextIndexParams(
                        type="text",
                        tokenizer=models.TokenizerType.WORD,  # Standardní tokenizer pro slova
                        min_token_len=2,
                        max_token_len=20,
                        lowercase=True
                    )
                )
                logger.info(f"Vytvořen WORD fulltextový index pro pole {field} v kolekci {collection_name}")
        except Exception as e:
            # Pokud index již existuje, Qdrant vrátí chybu, kterou můžeme ignorovat
            if "already exists" in str(e):
                logger.info(f"Fulltextový index pro pole {field} v kolekci {collection_name} již existuje")
            else:
                logger.warning(f"Nepodařilo se vytvořit fulltextový index pro pole {field}: {e}")


async def qdrant_fulltext_search(qdrant_client: AsyncQdrantClient, tenant_id: str, query: str, limit: int = 100):
    """
    Fulltextové vyhledávání přímo v Qdrantu pomocí nativních fulltextových indexů.
    Optimalizováno pro detekci produktových kódů a standardní fulltextové vyhledávání.
    """
    collection_name = f"real_products_{tenant_id}"
    
    # Detekce produktového kódu
    is_product_code = bool(re.match(r'^[\w\d]+$', query.strip())) and len(query.strip()) >= 4
    
    search_conditions = []
    
    if is_product_code:
        # Přesné vyhledávání podle produktového kódu s vysokou váhou
        search_conditions = [
            models.FieldCondition(
                key="product_code",
                match=models.MatchText(text=query)
            ),
            models.FieldCondition(
                key="sku",
                match=models.MatchText(text=query)
            ),
            models.FieldCondition(
                key="code",
                match=models.MatchText(text=query)
            )
        ]
    else:
        # Standardní fulltextové vyhledávání ve všech indexovaných polích
        search_conditions = [
            models.FieldCondition(
                key="name",
                match=models.MatchText(text=query)
            ),
            models.FieldCondition(
                key="description",
                match=models.MatchText(text=query)
            ),
            models.FieldCondition(
                key="category",
                match=models.MatchText(text=query)
            ),
            models.FieldCondition(
                key="brand",
                match=models.MatchText(text=query)
            )
        ]
    
    # Vytvoření filtru se spojením podmínek pomocí OR
    search_filter = models.Filter(
        should=search_conditions  # "should" znamená OR
    )
    
    try:
        # Provedení vyhledávání pomocí scroll metody, která umožňuje filtraci
        results = await qdrant_client.scroll(
            collection_name=collection_name,
            scroll_filter=search_filter,
            limit=limit,
            with_payload=True
        )
        
        # Zpracování výsledků
        fulltext_results = []
        
        if results and results[0]:
            for point in results[0]:
                product = point.payload
                
                # Přidání relevantních polí
                fulltext_results.append({
                    **product,
                    "score": 10.0 if is_product_code else 5.0,  # Simulované skóre
                    "method": "fulltext_qdrant"
                })
        
        logger.info(f"Qdrant fulltext search pro '{query}' vrátil {len(fulltext_results)} výsledků")
        return fulltext_results
    
    except Exception as e:
        logger.error(f"Chyba při fulltext vyhledávání v Qdrantu pro dotaz '{query}': {e}", exc_info=True)
        return []
