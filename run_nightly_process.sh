#!/bin/bash
# run_nightly_process.sh
# Skript pro spuštění kompletního nočního procesu

# Výchozí hodnoty
TENANTS=""
BATCH_SIZE=50
WORKERS=10
FORCE_SYNC=false
SKIP_SYNC=false
SKIP_COMPUTE=false
SKIP_CLEAN=false

# Zpracování parametrů
while [[ $# -gt 0 ]]; do
  case $1 in
    -t|--tenants)
      TENANTS="$2"
      shift 2
      ;;
    -b|--batch-size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    -w|--workers)
      WORKERS="$2"
      shift 2
      ;;
    -f|--force-sync)
      FORCE_SYNC=true
      shift
      ;;
    --skip-sync)
      SKIP_SYNC=true
      shift
      ;;
    --skip-compute)
      SKIP_COMPUTE=true
      shift
      ;;
    --skip-clean)
      SKIP_CLEAN=true
      shift
      ;;
    -h|--help)
      echo "Použití: $0 [MOŽNOSTI]"
      echo "Spouští kompletní no<PERSON>n<PERSON> proces synchronizace a aktualizace produktů."
      echo ""
      echo "Možnosti:"
      echo "  -t, --tenants SEZNAM      Seznam ID tenantů oddělených čárkou (výchozí: všichni dostupní)"
      echo "  -b, --batch-size POČET    Velikost dávky pro zpracování produktů (výchozí: 50)"
      echo "  -w, --workers POČET       Počet paralelních workerů (výchozí: 10)"
      echo "  -f, --force-sync          Vynutit aktualizaci všech produktů při synchronizaci"
      echo "  --skip-sync               Přeskočit krok synchronizace produktů"
      echo "  --skip-compute            Přeskočit krok výpočtu komplementárních produktů"
      echo "  --skip-clean              Přeskočit krok čištění referencí"
      echo "  -h, --help                Zobrazí tuto nápovědu"
      exit 0
      ;;
    *)
      echo "Neznámý parametr: $1"
      echo "Použijte --help pro zobrazení nápovědy."
      exit 1
      ;;
  esac
done

# Příprava parametrů pro skript
PARAMS=()

if [ -n "$TENANTS" ]; then
  PARAMS+=(--tenants "$TENANTS")
fi

PARAMS+=(--batch-size "$BATCH_SIZE")
PARAMS+=(--workers "$WORKERS")

if [ "$FORCE_SYNC" = true ]; then
  PARAMS+=(--force-sync)
fi

if [ "$SKIP_SYNC" = true ]; then
  PARAMS+=(--skip-sync)
fi

if [ "$SKIP_COMPUTE" = true ]; then
  PARAMS+=(--skip-compute)
fi

if [ "$SKIP_CLEAN" = true ]; then
  PARAMS+=(--skip-clean)
fi

# Výpis informací o spuštění
echo "=== SPOUŠTÍM KOMPLETNÍ NOČNÍ PROCES ==="
if [ -n "$TENANTS" ]; then
  echo "Tenanty: $TENANTS"
else
  echo "Tenanty: všichni dostupní"
fi
echo "Batch size: $BATCH_SIZE"
echo "Workers: $WORKERS"
echo "Force sync: $FORCE_SYNC"
echo "Skip sync: $SKIP_SYNC"
echo "Skip compute: $SKIP_COMPUTE"
echo "Skip clean: $SKIP_CLEAN"
echo ""

# Prevence uspání systému během dlouhého běhu
# "caffeinate" je příkaz na macOS, pro Linux by se použilo "systemd-inhibit" nebo podobné
if command -v caffeinate &> /dev/null; then
  caffeinate -s python run_nightly_process.py "${PARAMS[@]}"
else
  python run_nightly_process.py "${PARAMS[@]}"
fi

echo ""
echo "=== KOMPLETNÍ NOČNÍ PROCES DOKONČEN ===" 