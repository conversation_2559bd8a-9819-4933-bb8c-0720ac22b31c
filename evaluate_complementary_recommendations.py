#!/usr/bin/env python3
"""
Skript pro evaluaci kvality komplementárních doporučení.
Projde vybrané produkty a zobrazí jejich doporučení pro manuální posouzení.
"""

import requests
import json
from qdrant_client import QdrantClient
import random
from typing import List, Dict, Any
import sys

# Konfigurace
API_URL = "http://localhost:8000"
API_KEY = "jabkolevne-prod-2025-mN9kL2p4"
TENANT_ID = "jabkolevne"
QDRANT_URL = "http://localhost:6333"

def get_sample_products(client: QdrantClient, collection_name: str, sample_size: int = 10) -> List[Dict[str, Any]]:
    """Získá náhodný vzorek produktů z kolekce."""
    # Získáme celkový počet produktů
    collection_info = client.get_collection(collection_name)
    total_points = collection_info.points_count
    
    print(f"Celkem produktů v kolekci: {total_points}")
    
    # Získáme náhodný vzorek
    products = []
    offset = None
    limit = min(100, total_points)  # Načteme po 100 produktech
    
    all_products = []
    while len(all_products) < min(1000, total_points):  # Načteme max 1000 produktů
        result, next_offset = client.scroll(
            collection_name=collection_name,
            limit=limit,
            offset=offset,
            with_payload=True
        )
        all_products.extend(result)
        offset = next_offset
        if not offset:
            break
    
    # Vybereme náhodný vzorek
    if len(all_products) > sample_size:
        products = random.sample(all_products, sample_size)
    else:
        products = all_products
    
    return [
        {
            "id": p.payload.get("product_id") or p.payload.get("original_id"),
            "name": p.payload.get("name", ""),
            "category": p.payload.get("category", ""),
            "price": p.payload.get("price", 0)
        }
        for p in products
    ]

def get_complementary_products(product_id: str) -> List[Dict[str, Any]]:
    """Získá komplementární produkty pro daný produkt ID přes API."""
    headers = {"X-API-Key": API_KEY}
    url = f"{API_URL}/complementary-products/{TENANT_ID}/by-product-id/{product_id}"
    
    try:
        response = requests.get(url, headers=headers, params={"limit": 10})
        if response.status_code == 200:
            return response.json()
        else:
            print(f"Chyba API: {response.status_code} - {response.text}")
            return []
    except Exception as e:
        print(f"Chyba při volání API: {e}")
        return []

def evaluate_recommendations():
    """Hlavní funkce pro evaluaci doporučení."""
    # Připojení k Qdrant
    client = QdrantClient(url=QDRANT_URL)
    collection_name = f"real_products_{TENANT_ID}"
    
    # Získat vzorek produktů
    print("Získávám vzorek produktů...")
    sample_products = get_sample_products(client, collection_name, sample_size=15)
    
    print(f"\nZískáno {len(sample_products)} produktů pro evaluaci\n")
    print("=" * 80)
    
    # Pro každý produkt získat a zobrazit doporučení
    for i, product in enumerate(sample_products, 1):
        print(f"\n[{i}/{len(sample_products)}] ZDROJOVÝ PRODUKT:")
        print(f"ID: {product['id']}")
        print(f"Název: {product['name']}")
        print(f"Kategorie: {product['category']}")
        print(f"Cena: {product['price']} CZK")
        
        # Získat komplementární produkty
        print("\n📦 KOMPLEMENTÁRNÍ DOPORUČENÍ:")
        recommendations = get_complementary_products(product['id'])
        
        if recommendations:
            print(f"Nalezeno {len(recommendations)} doporučení:\n")
            for j, rec in enumerate(recommendations, 1):
                print(f"  {j}. {rec['name']}")
                print(f"     - ID: {rec['product_id']}")
                print(f"     - Cena: {rec['price']}")
                print(f"     - Skóre: {rec.get('similarity', 'N/A')}")
                print()
        else:
            print("❌ Žádná doporučení nenalezena")
        
        print("-" * 80)
        
        # Možnost přeskočit nebo ukončit
        if i < len(sample_products):
            user_input = input("\nStiskněte Enter pro další produkt, 'q' pro ukončení: ")
            if user_input.lower() == 'q':
                break

def analyze_recommendation_patterns():
    """Analyzuje vzory v doporučeních."""
    client = QdrantClient(url=QDRANT_URL)
    cache_collection = f"complementary_products_{TENANT_ID}"
    
    print("\n📊 ANALÝZA VZORŮ DOPORUČENÍ")
    print("=" * 80)
    
    # Získat všechny záznamy z cache
    all_records = []
    offset = None
    while True:
        result, next_offset = client.scroll(
            collection_name=cache_collection,
            limit=100,
            offset=offset,
            with_payload=True
        )
        all_records.extend(result)
        offset = next_offset
        if not offset or len(all_records) >= 1000:  # Max 1000 záznamů
            break
    
    print(f"Analyzuji {len(all_records)} záznamů...")
    
    # Statistiky
    total_recommendations = 0
    recommendation_counts = {}
    score_distribution = {
        "0.9-1.0": 0,
        "0.8-0.9": 0,
        "0.7-0.8": 0,
        "0.6-0.7": 0,
        "0.5-0.6": 0,
        "<0.5": 0
    }
    
    for record in all_records:
        complementary_ids = record.payload.get("complementary_ids", [])
        scores = record.payload.get("scores", [])
        
        total_recommendations += len(complementary_ids)
        
        # Počítáme, kolikrát se každý produkt objevuje jako doporučení
        for comp_id in complementary_ids:
            recommendation_counts[comp_id] = recommendation_counts.get(comp_id, 0) + 1
        
        # Distribuce skóre
        for score in scores:
            if score >= 0.9:
                score_distribution["0.9-1.0"] += 1
            elif score >= 0.8:
                score_distribution["0.8-0.9"] += 1
            elif score >= 0.7:
                score_distribution["0.7-0.8"] += 1
            elif score >= 0.6:
                score_distribution["0.6-0.7"] += 1
            elif score >= 0.5:
                score_distribution["0.5-0.6"] += 1
            else:
                score_distribution["<0.5"] += 1
    
    # Zobrazit výsledky
    print(f"\n📈 Celkem doporučení: {total_recommendations}")
    print(f"📊 Průměr doporučení na produkt: {total_recommendations / len(all_records):.1f}")
    
    print("\n🎯 Distribuce skóre:")
    for range_name, count in score_distribution.items():
        percentage = (count / total_recommendations * 100) if total_recommendations > 0 else 0
        print(f"  {range_name}: {count} ({percentage:.1f}%)")
    
    print("\n🏆 Top 20 nejčastěji doporučovaných produktů:")
    top_recommendations = sorted(recommendation_counts.items(), key=lambda x: x[1], reverse=True)[:20]
    
    # Získat detaily produktů
    products_collection = f"real_products_{TENANT_ID}"
    for i, (product_id, count) in enumerate(top_recommendations, 1):
        # Získat detail produktu
        try:
            results = client.scroll(
                collection_name=products_collection,
                scroll_filter={"must": [{"key": "product_id", "match": {"value": product_id}}]},
                limit=1,
                with_payload=True
            )
            if results[0]:
                product = results[0][0].payload
                print(f"  {i}. {product.get('name', 'N/A')} (ID: {product_id})")
                print(f"     Kategorie: {product.get('category', 'N/A')}")
                print(f"     Doporučeno: {count}x")
                print()
        except:
            print(f"  {i}. ID: {product_id} - doporučeno {count}x")

if __name__ == "__main__":
    print("🔍 EVALUACE KOMPLEMENTÁRNÍCH DOPORUČENÍ")
    print("=" * 80)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--analyze":
        analyze_recommendation_patterns()
    else:
        evaluate_recommendations()
        
        print("\n" + "=" * 80)
        user_input = input("\nChcete zobrazit analýzu vzorů? (a/n): ")
        if user_input.lower() == 'a':
            analyze_recommendation_patterns() 