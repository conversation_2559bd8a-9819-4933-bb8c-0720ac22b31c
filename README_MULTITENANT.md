# Multitenant 3-v<PERSON>vá architektura pro doporučování a vyhledávání produktů

Tento dokument popisuje, jak používat multitenant skripty pro inicializaci a testování 3-vrstvé architektury pro doporučování a vyhledávání produktů.

## P<PERSON>ehled

Systém podporuje více tenantů (e-shopů) a pro každého z nich lze spustit inicializaci a testování pomocí univerzálních skriptů. Není potřeba vytvářet samostatné skripty pro každého tenanta.

## Dostupné skripty

### 1. Inicializace multi-embedding kolekce

```bash
./init_multi_embedding_collection_multitenant.sh <tenant_id>
```

Příklad:
```bash
./init_multi_embedding_collection_multitenant.sh filsonstore
```

### 2. Spuštěn<PERSON> vylepšené verze 3-vrstv<PERSON> architektury

```bash
./run_enhanced_multitenant.sh <tenant_id> [mo<PERSON><PERSON><PERSON>]
```

Volitelné parametry:
- `-h, --help` - Zobraz<PERSON> nápovědu
- `-l, --llm-provider <prov>` - LLM provider (anthropic, openai) [výchozí: anthropic]
- `-w, --max-workers <num>` - Maximální počet paralelních workerů [výchozí: 5]
- `-b, --batch-size <num>` - Velikost dávky pro zpracování [výchozí: 20]
- `-s, --sample <num>` - Velikost vzorku (počet produktů)
- `-c, --complementary` - Použít LLM pro generování komplementárních produktů
- `-f, --force` - Vynutit aktualizaci, i když data již existují

Příklad:
```bash
./run_enhanced_multitenant.sh filsonstore -w 10 -b 50 -c -f
```

### 3. Spuštění testů

```bash
./run_tests_multitenant.sh <tenant_id> [možnosti]
```

Volitelné parametry:
- `-h, --help` - Zobrazí nápovědu
- `-l, --llm-provider <prov>` - LLM provider (anthropic, openai) [výchozí: anthropic]
- `-w, --max-workers <num>` - Maximální počet paralelních workerů [výchozí: 5]
- `-b, --batch-size <num>` - Velikost dávky pro zpracování [výchozí: 20]
- `-s, --sample <num>` - Velikost vzorku (počet produktů) [výchozí: 50]
- `-L, --large-test` - Spustit test na velkém datasetu

Příklad:
```bash
./run_tests_multitenant.sh filsonstore -w 10 -b 50 -s 100
```

Pro spuštění testu na velkém datasetu:
```bash
./run_tests_multitenant.sh filsonstore -L
```

## Podporovaní tenanti

Systém aktuálně podporuje následující tenanty:
- filsonstore
- avenberg
- coolpohyb
- brawson
- brawson_2
- testshop

## Komponenty 3-vrstvé architektury

1. **Multi-Embedding Stratifikace**
   - Implementace 5 různých typů embeddingů pro každý produkt (kombinovaný, kategoriální, značkový, názvový, popisný)
   - Vytvoření třídy MultiEmbeddingStrategy pro generování různých typů embeddingů
   - Podpora pro vyhledávání a doporučování pomocí různých typů embeddingů

2. **Dynamický Kontext Builder**
   - Implementace třídy DynamicContextBuilder pro generování bohatého kontextu pro LLM dotazy
   - Podpora pro generování kontextu na základě produktů v kategorii
   - Integrace s LLM pro lepší pochopení produktů a jejich vztahů

3. **Adaptive Cluster Representation**
   - Implementace třídy AdaptiveClusterRepresentation pro identifikaci reprezentativních produktů
   - Podpora pro výběr produktů, které nejlépe reprezentují různorodost v kategorii
   - Algoritmy pro výpočet reprezentativnosti produktů

4. **Optimalizace výkonu**
   - Implementace paralelního zpracování embeddingů (2.72x zrychlení)
   - Implementace cachingu embeddingů a kontextů (2.17x zrychlení)
   - Implementace monitoringu výkonu a kvality doporučení

## Analýza výsledků

Po dokončení testů se automaticky spustí analýza výsledků, která zobrazí statistiky výkonu a kvality doporučení. Výsledky jsou uloženy v adresáři `logs/performance/<tenant_id>/reports/`.

## Příklady použití

### Inicializace a spuštění pro filsonstore

```bash
# Inicializace multi-embedding kolekce
./init_multi_embedding_collection_multitenant.sh filsonstore

# Spuštění vylepšené verze s generováním komplementárních produktů
./run_enhanced_multitenant.sh filsonstore -c -f

# Spuštění testů
./run_tests_multitenant.sh filsonstore
```

### Inicializace a spuštění pro avenberg

```bash
# Inicializace multi-embedding kolekce
./init_multi_embedding_collection_multitenant.sh avenberg

# Spuštění vylepšené verze s generováním komplementárních produktů
./run_enhanced_multitenant.sh avenberg -c -f

# Spuštění testů
./run_tests_multitenant.sh avenberg
```
