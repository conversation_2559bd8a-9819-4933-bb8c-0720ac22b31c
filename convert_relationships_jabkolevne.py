#!/usr/bin/env python3
"""
Skript pro převod COMPLEMENTARY_TO vztahů na RELATED_TO vztahy v Neo4j databázi.
Toto je nutné pro kompatibilitu s ultra optimalizovaným skriptem compute_complementary.py.
"""

import os
import sys
from pathlib import Path
from neo4j import GraphDatabase, basic_auth
from dotenv import load_dotenv
import logging

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
sys.path.insert(0, project_root)

# Načtení .env
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# Konfigurace logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def get_neo4j_driver():
    """Vyt<PERSON>ří Neo4j driver."""
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    driver = GraphDatabase.driver(
        neo4j_uri,
        auth=basic_auth(neo4j_user, neo4j_password)
    )
    return driver

def convert_relationships(tenant_id: str = "jabkolevne"):
    """
    Převede všechny COMPLEMENTARY_TO vztahy na RELATED_TO vztahy.
    
    Analógia: Jako když přejmenováváme typ cesty na mapě - obsah zůstává stejný,
    jen název typu se změní pro lepší kompatibilitu s navigačním systémem.
    """
    driver = get_neo4j_driver()
    
    try:
        with driver.session() as session:
            # Nejprve zjistíme současný stav
            logger.info("🔍 Analyzuji současný stav vztahů...")
            
            # Počet COMPLEMENTARY_TO vztahů
            result = session.run(
                "MATCH ()-[r:COMPLEMENTARY_TO]->() RETURN count(r) as total"
            )
            complementary_count = result.single()["total"]
            logger.info(f"📊 Nalezeno {complementary_count} COMPLEMENTARY_TO vztahů")
            
            # Počet RELATED_TO vztahů
            result = session.run(
                "MATCH ()-[r:RELATED_TO]->() RETURN count(r) as total"
            )
            related_count = result.single()["total"]
            logger.info(f"📊 Nalezeno {related_count} RELATED_TO vztahů")
            
            if complementary_count == 0:
                logger.warning("⚠️ Žádné COMPLEMENTARY_TO vztahy nebyly nalezeny!")
                return
            
            if related_count > 0:
                logger.warning(f"⚠️ Již existuje {related_count} RELATED_TO vztahů!")
                response = input("Chcete pokračovat? (y/N): ")
                if response.lower() != 'y':
                    logger.info("❌ Operace zrušena uživatelem")
                    return
            
            # Převod vztahů - vytvoření nových RELATED_TO z COMPLEMENTARY_TO
            logger.info("🔄 Převádím COMPLEMENTARY_TO vztahy na RELATED_TO...")
            
            conversion_query = """
            MATCH (source:Category_jabkolevne)-[old:COMPLEMENTARY_TO]->(target:Category_jabkolevne)
            CREATE (source)-[new:RELATED_TO {weight: old.weight}]->(target)
            RETURN count(new) as created
            """
            
            result = session.run(conversion_query)
            created_count = result.single()["created"]
            logger.info(f"✅ Vytvořeno {created_count} nových RELATED_TO vztahů")
            
            # Ověření, že se nové vztahy vytvořily správně
            result = session.run(
                "MATCH ()-[r:RELATED_TO]->() RETURN count(r) as total"
            )
            new_related_count = result.single()["total"]
            logger.info(f"📊 Celkem RELATED_TO vztahů po konverzi: {new_related_count}")
            
            # Smazání starých COMPLEMENTARY_TO vztahů
            logger.info("🗑️ Mažu staré COMPLEMENTARY_TO vztahy...")
            
            delete_query = """
            MATCH ()-[r:COMPLEMENTARY_TO]->()
            DELETE r
            RETURN count(r) as deleted
            """
            
            result = session.run(delete_query)
            # Note: count(r) v DELETE vrací 0, takže použijeme předchozí počet
            logger.info(f"✅ Smazáno {complementary_count} COMPLEMENTARY_TO vztahů")
            
            # Finální ověření
            result = session.run(
                "MATCH ()-[r:COMPLEMENTARY_TO]->() RETURN count(r) as remaining"
            )
            remaining_count = result.single()["remaining"]
            
            if remaining_count == 0:
                logger.info("🎉 Konverze úspěšně dokončena!")
                logger.info(f"📈 Vytvořeno {created_count} RELATED_TO vztahů")
                logger.info(f"🗑️ Smazáno {complementary_count} COMPLEMENTARY_TO vztahů")
            else:
                logger.warning(f"⚠️ Pozor: Zůstalo {remaining_count} COMPLEMENTARY_TO vztahů")
                
    except Exception as e:
        logger.error(f"❌ Chyba při konverzi vztahů: {e}")
        raise
    finally:
        driver.close()
        logger.info("🔒 Neo4j připojení uzavřeno")

def verify_conversion(tenant_id: str = "jabkolevne"):
    """
    Ověří, že konverze proběhla správně.
    
    Analógia: Jako kontrola na konci renovace - ověříme, že všechno funguje
    tak jak má a nic neschází.
    """
    driver = get_neo4j_driver()
    
    try:
        with driver.session() as session:
            logger.info("🔍 Ověřuji výsledky konverze...")
            
            # Počet kategorií
            result = session.run(
                f"MATCH (c:Category_{tenant_id}) RETURN count(c) as total"
            )
            categories_count = result.single()["total"]
            logger.info(f"📊 Kategorií: {categories_count}")
            
            # Počet RELATED_TO vztahů
            result = session.run(
                f"MATCH (c1:Category_{tenant_id})-[r:RELATED_TO]->(c2:Category_{tenant_id}) RETURN count(r) as total"
            )
            related_count = result.single()["total"]
            logger.info(f"📊 RELATED_TO vztahů: {related_count}")
            
            # Počet COMPLEMENTARY_TO vztahů (měl by být 0)
            result = session.run(
                f"MATCH (c1:Category_{tenant_id})-[r:COMPLEMENTARY_TO]->(c2:Category_{tenant_id}) RETURN count(r) as total"
            )
            complementary_count = result.single()["total"]
            logger.info(f"📊 COMPLEMENTARY_TO vztahů: {complementary_count}")
            
            # Vzorky vztahů
            result = session.run(
                f"""
                MATCH (source:Category_{tenant_id})-[r:RELATED_TO]->(target:Category_{tenant_id})
                RETURN source.name as source_name, target.name as target_name, r.weight as weight
                ORDER BY r.weight DESC
                LIMIT 5
                """
            )
            
            logger.info("🔗 Vzorky RELATED_TO vztahů (top 5 podle váhy):")
            for record in result:
                source = record["source_name"]
                target = record["target_name"]
                weight = record["weight"]
                logger.info(f"   {source} -> {target} (váha: {weight})")
                
            if complementary_count == 0 and related_count > 0:
                logger.info("✅ Konverze úspěšná - všechny kontroly prošły!")
                return True
            else:
                logger.warning("⚠️ Konverze možná neproběhla správně!")
                return False
                
    except Exception as e:
        logger.error(f"❌ Chyba při ověřování: {e}")
        return False
    finally:
        driver.close()

if __name__ == "__main__":
    logger.info("🚀 Spouštím konverzi vztahů pro jabkolevne...")
    logger.info("📝 Převádím COMPLEMENTARY_TO -> RELATED_TO pro kompatibilitu s ultra skriptem")
    
    try:
        # Konverze
        convert_relationships("jabkolevne")
        
        # Ověření
        success = verify_conversion("jabkolevne")
        
        if success:
            logger.info("🎉 === KONVERZE ÚSPĚŠNĚ DOKONČENA ===")
            logger.info("💡 Nyní můžete spustit ultra optimalizovaný skript!")
        else:
            logger.error("❌ === KONVERZE SELHALA ===")
            sys.exit(1)
            
    except Exception as e:
        logger.error(f"💥 Kritická chyba: {e}")
        sys.exit(1) 