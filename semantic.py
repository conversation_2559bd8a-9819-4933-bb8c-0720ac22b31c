import sys
import os
sys.path.insert(0, os.path.abspath(os.path.dirname(__file__)))

# semantic.py
import logging
import asyncio
from typing import Dict, List, Any, Optional, Union, Tuple
from qdrant_client import AsyncQdrantClient, models
from openai import AsyncOpenAI
import cohere
from qdrant_client.http.models import PointStruct, VectorParams, Distance
from thefuzz import fuzz, process
from core.utils import create_qdrant_id
from text_utils import normalize_text, extract_price_range_from_query

logger = logging.getLogger(__name__)
SEMANTIC_EMBEDDING_MODEL = "text-embedding-3-small"
SEMANTIC_VECTOR_DIM = 1536
COHERE_RERANK_MODEL = "rerank-v3.5"

def prepare_product_text_for_semantic(
    product_data: Dict[str, Any],
    fields_to_use: List[str] = ["name", "brand", "category", "description"],
    field_weights: Optional[Dict[str, int]] = None, # Váhy pro jednotlivá pole
    max_length: Optional[int] = None # Omezen<PERSON> d<PERSON> (v znacích, přibližně)
) -> str:
    """
    Kombinuje relevantní textová pole produktu do jednoho řetězce pro sémantický embedding.
    """
    # Extrakce dat produktu a normalizace na malá písmena
    name = product_data.get("name", "").lower()
    description = product_data.get("description", "").lower()
    category = product_data.get("category", "").lower()
    brand = product_data.get("brand", "").lower()

    # TODO: Zvážit přidání dalších atributů, pokud jsou relevantní a dostupné
    # Např. product_data.get("attributes", {}).get("color", "")

    # Jednoduché spojení, oddělené mezerou pro kontext
    # Váhy: Název x3, Kategorie x2, Značka x1, Popis x1
    # Použijeme váhy, pokud jsou definovány, jinak defaultní
    if field_weights:
         parts = []
         for field in fields_to_use:
             value = product_data.get(field, "")
             if value:
                 weight = field_weights.get(field, 1)
                 parts.extend([str(value)] * weight) # Přidáme hodnotu tolikrát, kolik je váha
         combined_text = " ".join(parts).strip()
    else: # Původní logika, pokud nejsou váhy
        combined_text = f"{name} {name} {name} {category} {category} {brand} {description}".strip()

    # Odstranění nadbytečných mezer
    combined_text = " ".join(combined_text.split())

    # Omezení délky, pokud je zadáno
    if max_length and len(combined_text) > max_length:
        # Jednoduché oříznutí - v praxi možná lepší strategie
        combined_text = combined_text[:max_length]
        logger.debug(f"Semantic text truncated to {max_length} chars for product ID {product_data.get('product_id', 'N/A')}")

    logger.debug(f"Prepared semantic text for product ID {product_data.get('product_id', 'N/A')}: {combined_text[:100]}...") # Log short preview
    return combined_text

async def generate_semantic_embedding(text: str, openai_client: AsyncOpenAI) -> Optional[List[float]]:
    """
    Generuje jeden sémantický embedding pro daný text pomocí OpenAI.
    """
    if not text:
        logger.warning("Cannot generate embedding for empty text.")
        return None
    try:
        # Ošetření pro případ velmi krátkého textu, který by API mohlo odmítnout
        if len(text.strip()) < 3:
             logger.warning(f"Text too short for embedding: '{text}'")
             return None
             
        response = await openai_client.embeddings.create(
            model=SEMANTIC_EMBEDDING_MODEL,
            input=[text] # API očekává list
        )
        if response.data and len(response.data) > 0:
            return response.data[0].embedding
        else:
            logger.error("OpenAI API returned no embedding data.")
            return None
    except Exception as e:
        # Logování specifické chyby, pokud je dostupná
        error_message = str(e)
        if "RateLimitError" in error_message:
             logger.error(f"Rate limit hit while generating semantic embedding: {e}")
        elif "AuthenticationError" in error_message:
             logger.error(f"Authentication error with OpenAI API: {e}")
        else:
             logger.error(f"Error generating semantic embedding: {e}", exc_info=True)
        return None

async def upsert_semantic_product_batch(client: AsyncQdrantClient, collection_name: str, products_data: List[Dict[str, Any]], openai_client: AsyncOpenAI):
    """
    Generuje a nahrává sémantické embeddingy pro dávku produktů do Qdrantu.
    """
    points_to_upsert = []
    texts_to_embed = []
    valid_product_indices = [] # Indexy produktů, pro které budeme generovat embedding

    for i, product_data in enumerate(products_data):
        product_id = product_data.get("product_id")
        if not product_id:
            logger.warning(f"Skipping product at index {i} due to missing 'product_id'. Data: {str(product_data)[:100]}")
            continue

        # Připravit text
        # Použijeme defaultní pole pro embedding
        semantic_text = prepare_product_text_for_semantic(product_data)
        if not semantic_text:
            logger.warning(f"Skipping product ID {product_id} due to empty semantic text.")
            continue
        
        texts_to_embed.append(semantic_text)
        valid_product_indices.append(i) # Uložíme index validního produktu

    if not texts_to_embed:
        logger.info("No valid products in batch to generate embeddings for.")
        return 0

    # Generovat embeddingy pro celou dávku najednou
    logger.debug(f"Generating semantic embeddings for {len(texts_to_embed)} products...")
    try:
        response = await openai_client.embeddings.create(
            model=SEMANTIC_EMBEDDING_MODEL,
            input=texts_to_embed
        )
        embeddings = [item.embedding for item in response.data]
    except Exception as e:
        logger.error(f"Error generating batch semantic embeddings: {e}", exc_info=True)
        return 0 # Selhala celá dávka

    if len(embeddings) != len(texts_to_embed):
         logger.error(f"Mismatch between number of embeddings ({len(embeddings)}) and texts ({len(texts_to_embed)}). Aborting batch upsert.")
         return 0

    # Připravit body pro Qdrant POUZE pro produkty, pro které máme embedding
    for i, embedding in enumerate(embeddings):
        original_index = valid_product_indices[i]
        product_data = products_data[original_index]
        product_id = product_data.get("product_id") # Mělo by zde být
        
        # Připravit payload (základní data)
        payload = {
            "product_id": product_id,
            "name": product_data.get("name"),
            "category": product_data.get("category"),
            "price": product_data.get("price"),
            "image_url": product_data.get("image_url"),
            "product_url": product_data.get("product_url"),
            "brand": product_data.get("brand")
        }
        # Odstraníme None hodnoty
        payload = {k: v for k, v in payload.items() if v is not None}
        
        points_to_upsert.append(
            PointStruct(
                id=create_qdrant_id(product_id),
                vector=embedding, # Přidáme embedding
                payload=payload
            )
        )

    # Nahrát dávku do Qdrant
    try:
        # Před nahráním zajistíme, že kolekce existuje
        await ensure_semantic_collection(client, collection_name)
        
        await client.upsert(
            collection_name=collection_name,
            points=points_to_upsert, # Přímo list PointStruct
            wait=True # Počkáme na potvrzení od Qdrantu
        )
        logger.info(f"Successfully upserted {len(points_to_upsert)} semantic products to collection '{collection_name}'.")
        return len(points_to_upsert)
    except Exception as e:
        logger.error(f"Error upserting batch semantic products to Qdrant: {e}", exc_info=True)
        # Zkusit logovat ID bodů, které selhaly, pokud je to možné
        failed_ids = [p.id for p in points_to_upsert]
        logger.error(f"Failed points IDs (approx): {failed_ids}")
        return 0

async def search_semantic_products(
    client: AsyncQdrantClient,
    collection_name: str,
    query_text: str,
    openai_client: AsyncOpenAI,
    cohere_client: Optional[cohere.Client] = None, # Cohere klient je nyní volitelný
    limit: int = 20,
    search_filter: Optional[models.Filter] = None,
    enable_reranking: bool = True,
    rerank_limit_multiplier: int = 4, # Defaultní násobitel, pokud není přepsán
    rerank_multiplier_override: Optional[int] = None, # Nový parametr pro přepsání násobitele
    enable_keyword_filter: bool = False,
    keyword_similarity_threshold: int = 30 # Změněno z 60 na 40
) -> List[Dict[str, Any]]:
    """
    Prohledá Qdrant, volitelně aplikuje fuzzy keyword filtr a volitelně rerankuje s Cohere.
    Použije rerank_multiplier_override, pokud je zadán, jinak rerank_limit_multiplier.
    """
    if not query_text:
        logger.warning("Semantic search query is empty.")
        return []
    
    # Normalizace dotazu na malá písmena
    query_text = query_text.lower()
    logger.debug(f"Normalized query to lowercase: '{query_text}'")

    # --- Extrakce cenového rozsahu a cílové ceny z dotazu ---
    min_price, max_price, target_price = extract_price_range_from_query(query_text)
    price_conditions = []

    # Pokud bylo detekováno "kolem X" a není explicitní min/max, vytvoříme rozsah +-15%
    if target_price is not None and min_price is None and max_price is None:
        logger.info(f"Using target_price {target_price} to create a price range.")
        min_price = target_price * 0.85
        max_price = target_price * 1.15
        # Zaokrouhlení pro hezčí čísla a praktičnost, např. na celé koruny
        min_price = round(min_price)
        max_price = round(max_price)
        logger.info(f"Derived price range from target: min={min_price}, max={max_price}")

    if min_price is not None:
        logger.info(f"Detected minimum price from query: {min_price}")
        price_conditions.append(models.FieldCondition(key="price", range=models.Range(gte=min_price)))
    if max_price is not None:
        logger.info(f"Detected maximum price from query: {max_price}")
        price_conditions.append(models.FieldCondition(key="price", range=models.Range(lte=max_price)))
    if target_price is not None:
         logger.info(f"Detected target price from query: {target_price}")
    
    # --- Kombinace filtrů --- 
    final_filter = search_filter # Začneme s filtrem z argumentů
    if price_conditions:
        # Pokud máme cenové podmínky, přidáme je
        if final_filter:
            # Pokud už existuje filtr, přidáme cenové podmínky do jeho 'must' klauzule
            # (Předpokládáme, že search_filter je typu models.Filter)
            if final_filter.must:
                final_filter.must.extend(price_conditions)
            else:
                final_filter.must = price_conditions
        else:
            # Pokud žádný filtr nebyl, vytvoříme nový jen s cenovými podmínkami
            final_filter = models.Filter(must=price_conditions)
    # Logování finálního filtru
    logger.debug(f"Final Qdrant filter: {final_filter}")
    # -------------------------

    # 1. Generovat embedding pro dotaz (OpenAI)
    logger.debug(f"Generating OpenAI embedding for query: '{query_text}'")
    query_vector = await generate_semantic_embedding(query_text, openai_client)
    if query_vector is None:
        logger.error("Failed to generate embedding for the search query.")
        return []

    # 2. Vyhledat v Qdrantu kandidáty
    # Určíme limit pro počáteční Qdrant vyhledávání
    needs_more_candidates = enable_reranking or enable_keyword_filter
    
    # Použijeme override, pokud je k dispozici, jinak defaultní násobitel
    actual_multiplier = rerank_multiplier_override if rerank_multiplier_override is not None else rerank_limit_multiplier
    logger.debug(f"Using rerank multiplier: {actual_multiplier} (Override: {rerank_multiplier_override}, Default: {rerank_limit_multiplier})")
    
    initial_limit = limit * actual_multiplier if needs_more_candidates else limit
    
    logger.debug(f"Searching Qdrant collection '{collection_name}' with query vector, limit={initial_limit}, filter applied: {final_filter is not None}")
    try:
        search_result = await client.search(
            collection_name=collection_name,
            query_vector=models.NamedVector(name="combined", vector=query_vector),
            query_filter=final_filter,
            limit=initial_limit,
            with_payload=True
        )
        
        # Zpracujeme výsledky z Qdrantu do seznamu slovníků
        candidates = []
        for hit in search_result:
            if hit.payload: # Zajistíme, že payload existuje
                 candidate = hit.payload
                 candidate['qdrant_score'] = hit.score # Uložíme původní Qdrant skóre
                 candidates.append(candidate)
            else:
                 logger.warning(f"Qdrant hit with ID {hit.id} has no payload, skipping.")

        if not candidates:
            logger.info(f"No initial candidates found for query '{query_text}' in collection '{collection_name}'.")
            return []
        logger.info(f"Found {len(candidates)} initial candidates from Qdrant.")

        # --- Fuzzy Keyword Filtrování (pokud je zapnuté A máme kandidáty) ---
        if enable_keyword_filter and candidates:
            # Extrakce slov z dotazu (delší než 2 znaky, malá písmena, unikátní, normalizované)
            # Nejprve normalizujeme celý dotaz
            normalized_query = normalize_text(query_text)
            query_words = sorted(list(set(kw for kw in normalized_query.split() if len(kw) > 2)), key=len, reverse=True)
            
            if query_words:
                logger.info(f"Applying fuzzy keyword filter (threshold={keyword_similarity_threshold}, diacritic insensitive) for words: {query_words}")
                filtered_candidates = []
                for candidate in candidates:
                    # Kombinujeme text z názvu, kategorie, značky (malá písmena) pro hledání shody
                    candidate_text_parts = [
                        candidate.get('name', ''),
                        candidate.get('category', ''),
                        candidate.get('brand', '')
                    ]
                    # Normalizujeme výsledný text kandidáta (včetně odstranění diakritiky)
                    candidate_search_text = normalize_text(" ".join(filter(None, candidate_text_parts)))
                    
                    # Kontrola shody pro ALESPOŇ JEDNO normalizované slovo z dotazu
                    match_score = 0
                    found_match = False
                    for query_word in query_words: # query_word je již normalizovaný
                         similarity = fuzz.partial_ratio(query_word, candidate_search_text) # Porovnáváme normalizované texty
                         if similarity >= keyword_similarity_threshold:
                             logger.debug(f"Keyword filter MATCH for '{query_word}' in '{candidate_search_text[:50]}...' (Similarity: {similarity})")
                             found_match = True
                             break # Stačí jedna shoda

                    if found_match:
                        filtered_candidates.append(candidate)
                    else:
                         logger.debug(f"Keyword filter MISS for query '{query_text}' in '{candidate_search_text[:50]}...'")

                logger.info(f"Fuzzy keyword filter reduced candidates from {len(candidates)} to {len(filtered_candidates)}.")
                if not filtered_candidates:
                     logger.info("No candidates remaining after keyword filter.")
                     return []
                candidates = filtered_candidates # Nahradíme původní kandidáty filtrovanými
            else:
                logger.info("No suitable keywords found in query for filtering, skipping fuzzy keyword filter.")
        
        # --- Re-ranking krok pomocí Cohere --- 
        perform_reranking = enable_reranking and candidates and cohere_client is not None

        if perform_reranking:
            # --- Detekce cenových deskriptorů pro úpravu reranker query --- 
            reranker_query = query_text # Začneme s původním dotazem
            query_lower_normalized = normalize_text(query_text) # Normalizovaný dotaz pro hledání klíčových slov
            
            cheap_keywords = ["levny", "levne", "levna", "levnou", "levnejsi"]
            expensive_keywords = ["drahy", "drahe", "draha", "drazsi", "luxusni"]
            
            found_cheap = any(keyword in query_lower_normalized.split() for keyword in cheap_keywords)
            found_expensive = any(keyword in query_lower_normalized.split() for keyword in expensive_keywords)
            
            if found_cheap and not found_expensive:
                reranker_query += " nizka cena" # Přidáme signál pro reranker
                logger.info("Appending 'nizka cena' to reranker query based on keywords.")
            elif found_expensive and not found_cheap:
                reranker_query += " vysoka cena" # Přidáme signál pro reranker
                logger.info("Appending 'vysoka cena' to reranker query based on keywords.")
            # Pokud jsou oba nebo žádný, reranker_query zůstává původní query_text
            # ---------------------------------------------------------------------

            logger.info(f"Performing Cohere re-ranking for {len(candidates)} candidates using model '{COHERE_RERANK_MODEL}'...")
            
            documents_for_rerank = []
            original_indices_map = {} 
            
            for idx, candidate in enumerate(candidates):
                 doc_text = f"Název: {candidate.get('name', 'N/A')}\nKategorie: {candidate.get('category', 'N/A')}\nZnačka: {candidate.get('brand', 'N/A')}"
                 # << OPRAVA: Zakomentovaný řádek, který způsoboval chybu >>
                 # price = candidate.get('price')
                 # if price: doc_text += f"\nCena: {price}" # <-- Toto způsobovalo chybu
                 documents_for_rerank.append(doc_text)
                 original_indices_map[idx] = candidate

            # << OPRAVA: Blok try pro reranking musí být zde >>
            try:
                rerank_response = await asyncio.to_thread(
                    cohere_client.rerank,
                    query=reranker_query, # Použijeme upravený dotaz
                    documents=documents_for_rerank,
                    top_n=limit,
                    model=COHERE_RERANK_MODEL
                )
                
                final_candidates = []
                if rerank_response.results:
                    logger.debug(f"Cohere rerank returned {len(rerank_response.results)} results.")
                    for result_item in rerank_response.results:
                        original_doc_index = result_item.index
                        relevance_score = result_item.relevance_score
                        if original_doc_index in original_indices_map:
                            candidate_data = original_indices_map[original_doc_index]
                            candidate_data['score'] = relevance_score 
                            candidate_data.pop('qdrant_score', None) 
                            candidate_data.pop('llm_relevance', None)
                            candidate_data.pop('combined_score', None)
                            final_candidates.append(candidate_data)
                        else:
                            logger.warning(f"Cohere rerank returned index {original_doc_index} which is out of bounds ({len(documents_for_rerank)}). Skipping.")
                    candidates = final_candidates
                    logger.info(f"Cohere re-ranking complete. Returning {len(candidates)} results.")
                else:
                    logger.warning("Cohere rerank returned no results. Falling back to Qdrant scores.")
                    for candidate in candidates:
                        candidate['score'] = candidate.pop('qdrant_score', 0.0)
                    candidates.sort(key=lambda x: x.get('score', 0.0), reverse=True)
                    candidates = candidates[:limit]
            # << OPRAVA: Správné odsazení except bloků >>
            except cohere.CohereAPIError as cohere_api_e:
                 logger.error(f"Cohere API error during re-ranking: {cohere_api_e}", exc_info=True)
                 logger.warning("Falling back to Qdrant scores due to Cohere API error.")
                 for candidate in candidates:
                     candidate['score'] = candidate.pop('qdrant_score', 0.0)
                 candidates.sort(key=lambda x: x.get('score', 0.0), reverse=True)
                 candidates = candidates[:limit]
            except Exception as e:
                 logger.error(f"Unexpected error during Cohere re-ranking: {e}", exc_info=True)
                 logger.warning("Falling back to Qdrant scores due to unexpected rerank error.")
                 for candidate in candidates:
                     candidate['score'] = candidate.pop('qdrant_score', 0.0)
                 candidates.sort(key=lambda x: x.get('score', 0.0), reverse=True)
                 candidates = candidates[:limit]
         
        # << OPRAVA: Správné odsazení else bloku >>
        else: 
            if not cohere_client and enable_reranking:
                logger.warning("Reranking is enabled but Cohere client is not available. Skipping reranking.")
            logger.info("Skipping Cohere re-ranking. Using Qdrant scores.")
            for candidate in candidates:
                candidate['score'] = candidate.pop('qdrant_score', 0.0)
            candidates.sort(key=lambda x: x.get('score', 0.0), reverse=True)
            candidates = candidates[:limit]

        # Ořízneme na finální limit
        candidates = candidates[:limit]

        # Odstranit vektorová pole z výsledků včetně 'embeddings' i z vnořeného payloadu
        VECTOR_KEYS = {"embedding", "vector", "name_brand", "description", "brand_category", "embeddings"}
        for product in candidates:
            # Z hlavního slovníku
            for key in list(product.keys()):
                if key in VECTOR_KEYS:
                    del product[key]
            # Z vnořeného slovníku 'payload', pokud existuje
            if "payload" in product and isinstance(product["payload"], dict):
                for key in list(product["payload"].keys()):
                    if key in VECTOR_KEYS:
                        del product["payload"][key]

        # << ZAČÁTEK FILTROVÁNÍ PODLE SKÓRE PRVNÍHO VÝSLEDKU (Relative Score) >>
        if candidates and len(candidates) > 1: # Má smysl filtrovat jen pokud máme více než 1 výsledek
            top_score = candidates[0].get('score')
            FILTER_DIVISOR = 2.5 # Faktor dělení pro práh
            
            if top_score is not None: # Máme skóre prvního výsledku?
                score_threshold = top_score / FILTER_DIVISOR
                
                original_count = len(candidates)
                filtered_candidates = [c for c in candidates if c.get('score', -1.0) >= score_threshold]
                
                if len(filtered_candidates) < original_count:
                    logger.info(f"Relative score filtering applied: Top score = {top_score:.4f}, threshold = {score_threshold:.4f}. Results reduced from {original_count} to {len(filtered_candidates)}.")
                    candidates = filtered_candidates # Nahradíme původní seznam filtrovaným
                else:
                    logger.debug(f"Relative score filtering did not remove any candidates (threshold = {score_threshold:.4f}).")
            else:
                 logger.warning("Could not get score from the top result for relative filtering.")
        # << KONEC FILTROVÁNÍ PODLE SKÓRE PRVNÍHO VÝSLEDKU >>

        # << ZAČÁTEK FILTROVÁNÍ PODLE MINIMÁLNÍHO ABSOLUTNÍHO SKÓRE (mazání) >>
        MIN_ABSOLUTE_SCORE = 0.05

        if candidates: # Máme nějaké výsledky po předchozím filtrování?
            top_candidate_payload = candidates[0] # Získáme celý payload (nového) prvního kandidáta
            top_score = top_candidate_payload.get('score')
            logger.debug(f"Checking MIN_ABSOLUTE_SCORE: Top score (after relative filter) is {top_score} (Threshold: {MIN_ABSOLUTE_SCORE}). Candidate ID: {top_candidate_payload.get('product_id', 'N/A')}")
            if top_score is not None and top_score < MIN_ABSOLUTE_SCORE:
                # Pokud je skóre příliš nízké, zahodíme všechny výsledky
                logger.info(f"Top score ({top_score:.4f}) is below the minimum absolute threshold ({MIN_ABSOLUTE_SCORE}). Returning empty results.")
                candidates = [] # Zahodíme všechny výsledky
            # else: # Skóre je v pořádku nebo None, nic neděláme
                 # logger.debug(f"Top score ({top_score}) meets or exceeds minimum absolute threshold ({MIN_ABSOLUTE_SCORE}) or score is None.")
        # << KONEC FILTROVÁNÍ PODLE MINIMÁLNÍHO ABSOLUTNÍHO SKÓRE >>

        # --- Logika: Úprava řazení podle blízkosti ceny (používá target_price přímo) ---
        # target_price je nyní přímo z extract_price_range_from_query

        # Pokud byla cílová cena v dotazu specifikována a máme nějaké výsledky
        if target_price is not None and candidates:
            logger.info(f"Target price {target_price} detected. Re-sorting top candidates by price proximity.")
            
            # Počet kandidátů z top sémantických/rerankovaných výsledků, které zvážíme pro cenové přeuspořádání
            PRICE_SORT_CANDIDATES = 5 
            
            top_candidates_for_price_sort = candidates[:PRICE_SORT_CANDIDATES]
            
            best_price_match_original_index = -1 # Index v původním poli candidates
            min_price_diff = float('inf')

            # Najdeme kandidáta s nejbližší cenou mezi top N
            for i, candidate in enumerate(top_candidates_for_price_sort):
                candidate_price_raw = candidate.get('payload', {}).get('price')
                if candidate_price_raw is not None:
                    try:
                        # Cena může být string, převedeme na float
                        candidate_price = float(candidate_price_raw)
                        price_diff = abs(candidate_price - target_price)
                        
                        # Pokud je tento rozdíl menší než dosavadní minimum
                        if price_diff < min_price_diff:
                            min_price_diff = price_diff
                            best_price_match_original_index = i # Zapamatujeme si index v původním poli
                            
                    except (ValueError, TypeError) as e:
                        # Logujeme chybu, pokud cena není platné číslo, ale pokračujeme
                        logger.warning(f"Could not parse price '{candidate_price_raw}' for product ID {candidate.get('payload', {}).get('product_id', 'N/A')} during price re-sorting: {e}")
                        continue

            # Pokud jsme našli shodu a není už na prvním místě
            if best_price_match_original_index > 0:
                logger.debug(f"Moving product at index {best_price_match_original_index} (payload: {candidates[best_price_match_original_index].get('payload')}) to the first position due to price proximity.")
                # Vyjmeme položku s nejlepší cenovou shodou z její původní pozice
                best_match_item = candidates.pop(best_price_match_original_index)
                # Vložíme ji na začátek seznamu
                candidates.insert(0, best_match_item)
            elif best_price_match_original_index == 0:
                 logger.debug("Best price match candidate is already at the first position.")
            else:
                 logger.debug("No suitable candidate found for price re-sorting within top candidates.")

        # --- Konec logiky pro úpravu řazení podle ceny ---

        # Omezit finální počet výsledků až zde
        candidates = candidates[:limit]
        logger.info(f"Returning {len(candidates)} final results after potential filtering and reranking.")
        return candidates

    except Exception as e:
        logger.error(f"Error during semantic search processing: {e}", exc_info=True)
        return [] 

async def ensure_semantic_collection(client: AsyncQdrantClient, collection_name: str):
    """
    Zajistí, že sémantická kolekce v Qdrantu existuje se správným nastavením.
    Používá nepojmenovaný vektor.
    """
    try:
        await client.get_collection(collection_name=collection_name)
        logger.debug(f"Semantic collection '{collection_name}' already exists.")
    except Exception as e:
        if "not found" in str(e).lower() or "status_code=404" in str(e) or "CollectionNotFound" in str(type(e).__name__):
            logger.info(f"Semantic collection '{collection_name}' not found. Attempting to create...")
            try:
                await client.create_collection(
                    collection_name=collection_name,
                    vectors_config=models.VectorParams(size=SEMANTIC_VECTOR_DIM, distance=models.Distance.COSINE)
                )
                logger.info(f"Successfully created semantic collection '{collection_name}'.")
            except Exception as create_e:
                 if "already exists" in str(create_e).lower():
                      logger.warning(f"Semantic collection '{collection_name}' already exists (created concurrently?).")
                 else:
                      logger.error(f"Failed to create semantic collection '{collection_name}': {create_e}", exc_info=True)
                      raise 
        else:
            logger.error(f"Error checking semantic collection '{collection_name}': {e}", exc_info=True)
            raise 

async def delete_semantic_product(client: AsyncQdrantClient, collection_name: str, product_id: Union[str, int]):
    pass # TODO: Implement function body
    # ... existing code ... was here, likely unnecessary comment now