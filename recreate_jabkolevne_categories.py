#!/usr/bin/env python3
"""
Skript pro kompletní recreate kategor<PERSON><PERSON> a komplementárních vztahů v Neo4j pro jabkolevne.
Smaže staré kategorie a vytvoří nové založené na skutečných datech z Qdrant databáze.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv
from collections import defaultdict, Counter

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
sys.path.insert(0, project_root)

# Načtení .env
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# Konfigurace logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from core.clients import get_qdrant_async_client
from neo4j import GraphDatabase, basic_auth

def get_neo4j_driver():
    """Vytvoří Neo4j driver."""
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    driver = GraphDatabase.driver(
        neo4j_uri,
        auth=basic_auth(neo4j_user, neo4j_password)
    )
    return driver

async def analyze_qdrant_categories(tenant_id: str = "jabkolevne"):
    """
    Analyzuje kategorie v Qdrant databázi.
    
    Analógia: Jako když inventarizujeme obchod - podíváme se, co skutečně máme
    na skladě a jak jsou produkty rozdělené podle typu.
    """
    logger.info(f"🔍 Analyzuji kategorie produktů pro tenant: {tenant_id}")
    
    qdrant_client = get_qdrant_async_client()
    products_collection = f"real_products_{tenant_id}"
    
    try:
        # Získání všech kategorií z Qdrant
        logger.info("📊 Načítám všechny produkty z Qdrant...")
        
        category_products = defaultdict(list)
        offset = None
        processed = 0
        
        while True:
            scroll_result, next_offset = await qdrant_client.scroll(
                collection_name=products_collection,
                limit=100,
                offset=offset,
                with_payload=True
            )
            
            if not scroll_result:
                break
            
            for point in scroll_result:
                category = point.payload.get("category", "")
                product_name = point.payload.get("name", "")
                product_id = str(point.payload.get("product_id") or point.id)
                
                if category and product_name:
                    category_products[category].append({
                        'id': product_id,
                        'name': product_name[:50]  # Zkráceno pro čitelnost
                    })
            
            processed += len(scroll_result)
            offset = next_offset
            
            if processed % 200 == 0:
                logger.info(f"   Zpracováno {processed} produktů, nalezeno {len(category_products)} kategorií")
            
            if not next_offset:
                break
        
        logger.info(f"✅ Celkem zpracováno {processed} produktů")
        logger.info(f"✅ Nalezeno {len(category_products)} kategorií")
        
        # Seřazení podle počtu produktů
        sorted_categories = sorted(category_products.items(), key=lambda x: len(x[1]), reverse=True)
        
        logger.info("🏆 Top 20 kategorií podle počtu produktů:")
        for i, (category, products) in enumerate(sorted_categories[:20]):
            logger.info(f"   {i+1:2d}. {category} ({len(products)} produktů)")
        
        return category_products
        
    finally:
        await qdrant_client.close()

def create_smart_categories(category_products: dict):
    """
    Vytvoří chytré kategorie založené na reálných datech.
    
    Analógia: Jako když organizujeme knihovnu - seskupíme knihy podle tématu
    tak, aby bylo snadné najít podobné tituly a doporučit čtenáři další.
    """
    logger.info("🧠 Vytvářím chytré kategorie pro komplementární doporučení...")
    
    # Definice chytrých kategorií na základě skutečných dat
    smart_categories = {}
    
    # Analýza kategorií a vytvoření mapování
    for full_category, products in category_products.items():
        simplified_name = None
        
        # iPhone produkty
        if "iPhone" in full_category:
            if "kryt" in full_category.lower() or "pouzdro" in full_category.lower():
                simplified_name = "Pouzdra pro iPhone"
            elif "sklo" in full_category.lower() or "fólie" in full_category.lower():
                simplified_name = "Ochranná skla pro iPhone"
            elif any(model in full_category for model in ["iPhone 11", "iPhone 12", "iPhone 13", "iPhone 14", "iPhone 15", "iPhone 16"]):
                simplified_name = "iPhone"
            else:
                simplified_name = "iPhone příslušenství"
        
        # iPad produkty
        elif "iPad" in full_category:
            if "kryt" in full_category.lower() or "pouzdro" in full_category.lower():
                simplified_name = "Pouzdra pro iPad"
            elif "sklo" in full_category.lower() or "fólie" in full_category.lower():
                simplified_name = "Ochranná skla pro iPad"
            elif any(model in full_category for model in ["iPad Pro", "iPad Air", "iPad mini"]):
                simplified_name = "iPad"
            else:
                simplified_name = "iPad příslušenství"
        
        # Mac produkty
        elif any(keyword in full_category for keyword in ["MacBook", "Mac Studio", "iMac", "Mac mini"]):
            simplified_name = "MacBook"
        
        # Apple Watch
        elif "Watch" in full_category or "řemínek" in full_category.lower():
            if "řemínek" in full_category.lower():
                simplified_name = "Řemínky pro Apple Watch"
            else:
                simplified_name = "Apple Watch"
        
        # AirPods
        elif "AirPods" in full_category:
            if "pouzdro" in full_category.lower() or "kryt" in full_category.lower():
                simplified_name = "Pouzdra pro AirPods"
            else:
                simplified_name = "AirPods"
        
        # Kabely
        elif any(keyword in full_category.lower() for keyword in ["kabel", "lightning", "usb-c", "usb"]):
            simplified_name = "Kabely (Apple)"
        
        # Nabíječky
        elif any(keyword in full_category.lower() for keyword in ["nabíjec", "adaptér", "charger"]):
            if "bezdrát" in full_category.lower():
                simplified_name = "Bezdrátové nabíječky (Apple)"
            else:
                simplified_name = "Nabíječky (Apple)"
        
        # Powerbanky
        elif "powerbank" in full_category.lower():
            simplified_name = "Powerbanky (pro Apple)"
        
        # Apple TV
        elif "Apple TV" in full_category:
            simplified_name = "Apple TV"
        
        # AirTag
        elif "AirTag" in full_category:
            simplified_name = "AirTag"
        
        # Apple Pencil
        elif "Apple Pencil" in full_category or "Pencil" in full_category:
            simplified_name = "Apple Pencil"
        
        # Sluchátka
        elif "sluchátka" in full_category.lower() or "headphone" in full_category.lower():
            simplified_name = "Sluchátka (Apple kompatibilní)"
        
        # Reproduktory
        elif "reproduktor" in full_category.lower() or "speaker" in full_category.lower():
            simplified_name = "Reproduktory (Apple kompatibilní)"
        
        # Ostatní příslušenství
        elif "příslušenství" in full_category.lower():
            if "auto" in full_category.lower():
                simplified_name = "Příslušenství do auta (Apple)"
            else:
                simplified_name = "Ostatní příslušenství (Apple)"
        
        if simplified_name:
            if simplified_name not in smart_categories:
                smart_categories[simplified_name] = []
            smart_categories[simplified_name].extend(products)
    
    # Vytvoření finálního mapování
    final_categories = {}
    for category_name, products in smart_categories.items():
        # Deduplikace produktů podle ID
        unique_products = {}
        for product in products:
            unique_products[product['id']] = product
        
        final_categories[category_name] = list(unique_products.values())
    
    logger.info("📋 Vytvořené chytré kategorie:")
    for category_name, products in sorted(final_categories.items(), key=lambda x: len(x[1]), reverse=True):
        logger.info(f"   {category_name}: {len(products)} produktů")
    
    return final_categories

def create_complementary_relationships():
    """
    Definuje komplementární vztahy mezi kategoriemi.
    
    Analógia: Jako když vytváříme pravidla pro doporučení "Zákazníci, kteří 
    koupili X, také koupili Y" - definujeme, které produkty se k sobě hodí.
    """
    logger.info("🔗 Definuji komplementární vztahy...")
    
    # Definice komplementárních vztahů s váhami
    complementary_relationships = [
        # iPhone komplementy
        ("iPhone", "Pouzdra pro iPhone", 1.0),
        ("iPhone", "Ochranná skla pro iPhone", 1.0),
        ("iPhone", "Kabely (Apple)", 0.9),
        ("iPhone", "Nabíječky (Apple)", 0.9),
        ("iPhone", "Bezdrátové nabíječky (Apple)", 0.8),
        ("iPhone", "Powerbanky (pro Apple)", 0.8),
        ("iPhone", "AirPods", 0.7),
        ("iPhone", "Apple Watch", 0.6),
        
        # iPad komplementy
        ("iPad", "Pouzdra pro iPad", 1.0),
        ("iPad", "Ochranná skla pro iPad", 1.0),
        ("iPad", "Apple Pencil", 0.9),
        ("iPad", "Kabely (Apple)", 0.8),
        ("iPad", "Nabíječky (Apple)", 0.8),
        ("iPad", "Bezdrátové nabíječky (Apple)", 0.7),
        ("iPad", "AirPods", 0.6),
        
        # MacBook komplementy
        ("MacBook", "Kabely (Apple)", 0.9),
        ("MacBook", "Nabíječky (Apple)", 0.9),
        ("MacBook", "Powerbanky (pro Apple)", 0.7),
        ("MacBook", "AirPods", 0.6),
        ("MacBook", "Apple Watch", 0.5),
        
        # Apple Watch komplementy
        ("Apple Watch", "Řemínky pro Apple Watch", 1.0),
        ("Apple Watch", "Nabíječky (Apple)", 0.8),
        ("Apple Watch", "iPhone", 0.7),
        
        # AirPods komplementy
        ("AirPods", "Pouzdra pro AirPods", 1.0),
        ("AirPods", "iPhone", 0.8),
        ("AirPods", "iPad", 0.6),
        ("AirPods", "MacBook", 0.6),
        
        # Obousměrné vztahy pro příslušenství
        ("Pouzdra pro iPhone", "Ochranná skla pro iPhone", 0.8),
        ("Pouzdra pro iPad", "Ochranná skla pro iPad", 0.8),
        ("Kabely (Apple)", "Nabíječky (Apple)", 0.7),
        ("Nabíječky (Apple)", "Powerbanky (pro Apple)", 0.6),
    ]
    
    logger.info(f"✅ Definováno {len(complementary_relationships)} komplementárních vztahů")
    return complementary_relationships

def recreate_neo4j_data(smart_categories: dict, relationships: list, tenant_id: str = "jabkolevne"):
    """
    Smaže staré kategorie a vytvoří nové s komplementárními vztahy.
    
    Analógia: Jako když kompletně reorganizujeme sklad - vyházíme staré značení,
    vytvoříme nové štítky a návody, kde co najít.
    """
    logger.info(f"🔄 Recreating Neo4j kategorie a vztahy pro tenant: {tenant_id}")
    
    driver = get_neo4j_driver()
    
    try:
        with driver.session() as session:
            # 1. Smazání starých dat
            logger.info("🗑️ Mažu staré kategorie a vztahy...")
            
            # Smazání vztahů (zjednodušeno)
            result = session.run(
                f"MATCH (c1:Category_{tenant_id})-[r:RELATED_TO]-(c2:Category_{tenant_id}) DELETE r RETURN count(r) as deleted"
            )
            try:
                deleted_relationships = result.single()["deleted"]
                logger.info(f"   ✅ Smazáno {deleted_relationships} starých vztahů")
            except:
                logger.info("   ✅ Smazány staré vztahy (žádné nebyly nalezeny)")
            
            # Smazání kategorií
            result = session.run(
                f"MATCH (c:Category_{tenant_id}) DELETE c RETURN count(c) as deleted"
            )
            deleted_categories = result.single()["deleted"]
            logger.info(f"   ✅ Smazáno {deleted_categories} starých kategorií")
            
            # 2. Vytvoření nových kategorií
            logger.info("🆕 Vytvářím nové kategorie...")
            
            categories_created = 0
            for category_name, products in smart_categories.items():
                session.run(
                    f"CREATE (c:Category_{tenant_id} {{name: $name, product_count: $count}})",
                    name=category_name, count=len(products)
                )
                categories_created += 1
                logger.info(f"   ✅ {category_name} ({len(products)} produktů)")
            
            logger.info(f"✅ Vytvořeno {categories_created} nových kategorií")
            
            # 3. Vytvoření komplementárních vztahů
            logger.info("🔗 Vytvářím komplementární vztahy...")
            
            relationships_created = 0
            for source_cat, target_cat, weight in relationships:
                # Kontrola existence kategorií
                source_exists = session.run(
                    f"MATCH (c:Category_{tenant_id} {{name: $name}}) RETURN count(c) > 0 as exists",
                    name=source_cat
                ).single()["exists"]
                
                target_exists = session.run(
                    f"MATCH (c:Category_{tenant_id} {{name: $name}}) RETURN count(c) > 0 as exists",
                    name=target_cat
                ).single()["exists"]
                
                if source_exists and target_exists:
                    session.run(
                        f"""
                        MATCH (source:Category_{tenant_id} {{name: $source}})
                        MATCH (target:Category_{tenant_id} {{name: $target}})
                        CREATE (source)-[r:RELATED_TO {{weight: $weight}}]->(target)
                        """,
                        source=source_cat, target=target_cat, weight=weight
                    )
                    relationships_created += 1
                    logger.info(f"   ✅ {source_cat} -> {target_cat} (váha: {weight})")
                else:
                    logger.warning(f"   ⚠️ Přeskočen vztah {source_cat} -> {target_cat} (kategorie neexistuje)")
            
            logger.info(f"✅ Vytvořeno {relationships_created} komplementárních vztahů")
            
            # 4. Ověření výsledků
            logger.info("🔍 Ověřuji výsledky...")
            
            result = session.run(f"MATCH (c:Category_{tenant_id}) RETURN count(c) as total")
            final_categories = result.single()["total"]
            
            result = session.run(
                f"MATCH (c1:Category_{tenant_id})-[r:RELATED_TO]->(c2:Category_{tenant_id}) RETURN count(r) as total"
            )
            final_relationships = result.single()["total"]
            
            logger.info(f"📊 Finální stav:")
            logger.info(f"   Kategorie: {final_categories}")
            logger.info(f"   Vztahy: {final_relationships}")
            
    except Exception as e:
        logger.error(f"❌ Chyba při recreate Neo4j dat: {e}")
        raise
    finally:
        driver.close()
        logger.info("🔒 Neo4j připojení uzavřeno")

async def main():
    """
    Hlavní funkce pro kompletní recreate kategorií.
    
    Analógia: Jako generální úklid a reorganizace - vyčistíme vše staré
    a založíme nový, lépe organizovaný systém.
    """
    logger.info("🚀 Spouštím kompletní recreate kategorií pro jabkolevne...")
    
    try:
        # 1. Analýza Qdrant kategorií
        category_products = await analyze_qdrant_categories("jabkolevne")
        
        # 2. Vytvoření chytrých kategorií
        smart_categories = create_smart_categories(category_products)
        
        # 3. Definice komplementárních vztahů
        relationships = create_complementary_relationships()
        
        # 4. Recreate Neo4j dat
        recreate_neo4j_data(smart_categories, relationships, "jabkolevne")
        
        logger.info("🎉 === RECREATE KATEGORIÍ DOKONČEN ===")
        logger.info("💡 Nyní by ultra skript měl správně fungovat!")
        logger.info("🧪 Doporučuji spustit debug test pro ověření")
        
    except Exception as e:
        logger.error(f"💥 Kritická chyba: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 