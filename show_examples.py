#!/usr/bin/env python3
"""
Skript pro zobrazení příkladů komplementárních produktů.
"""

import json

def show_examples():
    try:
        with open('all_complementary_jabkolevne.json', 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        products = data['complementary_products']
        
        print("📱 === PŘÍKLADY KOMPLEMENTÁRNÍCH DOPORUČENÍ ===")
        print()
        
        count = 0
        for product_id, item in products.items():
            if count >= 5:  # Ukážeme jen 5 příkladů
                break
                
            source = item['source_product']
            recommendations = item['complementary_products'][:5]  # Top 5 doporučení
            
            print(f"🛍️ Produkt: {source['name']}")
            print(f"   💰 Cena: {source['price']} Kč")
            print(f"   📂 Kategorie: {source['category']}")
            print(f"   🔗 Top {len(recommendations)} doporučení:")
            
            for comp in recommendations:
                print(f"      - {comp['name']} ({comp['price']} Kč) [váha: {comp['complementary_weight']}]")
            
            print()
            count += 1
            
    except Exception as e:
        print(f"❌ Chyba: {e}")

if __name__ == "__main__":
    show_examples() 