#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import logging
import os
import sys
import time
from typing import Dict, Any, List, Optional

# Nastavení logování
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("test_search_api")

# Importy z projektu
from dotenv import load_dotenv
from core.clients import get_qdrant_async_client, get_openai_client, get_cohere_client
from semantic import search_semantic_products

# Načtení proměnných z .env souboru
load_dotenv()

async def test_search_direct(tenant_id: str, query: str, limit: int = 10, enable_reranking: bool = True):
    """Test přímého volání search_semantic_products bez API endpointu."""
    logger.info(f"=== TEST DIRECT SEARCH ===\nTenant: {tenant_id}\nQuery: {query}\nLimit: {limit}\nReranking: {enable_reranking}")
    
    # <PERSON><PERSON><PERSON><PERSON><PERSON> klientů
    qdrant_client = get_qdrant_async_client()
    openai_client = get_openai_client()
    cohere_client = get_cohere_client()
    
    if cohere_client is None and enable_reranking:
        logger.warning("Cohere client is None, reranking will be skipped.")
    
    # Měření času
    t_start = time.time()
    
    # Volání funkce search_semantic_products
    products_collection_name = f"real_products_{tenant_id}"
    logger.info(f"Collection name: {products_collection_name}")
    
    try:
        results = await search_semantic_products(
            client=qdrant_client,
            collection_name=products_collection_name,
            query_text=query,
            openai_client=openai_client,
            cohere_client=cohere_client,
            limit=limit,
            enable_reranking=enable_reranking
        )
        
        t_end = time.time()
        logger.info(f"Search completed in {t_end - t_start:.4f}s")
        
        # Výpis výsledků
        logger.info(f"Found {len(results)} results:")
        for i, result in enumerate(results[:5]):  # Zobrazíme max 5 výsledků
            product_id = result.get("product_id", "N/A")
            name = result.get("name", "N/A")
            score = result.get("score", 0.0)
            availability = result.get("availability", "N/A")
            logger.info(f"  {i+1}. {product_id} - {name} (score: {score:.4f}, availability: {availability})")
        
        # Kontrola, kolik výsledků má availability == "in stock"
        in_stock_count = sum(1 for r in results if r.get("availability") == "in stock")
        logger.info(f"Products with availability 'in stock': {in_stock_count}/{len(results)}")
        
        # Kontrola, jaké hodnoty availability se vyskytují
        availability_values = set(r.get("availability") for r in results)
        logger.info(f"Unique availability values: {availability_values}")
        
        return results
    except Exception as e:
        logger.error(f"Error during search: {e}", exc_info=True)
        return []

async def test_api_endpoint_simulation(tenant_id: str, query: str, limit: int = 10, enable_reranking: bool = True):
    """Simulace API endpointu /search/{tenant_id} s podrobným logováním."""
    logger.info(f"=== TEST API ENDPOINT SIMULATION ===\nTenant: {tenant_id}\nQuery: {query}\nLimit: {limit}\nReranking: {enable_reranking}")
    
    # Získání klientů
    qdrant_client = get_qdrant_async_client()
    openai_client = get_openai_client()
    cohere_client = get_cohere_client()
    
    if cohere_client is None and enable_reranking:
        logger.warning("Cohere client is None, reranking will be skipped.")
    
    # Měření času
    t_start = time.time()
    
    # Volání funkce search_semantic_products
    products_collection_name = f"real_products_{tenant_id}"
    logger.info(f"Collection name: {products_collection_name}")
    
    try:
        results = await search_semantic_products(
            client=qdrant_client,
            collection_name=products_collection_name,
            query_text=query,
            openai_client=openai_client,
            cohere_client=cohere_client,
            limit=limit,
            enable_reranking=enable_reranking
        )
        
        t_end = time.time()
        logger.info(f"Search completed in {t_end - t_start:.4f}s")
        
        # Výpis výsledků před filtrací
        logger.info(f"Found {len(results)} results before filtering:")
        
        # Simulace filtrace podle availability == "in stock" jako v API endpointu
        filtered_results = [r for r in results if r.get("availability") == "in stock"]
        
        logger.info(f"After filtering by 'in stock': {len(filtered_results)} results")
        for i, result in enumerate(filtered_results[:5]):  # Zobrazíme max 5 výsledků
            product_id = result.get("product_id", "N/A")
            name = result.get("name", "N/A")
            score = result.get("score", 0.0)
            logger.info(f"  {i+1}. {product_id} - {name} (score: {score:.4f})")
        
        return filtered_results
    except Exception as e:
        logger.error(f"Error during search: {e}", exc_info=True)
        return []

async def main():
    # Parametry testu
    tenant_id = "avenberg"  # Tenant ID
    query = "sprcha"        # Vyhledávací dotaz
    limit = 10              # Počet výsledků
    enable_reranking = True # Zapnout/vypnout reranking
    
    # Test přímého vyhledávání
    await test_search_direct(tenant_id, query, limit, enable_reranking)
    
    # Test simulace API endpointu
    await test_api_endpoint_simulation(tenant_id, query, limit, enable_reranking)

if __name__ == "__main__":
    asyncio.run(main())
