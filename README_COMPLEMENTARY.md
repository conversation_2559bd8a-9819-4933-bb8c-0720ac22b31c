# Komplementární doporučení produktů

Tento modul implementuje systém pro generování a vyhledávání komplementárních doporučení produktů pomocí 3-vrstvé architektury a Claude 3.5 Haiku.

## Přehled architektury

Systém využívá následující komponenty:

1. **3-vrstvá embedding architektura**:
   - Multi-Embedding Stratifikace (5 typů embeddingů pro každý produkt)
   - Dynamický Kontext Builder (generování bohatého kontextu)
   - Adaptive Cluster Representation (výběr reprezentativních produktů)

2. **Claude 3.5 Haiku LLM**:
   - Generování komplementárních doporučení na základě kontextu
   - Strukturované výstupy ve formátu JSON

3. **Qdrant vektorová databáze**:
   - Ukládání předgenerovaných doporučení
   - Rychlé vyhledávání doporučení v reálném čase

## Komponenty systému

### 1. Generování komplementárních doporučení

Skript `generate_complementary_recommendations.py` generuje komplementární doporučení pro produkty a ukládá je do Qdrantu:

```bash
python generate_complementary_recommendations.py --tenant avenberg --category "Grilování" --max-products 10 --force
```

Parametry:
- `--tenant`: ID tenanta
- `--batch-size`: Velikost dávky pro zpracování (výchozí: 50)
- `--force`: Vynutit aktualizaci, i když doporučení již existují
- `--max-products`: Maximální počet produktů ke zpracování v každé kategorii
- `--categories`: Seznam kategorií ke zpracování (oddělené mezerou)

### 2. API pro vyhledávání doporučení

Skript `complementary_recommendations_api.py` implementuje FastAPI server pro vyhledávání předgenerovaných doporučení:

```bash
python run_api_server.py --port 8000
```

Endpointy:
- `GET /api/products/{product_id}/complementary`: Získá komplementární produkty k danému produktu
- `GET /api/categories/{category}/recommendations`: Získá doporučení pro kategorii

### 3. Testování

Skript `test_complementary_recommendations.py` umožňuje testovat generování a vyhledávání doporučení:

```bash
# Generování doporučení
python test_complementary_recommendations.py --tenant avenberg --generate --category "Grilování" --max-products 3

# Vyhledávání doporučení
python test_complementary_recommendations.py --tenant avenberg --retrieve --product-id 278
```

## Workflow

### 1. Generování doporučení (offline)

1. Načtení produktů z Qdrantu a rozdělení podle kategorií
2. Pro každou kategorii:
   - Vytvoření clusteru z produktů v kategorii
   - Výběr reprezentativních produktů pomocí Adaptive Cluster Representation
   - Generování kontextu pro kategorii pomocí Dynamického Kontext Builderu
3. Pro každý produkt v kategorii:
   - Generování komplementárních doporučení pomocí Claude 3.5 Haiku
   - Uložení doporučení do Qdrantu

### 2. Vyhledávání doporučení (online)

1. Přijetí požadavku na doporučení pro produkt
2. Vyhledání předgenerovaných doporučení v Qdrantu
3. Mapování doporučení na skutečné produkty v katalogu
4. Vrácení strukturovaných doporučení klientovi

## Výhody tohoto přístupu

1. **Nízká latence**: Doporučení jsou předgenerována a rychle dostupná
2. **Nízké náklady**: Minimální počet volání LLM API v produkčním prostředí
3. **Vysoká kvalita**: Využití 3-vrstvé architektury a Claude 3.5 Haiku pro kvalitní doporučení
4. **Škálovatelnost**: Systém zvládne velké množství produktů a uživatelů
5. **Flexibilita**: Možnost přizpůsobení doporučení různým kategoriím a typům produktů

## Příklad výstupu

```json
{
  "product_id": "278",
  "tenant_id": "avenberg",
  "recommendations": {
    "Grilovací příslušenství": [
      {
        "product": {
          "id": "123",
          "name": "Grilovací kleště PREMIUM",
          "category": "Grilovací příslušenství",
          "price": 299.0,
          "brand": "Avenberg",
          "image_url": "https://example.com/images/123.jpg"
        },
        "score": 0.92,
        "explanation": "Základní nástroje pro manipulaci s potravinami na grilu, které zajistí bezpečné a pohodlné grilování."
      },
      ...
    ],
    "Palivo": [
      ...
    ],
    ...
  },
  "generated_at": 1684156789.123
}
```

## Instalace a spuštění

### Požadavky

- Python 3.8+
- Qdrant server
- OpenAI API klíč
- Anthropic API klíč

### Instalace

```bash
pip install -r requirements.txt
```

### Spuštění

1. Generování doporučení:
```bash
python generate_complementary_recommendations.py --tenant avenberg
```

2. Spuštění API serveru:
```bash
python run_api_server.py
```

## Další vývoj

1. **Personalizace**: Přizpůsobení doporučení na základě historie uživatele
2. **A/B testování**: Testování různých strategií doporučení
3. **Rozšíření kontextu**: Přidání informací o trendech, sezónnosti, atd.
4. **Hybridní doporučení**: Kombinace LLM doporučení s kolaborativním filtrováním
5. **Monitoring a zpětná vazba**: Sledování efektivity doporučení a jejich optimalizace
