# Základní konfigurace - <PERSON><PERSON><PERSON>e být přepsána tenant-specifickou konfigurací

qdrant_url: "http://localhost:6333/"
openai_api_key_env: "OPENAI_API_KEY"  # Název proměnné prostředí pro OpenAI klíč
default_batch_size: 50
default_workers: 10
log_level: "INFO" # DEBUG, INFO, WARNING, ERROR, CRITICAL

# Výchozí strategie pro embeddingy (pokud není specifikováno tenantem)
default_embedding_strategy: "multi" # Možnosti: "openai", "multi", "custom" (vyžaduje definici v tenantské konf.)

# Výchozí cesta ke grafu kategorií (může být přepsána tenantem)
category_graph_path: "default_product_graph.json"

# OpenAI model (může být přepsáno tenantem)
openai_model: "text-embedding-3-large"

# Parametry pro MultiEmbeddingStrategy (mohou být přepsány tenantem)
# <PERSON><PERSON><PERSON> by m<PERSON><PERSON> id<PERSON> dávat součet 1, ale není to strikt<PERSON><PERSON> podmínka
multi_embedding_weights:
  name_brand: 0.2       # Název a značka
  category_hierarchy: 0.4 # Plná cesta kategorie
  pure_description: 0.6  # Čistý popis bez HTML
  combined: 0.5          # Kombinace výše uvedených
  brand_category: 0.2    # Pouze značka a poslední kategorie 