#!/usr/bin/env python3
from qdrant_client import QdrantClient
import subprocess
import sys
from neo4j import GraphDatabase
import os
from dotenv import load_dotenv
import logging
import time

# Nastavení loggingu
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Načtení proměnných prostředí
load_dotenv()

# Tenant, se kterým pracujeme
TENANT_ID = "jabkolevne"
QDRANT_COLLECTION = f"real_products_{TENANT_ID}"
CATEGORY_LABEL = f"Category_{TENANT_ID}"

# Neo4j konfigurace
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "Graph2025Secure!")

def get_qdrant_categories():
    """Získá všechny kategorie z Qdrant databáze pro daný tenant."""
    try:
        logger.info(f"Připojuji se k Qdrant databázi a načítám kategorie z kolekce {QDRANT_COLLECTION}")
        client = QdrantClient(host='localhost', port=6333)
        
        # Zjistíme, jestli kolekce existuje
        try:
            collections = client.get_collections().collections
            collection_names = [col.name for col in collections]
            if QDRANT_COLLECTION not in collection_names:
                logger.error(f"Kolekce {QDRANT_COLLECTION} nebyla nalezena v Qdrant databázi")
                return set()
        except Exception as e:
            logger.error(f"Chyba při kontrole existence kolekce: {e}")
            return set()
        
        # Postupně načítáme všechny produkty
        all_points = []
        offset = None
        limit = 100  # Menší limit pro lepší výkon
        
        while True:
            results = client.scroll(
                collection_name=QDRANT_COLLECTION,
                limit=limit,
                offset=offset,
                with_payload=['category']
            )
            
            points, next_offset = results
            all_points.extend(points)
            
            if next_offset is None or len(points) == 0:
                break
                
            offset = next_offset
            logger.info(f"Načteno {len(all_points)} produktů z Qdrantu")
        
        # Extrakce kategorií
        categories = set()
        for point in all_points:
            category = point.payload.get('category')
            if category and isinstance(category, str):
                # Rozdělíme hierarchické kategorie a přidáme všechny úrovně
                if ' > ' in category:
                    parts = category.split(' > ')
                    # Přidáme celou cestu
                    categories.add(category)
                    
                    # Přidáme také všechny předky (rodičovské kategorie)
                    for i in range(1, len(parts)):
                        parent = ' > '.join(parts[:i])
                        categories.add(parent)
                else:
                    categories.add(category)
        
        logger.info(f"V Qdrantu je celkem {len(categories)} kategorií pro tenant {TENANT_ID}")
        return categories
    
    except Exception as e:
        logger.error(f"Chyba při získávání kategorií z Qdrantu: {e}")
        return set()

def connect_to_neo4j():
    """Připojí se k Neo4j databázi."""
    try:
        logger.info(f"Připojuji se k Neo4j databázi na {NEO4J_URI}")
        driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        # Test připojení
        with driver.session() as session:
            result = session.run("RETURN 1 AS test")
            assert result.single()["test"] == 1
            logger.info("Připojení k Neo4j úspěšné")
        return driver
    except Exception as e:
        logger.error(f"Chyba při připojení k Neo4j: {e}")
        sys.exit(1)

def get_neo4j_categories(driver):
    """Získá existující kategorie z Neo4j."""
    try:
        with driver.session() as session:
            query = f"MATCH (c:{CATEGORY_LABEL}) RETURN c.name"
            result = session.run(query)
            categories = set(record["c.name"] for record in result if record["c.name"])
            logger.info(f"V Neo4j je celkem {len(categories)} kategorií pro label {CATEGORY_LABEL}")
            return categories
    except Exception as e:
        logger.error(f"Chyba při získávání kategorií z Neo4j: {e}")
        return set()

def create_categories_in_neo4j(driver, categories):
    """Vytvoří kategorie v Neo4j, pokud ještě neexistují."""
    try:
        with driver.session() as session:
            # Vytvoříme kategorie v dávkách
            batch_size = 100
            total_batches = (len(categories) + batch_size - 1) // batch_size
            
            categories_list = list(categories)
            created_count = 0
            
            for i in range(0, len(categories_list), batch_size):
                batch = categories_list[i:i+batch_size]
                batch_num = i // batch_size + 1
                
                logger.info(f"Zpracovávám dávku {batch_num}/{total_batches} ({len(batch)} kategorií)")
                
                # Vytvoříme uzly pro kategorie, které ještě neexistují
                query = f"""
                UNWIND $categories AS category
                MERGE (c:{CATEGORY_LABEL} {{name: category}})
                RETURN count(c) as count
                """
                
                result = session.run(query, {"categories": batch})
                created_count += result.single()["count"]
                
                # Krátká pauza mezi dávkami
                time.sleep(0.1)
            
            logger.info(f"Vytvořeno nebo aktualizováno {created_count} kategorií v Neo4j")
            return True
    
    except Exception as e:
        logger.error(f"Chyba při vytváření kategorií v Neo4j: {e}")
        return False

def create_category_relationships(driver, categories):
    """Vytvoří hierarchické vztahy mezi kategoriemi."""
    try:
        hierarchical_categories = [c for c in categories if ' > ' in c]
        
        if not hierarchical_categories:
            logger.info("Žádné hierarchické kategorie k zpracování")
            return True
        
        logger.info(f"Zpracovávám {len(hierarchical_categories)} hierarchických kategorií")
        
        with driver.session() as session:
            # Vytvoříme vztahy v dávkách
            batch_size = 50
            total_batches = (len(hierarchical_categories) + batch_size - 1) // batch_size
            
            relationship_count = 0
            
            for i in range(0, len(hierarchical_categories), batch_size):
                batch = hierarchical_categories[i:i+batch_size]
                batch_num = i // batch_size + 1
                
                logger.info(f"Zpracovávám dávku vztahů {batch_num}/{total_batches} ({len(batch)} kategorií)")
                
                for category in batch:
                    parts = category.split(' > ')
                    
                    # Pro každou úroveň kromě poslední vytvoříme vztah k následující úrovni
                    for j in range(len(parts) - 1):
                        parent = ' > '.join(parts[:j+1])
                        child = ' > '.join(parts[:j+2])
                        
                        query = f"""
                        MATCH (parent:{CATEGORY_LABEL} {{name: $parent}})
                        MATCH (child:{CATEGORY_LABEL} {{name: $child}})
                        MERGE (parent)-[r:HAS_SUBCATEGORY]->(child)
                        RETURN count(r) as count
                        """
                        
                        result = session.run(query, {"parent": parent, "child": child})
                        relationship_count += result.single()["count"]
                
                # Krátká pauza mezi dávkami
                time.sleep(0.2)
            
            logger.info(f"Vytvořeno nebo aktualizováno {relationship_count} vztahů mezi kategoriemi")
            return True
    
    except Exception as e:
        logger.error(f"Chyba při vytváření vztahů mezi kategoriemi: {e}")
        return False

def create_category_similarity(driver):
    """Vytvoří vztahy podobnosti mezi kategoriemi na stejné úrovni."""
    try:
        with driver.session() as session:
            # Najdeme kategorie, které mají stejného rodiče, a vytvoříme mezi nimi vztah SIMILAR_TO
            query = f"""
            MATCH (parent:{CATEGORY_LABEL})-[:HAS_SUBCATEGORY]->(c1:{CATEGORY_LABEL})
            MATCH (parent)-[:HAS_SUBCATEGORY]->(c2:{CATEGORY_LABEL})
            WHERE c1 <> c2
            MERGE (c1)-[r:SIMILAR_TO]->(c2)
            RETURN count(r) as count
            """
            
            result = session.run(query)
            similarity_count = result.single()["count"]
            
            logger.info(f"Vytvořeno {similarity_count} vztahů podobnosti mezi kategoriemi")
            return True
    
    except Exception as e:
        logger.error(f"Chyba při vytváření vztahů podobnosti: {e}")
        return False

def create_complementary_relationships(driver):
    """Vytvoří vztahy komplementarity mezi kategoriemi na různých větvích."""
    try:
        with driver.session() as session:
            # Vytvoříme vztahy COMPLEMENTARY_TO mezi kategoriemi, které jsou v různých hlavních větvích
            # Nejprve najdeme všechny hlavní kategorie (první úroveň)
            query = f"""
            MATCH (c:{CATEGORY_LABEL})
            WHERE NOT ()-[:HAS_SUBCATEGORY]->(c)
            RETURN c.name as name
            """
            
            result = session.run(query)
            top_categories = [record["name"] for record in result]
            
            logger.info(f"Nalezeno {len(top_categories)} hlavních kategorií")
            
            # Pro každou dvojici hlavních kategorií vytvoříme vztahy komplementarity mezi jejich podkategoriemi
            complementary_count = 0
            
            for i in range(len(top_categories)):
                for j in range(i+1, len(top_categories)):
                    cat1 = top_categories[i]
                    cat2 = top_categories[j]
                    
                    # Vztahy mezi kategoriemi z různých hlavních větví
                    query = f"""
                    MATCH (root1:{CATEGORY_LABEL} {{name: $cat1}})-[:HAS_SUBCATEGORY*]->(c1:{CATEGORY_LABEL})
                    MATCH (root2:{CATEGORY_LABEL} {{name: $cat2}})-[:HAS_SUBCATEGORY*]->(c2:{CATEGORY_LABEL})
                    WHERE NOT (c1)-[:COMPLEMENTARY_TO]-(c2)
                    MERGE (c1)-[r:COMPLEMENTARY_TO]->(c2)
                    RETURN count(r) as count
                    """
                    
                    result = session.run(query, {"cat1": cat1, "cat2": cat2})
                    complementary_count += result.single()["count"]
            
            logger.info(f"Vytvořeno {complementary_count} vztahů komplementarity mezi kategoriemi")
            return True
    
    except Exception as e:
        logger.error(f"Chyba při vytváření vztahů komplementarity: {e}")
        return False

def main():
    """Hlavní funkce pro aktualizaci kategorií v Neo4j."""
    logger.info(f"=== Začátek aktualizace kategorií pro tenant {TENANT_ID} ===")
    
    # 1. Získání kategorií z Qdrantu
    qdrant_categories = get_qdrant_categories()
    if not qdrant_categories:
        logger.error("Nepodařilo se získat kategorie z Qdrantu. Ukončuji.")
        sys.exit(1)
    
    # 2. Připojení k Neo4j
    driver = connect_to_neo4j()
    
    # 3. Získání existujících kategorií z Neo4j
    neo4j_categories = get_neo4j_categories(driver)
    
    # 4. Porovnání kategorií
    new_categories = qdrant_categories - neo4j_categories
    logger.info(f"Nových kategorií k přidání: {len(new_categories)}")
    
    # 5. Vytvoření chybějících kategorií v Neo4j
    if new_categories:
        logger.info("Vytvářím nové kategorie v Neo4j...")
        if not create_categories_in_neo4j(driver, qdrant_categories):  # Všechny kategorie pro jistotu
            logger.error("Nepodařilo se vytvořit kategorie v Neo4j. Ukončuji.")
            driver.close()
            sys.exit(1)
    else:
        logger.info("Žádné nové kategorie k přidání.")
    
    # 6. Vytvoření hierarchických vztahů mezi kategoriemi
    logger.info("Vytvářím hierarchické vztahy mezi kategoriemi...")
    if not create_category_relationships(driver, qdrant_categories):
        logger.error("Nepodařilo se vytvořit hierarchické vztahy. Ukončuji.")
        driver.close()
        sys.exit(1)
    
    # 7. Vytvoření vztahů podobnosti mezi kategoriemi
    logger.info("Vytvářím vztahy podobnosti mezi kategoriemi...")
    if not create_category_similarity(driver):
        logger.error("Nepodařilo se vytvořit vztahy podobnosti. Ukončuji.")
        driver.close()
        sys.exit(1)
    
    # 8. Vytvoření vztahů komplementarity mezi kategoriemi
    logger.info("Vytvářím vztahy komplementarity mezi kategoriemi...")
    if not create_complementary_relationships(driver):
        logger.error("Nepodařilo se vytvořit vztahy komplementarity. Ukončuji.")
        driver.close()
        sys.exit(1)
    
    # Hotovo
    driver.close()
    logger.info(f"=== Aktualizace kategorií pro tenant {TENANT_ID} úspěšně dokončena ===")

if __name__ == "__main__":
    main()
