#!/bin/bash
# run_nightly_process.sh - Skript pro spuštění kompletního nočního procesu

# Výchozí hodnoty
TENANTS=""
BATCH_SIZE=50
WORKERS=10
FORCE_SYNC=false
SKIP_SYNC=false
SKIP_COMPUTE=false
SKIP_CLEAN=false

# Zpracování parametrů
while [[ $# -gt 0 ]]; do
  case $1 in
    -t|--tenants)
      TENANTS="$2"
      shift 2
      ;;
    -b|--batch-size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    -w|--workers)
      WORKERS="$2"
      shift 2
      ;;
    -f|--force-sync)
      FORCE_SYNC=true
      shift
      ;;
    --skip-sync)
      SKIP_SYNC=true
      shift
      ;;
    --skip-compute)
      SKIP_COMPUTE=true
      shift
      ;;
    --skip-clean)
      SKIP_CLEAN=true
      shift
      ;;
    -h|--help)
      echo "Použití: $0 [MOŽNOSTI]"
      echo "Spustí kompletní noční proces pro tenanty."
      echo
      echo "Možnosti:"
      echo "  -t, --tenants TENANTS   Seznam tenantů oddě<PERSON> (např. 'tenant1,tenant2')"
      echo "  -b, --batch-size SIZE   Velikost dávky pro zpracování produktů (výchozí: 50)"
      echo "  -w, --workers COUNT     Počet paralelních workerů (výchozí: 10)"
      echo "  -f, --force-sync        Vynutit aktualizaci všech produktů při synchronizaci"
      echo "  --skip-sync             Přeskočit krok synchronizace produktů"
      echo "  --skip-compute          Přeskočit krok výpočtu komplementárních produktů"
      echo "  --skip-clean            Přeskočit krok čištění referencí"
      echo "  -h, --help              Zobrazit tuto nápovědu"
      exit 0
      ;;
    *)
      echo "Neznámý parametr: $1"
      exit 1
      ;;
  esac
done

# Výpis parametrů
echo "=== SPOUŠTÍM KOMPLETNÍ NOČNÍ PROCES ==="
echo "Tenanty: ${TENANTS:-"všichni dostupní tenanty"}"
echo "Batch size: $BATCH_SIZE"
echo "Workers: $WORKERS"
echo "Force sync: $FORCE_SYNC"
echo "Skip sync: $SKIP_SYNC"
echo "Skip compute: $SKIP_COMPUTE"
echo "Skip clean: $SKIP_CLEAN"
echo

# Aktivace virtuálního prostředí
source /opt/gallitec/recommendatio_and_semantic/env/bin/activate

# Nastavení parametrů pro Python skript
PARAMS=()

if [ -n "$TENANTS" ]; then
  PARAMS+=("--tenants" "$TENANTS")
fi

PARAMS+=("--batch-size" "$BATCH_SIZE")
PARAMS+=("--workers" "$WORKERS")

if [ "$FORCE_SYNC" = true ]; then
  PARAMS+=("--force-sync")
fi

if [ "$SKIP_SYNC" = true ]; then
  PARAMS+=("--skip-sync")
fi

if [ "$SKIP_COMPUTE" = true ]; then
  PARAMS+=("--skip-compute")
fi

if [ "$SKIP_CLEAN" = true ]; then
  PARAMS+=("--skip-clean")
fi

# Spuštění Python skriptu s našimi parametry
python3 run_nightly_process.py "${PARAMS[@]}"

# Výpis ukončení
echo
echo "=== KOMPLETNÍ NOČNÍ PROCES DOKONČEN ===" 