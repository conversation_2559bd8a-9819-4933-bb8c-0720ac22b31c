"""\nReciprocal Rank Fusion (RRF) for combining multiple ranked lists of search results.\n"""
from typing import List, Dict, Any, Optional
import logging

logger = logging.getLogger(__name__)

class ReciprocalRankFusion:
    """
    Implements Reciprocal Rank Fusion for combining multiple ranked lists.
    
    Based on: https://www.elastic.co/guide/en/elasticsearch/reference/current/rrf.html
    """
    
    def __init__(self, k: int = 60):
        """
        Initialize RRF.
        
        Args:
            k: Ranking constant (higher = less influence of individual rankings)
        """
        self.k = k
    
    def fuse(
        self, 
        ranked_lists: List[List[Dict[str, Any]]],
        weights: Optional[List[float]] = None,
        score_key: str = "score"
    ) -> List[Dict[str, Any]]:
        """
        Fuse multiple ranked lists using RRF.
        
        Args:
            ranked_lists: List of ranked lists, where each item is a dict with 'id' and score_key
            weights: Optional weights for each ranked list (must match length of ranked_lists)
            score_key: Key in result dicts containing the score
            
        Returns:
            Fused and sorted list of results
        """
        if not ranked_lists:
            return []
            
        if weights is None:
            weights = [1.0] * len(ranked_lists)
        elif len(weights) != len(ranked_lists):
            raise ValueError("Number of weights must match number of ranked lists")
        
        fused_scores = {}
        
        # Process each ranked list
        for lst, weight in zip(ranked_lists, weights):
            for rank, item in enumerate(lst, 1):
                doc_id = item.get("id")
                if not doc_id:
                    logger.warning("Item missing 'id' in ranked list, skipping")
                    continue
                
                # Initialize score for this document if not exists
                if doc_id not in fused_scores:
                    fused_scores[doc_id] = {
                        "id": doc_id,
                        "score": 0.0,
                        "payload": item.get("payload", {}),
                        "scores": {}
                    }
                
                # Calculate RRF score for this ranking
                rrf_score = weight / (self.k + rank)
                fused_scores[doc_id]["score"] += rrf_score
                
                # Store individual scores for debugging/analysis
                list_name = f"list_{len(fused_scores[doc_id]['scores'])}"
                fused_scores[doc_id]["scores"][list_name] = {
                    "rank": rank,
                    "weight": weight,
                    "rrf_score": rrf_score
                }
        
        # Convert to list and sort by fused score (descending)
        sorted_results = sorted(
            fused_scores.values(),
            key=lambda x: x["score"],
            reverse=True
        )
        
        return sorted_results

    @staticmethod
    def normalize_scores(results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """
        Normalize scores to 0-1 range.
        
        Args:
            results: List of result dicts with 'score' key
            
        Returns:
            List of results with normalized scores
        """
        if not results:
            return []
            
        scores = [r.get("score", 0) for r in results]
        if not scores:
            return results
            
        min_score = min(scores)
        max_score = max(scores)
        score_range = max_score - min_score if max_score > min_score else 1.0
        
        for result in results:
            if "score" in result:
                result["normalized_score"] = (
                    (result["score"] - min_score) / score_range
                    if score_range > 0 else 0.5
                )
        
        return results
