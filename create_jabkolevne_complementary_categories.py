#!/usr/bin/env python3
"""
Skript pro vytvoření komplementárních kategorií a vztahů pro jabkolevne.cz v Neo4j databázi.
Tento skript vytváří zjednodušené kategorie a specifické komplementární vztahy 
podle návrhů pro e-shop jabkolevne.cz.

Autor: <PERSON>: 2025-01-17
"""

from neo4j import GraphDatabase
import os
from dotenv import load_dotenv
import logging
import time
from typing import Dict, List, Tuple
import json

# Nastavení loggingu
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Načtení proměnných prostředí
load_dotenv()

# Tenant, se kterým pracujeme
TENANT_ID = "jabkolevne"
CATEGORY_LABEL = f"Category_{TENANT_ID}"

# Neo4j konfigurace
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "Graph2025Secure!")

# Definice zjednodušených kategorií pro jabkolevne.cz
# Tyto kategorie by měly odpovídat hodnotám v poli category u produktů v Qdrant
SIMPLIFIED_CATEGORIES = [
    "iPhone",
    "iPad", 
    "MacBook",
    "Apple Watch",
    "AirPods",
    "Nabíječky (Apple)",
    "Kabely (Apple)",
    "Pouzdra pro iPhone",
    "Ochranná skla pro iPhone",
    "Pouzdra pro iPad",
    "Ochranná skla pro iPad",
    "Řemínky pro Apple Watch",
    "Bezdrátové nabíječky (Apple)",
    "Powerbanky (pro Apple)",
    "Apple Pencil",
    "AirTag",
    "Pouzdra pro AirPods",
    "Sluchátka (Apple ostatní)",
    "Reproduktory (Apple kompatibilní)",
    "Apple TV",
    "Příslušenství do auta (Apple)"
]

# Mapování z původních kategorií (z Qdrant) na zjednodušené kategorie
# Klíče jsou části původních kategorií, hodnoty jsou zjednodušené kategorie
CATEGORY_MAPPING = {
    # Hlavní produktové kategorie
    "iPhone": "iPhone",
    "iPad": "iPad", 
    "MacBook": "MacBook",
    "Mac": "MacBook",  # Mac produkty mapujeme na MacBook (hlavní kategorie Mac)
    "Watch": "Apple Watch",
    "Apple Watch": "Apple Watch",
    
    # Audio a příslušenství
    "AirPods": "AirPods",
    "Sluchátka": "Sluchátka (Apple ostatní)",
    "Reproduktory": "Reproduktory (Apple kompatibilní)",
    "Audio": "Sluchátka (Apple ostatní)",  # Obecné audio produkty
    
    # Příslušenství - pouzdra a kryty
    "Kryty na mobilní telefony Apple": "Pouzdra pro iPhone",
    "Pouzdra na sluchátka": "Pouzdra pro AirPods",
    
    # Příslušenství - ochranná skla
    "Tvrzená skla pro iPhone": "Ochranná skla pro iPhone",
    "Tvrzená skla": "Ochranná skla pro iPhone",  # Výchozí pro ochranná skla
    
    # Nabíjecí příslušenství
    "Nabíječky": "Nabíječky (Apple)",
    "Nabíječky pro mobilní telefony": "Nabíječky (Apple)",
    "Bezdrátové nabíječky": "Bezdrátové nabíječky (Apple)",
    "Powerbanky": "Powerbanky (pro Apple)",
    "kabely": "Kabely (Apple)",
    
    # Specifické Apple produkty
    "Apple Pencil": "Apple Pencil",
    "AirTag": "AirTag", 
    "Apple TV": "Apple TV",
    
    # Auto příslušenství
    "Příslušenství do auta": "Příslušenství do auta (Apple)"
}

# Definice komplementárních vztahů podle návrhů
# Struktura: (zdrojová_kategorie, cílová_kategorie, váha)
COMPLEMENTARY_RELATIONSHIPS = [
    # iPhone komplementární produkty
    ("iPhone", "Pouzdra pro iPhone", 1.0),
    ("iPhone", "Ochranná skla pro iPhone", 1.0),
    ("iPhone", "Nabíječky (Apple)", 0.9),
    ("iPhone", "Kabely (Apple)", 0.9),
    ("iPhone", "Bezdrátové nabíječky (Apple)", 0.8),
    ("iPhone", "Powerbanky (pro Apple)", 0.7),
    ("iPhone", "AirPods", 0.8),
    ("iPhone", "Příslušenství do auta (Apple)", 0.6),
    
    # iPad komplementární produkty
    ("iPad", "Pouzdra pro iPad", 1.0),
    ("iPad", "Ochranná skla pro iPad", 1.0),
    ("iPad", "Apple Pencil", 0.9),
    ("iPad", "Nabíječky (Apple)", 0.9),
    ("iPad", "Kabely (Apple)", 0.9),
    ("iPad", "AirPods", 0.7),
    
    # MacBook komplementární produkty
    ("MacBook", "Nabíječky (Apple)", 0.9),
    ("MacBook", "Kabely (Apple)", 0.8),
    ("MacBook", "AirPods", 0.7),
    
    # Apple Watch komplementární produkty
    ("Apple Watch", "Řemínky pro Apple Watch", 1.0),
    ("Apple Watch", "Nabíječky (Apple)", 0.8),
    ("Apple Watch", "AirPods", 0.7),
    
    # AirPods komplementární produkty
    ("AirPods", "Pouzdra pro AirPods", 0.9),
    ("AirPods", "Bezdrátové nabíječky (Apple)", 0.7),
]

def connect_to_neo4j():
    """Připojí se k Neo4j databázi."""
    try:
        logger.info(f"Připojuji se k Neo4j databázi na {NEO4J_URI}")
        driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        # Test připojení
        with driver.session() as session:
            result = session.run("RETURN 1 AS test")
            assert result.single()["test"] == 1
            logger.info("✅ Připojení k Neo4j úspěšné")
        return driver
    except Exception as e:
        logger.error(f"❌ Chyba při připojení k Neo4j: {e}")
        return None

def load_existing_categories():
    """Načte existující kategorie z JSON souboru."""
    try:
        with open('categories_jabkolevne.json', 'r', encoding='utf-8') as f:
            categories = json.load(f)
        logger.info(f"📂 Načteno {len(categories)} existujících kategorií z JSON")
        return categories
    except Exception as e:
        logger.error(f"❌ Chyba při načítání kategorií z JSON: {e}")
        return []

def map_categories_to_simplified(existing_categories: List[str]) -> Dict[str, str]:
    """Mapuje existující kategorie na zjednodušené kategorie."""
    mapping_result = {}
    unmapped_categories = []
    
    for category in existing_categories:
        simplified = None
        
        # Zkusíme najít nejlepší match
        for pattern, target in CATEGORY_MAPPING.items():
            if pattern.lower() in category.lower():
                simplified = target
                break
        
        if simplified:
            mapping_result[category] = simplified
        else:
            unmapped_categories.append(category)
    
    logger.info(f"📊 Mapování kategorií:")
    logger.info(f"   ✅ Mapováno: {len(mapping_result)} kategorií")
    logger.info(f"   ❓ Nemapováno: {len(unmapped_categories)} kategorií")
    
    if unmapped_categories[:5]:  # Ukázat prvních 5 nemapovaných
        logger.info(f"   📝 Ukázka nemapovaných: {unmapped_categories[:5]}")
    
    return mapping_result

def create_simplified_categories(driver, categories: List[str]):
    """Vytvoří zjednodušené kategorie v Neo4j."""
    try:
        with driver.session() as session:
            # Vytvoříme kategorie v dávce
            logger.info(f"🏗️  Vytvářím {len(categories)} zjednodušených kategorií...")
            
            query = f"""
            UNWIND $categories AS category
            MERGE (c:{CATEGORY_LABEL} {{name: category}})
            SET c.type = 'simplified'
            RETURN count(c) as count
            """
            
            result = session.run(query, {"categories": categories})
            created_count = result.single()["count"]
            
            logger.info(f"✅ Vytvořeno/aktualizováno {created_count} zjednodušených kategorií")
            return True
    
    except Exception as e:
        logger.error(f"❌ Chyba při vytváření zjednodušených kategorií: {e}")
        return False

def create_complementary_relationships(driver):
    """Vytvoří komplementární vztahy mezi kategoriemi."""
    try:
        with driver.session() as session:
            logger.info(f"🔗 Vytvářím {len(COMPLEMENTARY_RELATIONSHIPS)} komplementárních vztahů...")
            
            created_count = 0
            
            for source, target, weight in COMPLEMENTARY_RELATIONSHIPS:
                # Obousměrný vztah s váhou
                query = f"""
                MATCH (source:{CATEGORY_LABEL} {{name: $source}})
                MATCH (target:{CATEGORY_LABEL} {{name: $target}})
                MERGE (source)-[r:COMPLEMENTARY_TO]->(target)
                SET r.weight = $weight, r.created_by = 'jabkolevne_script'
                RETURN count(r) as count
                """
                
                result = session.run(query, {
                    "source": source, 
                    "target": target, 
                    "weight": weight
                })
                created_count += result.single()["count"]
                
                # Také vytvoříme zpětný vztah (může mít jinou váhu)
                reverse_query = f"""
                MATCH (source:{CATEGORY_LABEL} {{name: $source}})
                MATCH (target:{CATEGORY_LABEL} {{name: $target}})
                MERGE (target)-[r:COMPLEMENTARY_TO]->(source)
                SET r.weight = $weight, r.created_by = 'jabkolevne_script'
                RETURN count(r) as count
                """
                
                result = session.run(reverse_query, {
                    "source": source, 
                    "target": target, 
                    "weight": weight * 0.8  # Zpětný vztah má menší váhu
                })
                created_count += result.single()["count"]
            
            logger.info(f"✅ Vytvořeno {created_count} komplementárních vztahů")
            return True
    
    except Exception as e:
        logger.error(f"❌ Chyba při vytváření komplementárních vztahů: {e}")
        return False

def verify_relationships(driver):
    """Ověří vytvořené vztahy."""
    try:
        with driver.session() as session:
            # Počet kategorií
            result = session.run(f"MATCH (c:{CATEGORY_LABEL}) RETURN count(c) as count")
            cat_count = result.single()["count"]
            
            # Počet komplementárních vztahů
            result = session.run(f"""
                MATCH (c1:{CATEGORY_LABEL})-[r:COMPLEMENTARY_TO]->(c2:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN count(r) as count
            """)
            rel_count = result.single()["count"]
            
            # Ukázka vztahů s nejvyšší váhou
            result = session.run(f"""
                MATCH (c1:{CATEGORY_LABEL})-[r:COMPLEMENTARY_TO]->(c2:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN c1.name, c2.name, r.weight
                ORDER BY r.weight DESC
                LIMIT 5
            """)
            
            logger.info(f"📊 Verifikace výsledků:")
            logger.info(f"   📂 Celkem kategorií: {cat_count}")
            logger.info(f"   🔗 Komplementárních vztahů: {rel_count}")
            logger.info(f"   🏆 Top 5 vztahů podle váhy:")
            
            for record in result:
                logger.info(f"      {record['c1.name']} -> {record['c2.name']} (váha: {record['r.weight']})")
            
            return True
    
    except Exception as e:
        logger.error(f"❌ Chyba při verifikaci: {e}")
        return False

def main():
    """Hlavní funkce pro vytvoření komplementárních kategorií jabkolevne."""
    logger.info("🚀 === Vytváření komplementárních kategorií pro jabkolevne.cz ===")
    
    # 1. Připojení k Neo4j
    driver = connect_to_neo4j()
    if not driver:
        logger.error("❌ Nepodařilo se připojit k Neo4j. Ukončuji.")
        return False
    
    try:
        # 2. Načtení existujících kategorií
        existing_categories = load_existing_categories()
        if not existing_categories:
            logger.error("❌ Nepodařilo se načíst existující kategorie. Ukončuji.")
            return False
        
        # 3. Mapování na zjednodušené kategorie
        category_mapping = map_categories_to_simplified(existing_categories)
        
        # 4. Vytvoření zjednodušených kategorií
        unique_simplified = list(set(SIMPLIFIED_CATEGORIES))
        if not create_simplified_categories(driver, unique_simplified):
            logger.error("❌ Nepodařilo se vytvořit zjednodušené kategorie. Ukončuji.")
            return False
        
        # 5. Vytvoření komplementárních vztahů
        if not create_complementary_relationships(driver):
            logger.error("❌ Nepodařilo se vytvořit komplementární vztahy. Ukončuji.")
            return False
        
        # 6. Verifikace výsledků
        if not verify_relationships(driver):
            logger.error("❌ Verifikace selhala.")
            return False
        
        logger.info("🎉 === Úspěšně dokončeno vytváření komplementárních kategorií ===")
        return True
    
    finally:
        driver.close()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 