# data_models.py
from pydantic import BaseModel, Field, HttpUrl, validator, field_validator
from typing import List, Optional, Dict, Any, Union
import uuid

class Product(BaseModel):
    """Základní model produktu po transformaci z feedu."""
    id: str # Původní ID z feedu
    name: str
    category: str # Předpokládá se již standardizovaný formát!
    description: Optional[str] = ""
    price: Optional[float] = 0.0
    image_url: Optional[str] = None # Používáme str, HttpUrl může být příliš striktní
    product_url: Optional[str] = None # Používáme str
    brand: Optional[str] = None
    availability: Optional[str] = None
    raw_data: Dict[str, Any] = Field(default_factory=dict) # Pro uchování původních dat z feedu

    # Validátor pro zajištění, že ID a Name nejsou prázdné
    @field_validator('id', 'name', 'category')
    def check_not_empty(cls, value):
        if not value or not str(value).strip():
            raise ValueError("Field cannot be empty")
        return value

class ProductInternal(Product):
    """Interní reprezentace produktu s přidanými poli."""
    qdrant_id: Union[int, str] # UUID nebo int ID pro Qdrant
    embedding: Optional[List[float]] = None
    functional_contexts: List[str] = Field(default_factory=list)

class QdrantPayload(BaseModel):
    """Payload pro Qdrant bod, odvozený z produktu."""
    original_id: str
    product_id: str # Opakujeme pro snazší filtrování
    name: str
    category: str
    description: Optional[str] = ""
    price: Optional[float] = 0.0
    image_url: Optional[str] = None
    product_url: Optional[str] = None
    brand: Optional[str] = None
    availability: Optional[str] = None
    functional_contexts: List[str] = Field(default_factory=list)
    tenant_id: str

class CategoryRelationship(BaseModel):
    """Vztah mezi kategoriemi."""
    source_category: str
    target_category: str
    strength: float = Field(ge=0.0, le=1.0)
    reason: Optional[str] = None

class FunctionalContext(BaseModel):
    """Funkční kontext."""
    name: str # snake_case název
    description: str

class LLMComplementaryProductInfo(BaseModel):
    """Info o komplementárním produktu vrácené LLM."""
    complementary_product_id: str
    reason: str
    relevance_score: float = Field(default=0.0, ge=0.0, le=1.0)

class LLMComplementaryRelationship(BaseModel):
    """Struktura odpovědi LLM pro komplementární produkty."""
    main_product_id: str
    complementary_products: List[LLMComplementaryProductInfo]

class ComplementaryProductRecord(BaseModel):
    """Záznam v Qdrant kolekci komplementárních produktů."""
    main_product_id: str
    main_product_name: str
    main_product_url: Optional[str] = None
    main_product_category: str
    complementary_product_ids: List[str] # Seznam ID doporučených produktů
    complementary_product_reasons: Dict[str, str] # Mapování ID -> důvod
    relationship_type: str = "llm_generated"
    tenant_id: str