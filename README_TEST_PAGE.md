# Testovací stránka pro doporučení produktů

Tato testovací stránka umožňuje vizualizovat a testovat doporučovací systém produktů s využitím 3-vrstv<PERSON> architektury.

## Funkce

Stránka poskytuje následující funkce:

1. **Výběr tenanta** - Možnost přepínat mezi různými tenanty (e-shopy)
2. **Výběr produktu** - Dropdown pro výběr produktu, pro který chcete zobrazit doporučení
3. **Vyhledávání** - Vyhledávací pole s možností použití sémantického vyhledávání pomocí různých typů embeddingů
4. **Detail produktu** - Zobrazení detailních informací o vybraném produktu
5. **Doporučení produktů** - Čtyři typy doporučení:
   - **Podobné produkty** - Využívá kombinovaný embedding
   - **Lidé také kupuj<PERSON>** - Využívá kategoriální embedding
   - **Doplňky k produktu** - Využívá popisný embedding nebo předgenerovaná komplementární doporučení
   - **Další produkty stejné značky** - Využívá značkový embedding

## Spuštění

Pro spuštění testovací stránky použijte následující příkaz:

```bash
python run_test_page.py
```

Tento skript:
1. Spustí API server na portu 8000
2. Otevře testovací stránku v prohlížeči
3. Pro ukončení stiskněte Ctrl+C v terminálu

## Požadavky

- Python 3.8+
- FastAPI
- Uvicorn
- Qdrant klient
- Běžící Qdrant server s nahranými daty

## Struktura souborů

- `test_recommendations.html` - HTML stránka s UI pro testování doporučení
- `test_api_server.py` - FastAPI server poskytující API endpointy pro doporučení
- `run_test_page.py` - Skript pro spuštění API serveru a otevření testovací stránky

## API Endpointy

Testovací API poskytuje následující endpointy:

- `GET /api/products` - Seznam produktů pro daného tenanta
- `GET /api/product/{product_id}` - Detail produktu
- `GET /api/recommendations/similar` - Podobné produkty (kombinovaný embedding)
- `GET /api/recommendations/also_bought` - Produkty, které lidé také kupují (kategoriální embedding)
- `GET /api/recommendations/accessories` - Doplňky k produktu (popisný embedding)
- `GET /api/recommendations/same_brand` - Další produkty stejné značky (značkový embedding)
- `GET /api/search` - Vyhledávání produktů s možností použití embeddingů
- `GET /api/semantic-search` - Sémantické vyhledávání pomocí embeddingů

## Testování různých typů doporučení

1. **Podobné produkty**
   - Testuje schopnost systému najít produkty s podobnými vlastnostmi
   - Využívá kombinovaný embedding, který zahrnuje všechny aspekty produktu

2. **Lidé také kupují**
   - Testuje schopnost systému najít produkty, které patří do stejné kategorie
   - Využívá kategoriální embedding, který se zaměřuje na hierarchii kategorií

3. **Doplňky k produktu**
   - Testuje schopnost systému najít komplementární produkty
   - Využívá popisný embedding nebo předgenerovaná komplementární doporučení

4. **Další produkty stejné značky**
   - Testuje schopnost systému najít produkty od stejného výrobce
   - Využívá značkový embedding, který se zaměřuje na značku produktu

## Tipy pro testování

- Vyzkoušejte různé tenanty a porovnejte kvalitu doporučení
- Testujte produkty z různých kategorií
- Porovnejte výsledky vyhledávání s doporučeními
- Sledujte, jak se liší doporučení pro různé typy produktů
- Porovnejte výsledky textového a sémantického vyhledávání
- Vyzkoušejte různé typy embeddingů pro vyhledávání a sledujte rozdíly ve výsledcích

## Typy embeddingů pro vyhledávání

1. **Název a značka (name_brand)** - Vhodné pro vyhledávání konkrétních produktů podle názvu nebo značky
2. **Kombinovaný (combined)** - Nejvíce všestranný embedding, který kombinuje všechny ostatní typy
3. **Kategorie (category_hierarchy)** - Vhodné pro vyhledávání produktů v určité kategorii
4. **Značka a kategorie (brand_category)** - Vhodné pro vyhledávání produktů určité značky v určité kategorii
5. **Popis (pure_description)** - Vhodné pro vyhledávání podle funkcí a vlastností produktů
