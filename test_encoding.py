#!/usr/bin/env python3
# test_encoding.py
import os
import sys
import logging
import json
import requests
from typing import Dict, Any, List

# Přidáme cestu k root adresáři
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("test_encoding")

def test_filsonstore_connector(config: Dict[str, Any]):
    """Testuje konektor Filsonstore s opravou kódování"""
    try:
        # Import konektoru až po přidání cesty
        from core.connectors.filsonstore_connector import FilsonstoreConnector
        
        logger.info("Inicializuji FilsonstoreConnector...")
        connector = FilsonstoreConnector(config)
        
        logger.info("Získávám data z feedu...")
        products = connector.get_feed()
        
        if not products:
            logger.error("Nebyly nalezeny žádné produkty!")
            return False
        
        logger.info(f"Nalezeno {len(products)} produktů")
        
        # Kontrola kódování v prvních 5 produktech
        logger.info("Ukázka prvních 5 produktů pro kontrolu kódování:")
        for i, product in enumerate(products[:5]):
            logger.info(f"Produkt {i+1}:")
            logger.info(f"  ID: {product.get('ID', 'N/A')}")
            logger.info(f"  Název: {product.get('Name', 'N/A')}")
            logger.info(f"  Kategorie: {product.get('Category', 'N/A')}")
            logger.info(f"  Značka: {product.get('Brand', 'N/A')}")
            
            # Kontrola českých znaků v názvu a kategorii
            czech_chars = 'ěščřžýáíéúůťďňóĚŠČŘŽÝÁÍÉÚŮŤĎŇÓ'
            name_czech_chars = sum(1 for char in product.get('Name', '') if char in czech_chars)
            category_czech_chars = sum(1 for char in product.get('Category', '') if char in czech_chars)
            
            logger.info(f"  Počet českých znaků v názvu: {name_czech_chars}")
            logger.info(f"  Počet českých znaků v kategorii: {category_czech_chars}")
            
            # Kontrola problematických znaků
            problem_chars = ['Å', 'Ã', 'Ä', '\xad', '\xbd']
            has_problems = any(char in product.get('Name', '') + product.get('Category', '') for char in problem_chars)
            
            if has_problems:
                logger.warning(f"  NALEZENY PROBLEMATICKÉ ZNAKY v produktu {product.get('ID', 'N/A')}!")
            logger.info("  ---")
        
        # Sumarizace
        total_products = len(products)
        products_with_problems = 0
        
        for product in products:
            problem_chars = ['Å', 'Ã', 'Ä', '\xad', '\xbd']
            if any(char in product.get('Name', '') + product.get('Category', '') for char in problem_chars):
                products_with_problems += 1
        
        logger.info(f"SOUHRN: {products_with_problems} z {total_products} produktů stále obsahuje problematické znaky")
        
        if products_with_problems > 0:
            problem_percent = (products_with_problems / total_products) * 100
            logger.warning(f"Stále máme problémy s kódováním u {problem_percent:.2f}% produktů")
            return False
        else:
            logger.info("Všechny produkty mají správné kódování!")
            return True
        
    except Exception as e:
        logger.error(f"Chyba při testování konektoru: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_qdrant_encoding():
    """Testuje kódování znaků v databázi Qdrant"""
    try:
        logger.info("Kontroluji kódování v databázi Qdrant...")
        
        # Získání prvního produktu z kolekce
        response = requests.post(
            "http://localhost:6333/collections/real_products_filsonstore/points/scroll",
            json={"limit": 5},
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code != 200:
            logger.error(f"Chyba při komunikaci s Qdrant: {response.status_code} - {response.text}")
            return False
        
        data = response.json()
        if "result" not in data or "points" not in data["result"] or not data["result"]["points"]:
            logger.error("Nenalezeny žádné body v kolekci")
            return False
        
        logger.info("Ukázka produktů z Qdrant pro kontrolu kódování:")
        for i, point in enumerate(data["result"]["points"][:5]):
            payload = point.get("payload", {})
            logger.info(f"Produkt {i+1}:")
            logger.info(f"  ID: {payload.get('product_id', 'N/A')}")
            logger.info(f"  Název: {payload.get('name', 'N/A')}")
            logger.info(f"  Kategorie: {payload.get('category', 'N/A')}")
            logger.info(f"  Značka: {payload.get('brand', 'N/A')}")
            
            # Kontrola českých znaků v názvu a kategorii
            czech_chars = 'ěščřžýáíéúůťďňóĚŠČŘŽÝÁÍÉÚŮŤĎŇÓ'
            name_czech_chars = sum(1 for char in payload.get('name', '') if char in czech_chars)
            category_czech_chars = sum(1 for char in payload.get('category', '') if char in czech_chars)
            
            logger.info(f"  Počet českých znaků v názvu: {name_czech_chars}")
            logger.info(f"  Počet českých znaků v kategorii: {category_czech_chars}")
            
            # Kontrola problematických znaků
            problem_chars = ['Å', 'Ã', 'Ä', '\xad', '\xbd']
            has_problems = any(char in payload.get('name', '') + payload.get('category', '') for char in problem_chars)
            
            if has_problems:
                logger.warning(f"  NALEZENY PROBLEMATICKÉ ZNAKY v produktu {payload.get('product_id', 'N/A')}!")
            logger.info("  ---")
        
        return True
        
    except Exception as e:
        logger.error(f"Chyba při testování kódování v Qdrant: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    # Konfigurace pro testování konektoru
    filsonstore_config = {
        "feed_url": "https://www.filsonstore.cz/mergado/5d6a0c9eccc51.xml"
    }
    
    # Test konektoru
    logger.info("=== TESTOVÁNÍ KONEKTORU FILSONSTORE ===")
    connector_ok = test_filsonstore_connector(filsonstore_config)
    logger.info(f"Test konektoru: {'OK' if connector_ok else 'SELHALO'}")
    
    # Test kódování v databázi
    logger.info("\n=== TESTOVÁNÍ KÓDOVÁNÍ V QDRANT ===")
    db_ok = test_qdrant_encoding()
    logger.info(f"Test kódování v Qdrant: {'OK' if db_ok else 'SELHALO'}")
    
    # Celkový výsledek
    logger.info("\n=== VÝSLEDEK TESTŮ ===")
    if connector_ok and db_ok:
        logger.info("Všechny testy prošly úspěšně!")
        sys.exit(0)
    else:
        logger.error("Některé testy selhaly!")
        sys.exit(1) 