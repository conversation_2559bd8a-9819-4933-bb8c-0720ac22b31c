from fastapi import FastAP<PERSON>, HTTPException, Query, Depends, status, Header
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from typing import List, Dict, Optional, Union, Any, Tuple
from pydantic import BaseModel, Field
import uvicorn
import os
from dotenv import load_dotenv
from qdrant_client import QdrantClient
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, SearchParams, PointStruct, NamedVector, Distance, MatchAny
import numpy as np
from qdrant_client import AsyncQdrantClient
from core.clients import get_qdrant_async_client, get_openai_client, get_qdrant_sync_client, get_gemini_client, get_cohere_client
from semantic import search_semantic_products
import logging
from openai import AsyncOpenAI
import asyncio
import json
import yaml # Přidáno pro čtení YAML konfigurace
from qdrant_client.http import models # Přidán import pro models.HasIdCondition
from core.utils import create_qdrant_id # <--- <PERSON><PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON>í import
from core.utils import get_category_level # <--- Přidávám import pro novou funkci
import time # <PERSON><PERSON><PERSON><PERSON> chybějící import času
import math # Přidán import pro matematické operace
from core.config_manager import ConfigManager
import glob # Přidán import pro hledání souborů
import google.generativeai as genai # <- 1. Opravený import
import cohere # <- Přidán zpět import cohere
import random # Přidán import pro náhodné zamíchání

# Načtení proměnných z .env souboru
load_dotenv()

# Globální slovník pro uložené API klíče tenantů
TENANT_API_KEYS: Dict[str, str] = {}

# --- Funkce pro načtení klíčů při startu ---
def load_tenant_api_keys():
    """Načte API klíče pro všechny tenanty z .env na základě jejich konfigurace."""
    logger.info("Načítám API klíče tenantů...")
    config_dir = os.path.join("core", "config", "tenants")
    yaml_files = glob.glob(os.path.join(config_dir, "*.yaml"))

    for yaml_file in yaml_files:
        tenant_id = os.path.splitext(os.path.basename(yaml_file))[0]
        try:
            config_manager = ConfigManager(tenant_id=tenant_id)
            api_config = config_manager.get("api")

            if api_config and api_config.get("enabled") and api_config.get("key_env_prefix"):
                key_env_prefix = api_config.get("key_env_prefix")
                expected_key_env_var = f"{key_env_prefix}_API_KEY"
                expected_api_key = os.getenv(expected_key_env_var)
                if expected_api_key:
                    TENANT_API_KEYS[tenant_id] = expected_api_key
                    logger.debug(f"API klíč pro tenanta '{tenant_id}' načten.")
                else:
                    logger.warning(f"API klíč pro tenanta '{tenant_id}' (proměnná '{expected_key_env_var}') NENÍ nastaven v prostředí!")
            else:
                 logger.debug(f"API klíč pro tenanta '{tenant_id}' není konfigurován nebo je API vypnuto.")

        except Exception as e:
            logger.error(f"Chyba při načítání konfigurace nebo API klíče pro tenanta '{tenant_id}': {e}")

    logger.info(f"Načteno API klíčů pro {len(TENANT_API_KEYS)} tenantů.")

# --- Optimalizovaná kontrola API klíče ---
API_KEY_NAME = "X-API-Key"

async def verify_api_key(
    tenant_id: str,
    x_api_key: Optional[str] = Header(None, alias=API_KEY_NAME)
):
    """Závislost pro ověření tenant-specifického API klíče pomocí přednačtených hodnot."""
    # 1. Získat očekávaný klíč z přednačteného slovníku
    expected_api_key = TENANT_API_KEYS.get(tenant_id)

    if not expected_api_key:
        # Klíč nebyl načten při startu - buď není konfigurován, není v .env, nebo je API vypnuto.
        # Z bezpečnostních důvodů zde vrátíme Forbidden, i když by to mohla být chyba konfigurace.
        # Pro přesnější diagnostiku slouží logy při startu.
        logger.warning(f"Pokus o přístup pro tenanta '{tenant_id}', pro kterého není načten platný API klíč.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=f"Přístup pro tenanta '{tenant_id}' není povolen nebo konfigurován."
        )

    # 2. Ověřit poskytnutý klíč
    if not x_api_key:
        logger.warning(f"Chybějící API klíč v hlavičce '{API_KEY_NAME}' pro tenant '{tenant_id}'")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=f"Chybí API klíč v hlavičce '{API_KEY_NAME}'"
        )
    if x_api_key != expected_api_key:
        logger.warning(f"Poskytnutý API klíč je neplatný pro tenant '{tenant_id}'.")
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Neplatný API klíč"
        )

    # Klíč je platný
    logger.debug(f"API klíč pro tenant '{tenant_id}' úspěšně ověřen (z cache).")
    return
# --- Konec optimalizované kontroly ---

app = FastAPI(
    title="Gallitec API",
    description="API pro vyhledávání produktů a doporučení"
    # Globální závislost už nedává smysl, přidáváme ke každému endpointu zvlášť
)

# Konfigurace CORS
# Povolíme všechny zdroje pro lokální testování (v produkci by se mělo omezit)
origins = [
    "*", # Povolí všechny zdroje
    "https://72ae-178-255-168-83.ngrok-free.app", # <--- Přidána ngrok adresa
    # Případně můžeš explicitně povolit:
    # "http://localhost",
    # "http://localhost:8000", # Pokud by frontend běžel na jiném portu
    # "null" # Povolí požadavky z file:// (není vždy doporučeno pro produkci)
]

app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True, # Obvykle není potřeba pro GET, ale neškodí
    allow_methods=["*"],    # Povolí všechny metody (GET, POST, atd.)
    allow_headers=["*"],    # Povolí všechny hlavičky
)

# Modely pro API
class SimilarProduct(BaseModel):
    product_id: str
    name: str
    price: str
    product_url: str
    image_url: str
    similarity: Optional[float] = None
    reason: Optional[str] = None

class ComplementaryProduct(BaseModel):
    product_id: str
    name: str
    price: str
    product_url: str
    image_url: str
    similarity: Optional[float] = None
    reason: Optional[str] = None

class ProductRecommendations(BaseModel):
    similar_products: List[SimilarProduct] = Field(default_factory=list)
    complementary_products: List[ComplementaryProduct] = Field(default_factory=list)

# Globální proměnná pro Qdrant klienta
client = None

logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG) # <--- Explicitně nastavíme DEBUG úroveň pro logger modulu

def get_qdrant_client():
    """Vytvoří nebo vrátí existující instanci QdrantClient."""
    global client
    if client is None:
        qdrant_url = os.getenv("QDRANT_URL", "http://localhost:6333")
        client = QdrantClient(url=qdrant_url)
    return client

@app.get("/")
def read_root():
    """Vrátí obsah souboru test_search.html."""
    html_file_path = "test_search.html" # Předpokládáme, že je v rootu projektu
    if not os.path.exists(html_file_path):
        logger.error(f"Soubor {html_file_path} nebyl nalezen.")
        raise HTTPException(status_code=404, detail=f"{html_file_path} not found")
    return FileResponse(html_file_path)

@app.get("/tenants", response_model=List[str])
def list_tenants():
    """Vrátí seznam dostupných tenantů."""
    client = get_qdrant_sync_client()
    collections = client.get_collections().collections
    tenants = set()
    
    for collection in collections:
        # Parsování názvu kolekce - předpokládáme formát *_tenant_id
        parts = collection.name.split('_')
        if len(parts) >= 2:
            tenant_id = parts[-1]
            tenants.add(tenant_id)
    
    return list(tenants)

def are_vectors_default(vector):
    """
    Kontroluje, zda vektor má všechny hodnoty stejné (což znamená, že je výchozí dummy vektor).
    
    Args:
        vector: Vektor k testování
        
    Returns:
        bool: True, pokud je vektor výchozí dummy vektor, jinak False
    """
    # Vždy vracíme False, chceme používat cosine similarity
    return False

@app.get("/recommendations/{tenant_id}/by-product-id/{product_id}", response_model=ProductRecommendations, deprecated=True, dependencies=[Depends(verify_api_key)])
def get_recommendations_by_id(
    tenant_id: str, # tenant_id se předá do verify_api_key
    product_id: str,
    similar_limit: int = Query(5, description="Počet podobných produktů k vrácení"),
    complementary_limit: int = Query(5, description="Počet komplementárních produktů k vrácení")
):
    """[DEPRECATED] Použijte /similar-products a /complementary-products."""
    # ... (kód zůstává stejný, používá synchronní _get_product_details) ...
    # Tento endpoint nyní slouží jen pro zpětnou kompatibilitu a měl by být odstraněn
    logger.warning("Deprecated endpoint /recommendations called.")
    client = get_qdrant_sync_client() # Používá synchronního klienta
    products_collection = f"real_products_{tenant_id}"
    complementary_collection = f"complementary_products_{tenant_id}"
    try:
        product_details = _get_product_details(client, products_collection, product_id)
        if product_details is None: raise HTTPException(status_code=404, detail=f"Produkt s ID {product_id} nenalezen")
        product_info, product_vector, product_category, vector_name = product_details
        response = ProductRecommendations()
        # --- Získání podobných (zjednodušená logika z nové funkce) ---
        if product_vector and vector_name:
            similar_results = client.search(
                collection_name=products_collection, query_vector=(vector_name, product_vector),
                limit=similar_limit + 1, search_params=SearchParams(hnsw_ef=128, exact=False), with_payload=True
            )
            for point in similar_results:
                payload = point.payload; current_product_id = payload.get("product_id") or payload.get("original_id")
                if current_product_id == product_id: continue
                if len(response.similar_products) >= similar_limit: break
                response.similar_products.append(SimilarProduct(product_id=current_product_id or str(point.id), name=payload.get("name", ""), price=f"{payload.get('price', 0)} CZK", product_url=payload.get("product_url", ""), image_url=payload.get("image_url", ""), similarity=point.score))
        # --- Získání komplementárních (stará logika čtení z cache) ---
        collections = client.get_collections().collections
        if complementary_collection in [c.name for c in collections]:
             complementary_results = client.scroll(collection_name=complementary_collection, scroll_filter=Filter(must=[FieldCondition(key="main_product_id", match=MatchValue(value=product_id))]), limit=1, with_payload=True)
             if complementary_results and complementary_results[0]:
                 comp_point = complementary_results[0][0]; complementary_product_ids = comp_point.payload.get("complementary_product_ids", []); complementary_reasons = comp_point.payload.get("complementary_product_reasons", {})
                 if complementary_product_ids:
                      unique_ids = list(set(complementary_product_ids))[:complementary_limit]
                      for comp_id in unique_ids:
                           comp_details = _get_product_details(client, products_collection, comp_id)
                           if comp_details:
                                comp_payload, _, _, _ = comp_details
                                response.complementary_products.append(ComplementaryProduct(product_id=comp_id, name=comp_payload.get("name", ""), price=f"{comp_payload.get('price', 0)} CZK", product_url=comp_payload.get("product_url", ""), image_url=comp_payload.get("image_url", ""), reason=complementary_reasons.get(comp_id, "")))
        return response
    except Exception as e:
        logger.error(f"Chyba v get_recommendations_by_id: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Chyba při zpracování požadavku: {str(e)}")

@app.get("/product/{tenant_id}/{product_id}", response_model=Dict[str, Any], dependencies=[Depends(verify_api_key)])
def get_product_detail(tenant_id: str, product_id: str):
    """Získá detail produktu podle ID."""
    client = get_qdrant_sync_client()
    products_collection = f"real_products_{tenant_id}"
    semantic_collection = f"semantic_products_{tenant_id}"

    try:
        # Zkusíme nejprve sémantickou kolekci, pokud existuje
        try:
             collections = client.get_collections().collections
             collection_names = [c.name for c in collections]
             if semantic_collection in collection_names:
                 # Qdrant ID může být UUID string nebo int
                 qdrant_id = None
                 try:
                     # Pokud je product_id int, použijeme ho
                     qdrant_id = int(product_id)
                 except ValueError:
                     # Jinak zkusíme parsovat jako UUID (nebo použijeme přímo string, Qdrant to zvládne)
                     qdrant_id = str(product_id) # create_qdrant_id by bylo lepší
                     # Ideálně bychom měli mít funkci, která konzistentně mapuje product_id na Qdrant ID
                 
                 results = client.retrieve(
                     collection_name=semantic_collection,
                     ids=[qdrant_id],
                     with_payload=True
                 )
                 if results:
                     logger.info(f"Product {product_id} found in semantic collection {semantic_collection}.")
                     return results[0].payload
                 else:
                    logger.warning(f"Product ID {product_id} (Qdrant ID {qdrant_id}) not found in {semantic_collection}.")

        except Exception as e:
            logger.warning(f"Error checking/retrieving from semantic collection {semantic_collection}: {e}. Falling back to real_products collection.")

        # Fallback na real_products kolekci (starší struktura)
        # Hledáme produkt podle ID - nejprve zkusíme přímé ID
        results = client.scroll(
            collection_name=products_collection,
            scroll_filter=Filter(
                must=[FieldCondition(key="product_id", match=MatchValue(value=product_id))]
            ),
            limit=1,
            with_payload=True,
            with_vectors=False # Vektor zde nepotřebujeme
        )
        
        if not results[0]:
            # Zkusíme hledat podle original_id v payloadu
            results = client.scroll(
                collection_name=products_collection,
                scroll_filter=Filter(
                    must=[FieldCondition(key="original_id", match=MatchValue(value=product_id))]
                ),
                limit=1,
                with_payload=True,
                with_vectors=False
            )
        
        if not results[0]:
            raise HTTPException(status_code=404, detail=f"Produkt s ID {product_id} nenalezen v žádné kolekci")
        
        return results[0][0].payload

    except HTTPException as he:
        raise he # Propagujeme HTTP výjimky
    except Exception as e:
        logger.error(f"Chyba při získávání detailu produktu {product_id} pro tenanta {tenant_id}: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail="Interní chyba serveru")

@app.get("/search/{tenant_id}", response_model=List[Dict[str, Any]])
async def search_products(
    tenant_id: str,
    query: str = Query(..., description="Vyhledávací dotaz"),
    final_limit: int = Query(10, ge=1, le=50, description="Finální počet výsledků po re-rankingu"),
    enable_reranking: bool = Query(True, description="Zapnout/vypnout Cohere re-ranking"),
    rerank_limit_multiplier: int = Query(4, ge=1, le=10, description="Násobitel pro počet kandidátů před re-rankingem"),
    enable_keyword_filter: bool = Query(False, description="Zapnout/vypnout filtrování klíčových slov (fuzzy)"),
    keyword_similarity_threshold: int = Query(40, ge=0, le=100, description="Minimální práh podobnosti (0-100) pro fuzzy keyword filtr"),
    qdrant_client: AsyncQdrantClient = Depends(get_qdrant_async_client),
    openai_client: AsyncOpenAI = Depends(get_openai_client),
    cohere_client: Optional[cohere.Client] = Depends(get_cohere_client)
):
    """
    Vyhledává produkty sémanticky podle dotazu, volitelně s rerankingem.
    """
    semantic_collection_name = f"real_products_{tenant_id}"

    try:
        search_results = await search_semantic_products(
            client=qdrant_client,
            collection_name=semantic_collection_name,
            query_text=query,
            openai_client=openai_client,
            cohere_client=cohere_client,
            limit=final_limit,
            enable_reranking=enable_reranking,
            rerank_limit_multiplier=rerank_limit_multiplier,
            enable_keyword_filter=enable_keyword_filter,
            keyword_similarity_threshold=keyword_similarity_threshold
        )
        return search_results

    except Exception as e:
        # Logování chyby pro lepší diagnostiku
        logger.error(f"Chyba při sémantickém vyhledávání pro tenanta {tenant_id} s dotazem '{query}': {e}", exc_info=True)
        # Vrátíme obecnou chybu klientovi
        raise HTTPException(status_code=500, detail=f"Chyba při zpracování vyhledávacího požadavku.")

# --- Pomocná funkce pro získání detailů produktu ---

def _get_product_details(client: QdrantClient, collection_name: str, product_id: str) -> Optional[tuple[dict, Optional[list[float]], str, Optional[str]]]:
    """
    Získá payload, vektor (prioritně 'combined', pak 'default') a jeho název, a kategorii pro dané ID produktu.
    Používá SYNCHRONNÍHO klienta.
    Vrací None, pokud produkt není nalezen.
    Hledá podle product_id a fallback na original_id.
    """
    results = client.scroll(
        collection_name=collection_name,
        scroll_filter=Filter(must=[FieldCondition(key="product_id", match=MatchValue(value=product_id))]),
        limit=1, with_payload=True, with_vectors=True
    )
    if not results or not results[0]:
        results = client.scroll(
            collection_name=collection_name,
            scroll_filter=Filter(must=[FieldCondition(key="original_id", match=MatchValue(value=product_id))]),
            limit=1, with_payload=True, with_vectors=True
        )
    if not results or not results[0]: return None
    product_point = results[0][0]
    product_info = product_point.payload
    product_category = product_info.get("category", "")
    product_vector, vector_name = None, None
    if product_point.vector:
        if isinstance(product_point.vector, dict):
            if "combined" in product_point.vector: product_vector, vector_name = product_point.vector["combined"], "combined"
            elif "default" in product_point.vector: product_vector, vector_name = product_point.vector["default"], "default"; logger.warning(f"Using 'default' vector for {product_id}")
            else:
                 keys = list(product_point.vector.keys());
                 if keys: product_vector, vector_name = product_point.vector[keys[0]], keys[0]; logger.warning(f"Using first vector '{vector_name}' for {product_id}")
        elif isinstance(product_point.vector, list): product_vector = product_point.vector; logger.warning(f"Vector for {product_id} is list")
    return product_info, product_vector, product_category, vector_name

# --- Nový endpoint pro podobné produkty ---
@app.get("/similar-products/{tenant_id}/by-product-id/{product_id}", response_model=List[SimilarProduct], dependencies=[Depends(verify_api_key)])
async def get_similar_products_by_id(
    tenant_id: str,
    product_id: str,
    limit: int = Query(5, ge=1, le=50, description="Počet podobných produktů k vrácení"),
    qdrant_client: AsyncQdrantClient = Depends(get_qdrant_async_client)
):
    """Získá podobné produkty pro daný produkt podle ID."""
    t_start = time.time()
    products_collection = f"real_products_{tenant_id}"
    similar_products_list: List[SimilarProduct] = []

    try:
        # Získání detailů původního produktu - použijeme async verzi
        t1 = time.time()
        source_payload = await _get_product_payload_async(qdrant_client, products_collection, product_id)
        t2 = time.time()
        logger.info(f"[Timing] _get_product_payload_async took {t2 - t1:.4f}s")
        if source_payload is None: raise HTTPException(status_code=404, detail=f"Produkt s ID {product_id} nenalezen (payload)")

        t1 = time.time()
        source_vector = await _get_product_vector_async(qdrant_client, products_collection, product_id, "combined")
        t2 = time.time()
        logger.info(f"[Timing] _get_product_vector_async took {t2 - t1:.4f}s")
        source_qdrant_id = create_qdrant_id(product_id) # ID pro vyloučení

        # Hledáme, pokud máme vektor
        if source_vector:
            logger.info(f"Hledám podobné produkty pro {product_id} pomocí vektoru 'combined'")
            try:
                # Vrátíme se k použití metody search, která fungovala
                t1_search = time.time()
                similar_results = await qdrant_client.search(
                    collection_name=products_collection,
                    query_vector=("combined", source_vector), # Použijeme tuple (název_vektoru, vektor)
                    query_filter=models.Filter(must_not=[models.HasIdCondition(has_id=[source_qdrant_id])]),
                    limit=limit, # Metoda search přímo omezuje výsledky
                    with_payload=True,
                    score_threshold=0.1 # Přidán nízký práh podobnosti
                )
                t2_search = time.time()
                logger.info(f"[Timing] qdrant_client.search took {t2_search - t1_search:.4f}s")
                # Metoda search vrací přímo list ScoredPoint, nepotřebujeme .points
                search_result_points = similar_results
            except Exception as search_exc:
                 logger.error(f"Chyba při hledání v Qdrant pro podobné {product_id}: {search_exc}", exc_info=True)
                 search_result_points = [] # V případě chyby prázdný seznam

            # Zpracování výsledků (mělo by být rychlé)
            for point in search_result_points:
                 payload = point.payload
                 #raw_data = payload.get("raw_data", {}) # Bezpečné získání raw_data # VRÁCENO ZPĚT
                 current_product_id = payload.get("product_id") or payload.get("original_id")

                 # Původní logika získávání dat z hlavního payloadu
                 price_str = f"{payload.get('price', 0)} CZK"
                 product_url = payload.get("product_url", "")
                 image_url = payload.get("image_url", "")

                 similar_product = SimilarProduct(
                    product_id=current_product_id or str(point.id),
                    name=payload.get("name", ""),
                    price=price_str, # Použijeme získanou/formátovanou cenu
                    product_url=product_url, # Použijeme získané URL
                    image_url=image_url, # Použijeme získané URL obrázku
                    similarity=point.score,
                    reason=None
                 )
                 similar_products_list.append(similar_product)

        # Fallback na kategorii, pokud nemáme vektor NEBO search selhal a nic nenašel
        if not similar_products_list:
             source_category = source_payload.get("category", "")
             if source_category:
                 logger.info(f"Produkt {product_id} nemá vektor nebo hledání selhalo, hledám podle kategorie: {source_category}")
                 try:
                     t1_scroll = time.time()
                     category_results, _ = await qdrant_client.scroll(
                         collection_name=products_collection,
                         scroll_filter=Filter(
                              must=[FieldCondition(key="category", match=MatchValue(value=source_category))],\
                              must_not=[models.HasIdCondition(has_id=[source_qdrant_id])] # Vyloučení sebe sama
                         ),\
                         limit=limit, with_payload=True
                     )
                     t2_scroll = time.time()
                     logger.info(f"[Timing] Fallback qdrant_client.scroll took {t2_scroll - t1_scroll:.4f}s")
                     # Zpracování výsledků (mělo by být rychlé)
                     for point in category_results:
                          payload = point.payload
                          #raw_data = payload.get("raw_data", {}) # Bezpečné získání raw_data # VRÁCENO ZPĚT
                          current_product_id = payload.get("product_id") or payload.get("original_id")

                          # Původní logika získávání dat z hlavního payloadu
                          price_str = f"{payload.get('price', 0)} CZK"
                          product_url = payload.get("product_url", "")
                          image_url = payload.get("image_url", "")

                          similar_product = SimilarProduct(
                               product_id=current_product_id or str(point.id),\
                               name=payload.get("name", ""),\
                               price=price_str,\
                               product_url=product_url,\
                               image_url=image_url,\
                               similarity=None, reason="Stejná kategorie (fallback)"\
                          )
                          similar_products_list.append(similar_product)
                 except Exception as scroll_exc:
                     logger.error(f"Chyba při scroll pro fallback kategorii {product_id}: {scroll_exc}", exc_info=True)

        t_end = time.time()
        logger.info(f"[Timing] Total get_similar_products_by_id took {t_end - t_start:.4f}s")
        return similar_products_list

    except Exception as e:
        logger.error(f"Chyba v get_similar_products_by_id: {e}", exc_info=True)
        raise HTTPException(status_code=500, detail=f"Chyba při hledání podobných produktů: {str(e)}")

# --- Nový endpoint pro komplementární produkty ---
@app.get("/complementary-products/{tenant_id}/by-product-id/{product_id}", response_model=List[ComplementaryProduct], dependencies=[Depends(verify_api_key)])
async def get_complementary_products_by_id(
    tenant_id: str,
    product_id: str,
    limit: int = Query(10, ge=1, le=50, description="Maximální počet komplementárních produktů k vrácení z cache."), # Upraven popis
    qdrant_client: AsyncQdrantClient = Depends(get_qdrant_async_client)
):
    """Získá PŘEDPOČÍTANÉ komplementární (cross-sell) produkty z cache.""" # Upraven docstring

    products_collection = f"real_products_{tenant_id}"
    complementary_cache_collection = f"complementary_products_{tenant_id}" # Název cache kolekce
    source_qdrant_id = create_qdrant_id(product_id)
    complementary_products_list: List[ComplementaryProduct] = []

    # --- Pouze čtení z Cache ---
    try:
        cached_points = await qdrant_client.retrieve(
            collection_name=complementary_cache_collection,
            ids=[source_qdrant_id],
            with_payload=True
        )
        if cached_points:
            cached_payload = cached_points[0].payload
            # Zkontrolujeme, zda payload obsahuje očekávané klíče
            if cached_payload and "complementary_product_ids" in cached_payload:
                cached_product_ids = cached_payload.get("complementary_product_ids")
                cached_scores = cached_payload.get("gemini_scores", {}) # Očekáváme Gemini skóre

                if cached_product_ids:
                    logger.info(f"Nalezeny komplementární produkty v cache pro {product_id}.")
                    
                    # Omezíme IDčka PŘED načítáním detailů, pokud jich je v cache více než limit
                    ids_to_fetch = cached_product_ids[:limit]
                    scores_to_use = {pid: cached_scores.get(pid) for pid in ids_to_fetch} # Skóre jen pro vybraná ID
                    
                    # Načteme detaily produktů z hlavní kolekce POUZE pro potřebná ID
                    if ids_to_fetch:
                        details_points = await qdrant_client.retrieve(
                            collection_name=products_collection,
                            ids=[create_qdrant_id(pid) for pid in ids_to_fetch],
                            with_payload=True
                        )
                        details_map = {str(p.payload.get("product_id") or p.payload.get("original_id")): p.payload for p in details_points}

                        for pid in ids_to_fetch:
                            payload = details_map.get(pid)
                            if payload:
                                complementary_products_list.append(ComplementaryProduct(
                                    product_id=pid,
                                    name=payload.get("name", ""),
                                    price=f"{payload.get('price', 0)} CZK",
                                    product_url=payload.get("product_url", ""),
                                    image_url=payload.get("image_url", ""),
                                    similarity=scores_to_use.get(pid), # Použijeme uložené Gemini skóre
                                    reason=None # Z cache důvod neznáme
                                ))
                            else:
                                 logger.warning(f"Detail produktu {pid} z cache nenalezen v {products_collection}.")
                    # Nyní complementary_products_list obsahuje max `limit` produktů
                    return complementary_products_list
                else:
                    # Cache existuje, ale je prázdná (complementary_product_ids je [])
                    logger.info(f"Cache pro {product_id} existuje, ale neobsahuje žádné komplementární produkty.")
                    return [] # Vrátíme prázdný seznam
            else:
                # Cache point existuje, ale nemá očekávaný payload
                 logger.warning(f"Nalezen cache point pro {product_id}, ale payload nemá očekávaný formát (chybí 'complementary_product_ids').")
                 return [] # Neplatná cache, vrátíme prázdný seznam
        else:
            # Produkt nebyl v cache vůbec nalezen
            logger.info(f"Komplementární produkty pro {product_id} nenalezeny v cache ({complementary_cache_collection}).")
            return [] # Vrátíme prázdný seznam

    except Exception as cache_err:
        # Chyba při komunikaci s Qdrant nebo jiná neočekávaná chyba
        logger.error(f"Chyba při čtení cache {complementary_cache_collection} pro {product_id}: {cache_err}", exc_info=True)
        # V případě chyby vrátíme prázdný seznam, aby API nespadlo
        return []

    # --- Konec funkce - Všechny cesty by měly skončit returnem výše ---

# --- Odstraněna celá sekce výpočtu (dříve část 2.) ---

# ... (zbytek souboru zůstává) ...

def load_category_graph(graph_path: str) -> Optional[Dict[str, Dict[str, float]]]:
    """
    Načte graf kategorií z JSON souboru ve formátu slovníku slovníků.

    Args:
        graph_path: Cesta k JSON souboru s grafem.

    Returns:
        Slovník reprezentující graf kategorií (zdroj -> cíl -> váha),
        nebo None pokud soubor neexistuje nebo dojde k chybě při parsování.

    Příklad formátu JSON:
    {
        "Kategorie A": { "Kategorie B": 0.8, "Kategorie C": 0.6 },
        "Kategorie D": { "Kategorie A": 0.9 }
    }
    """
    if not os.path.exists(graph_path):
        logger.error(f"Soubor s grafem kategorií nebyl nalezen: {graph_path}")
        return None
    try:
        with open(graph_path, 'r', encoding='utf-8') as f:
            graph_data = json.load(f)
        # Jednoduchá validace, zda je to slovník
        if not isinstance(graph_data, dict):
             logger.error(f"Soubor s grafem {graph_path} nemá očekávaný formát kořenového slovníku.")
             return None
        # Případně detailnější validace struktury, pokud je potřeba
        # např. ověřit, že vnořené hodnoty jsou také slovníky s float čísly
        return graph_data
    except json.JSONDecodeError:
        logger.error(f"Chyba při parsování JSON souboru s grafem kategorií: {graph_path}", exc_info=True)
        return None
    except Exception as e:
        logger.error(f"Neočekávaná chyba při načítání grafu kategorií z {graph_path}: {e}", exc_info=True)
        return None

def get_complementary_categories_from_graph(graph_data: Dict[str, Dict[str, float]], source_category: str) -> List[Tuple[str, float]]:
    """
    Získá seznam komplementárních kategorií a jejich vah pro danou zdrojovou kategorii z načtených dat grafu.

    Args:
        graph_data: Slovník reprezentující graf kategorií (načtený pomocí load_category_graph).
        source_category: Název zdrojové kategorie.

    Returns:
        Seznam tuples (komplementární_kategorie, váha), seřazený sestupně podle váhy.
        Vrací prázdný seznam, pokud zdrojová kategorie není v grafu nalezena nebo nemá komplementární kategorie.
    """
    if source_category in graph_data:
        complementary_dict = graph_data[source_category]
        if isinstance(complementary_dict, dict):
            # Převedeme na seznam tuples a seřadíme podle váhy (sestupně)
            sorted_complementary = sorted(complementary_dict.items(), key=lambda item: item[1], reverse=True)
            return sorted_complementary
        else:
            logger.warning(f"Data pro zdrojovou kategorii '{source_category}' v grafu nejsou ve formátu slovníku.")
            return []
    else:
        # logger.debug(f"Zdrojová kategorie '{source_category}' nebyla nalezena v grafu kategorií.")
        return []

async def _get_product_payload_async(client: AsyncQdrantClient, collection_name: str, product_id: str) -> Optional[Dict[str, Any]]:
    """Asynchronně získá payload produktu podle ID."""
    try:
        # Zkusíme přímé ID
        points = await client.retrieve(collection_name=collection_name, ids=[create_qdrant_id(product_id)], with_payload=True)
        if points: return points[0].payload

        # Fallback na hledání v payloadu (pomalejší)
        scroll_result = await client.scroll(
            collection_name=collection_name,
            scroll_filter=Filter(must=[FieldCondition(key="product_id", match=MatchValue(value=product_id))]),
            limit=1, with_payload=True
        )
        if scroll_result and scroll_result[0]: return scroll_result[0][0].payload

        scroll_result = await client.scroll(
            collection_name=collection_name,
            scroll_filter=Filter(must=[FieldCondition(key="original_id", match=MatchValue(value=product_id))]),
            limit=1, with_payload=True
        )
        if scroll_result and scroll_result[0]: return scroll_result[0][0].payload

        return None
    except Exception as e:
        logger.error(f"Error retrieving payload for {product_id} in {collection_name}: {e}", exc_info=True)
        return None

async def _get_product_vector_async(client: AsyncQdrantClient, collection_name: str, product_id: str, vector_name: str = "combined") -> Optional[List[float]]:
    """Asynchronně získá specifický vektor produktu."""
    try:
        points = await client.retrieve(collection_name=collection_name, ids=[create_qdrant_id(product_id)], with_payload=False, with_vectors=[vector_name])
        if points and points[0].vector and vector_name in points[0].vector:
            return points[0].vector[vector_name]
        # Fallback na pokus o načtení 'default', pokud 'combined' selže a je požadován
        if vector_name == "combined":
             points = await client.retrieve(collection_name=collection_name, ids=[create_qdrant_id(product_id)], with_payload=False, with_vectors=["default"])
             if points and points[0].vector and "default" in points[0].vector:
                 logger.warning(f"Using 'default' vector for {product_id} as '{vector_name}' fallback.")
                 return points[0].vector["default"]
        return None
    except Exception as e:
        logger.error(f"Error retrieving vector '{vector_name}' for {product_id} in {collection_name}: {e}", exc_info=True)
        return None

# Funkce pro načtení tenant YAML konfigurace
def get_tenant_yaml_config(tenant_id: str) -> Optional[Dict[str, Any]]:
    """Načte konfiguraci tenanta z YAML souboru."""
    config_path = os.path.join("config", "tenants", f"{tenant_id}.yaml")
    if not os.path.exists(config_path):
        logger.error(f"Konfigurační soubor pro tenanta '{tenant_id}' nebyl nalezen na cestě: {config_path}")
        return None
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.info(f"Konfigurace pro tenanta '{tenant_id}' úspěšně načtena z {config_path}.")
        return config
    except Exception as e:
        logger.error(f"Chyba při načítání nebo parsování YAML konfigurace z {config_path}: {e}", exc_info=True)
        return None

# --- Spuštění funkce pro načtení klíčů při startu --- 
@app.on_event("startup")
async def startup_event():
    load_tenant_api_keys()

if __name__ == "__main__":
    # --- Konfigurace logování --- 
    log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    log_file = "api.log" # Název log souboru

    # Handler pro zápis do souboru (včetně DEBUG úrovně)
    file_handler = logging.handlers.RotatingFileHandler(log_file, maxBytes=10*1024*1024, backupCount=3, encoding='utf-8')
    file_handler.setFormatter(log_formatter)
    file_handler.setLevel(logging.DEBUG) # Zapisuje vše od DEBUG výše

    # Handler pro výpis do konzole (např. jen INFO)
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(log_formatter)
    console_handler.setLevel(logging.INFO) # Vypisuje jen INFO a vyšší

    # Získání root loggeru a přidání handlerů
    # Odstraníme případné existující handlery, aby se předešlo duplicitnímu logování
    root_logger = logging.getLogger()
    if root_logger.hasHandlers():
        root_logger.handlers.clear()
        
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    root_logger.setLevel(logging.DEBUG) # Celková úroveň loggeru musí být DEBUG

    # Informace o startu
    logger.info(f"Starting API server... Logging to console (INFO+) and file '{log_file}' (DEBUG+)")
    
    # Získání portu z .env nebo výchozí 8000
    port = int(os.getenv("PORT", 8000))
    
    # Spuštění Uvicorn serveru
    uvicorn.run(app, host="0.0.0.0", port=port) 