# Instrukce pro nasazení opravy kódování v konektoru Filsonstore

## Nalezený problém
V současném feedu z Filsonstore.cz dochází k problémům s kódováním českých znaků, co<PERSON>sobuje, že v databázi Qdrant se české znaky zobrazují nesprávně (např. "AutodoplÅky" místo "Autodoplňky").

## Řešení problému
Oprava spočívá v úpravě konektoru `filsonstore_connector.py`, který implementuje lepší zpracování kódování při stahování a parsování XML feedu a při normalizaci textů s českými znaky.

## Postup nasazení

1. **Vytvoření zálohy původního souboru**
   ```bash
   ssh root@************** "cp /opt/gallitec/recommendatio_and_semantic/core/connectors/filsonstore_connector.py /opt/gallitec/recommendatio_and_semantic/core/connectors/filsonstore_connector.py.bak_$(date +%Y%m%d)"
   ```

2. **Nahrání nového souboru**
   - Nahrajte soubor `filsonstore_connector_fix.py` na server
   - Přesuňte jej na místo původního konektoru:
   ```bash
   scp filsonstore_connector_fix.py root@**************:/opt/gallitec/recommendatio_and_semantic/core/connectors/filsonstore_connector.py
   ```

3. **Resetování kolekce Qdrant (volitelné)**
   - Pokud chcete kompletně přesynchronizovat data, můžete smazat a znovu vytvořit kolekci:
   ```bash
   ssh root@************** "curl -X DELETE http://localhost:6333/collections/real_products_filsonstore"
   ```
   - **POZOR**: Toto smaže všechna data, včetně doporučení. Použijte pouze pokud je to nezbytné.

4. **Spuštění synchronizace produktů**
   ```bash
   ssh root@************** "cd /opt/gallitec/recommendatio_and_semantic && python sync_products.py --tenant filsonstore --force"
   ```
   - Parametr `--force` způsobí kompletní přegenerování včetně embeddingů.
   - Alternativně můžete použít jen aktualizaci metadat bez regenerování embeddingů:
   ```bash
   ssh root@************** "cd /opt/gallitec/recommendatio_and_semantic && python sync_products.py --tenant filsonstore --skip-embedding-updates"
   ```

5. **Kontrola výsledku**
   - Zkontrolujte log soubor synchronizace:
   ```bash
   ssh root@************** "tail -f /opt/gallitec/recommendatio_and_semantic/logs/sync_products.log"
   ```
   - Ověřte správné kódování v databázi:
   ```bash
   ssh root@************** "curl -X POST -H 'Content-Type: application/json' http://localhost:6333/collections/real_products_filsonstore/points/scroll -d '{\"limit\": 1}'"
   ```

## Hlavní změny v konektoru
1. Explicitní nastavení kódování při stahování XML feedu
2. Analýza deklarace kódování v XML
3. Vylepšená metoda normalizace textu s více způsoby opravy kódování
4. Slovník známých problematických znaků a jejich správných ekvivalentů
5. Heuristika pro výběr nejlepšího výsledku opravy
6. Logování ukázky dat pro kontrolu kódování

## Poznámky
- Nový konektor by měl být kompatibilní se stávajícím systémem bez dalších změn
- Oprava by měla fungovat automaticky při dalších synchronizacích dat
- Pokud by se ukázaly další problémy s kódováním, je možné rozšířit slovník `replacements` v metodě `_normalize_text` 