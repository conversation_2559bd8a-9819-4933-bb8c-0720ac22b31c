version: '3'

services:
  neo4j:
    image: neo4j:latest
    container_name: neo4j
    ports:
      - "7474:7474"  # HTTP pro Neo4j Browser
      - "7687:7687"  # Bolt pro připojení aplikace
    volumes:
      - neo4j_data:/data
      - neo4j_plugins:/plugins
      - neo4j_logs:/logs
    environment:
      - NEO4J_AUTH=neo4j/Graph2025Secure!
      - NEO4J_ACCEPT_LICENSE_AGREEMENT=yes
    restart: unless-stopped

volumes:
  neo4j_data:
  neo4j_plugins:
  neo4j_logs: 