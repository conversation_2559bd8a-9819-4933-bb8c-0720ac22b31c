#!/usr/bin/env python3
"""Kontrola dat v complementary_products kolekci."""

from qdrant_client import QdrantClient
import json

def main():
    client = QdrantClient('http://localhost:6333')
    collection_name = 'complementary_products_jabkolevne'
    
    # Získat několik záznamů
    result, _ = client.scroll(
        collection_name=collection_name,
        limit=5,
        with_payload=True
    )
    
    print(f"Počet záznamů v kolekci: {len(result)}")
    print("\nPrvních 5 záznamů:")
    
    for i, point in enumerate(result):
        print(f"\n--- Záznam {i+1} ---")
        print(f"ID: {point.id}")
        print(f"Payload klíče: {list(point.payload.keys())}")
        
        # Zobrazit detaily payloadu
        if 'product_id' in point.payload:
            print(f"Product ID: {point.payload['product_id']}")
        
        # Zkontrolovat různé možn<PERSON> kl<PERSON> pro komplementární produkty
        for key in ['complementary_ids', 'complementary_product_ids', 'complementary_products']:
            if key in point.payload:
                ids = point.payload[key]
                print(f"{key}: {len(ids)} produktů")
                if ids:
                    print(f"  První 3: {ids[:3]}")
        
        # Zkontrolovat skóre
        for key in ['scores', 'gemini_scores']:
            if key in point.payload:
                scores = point.payload[key]
                print(f"{key}: {len(scores)} skóre")

if __name__ == "__main__":
    main() 