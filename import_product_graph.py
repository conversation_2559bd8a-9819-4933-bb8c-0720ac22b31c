#!/usr/bin/env python3
import json
import sys
import argparse
from neo4j import GraphDatabase

# Konfigurace připojení k Neo4j
URI = "bolt://localhost:7687"
AUTH = ("neo4j", "Graph2025Secure!")

def import_product_graph(json_file_path, tenant_id, use_tenant_prefixes=True):
    """
    Importuje data z JSON souboru do Neo4j databáze pro konkrétního tenanta.
    JSON struktura: {kategorie1: {kategorie2: váha, ...}, ...}
    
    Args:
        json_file_path: Cesta k JSON souboru s daty grafu
        tenant_id: ID tenanta
        use_tenant_prefixes: Použít prefixy labelů místo samostatných databází (pro Community Edition)
    """
    # Načtení dat z JSON souboru
    with open(json_file_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
    
    # P<PERSON>ip<PERSON>jen<PERSON> k Neo4j
    with GraphDatabase.driver(URI, auth=AUTH) as driver:
        # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> připojení
        driver.verify_connectivity()
        print("Připojení k Neo4j úspěšně navázáno!")
        
        db_name = "neo4j"  # Výchozí název databáze
        category_label = "Category"  # Výchozí label pro kategorie
        
        # Pro Enterprise Edition - pokusíme se vytvořit samostatnou databázi pro tenanta
        if not use_tenant_prefixes:
            try:
                with driver.session(database="system") as system_session:
                    # Kontrola, zda databáze již existuje
                    result = system_session.run("SHOW DATABASES")
                    databases = [record["name"] for record in result]
                    
                    # Pokud databáze neexistuje, vytvoříme ji
                    db_name = f"tenant_{tenant_id}"
                    if db_name not in databases:
                        print(f"Vytvářím novou databázi pro tenanta {tenant_id}...")
                        system_session.run(f"CREATE DATABASE {db_name}")
                        print(f"Databáze {db_name} vytvořena.")
                    else:
                        print(f"Databáze {db_name} již existuje.")
            except Exception as e:
                print(f"Chyba při vytváření databáze: {e}")
                print("Pravděpodobně používáte Community Edition - používám tenant prefixy.")
                use_tenant_prefixes = True
        
        # Pro Community Edition - použijeme tenant prefixy
        if use_tenant_prefixes:
            category_label = f"Category_{tenant_id}"
            print(f"Používám label s prefixem tenanta: {category_label}")
        
        # Vytvoření session pro práci s databází
        with driver.session(database=db_name) as session:
            # 1. Nejprve vytvoříme všechny uzly (kategorie)
            categories = set(data.keys())
            for related_cats in data.values():
                categories.update(related_cats.keys())
            
            # Odstranění prázdných stringů nebo None hodnot
            categories = {cat for cat in categories if cat}
            
            # Vytvoření všech kategorií jako uzlů
            print(f"Vytvářím {len(categories)} uzlů kategorií...")
            for category in categories:
                # MERGE zajistí, že kategorie bude vytvořena pouze jednou
                session.run(
                    f"MERGE (c:{category_label} {{name: $name}}) RETURN c",
                    name=category
                )
            
            # 2. Vytvoření vztahů mezi kategoriemi
            print("Vytvářím vztahy mezi kategoriemi...")
            created_relationships = 0
            
            for source_category, related_categories in data.items():
                for target_category, weight in related_categories.items():
                    if not target_category:  # Přeskočit prázdné cíle
                        continue
                    
                    # Vytvoření vztahu s váhou
                    result = session.run(
                        f"""
                        MATCH (source:{category_label} {{name: $source_name}})
                        MATCH (target:{category_label} {{name: $target_name}})
                        MERGE (source)-[r:RELATED_TO {{weight: $weight}}]->(target)
                        RETURN count(r) as rel_count
                        """,
                        source_name=source_category,
                        target_name=target_category,
                        weight=weight
                    )
                    created_relationships += result.single()["rel_count"]
            
            print(f"Vytvořeno {created_relationships} vztahů mezi kategoriemi.")
            
            # 3. Vytvoření indexu pro rychlejší vyhledávání podle jména kategorie
            try:
                session.run(f"CREATE INDEX IF NOT EXISTS FOR (c:{category_label}) ON (c.name)")
                print(f"Vytvořen index pro {category_label}.name")
            except Exception as e:
                print(f"Varování: Nepodařilo se vytvořit index: {e}")
            
            # 4. Kontrola importu - počet uzlů a vztahů v databázi
            node_count = session.run(f"MATCH (c:{category_label}) RETURN count(c) as count").single()["count"]
            rel_count = session.run(f"MATCH (:{category_label})-[r:RELATED_TO]->(:{category_label}) RETURN count(r) as count").single()["count"]
            
            print(f"Celkový počet uzlů: {node_count}")
            print(f"Celkový počet vztahů: {rel_count}")
            
            # 5. Zobrazení několika příkladů pro kontrolu
            print("\nPříklad dat v databázi:")
            result = session.run(f"""
                MATCH (source:{category_label})-[r:RELATED_TO]->(target:{category_label})
                RETURN source.name, target.name, r.weight
                LIMIT 5
            """)
            for record in result:
                print(f"{record['source.name']} --[{record['r.weight']}]--> {record['target.name']}")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Import grafových dat pro kategorie produktů do Neo4j")
    parser.add_argument("json_file", help="Cesta k JSON souboru s daty grafu")
    parser.add_argument("tenant_id", help="ID tenanta")
    parser.add_argument("--use-database", action="store_true", 
                        help="Použít samostatnou databázi (pouze pro Enterprise Edition)")
    
    args = parser.parse_args()
    
    import_product_graph(args.json_file, args.tenant_id, not args.use_database)
    print("Import dokončen!")