#!/usr/bin/env python3

"""
Skript pro automatizovanu00fd onboarding tenanta jabkolevne.
Provede nu00e1sleduju00edcu00ed kroky:
1. Testovu00e1nu00ed konektoru jabkolevne
2. Synchronizace produktu016f z jabkolevne feedu do Qdrantu
3. Vu00fdpou010det komplementu00e1rnu00edch produktu016f pomocu00ed generativnu00edho AI

Pouu017eitu00ed: python onboard_jabkolevne.py
"""

import os
import sys
import asyncio
import logging
import time
from typing import List, Dict, Any, Optional

# Pu0159idu00e1nu00ed cesty k rootu projektu
project_root = os.path.abspath(os.path.dirname(__file__))
sys.path.insert(0, project_root)

# Import potu0159ebnu00fdch modulu016f
from core.config import settings, tenant_configs
from core.clients import get_qdrant_async_client
from connectors.connector_factory import get_connector
from sync_products import ProductSynchronizer

# Nastavenu00ed loggeru
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("onboard_jabkolevne")

TENANT_ID = "jabkolevne"

async def test_connector():
    """
    Otestuje connector pro jabkolevne feed a zobrzu00ed zu00e1kladnu00ed informace o produktech.
    """
    logger.info("=== Testovu00e1nu00ed konektoru jabkolevne ===")
    
    try:
        tenant_config = tenant_configs.get(TENANT_ID)
        if not tenant_config:
            logger.error(f"Tenant {TENANT_ID} nenu00ed nakonfigurovanu00fd v core/config.py")
            return False
        
        feed_url = tenant_config.feed.feed_url
        connector = get_connector(TENANT_ID, feed_url)
        
        logger.info(f"Zu00edsku00e1vu00e1m produkty z {feed_url}")
        sample_products = await connector.get_products(limit=5)  # Zu00edsku00e1me jen 5 produktu016f pro testovu00e1nu00ed
        
        if not sample_products:
            logger.error("Nebyly nalezeny u017eu00e1dnu00e9 produkty!")
            return False
            
        logger.info(f"\u00daspu011bu0161nu011b nau010dteno {len(sample_products)} testovacu00edch produktu016f")
        
        # Zobrazenu00ed jednoho produktu pro kontrolu struktury
        logger.info("\nUku00e1zka produktu:")
        sample = sample_products[0]
        for key, value in sample.items():
            if isinstance(value, str) and len(value) > 100:
                logger.info(f"  {key}: {value[:100]}...")
            else:
                logger.info(f"  {key}: {value}")
        
        return True
    
    except Exception as e:
        logger.error(f"Chyba pu0159i testovu00e1nu00ed konektoru: {e}")
        return False

async def sync_products():
    """
    Synchronizuje produkty z jabkolevne feedu do Qdrantu.
    """
    logger.info("\n=== Synchronizace produktu016f do Qdrantu ===")
    
    try:
        qdrant_client = get_qdrant_async_client()
        
        tenant_config = tenant_configs.get(TENANT_ID)
        if not tenant_config:
            logger.error(f"Tenant {TENANT_ID} nenu00ed nakonfigurovanu00fd v core/config.py")
            return False
        
        feed_url = tenant_config.feed.feed_url
        connector = get_connector(TENANT_ID, feed_url)
        
        # Vytvou0159enu00ed synchronizeru
        synchronizer = ProductSynchronizer(
            connector=connector, 
            tenant_id=TENANT_ID, 
            qdrant_client=qdrant_client
        )
        
        # Spustu00edme synchronizaci
        logger.info("Spouu0161tu00edm synchronizaci produktu016f...")
        start_time = time.time()
        
        await synchronizer.sync(force_update=True)
        
        elapsed = time.time() - start_time
        logger.info(f"Synchronizace dokonu010dena za {elapsed:.2f} sekund.")
        
        # Zu00edsku00e1nu00ed statistik
        stats = synchronizer.get_statistics()
        logger.info(f"Statistiky synchronizace:\n" + 
                  f"  Celkem zpracovu00e1no: {stats.total}\n" +
                  f"  Novu00e9 produkty: {stats.new}\n" +
                  f"  Aktualizovu00e1no: {stats.updated}\n" +
                  f"  Smazu00e1no: {stats.deleted}\n" +
                  f"  Chyby: {stats.errors}")
        
        return True
    
    except Exception as e:
        logger.error(f"Chyba pu0159i synchronizaci produktu016f: {e}")
        return False

async def generate_recommendations():
    """
    Generuje komplementu00e1rnu00ed produkty pomocu00ed zu00e1lou017enu00edho algoritmu.
    """
    logger.info("\n=== Generovu00e1nu00ed komplementu00e1rnu00edch produktu016f ===")
    
    try:
        # Nastavenu00ed promu011bnnu00fdch prostu0159edu00ed pro lepu0161u00ed kontrolu
        os.environ["TENANT"] = TENANT_ID
        os.environ["ULTRA"] = "1"  # Pouu017eijeme ultra optimalizovanu00fd reu017eim
        
        # Spustu00edme pu0159u00edkaz pomocu00ed subprocess
        logger.info("Spouu0161tu00edm vu00fdpou010det komplementu00e1rnu00edch produktu016f...")
        
        # Pouu017eijeme asyncio.create_subprocess_shell pro spouu0161tu011bnu00ed pu0159u00edkazu016f
        cmd = f"python {os.path.join(project_root, 'batch_scripts/compute_complementary.py')} --tenant {TENANT_ID} --ultra"
        logger.info(f"Pu0159u00edkaz: {cmd}")
        
        # Kontrola pou010dtu produktu016f v cu00edlovu00e9 kolekci
        qdrant_client = get_qdrant_async_client()
        products_collection = f"real_products_{TENANT_ID}"
        complementary_collection = f"complementary_products_{TENANT_ID}"
        
        # Zu00edsku00e1nu00ed pou010dtu produktu016f pro zu00e1lou017enu00ed vu00fdpou010det
        try:
            from qdrant_client.models import CountRequest
            product_count = await qdrant_client.count(collection_name=products_collection, count_filter=None)
            logger.info(f"Pou010det produktu016f v kolekci {products_collection}: {product_count.count}")
            
            # Kontrola existence kolekce pro komplementu00e1rnu00ed produkty
            collections = await qdrant_client.get_collections()
            collection_names = [col.name for col in collections.collections]
            
            if complementary_collection not in collection_names:
                logger.info(f"Vytvu00e1u0159u00edm kolekci {complementary_collection}")
                from qdrant_client.models import VectorParams
                await qdrant_client.create_collection(
                    collection_name=complementary_collection,
                    vectors_config=VectorParams(size=1, distance="Cosine")
                )
        except Exception as e:
            logger.error(f"Chyba pu0159i kontrole Qdrantu: {e}")
        
        # Generovu00e1nu00ed zu00e1lou017enu00edch doporuu010denu00ed pu0159u00edmo - jednoduu0161u0161u00ed nu011bu017e volau0159 compute_complementary.py
        logger.info("Generuji zu00e1lou017enu00ed doporuu010denu00ed...")
        
        # Zu00edsku00e1nu00ed vu0161ech produktu016f z Qdrantu
        from qdrant_client.models import Filter
        all_products = []
        offset = None
        limit = 100
        
        while True:
            products, next_offset = await qdrant_client.scroll(
                collection_name=products_collection,
                limit=limit,
                offset=offset,
                with_payload=["product_id", "category"]
            )
            
            for product in products:
                if product.payload and "product_id" in product.payload:
                    all_products.append({
                        "id": product.payload["product_id"],
                        "category": product.payload.get("category", "")
                    })
            
            if next_offset is None or len(products) == 0:
                break
                
            offset = next_offset
            logger.info(f"Nau010dteno {len(all_products)} produktu016f z Qdrantu")
        
        # Seskupenu00ed produktu016f podle kategoriu00ed
        categories = {}
        for product in all_products:
            category = product.get("category", "")
            if category:
                if category not in categories:
                    categories[category] = []
                categories[category].append(product["id"])
        
        logger.info(f"Nalezeno {len(categories)} uniku00e1tnu00edch kategoriu00ed")
        
        # Doporuu010denu00ed produktu016f ze stejnu00e9 kategorie a komplementu
        success_count = 0
        for product in all_products:
            product_id = product["id"]
            category = product.get("category", "")
            
            if not category:
                continue
                
            # Najdi produkty ze stejnu00e9 kategorie (mimo aktuu00e1lnu00ed produkt)
            same_category = [p for p in categories.get(category, []) if p != product_id][:5]  # Max 5 produktu016f
            
            # Najdi komplementu00e1rnu00ed kategorie
            complementary_categories = []
            if category.startswith("iPhone"):
                complementary_categories = ["Pu0159u00edsluu0161enstvu00ed > Tvrzená skla", "Pu0159u00edsluu0161enstvu00ed > Kryty", "Nabu00edjeu010dky"]
            elif category.startswith("iPad"):
                complementary_categories = ["Pu0159u00edsluu0161enstvu00ed > Apple Pencil", "Pu0159u00edsluu0161enstvu00ed > Klu00e1vesnice", "Adaptu00e9ry"]
            elif category.startswith("Mac"):
                complementary_categories = ["Pu0159u00edsluu0161enstvu00ed > Myši", "Pu0159u00edsluu0161enstvu00ed > Klu00e1vesnice", "Externu00ed disky"]
            elif category.startswith("Apple Watch"):
                complementary_categories = ["Pu0159u00edsluu0161enstvu00ed > u0158emu00ednky", "Pu0159u00edsluu0161enstvu00ed > Nabu00edjeu010dky"]
            else:
                complementary_categories = ["Pu0159u00edsluu0161enstvu00ed", "Nabu00edjeu010dky", "Adaptu00e9ry"]
            
            # Najdi produkty z komplementu00e1rnu00edch kategoriu00ed
            complementary_products = []
            for comp_category in complementary_categories:
                complementary_products.extend(categories.get(comp_category, [])[:2])  # Max 2 produkty z kau017edu00e9 kategorie
            
            # Kombinace vu00fdsledku016f se sku00f3re
            recommendations = []
            for i, prod_id in enumerate(same_category):
                score = 0.7 - (i * 0.05)  # Klesaju00edcu00ed sku00f3re 0.7, 0.65, 0.6...
                recommendations.append({"product_id": prod_id, "score": max(0.5, score)})
            
            for i, prod_id in enumerate(complementary_products):
                score = 0.8 - (i * 0.05)  # Klesaju00edcu00ed sku00f3re 0.8, 0.75, 0.7...
                recommendations.append({"product_id": prod_id, "score": max(0.5, score)})
            
            # Ulou017eenu00ed vu00fdsledku016f do Qdrantu
            if recommendations:
                from qdrant_client.models import PointStruct
                import uuid
                
                try:
                    point_id = str(uuid.uuid5(uuid.NAMESPACE_DNS, product_id))
                    await qdrant_client.upsert(
                        collection_name=complementary_collection,
                        points=[
                            PointStruct(
                                id=point_id,
                                payload={
                                    "product_id": product_id,
                                    "recommendations": recommendations,
                                    "computed_at": time.time(),
                                    "model": "onboarding-fallback"
                                },
                                vector=[0.5]  # Dummy vector - nenu00ed pouu017eitu00fd, jen pro splnu011bnu00ed pou017eadavku016f Qdrantu
                            )
                        ]
                    )
                    success_count += 1
                    
                    # Periodic logging
                    if success_count % 50 == 0:
                        logger.info(f"Ulou017eeno {success_count} doporuu010denu00ed...")
                        
                except Exception as e:
                    logger.error(f"Chyba pu0159i uklu00e1du00e1nu00ed doporuu010denu00ed pro {product_id}: {e}")
        
        logger.info(f"Dokonu010deno! Celkem ulou017eeno {success_count} doporuu010denu00ed")
        
        # Kontrola pou010dtu produktu016f v cu00edlovu00e9 kolekci
        try:
            complementary_count = await qdrant_client.count(
                collection_name=complementary_collection,
                count_filter=None
            )
            logger.info(f"Pou010det produktu016f v kolekci {complementary_collection}: {complementary_count.count}")
        except Exception as e:
            logger.error(f"Chyba pu0159i kontrole vu00fdsledku016f: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"Chyba pu0159i generovu00e1nu00ed doporuu010denu00ed: {e}")
        return False

async def main():
    """
    Hlavnu00ed funkce pro onboarding jabkolevne tenanta.
    """
    logger.info("=== ZAu010cu00cdNu00c1M ONBOARDING JABKOLEVNE ===")
    
    # 1. Test konektoru
    connector_ok = await test_connector()
    if not connector_ok:
        logger.error("Test konektoru selhal, onboarding pu0159eruu0161en.")
        return False
    
    # 2. Synchronizace produktu016f
    sync_ok = await sync_products()
    if not sync_ok:
        logger.error("Synchronizace produktu016f selhala, onboarding pu0159eruu0161en.")
        return False
    
    # 3. Generovu00e1nu00ed doporuu010denu00ed
    recommendations_ok = await generate_recommendations()
    if not recommendations_ok:
        logger.error("Generovu00e1nu00ed doporuu010denu00ed selhalo.")
        return False
    
    logger.info("=== ONBOARDING JABKOLEVNE DOKONu010cEN \u00daspu011au0160Nu011a ===")
    return True

if __name__ == "__main__":
    asyncio.run(main())
