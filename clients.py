# clients.py
import logging
import openai
import anthropic
from qdrant_client import AsyncQdrantClient, QdrantClient # Async i sync pro r<PERSON><PERSON><PERSON><PERSON>
from config import settings
from typing import Optional

logger = logging.getLogger(__name__)

# Proměnné pro cachování klientů
_qdrant_async_client: Optional[AsyncQdrantClient] = None
_qdrant_sync_client: Optional[QdrantClient] = None
_openai_client: Optional[openai.AsyncOpenAI] = None
_anthropic_client: Optional[anthropic.AsyncAnthropic] = None

def get_qdrant_async_client() -> AsyncQdrantClient:
    """Vrac<PERSON> (nebo vytváří) asynchronního Qdrant klienta."""
    global _qdrant_async_client
    if _qdrant_async_client is None:
        logger.info(f"Creating AsyncQdrantClient for URL: {settings.qdrant_url}")
        _qdrant_async_client = AsyncQdrantClient(url=str(settings.qdrant_url))
    return _qdrant_async_client

def get_qdrant_sync_client() -> QdrantClient:
    """<PERSON><PERSON><PERSON> (nebo vytváří) synchronního Qdrant klienta."""
    global _qdrant_sync_client
    if _qdrant_sync_client is None:
        logger.info(f"Creating QdrantClient for URL: {settings.qdrant_url}")
        _qdrant_sync_client = QdrantClient(url=str(settings.qdrant_url))
    return _qdrant_sync_client

def get_openai_client() -> openai.AsyncOpenAI:
    """Vrací (nebo vytváří) asynchronního OpenAI klienta."""
    global _openai_client
    if _openai_client is None:
        if not settings.openai_api_key:
            logger.error("OPENAI_API_KEY is not configured.")
            raise ValueError("OPENAI_API_KEY must be set")
        logger.info("Creating AsyncOpenAI client.")
        _openai_client = openai.AsyncOpenAI(api_key=settings.openai_api_key)
    return _openai_client

def get_anthropic_client() -> anthropic.AsyncAnthropic:
    """Vrací (nebo vytváří) asynchronního Anthropic klienta."""
    global _anthropic_client
    if _anthropic_client is None:
        if not settings.anthropic_api_key:
            logger.error("ANTHROPIC_API_KEY is not configured.")
            raise ValueError("ANTHROPIC_API_KEY must be set")
        logger.info("Creating AsyncAnthropic client.")
        # Poznámka: Prompt Caching (beta) se zde explicitně nepovoluje,
        # pokud to API klienta vyžaduje globálně, bylo by to zde.
        # Zdá se, že novější API to řeší přes parametry volání.
        _anthropic_client = anthropic.AsyncAnthropic(api_key=settings.anthropic_api_key)
    return _anthropic_client

async def close_clients():
    """Zavře otevřené asynchronní klienty."""
    global _qdrant_async_client, _openai_client, _anthropic_client
    if _qdrant_async_client:
        await _qdrant_async_client.close()
        _qdrant_async_client = None
        logger.info("AsyncQdrantClient closed.")
    if _openai_client:
        await _openai_client.close()
        _openai_client = None
        logger.info("AsyncOpenAI client closed.")
    if _anthropic_client:
        await _anthropic_client.close()
        _anthropic_client = None
        logger.info("AsyncAnthropic client closed.")

    # Synchronní Qdrant klient nemá async close
    global _qdrant_sync_client
    if _qdrant_sync_client:
        # qdrant_client library sama spravuje spojení, explicitní close není standardně nutné
        _qdrant_sync_client = None
        logger.info("QdrantClient reference released.")