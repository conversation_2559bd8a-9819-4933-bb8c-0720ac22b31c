# Plán Implementace Hybridního Vyhledávání

## 1. <PERSON><PERSON><PERSON> relevanci a pokrytí vyhledávání kombinací sémantick<PERSON>ho (dense vektory) a keyword-based/fulltextového (sparse vektory) vyhledávání s následným rerankováním. Cílem je získat ~40 kandidátů ze sémantického vyhledávání a další relevantní kandidáty z keyword/sparse vyhledávání (cca 20-40), které se zkombinují a pošlou do rerankeru.

## 2. Příprava a Konfigurace Qdrant Kolekce

*   **Analýza a Výběr Sparse Modelu:** 
    *   Prozkoumány modely pro sparse vektory v `fastembed` ([https://qdrant.github.io/fastembed/examples/Supported_Models/](https://qdrant.github.io/fastembed/examples/Supported_Models/)).
    *   **Primární volba pro češtinu: `Qdrant/bm25`**.
        *   Popis: BM25 jako sparse embeddings, jazykov<PERSON> agnostický.
        *   Vyžaduje výpočet IDF z korpusu (FastEmbed by měl zařídit).
        *   Očekává se dobrá funkčnost pro češtinu za předpokladu kvalitní tokenizace.
    *   **Sekundární/Experimentální volba: `Qdrant/bm42-all-minilm-l6-v2-attentions`**.
        *   Popis: Vylepšený BM25 využívající attention váhy z `all-minilm-l6-v2` (primárně anglický model).
        *   Může nabídnout lepší vážení termů, ale existuje riziko suboptimálního výkonu pro češtinu kvůli anglickému základu attention vah.
        *   K otestování, pokud by `Qdrant/bm25` nedostačoval.
    *   Anglické SPLADE modely (např. `prithivida/Splade_PP_en_v1`) se pro češtinu nedoporučují kvůli jazykové specializaci.
    *   Cílem je model, který dobře funguje pro český jazyk a zachytává klíčová slova i jejich varianty.
*   **Úprava Qdrant Kolekce (již existující `products`):
    *   Stávající konfigurace dense vektoru (dle poskytnutých informací):
        *   Jméno: `combined`
        *   Velikost: `1536`
        *   Distance: `Cosine`
    *   Je potřeba **aktualizovat konfiguraci kolekce** pro přidání podpory pro sparse vektory. To se obvykle dělá operací, která modifikuje kolekci (např. `update_collection` v některých klientech, nebo pokud je potřeba změnit základní strukturu, může být nutné `recreate_collection` s novou konfigurací – **POZOR: `recreate_collection` SMAŽE DATA!** Je třeba zvolit správný postup pro aktualizaci existující kolekce, pokud to Qdrant klient umožňuje bez ztráty dat, nebo naplánovat migraci dat).
    *   **Nová Konfigurace Vektorů v Kolekci bude zahrnovat:**
        *   Dense vektor: `"combined": models.VectorParams(size=1536, distance=models.Distance.COSINE)`
        *   Nový sparse vektor: např. `"bm25_sparse": models.SparseVectorParams()`
        ```python
        # Příklad cílové konfigurace (POZOR na způsob aktualizace existující kolekce!)
        # Pokud Qdrant klient podporuje přímou aktualizaci pro přidání sparse_vectors_config:
        # client.update_collection(
        # collection_name="products",
        # sparse_vectors_config={
        # "bm25_sparse": models.SparseVectorParams()
        # }
        # )
        # Jinak, pokud by byla nutná rekreace (s rizikem ztráty dat, pokud není záloha/migrace):
        # client.recreate_collection(
        #     collection_name="products",
        #     vectors_config={
        #         "combined": models.VectorParams(size=1536, distance=models.Distance.COSINE)
        #     },
        #     sparse_vectors_config={
        #         "bm25_sparse": models.SparseVectorParams()
        #     }
        #     # ... zachovat ostatní parametry jako shard_number, replication_factor, hnsw_config atd.
        # )
        ```

## 3. Úprava Procesu Indexace Produktů (`semantic.py: _upsert_products_to_qdrant` nebo ekvivalentní funkce)

*   Při nahrávání/aktualizaci produktu:
    *   Generovat dense vektor (OpenAI) pro vektor `combined` jako dosud.
    *   **Nově:** Generovat sparse vektor pomocí `fastembed` modelu `Qdrant/bm25` pro vektor `bm25_sparse`.
        *   Vstupní text pro sparse model: kombinace relevantních polí (název, popis, kategorie, klíčová slova/atributy).
        *   FastEmbed klient se použije pro enkódování textu na sparse vektor.
    *   Uložit oba vektory (dense `combined` i sparse `bm25_sparse`) do Qdrantu pro daný produkt pod jejich definovanými jmény v rámci jednoho bodu.
    ```python
    points.append(models.PointStruct(
        id=product_id,
        payload=payload,
        vector={
            "combined": dense_vector, # list[float]
            "bm25_sparse": models.SparseVector(indices=sparse_indices, values=sparse_values) # models.SparseVector
        }
    ))
    ```

## 4. Úprava Vyhledávacího Procesu (`semantic.py: search_semantic_products`)

*   **Generování Dotazovacích Vektorů:**
    *   Pro uživatelský dotaz vygenerovat dense vektor (OpenAI) pro `combined`.
    *   Pro uživatelský dotaz vygenerovat sparse vektor (`fastembed` model `Qdrant/bm25`) pro `bm25_sparse`.
*   **Hybridní Vyhledávání v Qdrantu:**
    *   Použít `client.query_points` s parametrem `query` nastaveným na `models.FusionQuery` a `fusion` metodou nastavenou na `models.Fusion.RRF` (Reciprocal Rank Fusion).
    *   V parametru `prefetch` specifikovat dva poddotazy:
        *   Jeden pro dense vektor `combined`.
        *   Jeden pro sparse vektor `bm25_sparse`.
        *   Každý prefetch bude obsahovat `query` (příslušný vektor), `using` (jméno vektoru) a `limit`.
    ```python
    # Příklad hybridního dotazu
    dense_query_vector = # ... OpenAI embedding dotazu
    sparse_query_vector_indices, sparse_query_vector_values = # ... FastEmbed Qdrant/bm25 sparse embedding dotazu

    search_result = client.query_points(
        collection_name="products",
        prefetch=[
            models.Prefetch(
                query=dense_query_vector,
                using="combined",
                limit=40
            ),
            models.Prefetch(
                query=models.SparseVector(indices=sparse_query_vector_indices, values=sparse_query_vector_values),
                using="bm25_sparse",
                limit=30 
            ),
        ],
        query=models.FusionQuery(fusion=models.Fusion.RRF),
        limit=60, 
        with_payload=True,
        with_vectors=False
    ).points
    ```
*   **Deduplikace Kandidátů:** Po získání výsledků z Qdrantu zajistit odstranění duplicitních produktů. `product_id` je klíč.
*   **Aplikace Stávajících Filtrů:** Aplikovat stávající cenové filtry a volitelně fuzzy keyword filtr na tento zkombinovaný a fúzovaný seznam kandidátů.
*   **Rerankování (Cohere):** Zkombinovaný, fúzovaný a předfiltrovaný seznam kandidátů poslat do Cohere rerankeru pro finální seřazení a výběr top N výsledků.

## 5. Implementační Detaily a Knihovny

*   Přidat knihovnu `fastembed` do projektových závislostí (`requirements.txt`):
    *   `pip install fastembed`
    *   `pip install "qdrant-client[fastembed]>=1.7.0"` (nebo vyšší, ověřit kompatibilitu a nejnovější verzi).
*   Ověřit/aktualizovat knihovnu `qdrant-client` na verzi podporující sparse vektory a `FusionQuery` (minimálně 1.7.0, doporučeno nejnovější stabilní).
*   Zajistit potřebné API klíče pro OpenAI (stávající) a případné konfigurace pro `fastembed` modely (většinou se stahují automaticky).

## 6. Testování a Ladění

*   Otestovat indexaci produktů s oběma typy vektorů.
*   Otestovat hybridní vyhledávání s různými typy dotazů (sémantické, keyword-heavy, kombinované, dotazy v češtině).
*   Monitorovat logy pro správnou funkci a detekci chyb (zejména při generování a ukládání vektorů).
*   Ládit parametry hybridního vyhledávání:
    *   Limity pro dense a sparse `prefetch`.
    *   Celkový `limit` po fúzi.
*   Porovnat výsledky (relevanci, pokrytí) s a bez hybridního přístupu na testovací sadě dotazů.

## 7. Potenciální Rozšíření (Budoucnost)

*   Prozkoumat `Distribution-Based Score Fusion (DBSF)` jako alternativu k RRF, pokud by RRF nedávala optimální výsledky.
*   Možnost dynamického přizpůsobení vah/limitů pro dense vs. sparse vyhledávání na základě analýzy dotazu (pokročilé).
*   Využití pokročilejších sparse modelů z `fastembed`, pokud budou dostupné a relevantní.

---
*Zdroje pro studium:*
*   [Qdrant & FastEmbed Hybrid Search Tutorial](https://qdrant.tech/documentation/beginner-tutorials/hybrid-search-fastembed/)
*   [Qdrant Sparse Vectors Article](https://qdrant.tech/articles/sparse-vectors/)
*   [Qdrant Hybrid Queries Documentation](https://qdrant.tech/documentation/concepts/hybrid-queries/)
*   [LlamaIndex Qdrant Hybrid Search Example](https://docs.llamaindex.ai/en/stable/examples/vector_stores/qdrant_hybrid/)
*   [FastEmbed Supported Models](https://qdrant.github.io/fastembed/examples/Supported_Models/)
*   [Some Like It Small: Czech Semantic Embedding Models for Industry Applications (arXiv)](https://arxiv.org/abs/2311.13921) - pro kontext výzkumu českých embeddingů. 