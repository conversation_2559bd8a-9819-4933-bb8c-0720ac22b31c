#!/usr/bin/env python3
# run_nightly_process.py - Hlavní orchestrační skript pro noční zpracování dat
import asyncio
import subprocess
import os
import sys
import logging
import argparse
import time
import glob
import yaml
from typing import List, Dict, Optional, Set
from datetime import datetime
from pathlib import Path

# Nastavení cest a základních proměnných
PROJECT_ROOT = os.path.abspath(os.path.dirname(__file__))
LOG_DIR = os.path.join(PROJECT_ROOT, "logs")
os.makedirs(LOG_DIR, exist_ok=True)

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(LOG_DIR, f"nightly_process_{datetime.now().strftime('%Y%m%d')}.log"), encoding='utf-8')
    ]
)
logger = logging.getLogger("nightly_process")

class NightlyProcessor:
    """
    Orchestrační třída pro kompletní noční zpracování:
    1. Synchronizace produktů (sync_products.py)
    2. Výpočet komplementárních produktů (batch_scripts/compute_complementary.py)
    3. Čištění neplatných referencí v komplementárních produktech (clean_complementary_references.py)
    """
    
    def __init__(
        self, 
        tenants: List[str] = None, 
        batch_size: int = 50, 
        workers: int = 70,
        force_sync: bool = False,
        skip_sync: bool = False,
        skip_compute: bool = False,
        skip_clean: bool = False
    ):
        """
        Inicializace NightlyProcessor
        
        Args:
            tenants: Seznam ID tenantů k zpracování (None = všichni tenanty z /config/tenants/)
            batch_size: Velikost dávky pro zpracování produktů
            workers: Počet paralelních workerů
            force_sync: Vynutit aktualizaci všech produktů při synchronizaci
            skip_sync: Přeskočit krok synchronizace produktů
            skip_compute: Přeskočit krok výpočtu komplementárních produktů
            skip_clean: Přeskočit krok čištění referencí
        """
        self.batch_size = batch_size
        self.workers = workers
        self.force_sync = force_sync
        self.skip_sync = skip_sync
        self.skip_compute = skip_compute
        self.skip_clean = skip_clean
        
        # Načtení tenantů - buď z parametru nebo z config adresáře
        if tenants:
            self.tenants = tenants
        else:
            self.tenants = self._discover_tenants()
            
        logger.info(f"Inicializován noční proces pro tenanty: {', '.join(self.tenants)}")
        
        # Výsledky procesů pro každého tenanta
        self.results = {tenant: {"sync": None, "compute": None, "clean": None} for tenant in self.tenants}

    def _discover_tenants(self) -> List[str]:
        """Detekuje dostupné tenanty z adresáře /config/tenants/"""
        tenant_files = glob.glob(os.path.join(PROJECT_ROOT, "config", "tenants", "*.yaml"))
        
        # Pokud nejsou nalezeny konfigurační soubory, zkusíme alternativní cestu
        if not tenant_files:
            tenant_files = glob.glob(os.path.join(PROJECT_ROOT, "core", "config", "tenants", "*.yaml"))
            
        if not tenant_files:
            logger.warning("Nebyly nalezeny žádné konfigurační soubory tenantů!")
            return []
            
        # Extrakce jména tenanta z názvu souboru
        tenants = []
        for tenant_file in tenant_files:
            tenant_name = os.path.splitext(os.path.basename(tenant_file))[0]
            tenants.append(tenant_name)
            
        logger.info(f"Nalezeno {len(tenants)} tenantů: {', '.join(tenants)}")
        return tenants

    async def run_all(self) -> Dict:
        """
        Spustí kompletní noční proces pro všechny tenanty.
        
        Returns:
            Dictionary s výsledky pro každého tenanta a krok
        """
        start_time = time.time()
        logger.info("=== ZAHÁJENÍ NOČNÍHO PROCESU ===")
        logger.info(f"Tenanty ke zpracování: {', '.join(self.tenants)}")
        
        # Pro každého tenanta spustíme postupně všechny kroky
        for tenant in self.tenants:
            logger.info(f"=== ZAHÁJENÍ ZPRACOVÁNÍ TENANTA: {tenant} ===")
            
            try:
                # 1. Synchronizace produktů
                if not self.skip_sync:
                    self.results[tenant]["sync"] = await self._run_sync_products(tenant)
                else:
                    logger.info(f"Synchronizace produktů pro tenanta {tenant} přeskočena (--skip-sync)")
                
                # 2. Výpočet komplementárních produktů
                if not self.skip_compute:
                    self.results[tenant]["compute"] = await self._run_compute_complementary(tenant)
                else:
                    logger.info(f"Výpočet komplementárních produktů pro tenanta {tenant} přeskočen (--skip-compute)")
                
                # 3. Čištění neplatných referencí
                if not self.skip_clean:
                    self.results[tenant]["clean"] = await self._run_clean_references(tenant)
                else:
                    logger.info(f"Čištění referencí pro tenanta {tenant} přeskočeno (--skip-clean)")
                    
            except Exception as e:
                logger.error(f"Chyba při zpracování tenanta {tenant}: {e}")
                self.results[tenant]["error"] = str(e)
                
            logger.info(f"=== DOKONČENÍ ZPRACOVÁNÍ TENANTA: {tenant} ===")
        
        # Závěrečné shrnutí
        total_time = time.time() - start_time
        logger.info(f"=== DOKONČENÍ NOČNÍHO PROCESU (celkový čas: {total_time:.2f}s) ===")
        self._print_summary()
        
        return self.results
    
    async def _run_sync_products(self, tenant: str) -> bool:
        """
        Spustí synchronizaci produktů pro daného tenanta.
        
        Args:
            tenant: ID tenanta
            
        Returns:
            True pokud proces proběhl úspěšně, jinak False
        """
        logger.info(f"Spouštím synchronizaci produktů pro tenanta {tenant}...")
        start_time = time.time()
        
        # Příprava parametrů
        cmd = [
            "python", "sync_products.py",
            "--tenant", tenant,
            "--batch-size", str(self.batch_size),
            "--workers", str(self.workers)
        ]
        
        if self.force_sync:
            cmd.append("--force")
        
        # Spuštění procesu
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                elapsed_time = time.time() - start_time
                logger.info(f"Synchronizace produktů pro tenanta {tenant} dokončena úspěšně (čas: {elapsed_time:.2f}s)")
                return True
            else:
                logger.error(f"Synchronizace produktů pro tenanta {tenant} selhala s kódem {process.returncode}")
                logger.error(f"Stderr: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při spouštění synchronizace produktů pro tenanta {tenant}: {e}")
            return False
    
    async def _run_compute_complementary(self, tenant: str) -> bool:
        """
        Spustí výpočet komplementárních produktů pro daného tenanta.
        
        Args:
            tenant: ID tenanta
            
        Returns:
            True pokud proces proběhl úspěšně, jinak False
        """
        logger.info(f"Spouštím výpočet komplementárních produktů pro tenanta {tenant}...")
        start_time = time.time()
        
        # Příprava parametrů
        cmd = [
            "python", "batch_scripts/compute_complementary.py",
            tenant,  # Poziční argument tenant_id
            "-c", str(min(self.workers, 20)),  # Omeziné na 20 aby nedošlo k přetížení API
        ]
        
        # Spuštění procesu
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                elapsed_time = time.time() - start_time
                logger.info(f"Výpočet komplementárních produktů pro tenanta {tenant} dokončen úspěšně (čas: {elapsed_time:.2f}s)")
                return True
            else:
                logger.error(f"Výpočet komplementárních produktů pro tenanta {tenant} selhal s kódem {process.returncode}")
                logger.error(f"Stderr: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při spouštění výpočtu komplementárních produktů pro tenanta {tenant}: {e}")
            return False
    
    async def _run_clean_references(self, tenant: str) -> bool:
        """
        Spustí čištění neplatných referencí pro daného tenanta.
        
        Args:
            tenant: ID tenanta
            
        Returns:
            True pokud proces proběhl úspěšně, jinak False
        """
        logger.info(f"Spouštím čištění neplatných referencí pro tenanta {tenant}...")
        start_time = time.time()
        
        # Příprava parametrů
        cmd = [
            "python", "clean_complementary_references.py",
            "--tenant", tenant
        ]
        
        # Spuštění procesu
        try:
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            stdout, stderr = process.communicate()
            
            if process.returncode == 0:
                elapsed_time = time.time() - start_time
                logger.info(f"Čištění referencí pro tenanta {tenant} dokončeno úspěšně (čas: {elapsed_time:.2f}s)")
                return True
            else:
                logger.error(f"Čištění referencí pro tenanta {tenant} selhalo s kódem {process.returncode}")
                logger.error(f"Stderr: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Chyba při spouštění čištění referencí pro tenanta {tenant}: {e}")
            return False
    
    def _print_summary(self):
        """Vypíše souhrnné informace o výsledcích nočního procesu."""
        logger.info("\n=== SOUHRNNÉ VÝSLEDKY NOČNÍHO PROCESU ===")
        logger.info(f"Celkem zpracováno tenantů: {len(self.tenants)}")
        
        # Výsledky synchronizace
        if not self.skip_sync:
            sync_success = sum(1 for tenant in self.tenants if self.results[tenant]["sync"] == True)
            logger.info(f"Synchronizace produktů: {sync_success}/{len(self.tenants)} úspěšně")
        else:
            logger.info("Synchronizace produktů: Přeskočeno")
            
        # Výsledky výpočtu komplementárních produktů
        if not self.skip_compute:
            compute_success = sum(1 for tenant in self.tenants if self.results[tenant]["compute"] == True)
            logger.info(f"Výpočet komplementárních produktů: {compute_success}/{len(self.tenants)} úspěšně")
        else:
            logger.info("Výpočet komplementárních produktů: Přeskočeno")
            
        # Výsledky čištění referencí
        if not self.skip_clean:
            clean_success = sum(1 for tenant in self.tenants if self.results[tenant]["clean"] == True)
            logger.info(f"Čištění referencí: {clean_success}/{len(self.tenants)} úspěšně")
        else:
            logger.info("Čištění referencí: Přeskočeno")
        
        # Shrnutí pro jednotlivé tenanty
        logger.info("\nVýsledky podle tenantů:")
        for tenant in self.tenants:
            sync_result = "Přeskočeno" if self.skip_sync else ("Úspěch" if self.results[tenant]["sync"] else "Chyba")
            compute_result = "Přeskočeno" if self.skip_compute else ("Úspěch" if self.results[tenant]["compute"] else "Chyba")
            clean_result = "Přeskočeno" if self.skip_clean else ("Úspěch" if self.results[tenant]["clean"] else "Chyba")
            
            logger.info(f"  - {tenant}: Sync: {sync_result}, Compute: {compute_result}, Clean: {clean_result}")

async def main():
    """Hlavní funkce pro orchestraci nočního procesu"""
    
    # Nastavení parsování argumentů
    parser = argparse.ArgumentParser(description="Orchestrace kompletního nočního procesu synchronizace a aktualizace produktů")
    parser.add_argument("--tenants", type=str, 
                        help="Seznam ID tenantů oddělených čárkou. Pokud není uvedeno, použijí se všichni dostupní tenanty.")
    parser.add_argument("--batch-size", type=int, default=50, 
                        help="Velikost dávky pro zpracování produktů (výchozí: 50)")
    parser.add_argument("--workers", type=int, default=10, 
                        help="Počet paralelních workerů (výchozí: 10)")
    parser.add_argument("--force-sync", action="store_true", 
                        help="Vynutit aktualizaci všech produktů při synchronizaci")
    parser.add_argument("--skip-sync", action="store_true", 
                        help="Přeskočit krok synchronizace produktů")
    parser.add_argument("--skip-compute", action="store_true", 
                        help="Přeskočit krok výpočtu komplementárních produktů")
    parser.add_argument("--skip-clean", action="store_true", 
                        help="Přeskočit krok čištění referencí")
    args = parser.parse_args()
    
    # Získání seznamu tenantů (pokud byl zadán)
    tenants = None
    if args.tenants:
        tenants = [tenant.strip() for tenant in args.tenants.split(",")]
    
    # Vytvoření a spuštění NightlyProcessor
    processor = NightlyProcessor(
        tenants=tenants,
        batch_size=args.batch_size,
        workers=args.workers,
        force_sync=args.force_sync,
        skip_sync=args.skip_sync,
        skip_compute=args.skip_compute,
        skip_clean=args.skip_clean
    )
    
    await processor.run_all()

if __name__ == "__main__":
    asyncio.run(main()) 