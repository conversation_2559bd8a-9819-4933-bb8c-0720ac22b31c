# Avenberg Produkt Monitor - Stručný průvodce

## Co je Avenberg Produkt Monitor?

Tento nástroj sleduje změny v produktech Avenberg a ukazuje je v přehledném webovém rozhraní. Pomáhá vám zjistit:
- Nově přidané produkty
- Odstraněné produkty
- Změny cen
- Změny dostupnosti
- Změny v popisech produktů

## Jak aplikaci používat?

### 1. Spuštění aplikace

V příkazovém řádku (terminálu) spusťte:
```
python product_monitor.py --serve --port=5002
```

Poté otevřete webový prohlížeč a jděte na adresu: [http://localhost:5002](http://localhost:5002)

### 2. Použití webového rozhraní

#### Záložky v horn<PERSON> č<PERSON>
- **Přehled**: Základní statistiky a nedávné změny
- **Statistiky**: Detailní grafy a informace o změnách
- **Historie změn**: Kompletní seznam všech změn

#### Ovládací tlačítka
- **Spustit kontrolu**: Ručně zkontroluje změny v produktech
- **Exportovat do CSV**: Uloží všechny změny do CSV souboru pro další zpracování

### 3. Automatická aktualizace

Dashboard se sám aktualizuje každých 5 minut, ale můžete kdykoli kliknout na "Spustit kontrolu" pro okamžitou kontrolu.

### 4. Sdílení s ostatními (volitelné)

Pokud chcete zpřístupnit dashboard pro ostatní uživatele, spusťte v novém terminálovém okně:
```
ngrok http 5002
```

Tento příkaz vám zobrazí adresu URL, kterou můžete sdílet s kýmkoli na internetu.

## Jak číst informace na dashboardu?

### Přehled
- **Počet změn**: Celkový počet změn od začátku monitorování
- **Poslední kontrola**: Kdy byly produkty naposledy zkontrolovány
- **Nové produkty**: Počet nově přidaných produktů
- **Smazané produkty**: Počet odstraněných produktů
- **Změny cen**: Počet produktů se změnou ceny
- **Změny dostupnosti**: Počet produktů se změnou dostupnosti

### Historie změn
- Každá změna zobrazuje datum, typ změny, název produktu a detaily změny
- Změny jsou seřazené od nejnovějších po nejstarší

## Rozluštění problémů

Pokud aplikace nefunguje správně:
1. Ujistěte se, že server běží (příkaz v kroku 1)
2. Zkontrolujte, že používáte správnou adresu v prohlížeči
3. Zkuste obnovit stránku (klávesa F5)
4. Restartujte server (ukončete ho pomocí Ctrl+C a spusťte znovu)

## Kde najdu exportované CSV soubory?

Po kliknutí na tlačítko "Exportovat do CSV" se vám soubor automaticky stáhne do složky pro stažené soubory ve vašem prohlížeči.
