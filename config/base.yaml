# Základní konfigurace pro všechny tenanty
recommender:
  similar_products_count: 25  # Počet podobných produktů k vyhledání
  max_recommendations: 5      # Maximální počet doporučení
  temperature: 0.2           # Temperature pro AI model
  max_tokens: 1500          # Max tokens pro AI model
  model: "claude-3-5-haiku-20241022"
  fallback_rate: 0.999      # Rate pro použití cache
  count: 50                 # Počet doporučení pro každý produkt
  candidates: 25            # Počet kandidátů pro výběr doporučení

embeddings:
  batch_size: 25            # Sníženo pro lepší zvládání rate limitů
  model: "text-embedding-3-large"  # Model pro embeddingy
  dimensions: 1536          # Dimenze embeddingů
  category_boost: 0.1       # Boost pro produkty ve stejné kategorii
  retry_count: 5           # Počet pokusů při rate limitu
  retry_initial_delay: 2   # Počáteční prodleva v sekund<PERSON>ch
  retry_max_delay: 60      # Maximální prodleva v sekund<PERSON>ch
  retry_multiplier: 1.5    # Násobitel pro exponenciální backoff

cache:
  enabled: true            # Zda používat cache
  base_dir: "cache"        # Základní adresář pro cache
  max_age_days: 30        # Maximální stáří cache v dnech

processing:
  max_workers: 15           # Zvýšeno pro lepší paralelizaci
  batch_size: 25           # Zvýšeno pro efektivnější zpracování
  retry_count: 5           # Ponecháno
  retry_delay: 10          # Ponecháno

# Konfigurace API
api:
  enabled: true           # Zda je API povolené
  keys: []               # Seznam povolených API klíčů (prázdný = žádný přístup)
  rate_limit: 3000        # Zvýšený rate limit na 3000 požadavků za minutu

# Konfigurace doporučení
recommendations:
  visual_top_k: 5         # Počet vizuálně podobných produktů
  cross_sell_top_k: 5     # Počet cross-sell doporučení
  force_real_time: false  # Zda vždy generovat nová doporučení místo použití cache

feed:
  retry_count: 3          # Počet pokusů o stažení feedu
  retry_delay: 5          # Prodleva mezi pokusy v sekundách
  timeout: 30             # Timeout pro stažení feedu v sekundách

redis:
  host: "localhost"
  port: 6379
  db: 0
  expiration: 3600  # Cache expiruje po 1 hodině
