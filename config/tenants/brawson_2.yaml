# Konfigurace pro BRAWSON_2
feed:
  type: "brawson_2"           # Změna na brawson_2 konektor
  format: "xml"
  feed_url: "file://feed/brawson_2.xml"  # Používáme novější verzi feedu

force_recalculation: false  # Přesunuto do hlavní sekce

recommender:
  similar_products_count: 20  # Výchozí počet podobných produktů
  min_similarity: 0.3  # Snížený práh podobnosti pro více doporučení

# API konfigurace
api:
  enabled: true
  key_env_prefix: "BRAWSON_2"  # Prefix pro API klíče (BRAWSON_2_API_KEY_1, BRAWSON_2_API_KEY_2)

# Cache konfigurace
cache:
  postgres:
    max_age_days: 14  # Maximální stáří dat v PostgreSQL
    cleanup_interval_hours: 24  # Jak často se mají mazat stará data
  
  redis:
    host: "redis"  # Hostname v docker-compose síti
    port: 6379
    db: 0
    ttl: 3600  # TTL pro Redis cache v sekund<PERSON>ch (1 hodina)
    listener:
      enabled: true
      batch_size: 100  # Kolik expirovaných klíčů zpracovat najednou
      retry_delay: 5  # Prodleva mezi pokusy o obnovení spojení (sekundy)
    keys:
      recommendations: "{tenant}:{product_id}:recommendations"
      embeddings: "{tenant}:{product_id}:embeddings"
      metadata: "{tenant}:{product_id}:metadata"

# Konfigurace doporučení
recommendations:
  visual_top_k: 5  # Počet vizuálně podobných produktů
  cross_sell_top_k: 4  # Počet cross-sell doporučení
  validate_recommendations: false
  count: 5  # Počet doporučení pro každý produkt
  candidates: 25  # Počet kandidátů pro výběr doporučení
  force_real_time: true  # Změněno na true pro generování doporučení ihned po vytvoření embeddingů

processing:
  batch_size: 10           # Sníženo pro lepší zvládání databázových připojení
  max_workers: 3           # Sníženo pro lepší zvládání databázových připojení
  retry_count: 3
  retry_multiplier: 2
  retry_initial_delay: 1

# Další specifické konfigurace
custom:
  brand: "BRAWSON_2"
  language: "cs"