import pytest
from unittest.mock import AsyncMock, MagicMock, patch, call
import tiktoken

# Importuje<PERSON> testovanou třídu a <PERSON><PERSON>
from embedding_strategies import MultiEmbeddingStrategy, MAX_TOKENS
from openai import AsyncOpenAI
from openai.types.create_embedding_response import CreateEmbeddingResponse, Usage
from openai.types.embedding import Embedding

# --- Pytest Fixtures ---

@pytest.fixture
def mock_openai_client(mocker):
    """Mock pro AsyncOpenAI klienta."""
    mock_client = AsyncMock(spec=AsyncOpenAI)
    
    # Mock pro response.data[i].embedding
    def create_mock_embedding(index):
        # Vrací unikátní embedding pro každý vstupní text pro snazší testování
        return [float(index + 1 + i/1000) for i in range(1536)]

    # Mock pro embeddings.create
    async def mock_embeddings_create(model, input):
        mock_response = MagicMock(spec=CreateEmbeddingResponse)
        mock_response.data = []
        for i, text in enumerate(input):
             # Simulace chyby, pokud by text i po zkrácení byl p<PERSON><PERSON> (nemělo by nastat)
             if len(text) > 9000: # Jen pro jistotu
                  raise Exception("Simulated API error: Text too long even after truncation.")
             
             embedding_data = MagicMock(spec=Embedding)
             embedding_data.embedding = create_mock_embedding(i)
             embedding_data.index = i
             embedding_data.object = "embedding"
             mock_response.data.append(embedding_data)
             
        mock_response.model = model
        mock_response.object = "list"
        mock_response.usage = Usage(prompt_tokens=50, total_tokens=50) # Dummy usage data
        return mock_response

    # Oprava: Vytvoříme mock pro atribut 'embeddings'
    mock_embeddings_object = MagicMock()
    mock_embeddings_object.create = AsyncMock(side_effect=mock_embeddings_create)
    mock_client.embeddings = mock_embeddings_object
    
    return mock_client

@pytest.fixture
def embedding_strategy(mock_openai_client):
    """Fixture pro vytvoření instance MultiEmbeddingStrategy s mockovaným klientem."""
    # Zajistíme, že tiktoken.encoding_for_model vrátí funkční mock tokenizer
    with patch('tiktoken.encoding_for_model', return_value=tiktoken.get_encoding("cl100k_base")) as mock_encoding:
        strategy = MultiEmbeddingStrategy(openai_client=mock_openai_client)
        # Ověříme, že se tokenizer skutečně načetl
        assert strategy.tokenizer is not None
        mock_encoding.assert_called_once_with(strategy.embedding_model)
    return strategy

@pytest.fixture
def product_data_short():
    """Krátká testovací data produktu."""
    return {
        "id": "p1",
        "name": "Krátký Název",
        "description": "Krátký popis.",
        "category": "Kat A",
        "brand": "Značka X"
    }

@pytest.fixture
def product_data_long():
    """Testovací data produktu s velmi dlouhým popisem."""
    # Vygenerujeme text delší než MAX_TOKENS
    # Použijeme opakování, aby byl text deterministický
    long_description = "Toto je velmi dlouhý popis produktu, který se opakuje mnohokrát. " * (MAX_TOKENS // 10) 
    # Odhadneme počet tokenů a přidáme ještě trochu navíc
    tokenizer = tiktoken.get_encoding("cl100k_base")
    tokens = tokenizer.encode(long_description)
    print(f"Generovaný dlouhý popis má cca {len(tokens)} tokenů.") # Debug print
    
    return {
        "id": "p-long",
        "name": "Dlouhý Produkt",
        "description": long_description,
        "category": "Kat B",
        "brand": "Značka Y"
    }

# --- Testy ---

def test_init_loads_tokenizer(mock_openai_client):
     """Testuje, zda se při inicializaci načte tokenizer."""
     # Fixture embedding_strategy již obsahuje patch a assert pro načtení tokenizeru
     with patch('tiktoken.encoding_for_model', return_value=tiktoken.get_encoding("cl100k_base")) as mock_encoding:
          strategy = MultiEmbeddingStrategy(openai_client=mock_openai_client)
          assert strategy.tokenizer is not None
          mock_encoding.assert_called_once_with(strategy.embedding_model)

def test_init_tokenizer_load_error(mock_openai_client, caplog):
     """Testuje, co se stane, když se tokenizer nepodaří načíst."""
     with patch('tiktoken.encoding_for_model', side_effect=Exception("Load failed")) as mock_encoding:
          strategy = MultiEmbeddingStrategy(openai_client=mock_openai_client)
          assert strategy.tokenizer is None
          mock_encoding.assert_called_once_with(strategy.embedding_model)
          assert f"Nepodařilo se načíst tiktoken enkodér pro model {strategy.embedding_model}" in caplog.text
          assert "Load failed" in caplog.text

def test_create_combined_text(embedding_strategy):
    """Testuje interní metodu _create_combined_text."""
    name = "Název"
    desc = "Popis"
    cat = "Kategorie"
    brand = "Značka"
    
    # Očekávané počty opakování podle vah (name: 3, desc: 4, cat: 2, brand: 1)
    expected_text = f"{name} {name} {name} {desc} {desc} {desc} {desc} {cat} {cat} {brand}".replace("  "," ").strip()
    
    combined = embedding_strategy._create_combined_text(name, desc, cat, brand)
    # Jednoduché porovnání nemusí fungovat kvůli pořadí, použijeme split a porovnání slovníků počtů
    
    def word_counts(text):
        counts = {}
        for word in text.split():
            counts[word] = counts.get(word, 0) + 1
        return counts

    assert word_counts(combined) == word_counts(expected_text)

@pytest.mark.asyncio
async def test_create_embeddings_success(embedding_strategy, mock_openai_client, product_data_short):
    """Testuje úspěšné vytvoření embeddingů pro krátký text."""
    embeddings = await embedding_strategy.create_embeddings(product_data_short)

    # Ověření struktury výsledku
    expected_keys = {"name_brand", "pure_description", "category_hierarchy", "brand_category", "combined"}
    assert set(embeddings.keys()) == expected_keys
    for key in expected_keys:
        assert isinstance(embeddings[key], list)
        assert len(embeddings[key]) == embedding_strategy.embedding_dimensions

    # Ověření volání OpenAI API
    mock_openai_client.embeddings.create.assert_awaited_once()
    call_args = mock_openai_client.embeddings.create.await_args[1]
    assert call_args['model'] == embedding_strategy.embedding_model
    assert isinstance(call_args['input'], list)
    assert len(call_args['input']) == len(expected_keys) 
    # Ověření, že texty odpovídají tomu, co strategie generuje
    assert call_args['input'][0] == "krátký název značka x" # name_brand
    assert call_args['input'][1] == "krátký popis."       # pure_description
    # ... můžeme ověřit i další vstupy ...

@pytest.mark.asyncio
async def test_create_embeddings_truncation(embedding_strategy, mock_openai_client, product_data_long, caplog):
     """Testuje zkrácení textu, pokud je vstup příliš dlouhý."""
     # Připravíme původní dlouhé texty pro porovnání
     original_combined = embedding_strategy._create_combined_text(
         product_data_long["name"], product_data_long["description"], product_data_long["category"], product_data_long["brand"]
     )
     original_description = product_data_long["description"]
     
     # Zavoláme metodu
     embeddings = await embedding_strategy.create_embeddings(product_data_long)

     # Ověření, že volání API proběhlo
     mock_openai_client.embeddings.create.assert_awaited_once()
     call_args = mock_openai_client.embeddings.create.await_args[1]
     inputs_sent_to_api = call_args['input']

     # Ověření, že texty pro 'pure_description' a 'combined' byly zkráceny
     # Správné pořadí klíčů, jak jsou definovány v create_embeddings
     text_keys = ["name_brand", "pure_description", "category_hierarchy", "brand_category", "combined"]
     try:
         description_api_index = text_keys.index("pure_description")
         combined_api_index = text_keys.index("combined")
     except ValueError:
         pytest.fail("Klíče 'pure_description' nebo 'combined' nebyly nalezeny v očekávaném seznamu text_keys")

     assert len(embedding_strategy.tokenizer.encode(inputs_sent_to_api[description_api_index])) <= MAX_TOKENS
     assert len(inputs_sent_to_api[description_api_index]) < len(original_description)
     assert len(embedding_strategy.tokenizer.encode(inputs_sent_to_api[combined_api_index])) <= MAX_TOKENS
     assert len(inputs_sent_to_api[combined_api_index]) < len(original_combined)

     # Ověření logování varování o zkrácení
     assert f"Text pro embedding 'pure_description' produktu '{product_data_long['id']}' byl zkrácen" in caplog.text
     assert f"Text pro embedding 'combined' produktu '{product_data_long['id']}' byl zkrácen" in caplog.text
     
     # Ověření, že výsledek má stále správnou strukturu
     expected_keys = {"name_brand", "pure_description", "category_hierarchy", "brand_category", "combined"}
     assert set(embeddings.keys()) == expected_keys
     assert len(embeddings["pure_description"]) == embedding_strategy.embedding_dimensions

@pytest.mark.asyncio
async def test_create_embeddings_api_error(embedding_strategy, mock_openai_client, product_data_short, caplog):
    """Testuje zpracování chyby z OpenAI API."""
    # Nastavení mocku, aby vyhodil chybu
    mock_openai_client.embeddings.create.side_effect = Exception("API Connection Error")

    embeddings = await embedding_strategy.create_embeddings(product_data_short)

    # Ověření logování chyby
    assert f"Chyba při generování embeddingů pro produkt '{product_data_short['id']}': API Connection Error" in caplog.text

    # Ověření vrácených fallback embeddingů
    expected_keys = {"name_brand", "pure_description", "category_hierarchy", "brand_category", "combined"}
    assert set(embeddings.keys()) == expected_keys
    # Zkontrolujeme jen jeden, zda má správný fallback a délku
    assert embeddings["combined"] == [0.1] * embedding_strategy.embedding_dimensions

@pytest.mark.asyncio
async def test_create_embeddings_no_tokenizer(mock_openai_client, product_data_long, caplog):
    """Testuje chování, když není dostupný tokenizer (zkracování se neprovede)."""
    with patch('tiktoken.encoding_for_model', side_effect=Exception("Load failed")):
        strategy = MultiEmbeddingStrategy(openai_client=mock_openai_client)
        assert strategy.tokenizer is None # Ověření, že tokenizer opravdu není

        # Volání metody s dlouhým textem
        await strategy.create_embeddings(product_data_long)

        # Ověření, že API bylo voláno (i když by mohlo selhat kvůli délce, náš mock neselže)
        mock_openai_client.embeddings.create.assert_awaited_once()
        call_args = mock_openai_client.embeddings.create.await_args[1]
        inputs_sent_to_api = call_args['input']

        # Ověření, že texty NEBYLY zkráceny
        # Správné pořadí klíčů
        text_keys = ["name_brand", "pure_description", "category_hierarchy", "brand_category", "combined"]
        try:
            description_api_index = text_keys.index("pure_description")
        except ValueError:
            pytest.fail("Klíč 'pure_description' nebyl nalezen v očekávaném seznamu text_keys")
        
        assert len(inputs_sent_to_api[description_api_index]) == len(product_data_long["description"])

        # Ověření logování varování o nedostupném tokenizeru
        assert "Tiktoken tokenizer není dostupný, délka textu pro embedding nebyla zkontrolována." in caplog.text
        # Varování o zkrácení by se nemělo objevit
        assert "byl zkrácen z" not in caplog.text 