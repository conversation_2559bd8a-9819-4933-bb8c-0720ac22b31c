import pytest
from unittest.mock import AsyncMock, MagicMock, patch, ANY
import asyncio
import time
import sys
import os

# Přid<PERSON>í parent directory do Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# <PERSON><PERSON><PERSON><PERSON>o pro mockování odpo<PERSON><PERSON><PERSON><PERSON>
from qdrant_client.http.models import PointStruct, Record, ScrollResult, CollectionsResponse, CollectionDescription, PointIdsList

# Importujeme testovaný modul a také jeho z<PERSON>
import sync_products
from sync_products import ProductSynchronizer
from data_models import Product
from core.config import TenantSettings, FeedSettings

# --- Pytest Fixtures ---

@pytest.fixture
def mock_qdrant_client(mocker):
    """Mock pro Qdrant async klienta."""
    mock = AsyncMock()
    mock.get_collections.return_value = MagicMock(collections=[])
    mock.scroll.return_value = ([], None)
    mock.count.return_value = MagicMock(count=0)
    mocker.patch('sync_products.get_qdrant_async_client', return_value=mock)
    return mock

@pytest.fixture
def mock_openai_client(mocker):
    """Mock pro OpenAI klienta."""
    mock = MagicMock()
    mocker.patch('sync_products.get_openai_client', return_value=mock)
    return mock

@pytest.fixture
def mock_embedding_strategy(mocker):
    """Mock pro MultiEmbeddingStrategy."""
    mock = AsyncMock()
    mock.create_embeddings.return_value = {"combined": [0.1] * 1536}
    mocker.patch('sync_products.MultiEmbeddingStrategy', return_value=mock)
    return mock

@pytest.fixture
def mock_get_tenant_config(mocker):
    """Mock pro načítání konfigurace tenanta."""
    mock_settings = TenantSettings(
        tenant_id="test_tenant",
        feed=FeedSettings(type="test", config={})
    )
    mock = mocker.patch('sync_products.get_tenant_config', return_value=mock_settings)
    return mock

@pytest.fixture
def mock_load_and_transform_feed(mocker):
    """Mock pro načítání a transformaci feedu."""
    mock = mocker.patch('sync_products.load_and_transform_feed', return_value=[])
    return mock

@pytest.fixture
def mock_create_qdrant_id(mocker):
    """Mock pro vytváření Qdrant ID."""
    mock = mocker.patch('sync_products.create_qdrant_id', side_effect=lambda x: f"qdrant_{x}")
    return mock

@pytest.fixture
def mock_process_batches(mocker):
    """Mock pro process_batches_concurrently."""
    async def mock_sequential_processing(items, process_func, batch_size, concurrency_limit):
        results = []
        for i in range(0, len(items), batch_size):
            batch = items[i:i+batch_size]
            results.append(await process_func(batch))
        return results
    mock = mocker.patch('sync_products.process_batches_concurrently', side_effect=mock_sequential_processing)
    return mock

@pytest.fixture
def mock_close_clients(mocker):
    """Mock pro uzavření klientů."""
    mock = mocker.patch('sync_products.close_clients', new_callable=AsyncMock)
    return mock

@pytest.fixture
def synchronizer(mock_qdrant_client, mock_openai_client, mock_embedding_strategy, mock_get_tenant_config, mock_load_and_transform_feed, mock_create_qdrant_id, mock_process_batches, mock_close_clients):
    """Fixture pro vytvoření instance ProductSynchronizer s mockovanými závislostmi."""
    sync = ProductSynchronizer(tenant_id="test_tenant", batch_size=10, workers=2)
    return sync

# --- Testy ---

# Základní test pro ověření inicializace třídy ProductSynchronizer
@pytest.mark.asyncio
async def test_product_synchronizer_init(synchronizer):
    """Testuje, zda se ProductSynchronizer správně inicializuje."""
    tenant_id = "test_tenant"
    batch_size = 10
    workers = 2

    assert synchronizer.tenant_id == tenant_id
    assert synchronizer.batch_size == batch_size
    assert synchronizer.workers == workers
    assert synchronizer.collection_name == f"real_products_{tenant_id}"
    assert synchronizer.complementary_collection_name == f"complementary_products_{tenant_id}"
    assert synchronizer.changes_log_file == os.path.join("logs", f"product_changes_{tenant_id}.log")
    assert synchronizer.qdrant_client is None
    assert synchronizer.openai_client is None
    assert synchronizer.embedding_strategy is None
    assert synchronizer.stats["feed_products"] == 0
    assert synchronizer.stats["db_products"] == 0
    assert synchronizer.stats["new_products"] == 0
    assert synchronizer.stats["deleted_products"] == 0
    assert synchronizer.stats["updated_products"] == 0
    assert synchronizer.stats["unavailable_products"] == 0
    assert synchronizer.stats["processing_time"] == 0


# --- Testy pro _get_all_products_from_db ---

@pytest.mark.asyncio
async def test_get_all_products_from_db_empty(synchronizer, mock_qdrant_client):
    """Testuje načtení produktů z prázdné databáze."""
    # Nastavení mocků
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[CollectionDescription(name=synchronizer.collection_name)])
    mock_qdrant_client.scroll.return_value = ScrollResult(points=[], next_page_offset=None)

    # Inicializace klienta v synchronizeru (normálně dělá sync)
    synchronizer.qdrant_client = mock_qdrant_client

    # Volání testované metody
    db_products = await synchronizer._get_all_products_from_db()

    # Ověření
    assert db_products == {}
    mock_qdrant_client.scroll.assert_awaited_once_with(
        collection_name=synchronizer.collection_name,
        limit=synchronizer.batch_size,
        offset=None,
        with_payload=True,
        with_vectors=False
    )

@pytest.mark.asyncio
async def test_get_all_products_from_db_one_batch(synchronizer, mock_qdrant_client):
    """Testuje načtení produktů z databáze (jedna dávka)."""
    # Testovací data
    mock_points = [
        Record(id="qdrant_p1", payload={"product_id": "p1", "name": "Product 1"}, vector=None),
        Record(id="qdrant_p2", payload={"product_id": "p2", "name": "Product 2"}, vector=None),
    ]

    # Nastavení mocků
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[CollectionDescription(name=synchronizer.collection_name)])
    mock_qdrant_client.scroll.return_value = (mock_points, None)

    # Inicializace klienta v synchronizeru
    synchronizer.qdrant_client = mock_qdrant_client

    # Volání testované metody
    db_products = await synchronizer._get_all_products_from_db()

    # Ověření
    assert len(db_products) == 2
    assert db_products["qdrant_p1"] == {"product_id": "p1", "name": "Product 1"}
    assert db_products["qdrant_p2"] == {"product_id": "p2", "name": "Product 2"}
    mock_qdrant_client.scroll.assert_awaited_once()

@pytest.mark.asyncio
async def test_get_all_products_from_db_multiple_batches(synchronizer, mock_qdrant_client):
    """Testuje načtení produktů z databáze (více dávek)."""
    # Testovací data
    mock_points_batch1 = [Record(id=f"qdrant_p{i}", payload={"product_id": f"p{i}"}, vector=None) for i in range(10)]
    mock_points_batch2 = [Record(id=f"qdrant_p{i}", payload={"product_id": f"p{i}"}, vector=None) for i in range(10, 15)]

    # Nastavení mocků - scroll se volá dvakrát
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[CollectionDescription(name=synchronizer.collection_name)])
    mock_qdrant_client.scroll.side_effect = [
        (mock_points_batch1, 10),
        (mock_points_batch2, None)
    ]

    # Inicializace klienta v synchronizeru
    synchronizer.qdrant_client = mock_qdrant_client

    # Volání testované metody
    db_products = await synchronizer._get_all_products_from_db()

    # Ověření
    assert len(db_products) == 15
    assert "qdrant_p0" in db_products
    assert "qdrant_p14" in db_products
    assert mock_qdrant_client.scroll.await_count == 2
    # Ověření volání s offsetem
    call_args_list = mock_qdrant_client.scroll.await_args_list
    assert call_args_list[0][1]['offset'] is None # První volání bez offsetu
    assert call_args_list[1][1]['offset'] == 10   # Druhé volání s offsetem 10

@pytest.mark.asyncio
async def test_get_all_products_from_db_collection_not_exist(synchronizer, mock_qdrant_client):
    """Testuje případ, kdy kolekce neexistuje."""
    # Nastavení mocků - get_collections nevrátí naši kolekci
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[CollectionDescription(name="other_collection")])

    # Inicializace klienta v synchronizeru
    synchronizer.qdrant_client = mock_qdrant_client

    # Volání testované metody
    db_products = await synchronizer._get_all_products_from_db()

    # Ověření
    assert db_products == {}
    mock_qdrant_client.scroll.assert_not_awaited() # Scroll by se neměl volat

@pytest.mark.asyncio
async def test_get_all_products_from_db_qdrant_error(synchronizer, mock_qdrant_client, caplog):
    """Testuje chybu při volání Qdrantu."""
    # Nastavení mocků
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[CollectionDescription(name=synchronizer.collection_name)])
    mock_qdrant_client.scroll.side_effect = Exception("Qdrant connection error")

    # Inicializace klienta v synchronizeru
    synchronizer.qdrant_client = mock_qdrant_client

    # Volání testované metody
    db_products = await synchronizer._get_all_products_from_db()

    # Ověření
    assert db_products == {}
    mock_qdrant_client.scroll.assert_awaited_once() # Scroll byl volán
    # Ověření, že chyba byla zalogována (používáme pytest caplog fixture)
    assert "Chyba při načítání produktů z databáze: Qdrant connection error" in caplog.text

# --- Další testy ---

# --- Testy pro _process_new_products ---

# Pomocná funkce pro vytvoření testovacího produktu
def create_test_product(product_id: str, **kwargs) -> Product:
    defaults = {
        "id": product_id,
        "name": f"Test Product {product_id}",
        "description": f"Description for {product_id}",
        "category": "Test Category",
        "brand": "Test Brand",
        "price": 100.0,
        "image_url": f"http://example.com/{product_id}.jpg",
        "product_url": f"http://example.com/{product_id}",
        "availability": "in stock"
    }
    defaults.update(kwargs)
    return Product(**defaults)

@pytest.mark.asyncio
async def test_process_new_products_empty(synchronizer, mock_process_batches):
    """Testuje zpracování prázdného seznamu nových produktů."""
    await synchronizer._process_new_products([])
    # Ověříme, že process_batches_concurrently bylo voláno s prázdným seznamem
    mock_process_batches.assert_awaited_once_with(
        items=[],
        process_func=ANY, # process_func je vnitřní funkce, kontrolujeme jen, že byla předána
        batch_size=synchronizer.batch_size,
        concurrency_limit=synchronizer.workers
    )
    # Zde by se neměl volat upsert ani create_embeddings
    # (ale ty jsou volány uvnitř process_func, kterou mockujeme, takže to nekontrolujeme přímo zde)

@pytest.mark.asyncio
async def test_process_new_products_success(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_create_qdrant_id, mock_process_batches):
    """Testuje úspěšné zpracování nových produktů."""
    # Testovací data
    new_products = [
        create_test_product("p1"),
        create_test_product("p2")
    ]
    expected_qdrant_ids = ["qdrant_p1", "qdrant_p2"]
    expected_embeddings = {"combined": [0.1] * 1536} # Z mock_embedding_strategy

    # Inicializace klienta a strategie (normálně dělá sync)
    synchronizer.qdrant_client = mock_qdrant_client
    synchronizer.embedding_strategy = mock_embedding_strategy

    # Volání testované metody
    await synchronizer._process_new_products(new_products)

    # Ověření volání mock_process_batches
    mock_process_batches.assert_awaited_once()
    call_args = mock_process_batches.await_args_list[0][1]
    assert call_args['items'] == new_products

    # Ověření, že vnitřní process_batch volal správné funkce
    # (jelikož process_batches je mockováno sekvenčně, můžeme to snadno ověřit)
    assert mock_create_qdrant_id.call_count == 2
    mock_create_qdrant_id.assert_any_call("p1")
    mock_create_qdrant_id.assert_any_call("p2")

    assert mock_embedding_strategy.create_embeddings.await_count == 2
    # Ověříme, že embedding se volal s modelem produktu
    mock_embedding_strategy.create_embeddings.assert_any_await(new_products[0].model_dump())
    mock_embedding_strategy.create_embeddings.assert_any_await(new_products[1].model_dump())

    # Ověření volání upsert
    mock_qdrant_client.upsert.assert_awaited_once()
    upsert_call_args = mock_qdrant_client.upsert.await_args_list[0][1]
    assert upsert_call_args['collection_name'] == synchronizer.collection_name
    assert len(upsert_call_args['points']) == 2

    # Zkontrolujeme strukturu jednoho bodu
    point1 = upsert_call_args['points'][0]
    assert isinstance(point1, PointStruct)
    assert point1.id == expected_qdrant_ids[0]
    assert point1.vector == expected_embeddings["combined"]
    assert point1.payload["product_id"] == "p1"
    assert point1.payload["name"] == "Test Product p1"
    assert point1.payload["tenant_id"] == synchronizer.tenant_id
    assert "embeddings" in point1.payload # Ověříme, že payload obsahuje embeddings
    assert "created_at" in point1.payload
    assert "updated_at" in point1.payload

@pytest.mark.asyncio
async def test_process_new_products_embedding_error(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_process_batches, caplog):
    """Testuje zpracování, když generování embeddingu selže pro jeden produkt."""
    # Testovací data
    new_products = [
        create_test_product("p1"), # Úspěšný
        create_test_product("p2")  # Selže
    ]
    # Nastavení mocků
    mock_embedding_strategy.create_embeddings.side_effect = [
        {"combined": [0.1] * 1536}, # Úspěch pro p1
        Exception("Embedding API error") # Chyba pro p2
    ]

    # Inicializace
    synchronizer.qdrant_client = mock_qdrant_client
    synchronizer.embedding_strategy = mock_embedding_strategy

    # Volání
    await synchronizer._process_new_products(new_products)

    # Ověření
    mock_process_batches.assert_awaited_once()
    assert mock_embedding_strategy.create_embeddings.await_count == 2
    # Upsert by měl být volán jen pro úspěšný produkt p1
    mock_qdrant_client.upsert.assert_awaited_once()
    upsert_call_args = mock_qdrant_client.upsert.await_args_list[0][1]
    assert len(upsert_call_args['points']) == 1
    assert upsert_call_args['points'][0].id == "qdrant_p1"
    # Ověření logu
    assert "Chyba při generování embeddingů pro produkt p2: Embedding API error" in caplog.text

@pytest.mark.asyncio
async def test_process_new_products_upsert_error(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_process_batches, caplog):
    """Testuje zpracování, když selže nahrávání do Qdrantu."""
    # Testovací data
    new_products = [create_test_product("p1")]
    # Nastavení mocků
    mock_qdrant_client.upsert.side_effect = Exception("Qdrant upsert failed")

    # Inicializace
    synchronizer.qdrant_client = mock_qdrant_client
    synchronizer.embedding_strategy = mock_embedding_strategy

    # Volání
    await synchronizer._process_new_products(new_products)

    # Ověření
    mock_process_batches.assert_awaited_once()
    mock_embedding_strategy.create_embeddings.assert_awaited_once() # Embedding se zkusil vytvořit
    mock_qdrant_client.upsert.assert_awaited_once() # Upsert se zkusil zavolat
    # Ověření logu
    assert "Chyba při nahrávání produktů do Qdrantu: Qdrant upsert failed" in caplog.text

# --- Testy pro _update_existing_products ---

@pytest.mark.asyncio
async def test_update_existing_products_no_changes(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_process_batches):
    """Testuje aktualizaci, když _check_product_changes neindikuje žádné změny."""
    # Testovací data
    existing_products = [create_test_product("p1")]
    db_products = {"qdrant_p1": {"product_id": "p1", "name": "Test Product p1"}} # Data v DB

    # Mockování _check_product_changes, aby vždy vracela False
    with patch.object(synchronizer, '_check_product_changes', return_value=False) as mock_check:
        # Inicializace klienta a strategie
        synchronizer.qdrant_client = mock_qdrant_client
        synchronizer.embedding_strategy = mock_embedding_strategy

        await synchronizer._update_existing_products(existing_products, db_products)

        # Ověření
        mock_process_batches.assert_awaited_once() # Batch processing byl volán
        mock_check.assert_called_once_with(existing_products[0], db_products["qdrant_p1"]) # Check byl volán
        mock_embedding_strategy.create_embeddings.assert_not_awaited() # Embedding se neměl volat
        mock_qdrant_client.upsert.assert_not_awaited() # Upsert se neměl volat
        assert synchronizer.stats["updated_products"] == 0 # Statistiky by měly být 0

@pytest.mark.asyncio
async def test_update_existing_products_with_changes(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_create_qdrant_id, mock_process_batches):
    """Testuje aktualizaci, když _check_product_changes indikuje změny."""
    # Testovací data
    product1_new = create_test_product("p1", name="Updated Product 1") # Produkt se změnil
    product2_no_change = create_test_product("p2") # Produkt se nezměnil
    existing_products = [product1_new, product2_no_change]
    db_products = {
        "qdrant_p1": {"product_id": "p1", "name": "Test Product p1"}, # Stará verze v DB
        "qdrant_p2": {"product_id": "p2", "name": "Test Product p2"}  # Stejná verze v DB
    }

    # Mockování _check_product_changes, vrací True pro p1, False pro p2
    def check_side_effect(product, payload):
        if product.id == "p1":
            return True
        return False

    with patch.object(synchronizer, '_check_product_changes', side_effect=check_side_effect) as mock_check:
        # Inicializace
        synchronizer.qdrant_client = mock_qdrant_client
        synchronizer.embedding_strategy = mock_embedding_strategy

        await synchronizer._update_existing_products(existing_products, db_products)

        # Ověření
        mock_process_batches.assert_awaited_once()
        assert mock_check.call_count == 2 # Check byl volán pro oba produkty

        # Embedding a Upsert by se měly volat jen pro p1
        mock_embedding_strategy.create_embeddings.assert_awaited_once_with(product1_new.model_dump())
        mock_qdrant_client.upsert.assert_awaited_once()

        # Ověření dat poslaných do upsert
        upsert_call_args = mock_qdrant_client.upsert.await_args_list[0][1]
        assert len(upsert_call_args['points']) == 1
        point1 = upsert_call_args['points'][0]
        assert point1.id == "qdrant_p1"
        assert point1.payload["name"] == "Updated Product 1" # Ověření aktualizovaného jména
        assert "updated_at" in point1.payload

        # Ověření statistik
        assert synchronizer.stats["updated_products"] == 1

@pytest.mark.asyncio
async def test_update_existing_products_missing_qdrant_id(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_process_batches, caplog):
    """Testuje případ, kdy pro produkt chybí Qdrant ID v db_products (nemělo by nastat)."""
    existing_products = [create_test_product("p1")]
    db_products = {} # Prázdný slovník, qdrant_id se nenajde

    with patch.object(synchronizer, '_check_product_changes', return_value=True):
        synchronizer.qdrant_client = mock_qdrant_client
        synchronizer.embedding_strategy = mock_embedding_strategy
        await synchronizer._update_existing_products(existing_products, db_products)

        # Ověření - nic by se nemělo aktualizovat
        mock_process_batches.assert_awaited_once()
        # Check changes se ani nedostane ke slovu, protože chybí ID
        mock_qdrant_client.upsert.assert_not_awaited()
        assert "Nenalezeno Qdrant ID pro produkt p1" in caplog.text
        assert synchronizer.stats["updated_products"] == 0

@pytest.mark.asyncio
async def test_update_existing_products_embedding_error_on_update(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_process_batches, caplog):
    """Testuje chybu při generování embeddingu během aktualizace."""
    existing_products = [create_test_product("p1")]
    db_products = {"qdrant_p1": {"product_id": "p1", "name": "Old Name"}}

    # Mockování
    mock_embedding_strategy.create_embeddings.side_effect = Exception("Embedding API error")

    with patch.object(synchronizer, '_check_product_changes', return_value=True):
        synchronizer.qdrant_client = mock_qdrant_client
        synchronizer.embedding_strategy = mock_embedding_strategy
        await synchronizer._update_existing_products(existing_products, db_products)

        # Ověření
        mock_process_batches.assert_awaited_once()
        mock_embedding_strategy.create_embeddings.assert_awaited_once() # Volání selhalo
        mock_qdrant_client.upsert.assert_not_awaited() # Upsert se nevolal
        assert "Chyba při aktualizaci produktu p1: Embedding API error" in caplog.text
        assert synchronizer.stats["updated_products"] == 0

@pytest.mark.asyncio
async def test_update_existing_products_upsert_error_on_update(synchronizer, mock_qdrant_client, mock_embedding_strategy, mock_process_batches, caplog):
    """Testuje chybu při Qdrant upsert během aktualizace."""
    existing_products = [create_test_product("p1")]
    db_products = {"qdrant_p1": {"product_id": "p1", "name": "Old Name"}}

    # Mockování
    mock_qdrant_client.upsert.side_effect = Exception("Qdrant upsert failed")

    with patch.object(synchronizer, '_check_product_changes', return_value=True):
        synchronizer.qdrant_client = mock_qdrant_client
        synchronizer.embedding_strategy = mock_embedding_strategy
        await synchronizer._update_existing_products(existing_products, db_products)

        # Ověření
        mock_process_batches.assert_awaited_once()
        mock_embedding_strategy.create_embeddings.assert_awaited_once() # Embedding se vytvořil
        mock_qdrant_client.upsert.assert_awaited_once() # Upsert byl volán a selhal
        assert "Chyba při aktualizaci produktu p1: Qdrant upsert failed" in caplog.text
        assert synchronizer.stats["updated_products"] == 0

# --- Testy pro _check_product_changes ---

# Testovací data pro _check_product_changes
@pytest.fixture
def base_payload():
    return {
        "product_id": "p1",
        "name": "Test Product p1",
        "description": "Desc p1",
        "category": "Cat A",
        "brand": "Brand X",
        "price": 100.0,
        "image_url": "http://example.com/p1.jpg",
        "product_url": "http://example.com/p1",
        "availability": "in stock",
        "updated_at": time.time() - (10 * 24 * 60 * 60) # 10 dní staré
    }

@pytest.fixture
def product_from_payload():
    def _creator(payload):
        # Jednoduchý převod payloadu na Product objekt pro testy
        # ID se bere z product_id v payloadu
        p_id = payload.get("product_id", "unknown")
        return create_test_product(p_id, **{k: v for k, v in payload.items() if k not in ["product_id", "updated_at"]})
    return _creator

# Testy jednotlivých změn
@pytest.mark.parametrize("attribute, new_value, should_change", [
    ("name", "Test Product p1", False), # Beze změny
    ("name", "Updated Name", True),     # Změna jména
    ("description", "New Desc", True),    # Změna popisu
    ("category", "Cat B", True),        # Změna kategorie
    ("brand", "Brand Y", True),         # Změna značky
    ("price", 100.0, False),          # Cena float == float
    ("price", 100, False),            # Cena float == int (po převodu)
    ("price", "100.0", False),        # Cena float == str (po převodu)
    ("price", 100.1, True),           # Změna ceny float
    ("price", "101", True),           # Změna ceny str
    ("availability", "out of stock", True), # Změna dostupnosti
    ("image_url", "new.jpg", True),      # Změna obrázku
    ("product_url", "new_link", True),   # Změna URL
])
def test_check_product_changes_single_attr(synchronizer, base_payload, product_from_payload, attribute, new_value, should_change):
    """Testuje detekci změn jednotlivých atributů."""
    # Vytvoříme produkt s novou hodnotou
    changed_payload = base_payload.copy()
    changed_payload[attribute] = new_value
    product = product_from_payload(changed_payload)

    # Základní payload (stará verze)
    current_payload = base_payload.copy()

    assert synchronizer._check_product_changes(product, current_payload) == should_change

def test_check_product_changes_price_conversion_error(synchronizer, base_payload, product_from_payload):
    """Testuje, když nelze převést cenu v payloadu z DB."""
    # Produkt má platnou cenu
    product = product_from_payload(base_payload)
    # Payload z DB má neplatnou cenu
    current_payload = base_payload.copy()
    current_payload["price"] = "not a number"
    assert synchronizer._check_product_changes(product, current_payload) is True

def test_check_product_changes_missing_attribute(synchronizer, base_payload, product_from_payload):
    """Testuje, když atribut chybí v payloadu z DB."""
    product = product_from_payload(base_payload)
    current_payload = base_payload.copy()
    del current_payload["name"] # Odstraníme atribut z DB payloadu

    assert synchronizer._check_product_changes(product, current_payload) is True

def test_check_product_changes_old_embedding(synchronizer, base_payload, product_from_payload):
    """Testuje detekci změny kvůli stáří záznamu (updated_at)."""
    product = product_from_payload(base_payload)
    current_payload = base_payload.copy()
    # Nastavíme updated_at na více než 30 dní do minulosti
    current_payload["updated_at"] = time.time() - (40 * 24 * 60 * 60)

    assert synchronizer._check_product_changes(product, current_payload) is True

def test_check_product_changes_old_embedding_no_attr_change(synchronizer, base_payload, product_from_payload):
    """Testuje, že stáří se kontroluje, i když se atributy nezměnily."""
    # Žádná změna atributů
    product = product_from_payload(base_payload)
    current_payload = base_payload.copy()
    current_payload["updated_at"] = time.time() - (40 * 24 * 60 * 60)

    assert synchronizer._check_product_changes(product, current_payload) is True

def test_check_product_changes_recent_embedding_no_attr_change(synchronizer, base_payload, product_from_payload):
    """Testuje, že pokud se atributy nezmění a záznam je nový, změna není detekována."""
    product = product_from_payload(base_payload)
    current_payload = base_payload.copy()
    # updated_at je recentní (nastaveno ve fixture base_payload)
    assert synchronizer._check_product_changes(product, current_payload) is False

# --- Testy pro _delete_products ---

@pytest.mark.asyncio
async def test_delete_products_empty_set(synchronizer, mock_qdrant_client):
    """Testuje mazání prázdné množiny produktů."""
    # Inicializace klienta
    synchronizer.qdrant_client = mock_qdrant_client

    await synchronizer._delete_products(set(), {})

    # Ověření, že delete nebylo voláno
    mock_qdrant_client.delete.assert_not_awaited()

@pytest.mark.asyncio
async def test_delete_products_success_main_collection_only(synchronizer, mock_qdrant_client):
    """Testuje úspěšné smazání produktů (pouze hlavní kolekce)."""
    # Testovací data
    deleted_product_ids = {"p1", "p2"}
    db_products = {
        "qdrant_p1": {"product_id": "p1"},
        "qdrant_p2": {"product_id": "p2"},
        "qdrant_p3": {"product_id": "p3"} # Tento se nemá mazat
    }
    expected_qdrant_ids_to_delete = ["qdrant_p1", "qdrant_p2"]

    # Nastavení mocků - komplementární kolekce neexistuje
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[CollectionDescription(name=synchronizer.collection_name)])

    # Inicializace klienta
    synchronizer.qdrant_client = mock_qdrant_client

    await synchronizer._delete_products(deleted_product_ids, db_products)

    # Ověření volání delete pro hlavní kolekci - flexibilnější kontrola
    mock_qdrant_client.delete.assert_awaited_once() # Ověříme, že bylo voláno právě jednou
    call_args = mock_qdrant_client.delete.await_args_list[0][1]
    assert call_args['collection_name'] == synchronizer.collection_name
    # Porovnáme jako sety, abychom ignorovali pořadí
    assert isinstance(call_args['points_selector'], PointIdsList)
    assert set(call_args['points_selector'].points) == set(expected_qdrant_ids_to_delete)
    assert call_args['wait'] is True

    # Ověření, že se nevolalo get_collections pro kontrolu komplementární kolekce (optimalizace)
    # mock_qdrant_client.get_collections.assert_not_awaited() # TOTO BYLO CHYBNĚ - get_collections se volá vždy

@pytest.mark.asyncio
async def test_delete_products_success_with_complementary(synchronizer, mock_qdrant_client):
    """Testuje úspěšné smazání produktů z obou kolekcí."""
    # Testovací data
    deleted_product_ids = {"p1"}
    db_products = {"qdrant_p1": {"product_id": "p1"}}
    expected_qdrant_ids_to_delete = ["qdrant_p1"]

    # Nastavení mocků - obě kolekce existují
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[
        CollectionDescription(name=synchronizer.collection_name),
        CollectionDescription(name=synchronizer.complementary_collection_name)
    ])

    # Inicializace klienta
    synchronizer.qdrant_client = mock_qdrant_client

    await synchronizer._delete_products(deleted_product_ids, db_products)

    # Ověření volání delete pro obě kolekce
    assert mock_qdrant_client.delete.await_count == 2
    mock_qdrant_client.delete.assert_any_await(
        collection_name=synchronizer.collection_name,
        points_selector=PointIdsList(points=expected_qdrant_ids_to_delete),
        wait=True
    )
    mock_qdrant_client.delete.assert_any_await(
        collection_name=synchronizer.complementary_collection_name,
        points_selector=PointIdsList(points=expected_qdrant_ids_to_delete),
        wait=True
    )
    # Ověření volání kontroly existence komplementární kolekce
    mock_qdrant_client.get_collections.assert_awaited_once()

@pytest.mark.asyncio
async def test_delete_products_missing_qdrant_id_in_db(synchronizer, mock_qdrant_client):
    """Testuje případ, kdy ID ke smazání nemá záznam v db_products."""
    deleted_product_ids = {"p1"} # Chceme smazat p1
    db_products = {"qdrant_p2": {"product_id": "p2"}} # Ale v DB máme jen p2

    # Inicializace klienta
    synchronizer.qdrant_client = mock_qdrant_client

    await synchronizer._delete_products(deleted_product_ids, db_products)

    # Ověření - delete by se nemělo volat, protože jsme nenašli Qdrant ID
    mock_qdrant_client.delete.assert_not_awaited()

@pytest.mark.asyncio
async def test_delete_products_qdrant_error_main(synchronizer, mock_qdrant_client, caplog):
    """Testuje chybu při mazání z hlavní kolekce."""
    deleted_product_ids = {"p1"}
    db_products = {"qdrant_p1": {"product_id": "p1"}}
    expected_qdrant_ids_to_delete = ["qdrant_p1"]

    # Nastavení mocků
    mock_qdrant_client.delete.side_effect = Exception("Qdrant delete failed")
    # Komplementární neexistuje pro jednoduchost
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[CollectionDescription(name=synchronizer.collection_name)])

    # Inicializace klienta
    synchronizer.qdrant_client = mock_qdrant_client

    await synchronizer._delete_products(deleted_product_ids, db_products)

    # Ověření
    mock_qdrant_client.delete.assert_awaited_once() # Delete bylo voláno a selhalo
    assert "Chyba při mazání produktů z Qdrantu: Qdrant delete failed" in caplog.text
    # Kontrola komplementární se NEVOLALA, protože hlavní delete selhal A komplementární kolekce dle mocku neexistuje.
    # Pokud by komplementární existovala, get_collections by se volalo.
    # V tomto testu je správně, že se nevolá.
    # mock_qdrant_client.get_collections.assert_not_awaited() # TOTO BYLO CHYBNĚ - get_collections se volá vždy

@pytest.mark.asyncio
async def test_delete_products_qdrant_error_complementary(synchronizer, mock_qdrant_client, caplog):
    """Testuje chybu při mazání z komplementární kolekce."""
    deleted_product_ids = {"p1"}
    db_products = {"qdrant_p1": {"product_id": "p1"}}
    expected_qdrant_ids_to_delete = ["qdrant_p1"]

    # Nastavení mocků - hlavní delete projde, komplementární selže
    delete_main_mock = AsyncMock()
    delete_complementary_mock = AsyncMock(side_effect=Exception("Complementary delete failed"))

    async def delete_side_effect(*args, **kwargs):
        if kwargs.get("collection_name") == synchronizer.collection_name:
            return await delete_main_mock(*args, **kwargs)
        elif kwargs.get("collection_name") == synchronizer.complementary_collection_name:
            return await delete_complementary_mock(*args, **kwargs)
        raise ValueError("Unexpected collection name in delete mock")

    mock_qdrant_client.delete.side_effect = delete_side_effect
    mock_qdrant_client.get_collections.return_value = CollectionsResponse(collections=[
        CollectionDescription(name=synchronizer.collection_name),
        CollectionDescription(name=synchronizer.complementary_collection_name)
    ])

    # Inicializace klienta
    synchronizer.qdrant_client = mock_qdrant_client

    await synchronizer._delete_products(deleted_product_ids, db_products)

    # Ověření
    assert mock_qdrant_client.delete.await_count == 2 # Delete bylo voláno 2x
    delete_main_mock.assert_awaited_once() # Hlavní bylo voláno
    delete_complementary_mock.assert_awaited_once() # Komplementární bylo voláno (a selhalo)
    mock_qdrant_client.get_collections.assert_awaited_once() # Kontrola existence byla volána
    assert "Chyba při mazání komplementárních záznamů z Qdrantu: Complementary delete failed" in caplog.text

# --- Další testy ---

# --- Testy pro _check_product_availability ---

@pytest.mark.asyncio
async def test_check_availability_becomes_unavailable(synchronizer, mock_qdrant_client):
    """Testuje scénář, kdy se produkt stane nedostupným."""
    # Testovací data
    feed_products = [
        create_test_product("p1", availability="out of stock"), # Stal se nedostupným
        create_test_product("p2", availability="in stock")      # Zůstává dostupný
    ]
    # Předpokládaný stav v DB (před kontrolou)
    db_products_before = {
        "qdrant_p1": {"product_id": "p1", "availability": "in stock"},
        "qdrant_p2": {"product_id": "p2", "availability": "in stock"}
    }

    # Mockování interního volání _get_all_products_from_db
    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products_before) as mock_get_db:
        # Inicializace klienta
        synchronizer.qdrant_client = mock_qdrant_client

        await synchronizer._check_product_availability(feed_products)

        # Ověření
        mock_get_db.assert_awaited_once() # Ověříme, že se data z DB načetla
        # Ověříme volání set_payload pro p1
        mock_qdrant_client.set_payload.assert_awaited_once()
        call_args = mock_qdrant_client.set_payload.await_args_list[0][1]
        assert call_args['collection_name'] == synchronizer.collection_name
        assert call_args['points'] == ["qdrant_p1"]
        assert call_args['payload']["availability"] == "out of stock"
        assert "updated_at" in call_args['payload']
        assert call_args['wait'] is True
        # Ověření statistik
        assert synchronizer.stats["unavailable_products"] == 1

@pytest.mark.asyncio
async def test_check_availability_no_changes(synchronizer, mock_qdrant_client):
    """Testuje scénář, kdy nedochází ke změně dostupnosti."""
    feed_products = [create_test_product("p1", availability="in stock")]
    db_products_before = {"qdrant_p1": {"product_id": "p1", "availability": "in stock"}}

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products_before):
        synchronizer.qdrant_client = mock_qdrant_client
        await synchronizer._check_product_availability(feed_products)

        # Ověření - set_payload by se nemělo volat
        mock_qdrant_client.set_payload.assert_not_awaited()
        assert synchronizer.stats["unavailable_products"] == 0

@pytest.mark.asyncio
async def test_check_availability_empty_feed(synchronizer, mock_qdrant_client):
    """Testuje scénář s prázdným feedem."""
    feed_products = []
    db_products_before = {"qdrant_p1": {"product_id": "p1", "availability": "in stock"}}

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products_before):
        synchronizer.qdrant_client = mock_qdrant_client
        await synchronizer._check_product_availability(feed_products)

        # Ověření - nic by se nemělo dít
        mock_qdrant_client.set_payload.assert_not_awaited()
        assert synchronizer.stats["unavailable_products"] == 0

@pytest.mark.asyncio
async def test_check_availability_product_not_in_db(synchronizer, mock_qdrant_client):
    """Testuje scénář, kdy produkt z feedu není v DB (nemělo by nastat)."""
    feed_products = [create_test_product("p1", availability="out of stock")]
    db_products_before = {} # Produkt p1 chybí v DB

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products_before):
        synchronizer.qdrant_client = mock_qdrant_client
        await synchronizer._check_product_availability(feed_products)

        # Ověření - set_payload by se nemělo volat, protože jsme nenašli qdrant_id
        mock_qdrant_client.set_payload.assert_not_awaited()
        assert synchronizer.stats["unavailable_products"] == 1 # Statistiky počítají nedostupné z feedu

@pytest.mark.asyncio
async def test_check_availability_get_db_error(synchronizer, mock_qdrant_client, caplog):
    """Testuje chybu při interním volání _get_all_products_from_db."""
    feed_products = [create_test_product("p1", availability="out of stock")]

    # Mockování interního volání _get_all_products_from_db s chybou
    with patch.object(synchronizer, '_get_all_products_from_db', side_effect=Exception("DB Error")):
        synchronizer.qdrant_client = mock_qdrant_client
        # Očekáváme, že metoda zachytí chybu a nebude pokračovat
        # Přidáno zachytávání očekávané výjimky
        with pytest.raises(Exception, match="DB Error"):
            await synchronizer._check_product_availability(feed_products)

    # Ověření
    mock_qdrant_client.set_payload.assert_not_awaited()
    assert synchronizer.stats["unavailable_products"] == 0 # Statistiky se nenastaví
    # Chyba z _get_all_products_from_db by měla být zalogována v ní, ne zde

@pytest.mark.asyncio
async def test_check_availability_set_payload_error(synchronizer, mock_qdrant_client, caplog):
    """Testuje chybu při volání set_payload."""
    feed_products = [create_test_product("p1", availability="out of stock")]
    db_products_before = {"qdrant_p1": {"product_id": "p1", "availability": "in stock"}}

    # Mockování
    mock_qdrant_client.set_payload.side_effect = Exception("SetPayload Error")

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products_before):
        synchronizer.qdrant_client = mock_qdrant_client
        await synchronizer._check_product_availability(feed_products)

        # Ověření
        mock_qdrant_client.set_payload.assert_awaited_once() # Volání selhalo
        assert "Chyba při aktualizaci dostupnosti produktu p1: SetPayload Error" in caplog.text
        assert synchronizer.stats["unavailable_products"] == 1 # Statistiky se počítají i přes chybu

# --- Další testy ---

# --- Integrační testy pro metodu sync ---

@pytest.mark.asyncio
async def test_sync_initial_sync(synchronizer, mock_load_and_transform_feed, mock_get_tenant_config):
    """Testuje první synchronizaci, kdy je DB prázdná a feed obsahuje produkty."""
    # Feed má produkty
    feed_products = [create_test_product("p1"), create_test_product("p2")]
    mock_load_and_transform_feed.return_value = feed_products
    # DB je prázdná
    db_products = {}

    # Mockujeme interní metody, abychom ověřili jejich volání
    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products) as mock_get_db, \
         patch.object(synchronizer, '_process_new_products', new_callable=AsyncMock) as mock_process_new, \
         patch.object(synchronizer, '_update_existing_products', new_callable=AsyncMock) as mock_update, \
         patch.object(synchronizer, '_delete_products', new_callable=AsyncMock) as mock_delete, \
         patch.object(synchronizer, '_check_product_availability', new_callable=AsyncMock) as mock_check_avail, \
         patch.object(synchronizer, '_log_changes') as mock_log:

        # Spuštění sync
        result = await synchronizer.sync(force_update=False)

        # Ověření
        assert result is True
        mock_get_tenant_config.assert_called_once_with(synchronizer.tenant_id)
        mock_load_and_transform_feed.assert_awaited_once()
        mock_get_db.assert_awaited_once()

        # Měly by se zpracovat nové produkty
        mock_process_new.assert_awaited_once_with(feed_products)
        # Ostatní operace by se neměly volat
        mock_update.assert_not_awaited()
        mock_delete.assert_not_awaited()
        # Kontrola dostupnosti a logování by se měly volat
        mock_check_avail.assert_awaited_once_with(feed_products)
        mock_log.assert_called_once()
        # Zkontrolujeme ID nových produktů v logu
        log_args = mock_log.call_args[0]
        assert log_args[0] == {p.id for p in feed_products} # new_product_ids
        assert log_args[1] == set() # deleted_product_ids

        # Ověření statistik (základní)
        assert synchronizer.stats["feed_products"] == 2
        assert synchronizer.stats["db_products"] == 0
        assert synchronizer.stats["new_products"] == 2
        assert synchronizer.stats["deleted_products"] == 0

@pytest.mark.asyncio
async def test_sync_no_changes(synchronizer, mock_load_and_transform_feed):
    """Testuje synchronizaci, kdy feed a DB jsou identické."""
    # Feed a DB mají stejné produkty
    feed_products = [create_test_product("p1")]
    mock_load_and_transform_feed.return_value = feed_products
    db_products = {"qdrant_p1": {"product_id": "p1", "name": "Test Product p1"}}

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products) as mock_get_db, \
         patch.object(synchronizer, '_process_new_products', new_callable=AsyncMock) as mock_process_new, \
         patch.object(synchronizer, '_update_existing_products', new_callable=AsyncMock) as mock_update, \
         patch.object(synchronizer, '_delete_products', new_callable=AsyncMock) as mock_delete, \
         patch.object(synchronizer, '_check_product_availability', new_callable=AsyncMock) as mock_check_avail, \
         patch.object(synchronizer, '_log_changes') as mock_log:

        result = await synchronizer.sync(force_update=False)

        # Ověření
        assert result is True
        mock_load_and_transform_feed.assert_awaited_once()
        mock_get_db.assert_awaited_once()

        # Žádné změny -> žádné operace zápisu
        mock_process_new.assert_not_awaited()
        mock_update.assert_not_awaited() # Protože force_update=False
        mock_delete.assert_not_awaited()
        mock_check_avail.assert_awaited_once()
        mock_log.assert_called_once()
        # Zkontrolujeme ID v logu
        log_args = mock_log.call_args[0]
        assert log_args[0] == set() # new_product_ids
        assert log_args[1] == set() # deleted_product_ids

        assert synchronizer.stats["feed_products"] == 1
        assert synchronizer.stats["db_products"] == 1
        assert synchronizer.stats["new_products"] == 0
        assert synchronizer.stats["deleted_products"] == 0

@pytest.mark.asyncio
async def test_sync_new_product_added(synchronizer, mock_load_and_transform_feed):
    """Testuje přidání nového produktu do feedu."""
    # Feed má p1, p2; DB má jen p1
    feed_products = [create_test_product("p1"), create_test_product("p2")]
    mock_load_and_transform_feed.return_value = feed_products
    db_products = {"qdrant_p1": {"product_id": "p1"}}

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products) as mock_get_db, \
         patch.object(synchronizer, '_process_new_products', new_callable=AsyncMock) as mock_process_new, \
         patch.object(synchronizer, '_update_existing_products', new_callable=AsyncMock) as mock_update, \
         patch.object(synchronizer, '_delete_products', new_callable=AsyncMock) as mock_delete, \
         patch.object(synchronizer, '_check_product_availability', new_callable=AsyncMock) as mock_check_avail, \
         patch.object(synchronizer, '_log_changes') as mock_log:

        result = await synchronizer.sync(force_update=False)

        # Ověření
        assert result is True
        mock_process_new.assert_awaited_once_with([p for p in feed_products if p.id == 'p2']) # Voláno jen pro p2
        mock_update.assert_not_awaited()
        mock_delete.assert_not_awaited()
        mock_check_avail.assert_awaited_once()
        mock_log.assert_called_once()
        log_args = mock_log.call_args[0]
        assert log_args[0] == {"p2"} # new_product_ids
        assert log_args[1] == set() # deleted_product_ids

        assert synchronizer.stats["feed_products"] == 2
        assert synchronizer.stats["db_products"] == 1
        assert synchronizer.stats["new_products"] == 1
        assert synchronizer.stats["deleted_products"] == 0

@pytest.mark.asyncio
async def test_sync_product_deleted(synchronizer, mock_load_and_transform_feed):
    """Testuje odebrání produktu z feedu."""
    # Feed má jen p1; DB má p1, p2
    feed_products = [create_test_product("p1")]
    mock_load_and_transform_feed.return_value = feed_products
    db_products = {"qdrant_p1": {"product_id": "p1"}, "qdrant_p2": {"product_id": "p2"}}

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products) as mock_get_db, \
         patch.object(synchronizer, '_process_new_products', new_callable=AsyncMock) as mock_process_new, \
         patch.object(synchronizer, '_update_existing_products', new_callable=AsyncMock) as mock_update, \
         patch.object(synchronizer, '_delete_products', new_callable=AsyncMock) as mock_delete, \
         patch.object(synchronizer, '_check_product_availability', new_callable=AsyncMock) as mock_check_avail, \
         patch.object(synchronizer, '_log_changes') as mock_log:

        result = await synchronizer.sync(force_update=False)

        # Ověření
        assert result is True
        mock_process_new.assert_not_awaited()
        mock_update.assert_not_awaited()
        mock_delete.assert_awaited_once_with({"p2"}, db_products) # Voláno pro smazání p2
        mock_check_avail.assert_awaited_once()
        mock_log.assert_called_once()
        log_args = mock_log.call_args[0]
        assert log_args[0] == set() # new_product_ids
        assert log_args[1] == {"p2"} # deleted_product_ids

        assert synchronizer.stats["feed_products"] == 1
        assert synchronizer.stats["db_products"] == 2
        assert synchronizer.stats["new_products"] == 0
        assert synchronizer.stats["deleted_products"] == 1

@pytest.mark.asyncio
async def test_sync_force_update(synchronizer, mock_load_and_transform_feed):
    """Testuje vynucenou aktualizaci (force_update=True)."""
    # Feed a DB mají stejný produkt p1
    feed_products = [create_test_product("p1")]
    mock_load_and_transform_feed.return_value = feed_products
    db_products = {"qdrant_p1": {"product_id": "p1"}}

    with patch.object(synchronizer, '_get_all_products_from_db', return_value=db_products) as mock_get_db, \
         patch.object(synchronizer, '_process_new_products', new_callable=AsyncMock) as mock_process_new, \
         patch.object(synchronizer, '_update_existing_products', new_callable=AsyncMock) as mock_update, \
         patch.object(synchronizer, '_delete_products', new_callable=AsyncMock) as mock_delete, \
         patch.object(synchronizer, '_check_product_availability', new_callable=AsyncMock) as mock_check_avail, \
         patch.object(synchronizer, '_log_changes') as mock_log:

        result = await synchronizer.sync(force_update=True) # Force update je True

        # Ověření
        assert result is True
        mock_process_new.assert_not_awaited()
        # Update by se měl volat, i když nejsou detekované změny (v tomto testu)
        mock_update.assert_awaited_once_with(feed_products, db_products)
        mock_delete.assert_not_awaited()
        mock_check_avail.assert_awaited_once()
        mock_log.assert_called_once()

        assert synchronizer.stats["feed_products"] == 1
        assert synchronizer.stats["db_products"] == 1
        assert synchronizer.stats["new_products"] == 0
        assert synchronizer.stats["deleted_products"] == 0
        # Statistiky pro updated_products se nastavují uvnitř mockovaného _update_existing_products
        # Pokud bychom to chtěli testovat, museli bychom mocku nastavit side_effect, který mění self.stats

@pytest.mark.asyncio
async def test_sync_feed_load_error(synchronizer, mock_load_and_transform_feed, mock_get_tenant_config, caplog):
    """Testuje chybu při načítání feedu."""
    # Mockování load_and_transform_feed, aby vyhodilo chybu
    mock_load_and_transform_feed.side_effect = Exception("Feed URL not found")

    # Nepotřebujeme mockovat ostatní metody, protože sync by měl skončit dříve
    with patch.object(synchronizer, '_get_all_products_from_db', new_callable=AsyncMock) as mock_get_db:

        result = await synchronizer.sync(force_update=False)

        # Ověření
        assert result is False # Sync by měl selhat
        mock_get_tenant_config.assert_called_once() # Konfigurace se načetla
        mock_load_and_transform_feed.assert_awaited_once() # Načítání feedu selhalo
        mock_get_db.assert_not_awaited() # K načítání DB by nemělo dojít
        assert "Chyba při synchronizaci produktů: Feed URL not found" in caplog.text

@pytest.mark.asyncio
async def test_sync_get_db_error(synchronizer, mock_load_and_transform_feed, caplog):
    """Testuje chybu při načítání z DB."""
    # Feed se načte úspěšně
    feed_products = [create_test_product("p1")]
    mock_load_and_transform_feed.return_value = feed_products

    # Mockování _get_all_products_from_db, aby vyhodilo chybu
    with patch.object(synchronizer, '_get_all_products_from_db', side_effect=Exception("DB connection failed")) as mock_get_db:

        result = await synchronizer.sync(force_update=False)

        # Ověření
        assert result is False # Sync by měl selhat
        mock_load_and_transform_feed.assert_awaited_once()
        mock_get_db.assert_awaited_once() # Načítání DB selhalo
        # Chyba je zalogována uvnitř _get_all_products_from_db, sync zaloguje obecnější chybu
        assert "Chyba při synchronizaci produktů: DB connection failed" in caplog.text 