"""
⚙️ Testy konfigurace pro compute_complementary.py

Tento soubor testuje konfigurační aspekty:
- <PERSON><PERSON>ce parametrů a konstant
- Environment variables
- YAML konfigurace tenantů  
- Argumenty příkazové řádky
- Nastavení limitů a timeoutů

Analogie: Jako kontrola všech <PERSON>ů a matic před startem motoru
"""

import pytest
import os
import sys
import tempfile
import yaml
from unittest.mock import patch, Mock
from pathlib import Path

# Přidání cesty k projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from batch_scripts.compute_complementary import (
    ULTRA_BATCH_SIZE,
    ULTRA_CONCURRENCY, 
    ULTRA_CANDIDATE_LIMIT,
    ULTRA_RERANK_LIMIT,
    get_tenant_yaml_config,
    ProcessingMetrics
)


class TestConfigurationValidation:
    """
    🔧 Testy validace konfigurace - jako kontrola nastavení před vý<PERSON>, že všechny konfigurační parametry jsou správně nastavené
    """
    
    def test_ultra_constants_validation(self):
        """Test validace ULTRA konstant - jako kontrola bezpečnostních limitů"""
        # Základní validace rozsahů
        assert ULTRA_BATCH_SIZE > 0, "ULTRA_BATCH_SIZE musí být kladné"
        assert ULTRA_BATCH_SIZE <= 30, "ULTRA_BATCH_SIZE je příliš vysoké pro API limity"
        
        assert ULTRA_CONCURRENCY > 0, "ULTRA_CONCURRENCY musí být kladné"
        assert ULTRA_CONCURRENCY <= 100, "ULTRA_CONCURRENCY je nebezpečně vysoké"
        
        assert ULTRA_CANDIDATE_LIMIT > 0, "ULTRA_CANDIDATE_LIMIT musí být kladné"
        assert ULTRA_CANDIDATE_LIMIT >= ULTRA_RERANK_LIMIT, "Candidate limit musí být >= rerank limit"
        
        assert ULTRA_RERANK_LIMIT > 0, "ULTRA_RERANK_LIMIT musí být kladné"
        assert ULTRA_RERANK_LIMIT <= 50, "ULTRA_RERANK_LIMIT je příliš vysoký pro efektivitu"
    
    def test_constants_relationships(self):
        """Test vztahů mezi konstantami - jako kontrola převodových poměrů"""
        # Logické vztahy mezi parametry
        assert ULTRA_CANDIDATE_LIMIT >= ULTRA_RERANK_LIMIT * 2, \
            "Candidate limit by měl být minimálně 2x větší než rerank limit"
        
        # Batch size vs concurrency balance
        total_load = ULTRA_BATCH_SIZE * ULTRA_CONCURRENCY
        assert total_load <= 500, \
            f"Celkové zatížení ({total_load}) může být příliš vysoké pro stabilitu"
    
    def test_performance_expectations(self):
        """Test očekávaného výkonu - jako benchmark očekávání"""
        # Výpočet očekávaného throughput
        expected_items_per_batch = ULTRA_BATCH_SIZE
        expected_batches_per_second = min(ULTRA_CONCURRENCY / 5, 10)  # Konzervativní odhad
        expected_throughput = expected_items_per_batch * expected_batches_per_second
        
        print(f"Očekávaný throughput: {expected_throughput:.1f} items/s")
        print(f"Batch size: {ULTRA_BATCH_SIZE}")
        print(f"Concurrency: {ULTRA_CONCURRENCY}")
        
        assert expected_throughput > 0, "Očekávaný throughput musí být pozitivní"
        assert expected_throughput < 1000, "Očekávaný throughput je nerealisticky vysoký"


class TestEnvironmentConfiguration:
    """
    🌍 Testy environment konfigurace - jako kontrola prostředí před během
    
    Testuje environment variables a jejich validaci
    """
    
    def test_required_env_variables(self):
        """Test požadovaných environment proměnných - jako kontrola palivových nádrží"""
        required_vars = [
            'QDRANT_URL',
            'NEO4J_URI', 
            'NEO4J_USER',
            'NEO4J_PASSWORD',
            'GEMINI_API_KEY'
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️ Chybějící environment variables: {missing_vars}")
            print("Toto je v pořádku pro testy, ale produkce vyžaduje všechny proměnné")
        
        # V testech nezpůsobujeme failure, jen varujeme
        assert True
    
    @patch.dict(os.environ, {
        'QDRANT_URL': 'http://test-qdrant:6333',
        'NEO4J_URI': 'bolt://test-neo4j:7687',
        'NEO4J_USER': 'test_user',
        'NEO4J_PASSWORD': 'test_password',
        'GEMINI_API_KEY': 'test_key_12345'
    })
    def test_env_variables_format_validation(self):
        """Test formátu environment proměnných - jako kontrola validity paliva"""
        # QDRANT_URL format
        qdrant_url = os.getenv('QDRANT_URL')
        assert qdrant_url.startswith(('http://', 'https://')), \
            "QDRANT_URL musí začínat http:// nebo https://"
        
        # NEO4J_URI format
        neo4j_uri = os.getenv('NEO4J_URI')
        assert neo4j_uri.startswith(('bolt://', 'neo4j://', 'bolt+s://', 'neo4j+s://')), \
            "NEO4J_URI musí mít platný protokol"
        
        # GEMINI_API_KEY format (základní kontrola)
        gemini_key = os.getenv('GEMINI_API_KEY')
        assert len(gemini_key) > 10, "GEMINI_API_KEY je příliš krátký"
        assert gemini_key.replace('_', '').replace('-', '').isalnum(), \
            "GEMINI_API_KEY obsahuje neplatné znaky"


class TestTenantConfiguration:
    """
    🏢 Testy tenant konfigurace - jako kontrola nastavení různých poboček
    
    Testuje YAML konfiguraci pro jednotlivé tenanty
    """
    
    def test_tenant_yaml_structure(self):
        """Test struktury tenant YAML - jako kontrola podnikového manuálu"""
        # Vytvoření testovacího YAML souboru
        test_config = {
            'tenant_id': 'test_tenant',
            'name': 'Test Tenant',
            'database': {
                'qdrant_collection_prefix': 'test_',
                'neo4j_label_suffix': '_test'
            },
            'processing': {
                'batch_size': 10,
                'candidate_limit': 100,
                'rerank_limit': 20,
                'cache_ttl_days': 30
            },
            'features': {
                'ultra_optimization': True,
                'neo4j_cache': True,
                'bulk_cache_check': True
            }
        }
        
        # Vytvoření dočasného souboru
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            yaml.dump(test_config, f)
            temp_path = f.name
        
        try:
            # Test načtení
            with open(temp_path, 'r') as f:
                loaded_config = yaml.safe_load(f)
            
            # Validace struktury
            assert 'tenant_id' in loaded_config
            assert 'database' in loaded_config
            assert 'processing' in loaded_config
            assert 'features' in loaded_config
            
            # Validace processing parametrů
            processing = loaded_config['processing']
            assert processing['batch_size'] > 0
            assert processing['candidate_limit'] >= processing['rerank_limit']
            assert processing['cache_ttl_days'] > 0
            
        finally:
            os.unlink(temp_path)
    
    def test_tenant_config_loading_error_handling(self):
        """Test error handling při načítání konfigurace - jako záchytka při chybě čtení"""
        # Test neexistujícího souboru
        result = get_tenant_yaml_config("nonexistent_tenant_12345")
        assert result is None
        
        # Test s neplatným YAML by vyžadoval vytvoření testovacího souboru
        # což je mimo rozsah tohoto testu


class TestParameterValidation:
    """
    📏 Testy validace parametrů - jako kontrola rozměrů před montáží
    
    Testuje validaci vstupních parametrů funkcí
    """
    
    def test_batch_size_validation(self):
        """Test validace batch size - jako kontrola velikosti nákladu"""
        valid_sizes = [1, 5, 10, 15, 20]
        invalid_sizes = [0, -1, 100, 500]
        
        for size in valid_sizes:
            assert size > 0, f"Batch size {size} by měl být pozitivní"
            assert size <= 50, f"Batch size {size} je příliš velký"
        
        for size in invalid_sizes:
            if size <= 0:
                assert size <= 0, f"Batch size {size} je neplatný (negativní/nula)"
            elif size > 50:
                print(f"⚠️ Batch size {size} je možná příliš velký pro efektivní zpracování")
    
    def test_concurrency_limits(self):
        """Test limitů concurrency - jako kontrola počtu pracovníků"""
        # Rozumné limity pro různé prostředí
        test_limits = {
            'development': 5,
            'testing': 10, 
            'staging': 15,
            'production': 20
        }
        
        for env, limit in test_limits.items():
            assert limit > 0, f"Concurrency limit pro {env} musí být pozitivní"
            assert limit <= 50, f"Concurrency limit {limit} pro {env} může být příliš vysoký"
            
            # Výpočet odhadovaného zatížení
            estimated_load = limit * ULTRA_BATCH_SIZE
            print(f"{env}: concurrency={limit}, estimated_load={estimated_load}")
    
    def test_timeout_configuration(self):
        """Test konfigurace timeoutů - jako nastavení časovačů"""
        # Doporučené timeouty pro různé operace
        timeouts = {
            'qdrant_query': 30.0,
            'neo4j_query': 15.0,
            'gemini_api': 60.0,
            'batch_preparation': 45.0,
            'cache_check': 10.0
        }
        
        for operation, timeout in timeouts.items():
            assert timeout > 0, f"Timeout pro {operation} musí být pozitivní"
            assert timeout < 300, f"Timeout {timeout}s pro {operation} je příliš dlouhý"
            
            # Kontrola logických vztahů
            if operation == 'gemini_api':
                assert timeout >= timeouts['qdrant_query'], \
                    "Gemini timeout by měl být delší než Qdrant timeout"


class TestProcessingMetricsConfiguration:
    """
    📊 Testy konfigurace metrik - jako nastavení měřicích přístrojů
    
    Testuje správné nastavení a funkcionalnost metrik
    """
    
    def test_metrics_initialization_defaults(self):
        """Test výchozích hodnot metrik - jako zkontrolování nových měřidel"""
        metrics = ProcessingMetrics()
        
        # Všechny číselné metriky by měly být inicializovány na 0
        numeric_fields = [
            'start_time', 'neo4j_query_time', 'total_gemini_time', 'qdrant_search_time',
            'gemini_api_calls', 'gemini_batch_calls', 'avg_gemini_time', 'errors',
            'processed_products', 'saved_results', 'cache_hits', 'cache_misses',
            'products_processed', 'products_skipped'
        ]
        
        for field in numeric_fields:
            value = getattr(metrics, field)
            assert isinstance(value, (int, float)), f"Field {field} by měl být číselný"
            assert value == 0 or value == 0.0, f"Field {field} by měl být inicializován na 0"
    
    def test_metrics_calculation_safety(self):
        """Test bezpečnosti výpočtů metrik - jako pojistky proti přetížení"""
        metrics = ProcessingMetrics()
        
        # Test dělení nulou
        metrics.total_gemini_time = 0.0
        metrics.gemini_api_calls = 0
        
        # Tento výpočet by neměl hodit chybu
        if metrics.gemini_api_calls > 0:
            avg_time = metrics.total_gemini_time / metrics.gemini_api_calls
        else:
            avg_time = 0.0
        
        assert avg_time == 0.0
        
        # Test s platnými hodnotami
        metrics.total_gemini_time = 30.0
        metrics.gemini_api_calls = 10
        avg_time = metrics.total_gemini_time / metrics.gemini_api_calls
        assert avg_time == 3.0


class TestConfigurationRecommendations:
    """
    💡 Doporučení pro konfiguraci - jako rady od zkušených mechaniků
    
    Poskytuje doporučení pro optimální nastavení
    """
    
    def test_environment_recommendations(self):
        """Test doporučení pro různá prostředí - jako nastavení pro různé terény"""
        environments = {
            'development': {
                'batch_size': 2,
                'concurrency': 2,
                'candidate_limit': 20,
                'rerank_limit': 5,
                'cache_ttl_hours': 1
            },
            'testing': {
                'batch_size': 3,
                'concurrency': 3,
                'candidate_limit': 50,
                'rerank_limit': 10,
                'cache_ttl_hours': 6
            },
            'staging': {
                'batch_size': 8,
                'concurrency': 8,
                'candidate_limit': 80,
                'rerank_limit': 15,
                'cache_ttl_hours': 24
            },
            'production': {
                'batch_size': 12,
                'concurrency': 15,
                'candidate_limit': 100,
                'rerank_limit': 20,
                'cache_ttl_hours': 168  # 1 týden
            }
        }
        
        for env, config in environments.items():
            # Validace každé konfigurace
            assert config['batch_size'] <= config['concurrency'], \
                f"V {env}: batch_size by neměl být větší než concurrency"
            
            assert config['candidate_limit'] >= config['rerank_limit'] * 2, \
                f"V {env}: candidate_limit by měl být minimálně 2x větší než rerank_limit"
            
            # Výpočet očekávaného throughput
            estimated_throughput = (config['batch_size'] * config['concurrency']) / 10
            print(f"{env}: očekávaný throughput ~{estimated_throughput:.1f} items/s")
    
    def test_performance_tuning_recommendations(self):
        """Test doporučení pro ladění výkonu - jako tipy pro rychlejší jízdu"""
        recommendations = {
            'memory_optimization': {
                'use_streaming': True,
                'bulk_cache_check': True,
                'neo4j_cache_preload': True,
                'max_batch_size': 15
            },
            'speed_optimization': {
                'ultra_mode': True,
                'aggressive_timeouts': True,
                'reduced_candidate_limit': 40,
                'increased_concurrency': 20
            },
            'stability_optimization': {
                'conservative_batch_size': 5,
                'lower_concurrency': 8,
                'longer_timeouts': True,
                'extensive_error_handling': True
            }
        }
        
        for strategy, settings in recommendations.items():
            print(f"\n🎯 {strategy.upper()}:")
            for setting, value in settings.items():
                print(f"  {setting}: {value}")
            
            # Základní validace doporučení
            assert isinstance(settings, dict), f"Nastavení pro {strategy} by měla být dict"
            assert len(settings) > 0, f"Nastavení pro {strategy} nesmí být prázdné"


if __name__ == "__main__":
    # Spuštění testů konfigurace
    pytest.main([__file__, "-v", "--tb=short"]) 