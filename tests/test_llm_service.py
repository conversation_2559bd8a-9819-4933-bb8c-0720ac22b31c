import pytest
import asyncio
from unittest.mock import AsyncMock, patch
from typing import List
import sys
import os

# Přidání parent directory do Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from llm_service import (
    _create_products_document_for_cache,
    _create_complementary_batch_prompt,
    _initialize_complementary_cache,
    _generate_complementary_batch_with_cache,
    LLMComplementaryRelationship
)
from models import ProductInternal

@pytest.fixture
def sample_products():
    return [
        ProductInternal(
            id="1",
            name="Test Product 1",
            category="Test Category",
            brand="Test Brand",
            description="Test Description 1"
        ),
        ProductInternal(
            id="2",
            name="Test Product 2",
            category="Test Category",
            brand="Test Brand",
            description="Test Description 2"
        )
    ]

@pytest.fixture
def mock_anthropic_client():
    with patch('anthropic.AsyncAnthropic') as mock:
        client = AsyncMock()
        mock.return_value = client
        yield client

def test_create_products_document_for_cache(sample_products):
    """Test vytvoření dokumentu pro cache."""
    doc = _create_products_document_for_cache(sample_products)
    assert "Kontextový seznam produktů pro cache" in doc
    assert "Test Product 1" in doc
    assert "Test Product 2" in doc
    assert "Test Description 1" in doc
    assert "Test Description 2" in doc

def test_create_complementary_batch_prompt(sample_products):
    """Test vytvoření promptu pro dávku."""
    prompt = _create_complementary_batch_prompt(sample_products)
    assert "Produkt ID: 1" in prompt
    assert "Produkt ID: 2" in prompt
    assert "cache kontextu" in prompt

@pytest.mark.asyncio
async def test_initialize_complementary_cache(mock_anthropic_client, sample_products):
    """Test inicializace cache."""
    mock_anthropic_client.messages.create.return_value = AsyncMock()
    
    result = await _initialize_complementary_cache(mock_anthropic_client, sample_products)
    
    assert result is True
    mock_anthropic_client.messages.create.assert_called_once()
    call_args = mock_anthropic_client.messages.create.call_args[1]
    assert call_args['messages'][0]['cache_control']['type'] == 'reusable'
    assert 'anthropic-beta' in call_args['extra_headers']

@pytest.mark.asyncio
async def test_generate_complementary_batch_with_cache(mock_anthropic_client, sample_products):
    """Test generování komplementárních produktů s využitím cache."""
    # Mock odpovědi z API
    mock_response = AsyncMock()
    mock_response.content = [AsyncMock(text='''[{
        "main_product_id": "1",
        "complementary_products": [{
            "complementary_product_id": "2",
            "reason": "Test",
            "relevance_score": 0.9
        }]
    }]''')]
    mock_response.usage = AsyncMock(
        input_tokens=100,
        output_tokens=50,
        cache_creation_input_tokens=0,
        cache_read_input_tokens=80
    )
    mock_anthropic_client.messages.create.return_value = mock_response
    
    result = await _generate_complementary_batch_with_cache(
        mock_anthropic_client,
        [sample_products[0]],  # Použijeme jen první produkt pro test
        sample_products
    )
    
    assert result is not None
    assert len(result) == 1
    assert isinstance(result[0], LLMComplementaryRelationship)
    assert result[0].main_product_id == "1"
    assert len(result[0].complementary_products) == 1
    assert result[0].complementary_products[0].complementary_product_id == "2"
    
    # Ověření, že cache byla použita
    call_args = mock_anthropic_client.messages.create.call_args[1]
    assert call_args['messages'][0]['cache_control']['type'] == 'reusable'
    assert 'anthropic-beta' in call_args['extra_headers']

@pytest.mark.asyncio
async def test_generate_complementary_batch_with_cache_empty_batch(mock_anthropic_client, sample_products):
    """Test generování komplementárních produktů s prázdnou dávkou."""
    result = await _generate_complementary_batch_with_cache(
        mock_anthropic_client,
        [],
        sample_products
    )
    
    assert result == []

@pytest.mark.asyncio
async def test_generate_complementary_batch_with_cache_api_error(mock_anthropic_client, sample_products):
    """Test generování komplementárních produktů při chybě API."""
    mock_anthropic_client.messages.create.side_effect = Exception("API Error")
    
    result = await _generate_complementary_batch_with_cache(
        mock_anthropic_client,
        [sample_products[0]],
        sample_products
    )
    
    assert result is None 