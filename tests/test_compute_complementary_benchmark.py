"""
⚡ Benchmark testy pro compute_complementary.py

Tento soubor obsahuje výkonnostní testy pro měření rychlosti a efektivity:
- Batch processing benchmarks
- Cache performance tests  
- Memory usage monitoring
- Concurrency stress tests

Analogie: Jako měření rychlosti Formula 1 na různých okruzích
"""

import pytest
import asyncio
import time
import psutil
import os
import sys
from unittest.mock import AsyncMock, Mock
from typing import List
import statistics

# Přidání cesty k projektu  
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from batch_scripts.compute_complementary import (
    compute_complementary_ultra_fast_gemini,
    bulk_cache_check,
    ProductCandidate,
    BatchProcessingRequest,
    preload_neo4j_cache,
    NEO4J_CACHE
)


class BenchmarkResults:
    """
    📊 Sběr a analýza benchmark výsledků - jako stopky a měřidla na závodním okruhu
    """
    
    def __init__(self):
        self.times = []
        self.memory_usage = []
        self.throughput = []
    
    def add_measurement(self, duration: float, items_processed: int, memory_mb: float = None):
        """Přidá jedno měření - jako zápis kola na časomíře"""
        self.times.append(duration)
        self.throughput.append(items_processed / duration if duration > 0 else 0)
        if memory_mb:
            self.memory_usage.append(memory_mb)
    
    def get_statistics(self) -> dict:
        """Získá statistiky - jako vyhodnocení všech kol závodu"""
        return {
            "avg_time": statistics.mean(self.times) if self.times else 0,
            "min_time": min(self.times) if self.times else 0,
            "max_time": max(self.times) if self.times else 0,
            "avg_throughput": statistics.mean(self.throughput) if self.throughput else 0,
            "max_throughput": max(self.throughput) if self.throughput else 0,
            "avg_memory_mb": statistics.mean(self.memory_usage) if self.memory_usage else 0,
            "total_measurements": len(self.times)
        }


def get_memory_usage():
    """Získá aktuální využití paměti v MB - jako palivová měrka"""
    process = psutil.Process(os.getpid())
    return process.memory_info().rss / 1024 / 1024


@pytest.mark.benchmark
class TestBatchProcessingBenchmarks:
    """
    🏎️ Benchmarky batch processing - jako testy rychlosti různých vozů
    
    Měří výkon batch zpracování při různých velikostech dávek
    """
    
    @pytest.mark.asyncio
    async def test_gemini_batch_sizes_performance(self):
        """Test výkonu různých velikostí batch - jako porovnání rychlosti s různým zatížením"""
        results = BenchmarkResults()
        
        # Testování různých velikostí batch (1, 5, 10, 15, 20)
        batch_sizes = [1, 5, 10, 15, 20]
        
        for batch_size in batch_sizes:
            # Příprava mock Gemini s realistickou latencí
            mock_gemini = AsyncMock()
            mock_response = Mock()
            
            # Simulace odpovědi pro všechny produkty v batchi
            gemini_result = {}
            for i in range(batch_size):
                gemini_result[f"product_{i}"] = [
                    {"id": f"comp_{i}_1", "score": 0.9},
                    {"id": f"comp_{i}_2", "score": 0.8}
                ]
            
            mock_response.text = str(gemini_result).replace("'", '"')
            mock_gemini.generate_content_async.return_value = mock_response
            
            # Příprava batch požadavků
            batch_requests = []
            for i in range(batch_size):
                candidates = [
                    ProductCandidate(f"cand_{i}_1", {"name": f"Candidate {i}_1"}, 0.9, "Cat1"),
                    ProductCandidate(f"cand_{i}_2", {"name": f"Candidate {i}_2"}, 0.8, "Cat1")
                ]
                batch_requests.append(
                    BatchProcessingRequest(f"product_{i}", {"name": f"Product {i}"}, candidates)
                )
            
            # Simulace latence (reálná API má vždy nějakou latenci)
            async def mock_generate_with_latency(*args, **kwargs):
                await asyncio.sleep(0.1 + batch_size * 0.01)  # Lineární růst latence
                return mock_response
            
            mock_gemini.generate_content_async.side_effect = mock_generate_with_latency
            
            # Měření výkonu
            memory_before = get_memory_usage()
            start_time = time.time()
            
            result = await compute_complementary_ultra_fast_gemini(
                mock_gemini, batch_requests, rerank_limit=5
            )
            
            end_time = time.time()
            memory_after = get_memory_usage()
            
            duration = end_time - start_time
            memory_diff = memory_after - memory_before
            
            results.add_measurement(duration, batch_size, memory_diff)
            
            # Ověření, že výsledek odpovídá očekávání
            assert len(result) == batch_size
            
            print(f"Batch size {batch_size}: {duration:.3f}s, "
                  f"throughput: {batch_size/duration:.1f} items/s, "
                  f"memory: +{memory_diff:.1f}MB")
        
        # Analýza výsledků
        stats = results.get_statistics()
        print(f"\n📊 BATCH PERFORMANCE SUMMARY:")
        print(f"Avg time: {stats['avg_time']:.3f}s")
        print(f"Max throughput: {stats['max_throughput']:.1f} items/s")
        print(f"Avg memory usage: {stats['avg_memory_mb']:.1f}MB")
        
        # Ověření, že větší batch mají lepší throughput/item
        assert stats['max_throughput'] > stats['avg_throughput'] * 0.5
    
    @pytest.mark.asyncio
    async def test_concurrent_batch_processing(self):
        """Test souběžného zpracování více batch - jako závod více vozů současně"""
        concurrency_levels = [1, 3, 5, 10]
        results = BenchmarkResults()
        
        for concurrency in concurrency_levels:
            # Příprava mock Gemini
            mock_gemini = AsyncMock()
            mock_response = Mock()
            mock_response.text = '{"product_1": [{"id": "comp_1", "score": 0.9}]}'
            
            async def mock_generate_with_latency(*args, **kwargs):
                await asyncio.sleep(0.05)  # Konstantní latence
                return mock_response
            
            mock_gemini.generate_content_async.side_effect = mock_generate_with_latency
            
            # Příprava úkolů
            tasks = []
            for i in range(concurrency):
                batch_request = BatchProcessingRequest(
                    "product_1", 
                    {"name": "Test Product"}, 
                    [ProductCandidate("comp_1", {"name": "Comp"}, 0.9, "Cat1")]
                )
                tasks.append(
                    compute_complementary_ultra_fast_gemini(mock_gemini, [batch_request])
                )
            
            # Měření souběžného zpracování
            memory_before = get_memory_usage()
            start_time = time.time()
            
            results_list = await asyncio.gather(*tasks)
            
            end_time = time.time()
            memory_after = get_memory_usage()
            
            duration = end_time - start_time
            memory_diff = memory_after - memory_before
            
            results.add_measurement(duration, concurrency, memory_diff)
            
            # Ověření výsledků
            assert len(results_list) == concurrency
            assert all(len(r) > 0 for r in results_list)
            
            print(f"Concurrency {concurrency}: {duration:.3f}s, "
                  f"throughput: {concurrency/duration:.1f} batches/s, "
                  f"memory: +{memory_diff:.1f}MB")
        
        stats = results.get_statistics()
        print(f"\n⚡ CONCURRENCY PERFORMANCE SUMMARY:")
        print(f"Max concurrency throughput: {stats['max_throughput']:.1f} batches/s")
        print(f"Avg memory per concurrency level: {stats['avg_memory_mb']:.1f}MB")


@pytest.mark.benchmark  
class TestCacheBenchmarks:
    """
    💾 Benchmarky cache výkonu - jako měření rychlosti různých typů paměti
    
    Měří výkon cache operací a jejich efektivitu
    """
    
    @pytest.mark.asyncio
    async def test_neo4j_cache_loading_performance(self):
        """Test rychlosti načítání Neo4j cache - jako benchmark načítání mapy do GPS"""
        results = BenchmarkResults()
        
        # Testování různých velikostí cache dat
        data_sizes = [100, 500, 1000, 2000]
        
        for size in data_sizes:
            # Příprava mock Neo4j dat
            mock_driver = AsyncMock()
            mock_session = AsyncMock()
            mock_result = AsyncMock()
            
            # Generování testovacích dat
            mock_data = []
            for i in range(size):
                mock_data.append({
                    "source_name": f"Category_{i % 50}",  # 50 kategorií, každá má více vztahů
                    "target_name": f"Target_{i}",
                    "weight": 0.5 + (i % 10) * 0.05  # Váhy 0.5-0.95
                })
            
            mock_result.data.return_value = mock_data
            mock_session.run.return_value = mock_result
            mock_driver.session.return_value.__aenter__.return_value = mock_session
            
            # Vyčištění cache
            NEO4J_CACHE.clear()
            
            # Měření načítání
            memory_before = get_memory_usage()
            start_time = time.time()
            
            await preload_neo4j_cache(mock_driver, "test_tenant")
            
            end_time = time.time()
            memory_after = get_memory_usage()
            
            duration = end_time - start_time
            memory_diff = memory_after - memory_before
            
            results.add_measurement(duration, size, memory_diff)
            
            # Ověření načtení
            assert len(NEO4J_CACHE) > 1  # Minimálně nějaké kategorie + _loaded_at
            
            print(f"Cache size {size}: {duration:.3f}s, "
                  f"throughput: {size/duration:.0f} records/s, "
                  f"memory: +{memory_diff:.1f}MB")
        
        stats = results.get_statistics()
        print(f"\n💾 CACHE LOADING SUMMARY:")
        print(f"Max throughput: {stats['max_throughput']:.0f} records/s")
        print(f"Avg loading time: {stats['avg_time']:.3f}s")
    
    @pytest.mark.asyncio
    async def test_bulk_cache_check_performance(self):
        """Test výkonu bulk cache kontroly - jako rychlost kontroly všech klíčů"""
        results = BenchmarkResults()
        
        # Testování různých velikostí bulk kontroly
        bulk_sizes = [50, 100, 500, 1000]
        
        for bulk_size in bulk_sizes:
            mock_qdrant = AsyncMock()
            
            # Simulace cache bodů - 80% platných, 20% expirovaných
            current_time = time.time()
            mock_points = []
            for i in range(int(bulk_size * 0.8)):  # 80% platných
                mock_points.append(
                    Mock(payload={"computed_at": current_time - 100, "product_id": f"valid_{i}"})
                )
            
            # Chunk response (simulace jak Qdrant rozděluje response)
            chunk_size = 200
            chunks = [mock_points[i:i + chunk_size] for i in range(0, len(mock_points), chunk_size)]
            
            mock_qdrant.retrieve.side_effect = chunks
            
            product_ids = [f"product_{i}" for i in range(bulk_size)]
            
            # Měření bulk kontroly
            memory_before = get_memory_usage()
            start_time = time.time()
            
            valid_ids, needs_processing = await bulk_cache_check(
                mock_qdrant, "cache_collection", product_ids, ttl_seconds=86400
            )
            
            end_time = time.time()
            memory_after = get_memory_usage()
            
            duration = end_time - start_time
            memory_diff = memory_after - memory_before
            
            results.add_measurement(duration, bulk_size, memory_diff)
            
            print(f"Bulk size {bulk_size}: {duration:.3f}s, "
                  f"throughput: {bulk_size/duration:.0f} checks/s, "
                  f"valid: {len(valid_ids)}, "
                  f"memory: +{memory_diff:.1f}MB")
        
        stats = results.get_statistics()
        print(f"\n🔍 BULK CACHE CHECK SUMMARY:")
        print(f"Max throughput: {stats['max_throughput']:.0f} checks/s")
        print(f"Avg check time: {stats['avg_time']:.3f}s")


@pytest.mark.benchmark
class TestMemoryUsageBenchmarks:
    """
    🧠 Benchmarky využití paměti - jako monitorování spotřeby paliva
    
    Měří paměťové nároky různých operací
    """
    
    @pytest.mark.asyncio
    async def test_memory_leak_detection(self):
        """Test detekce memory leaks - jako kontrola netěsností v systému"""
        initial_memory = get_memory_usage()
        
        # Simulace opakovaných operací
        mock_gemini = AsyncMock()
        mock_response = Mock()
        mock_response.text = '{"product_1": [{"id": "comp_1", "score": 0.9}]}'
        mock_gemini.generate_content_async.return_value = mock_response
        
        memory_measurements = []
        
        for i in range(20):  # 20 iterací
            batch_request = BatchProcessingRequest(
                f"product_{i}", 
                {"name": f"Product {i}", "description": "A" * 1000},  # Větší data
                [ProductCandidate(f"comp_{i}", {"name": "Test"}, 0.9, "Cat1")]
            )
            
            # Operace
            await compute_complementary_ultra_fast_gemini(mock_gemini, [batch_request])
            
            # Měření paměti
            current_memory = get_memory_usage()
            memory_measurements.append(current_memory)
            
            if i % 5 == 0:
                print(f"Iteration {i}: {current_memory:.1f}MB")
        
        final_memory = get_memory_usage()
        memory_growth = final_memory - initial_memory
        
        print(f"\n🧠 MEMORY USAGE ANALYSIS:")
        print(f"Initial: {initial_memory:.1f}MB")
        print(f"Final: {final_memory:.1f}MB")  
        print(f"Growth: {memory_growth:.1f}MB")
        print(f"Growth per iteration: {memory_growth/20:.2f}MB")
        
        # Ověření, že paměť neroste excesivně (< 1MB per iteraci)
        assert memory_growth / 20 < 1.0, f"Possible memory leak: {memory_growth/20:.2f}MB per iteration"
    
    def test_data_structure_memory_efficiency(self):
        """Test efektivity datových struktur - jako porovnání velikosti různých kontejnerů"""
        import sys
        
        # Test různých způsobů ukládání ProductCandidate
        
        # 1. Dataclass (aktuální)
        candidates_dataclass = []
        for i in range(1000):
            candidates_dataclass.append(
                ProductCandidate(f"id_{i}", {"name": f"Product {i}"}, 0.9, "Category")
            )
        
        # 2. Dict
        candidates_dict = []
        for i in range(1000):
            candidates_dict.append({
                "product_id": f"id_{i}",
                "payload": {"name": f"Product {i}"},
                "qdrant_score": 0.9,
                "category": "Category"
            })
        
        # 3. Tuple
        candidates_tuple = []
        for i in range(1000):
            candidates_tuple.append(
                (f"id_{i}", {"name": f"Product {i}"}, 0.9, "Category")
            )
        
        # Měření velikostí
        size_dataclass = sys.getsizeof(candidates_dataclass)
        size_dict = sys.getsizeof(candidates_dict)
        size_tuple = sys.getsizeof(candidates_tuple)
        
        print(f"\n📦 DATA STRUCTURE MEMORY COMPARISON (1000 items):")
        print(f"Dataclass: {size_dataclass:,} bytes")
        print(f"Dict: {size_dict:,} bytes")
        print(f"Tuple: {size_tuple:,} bytes")
        print(f"Dataclass vs Dict: {(size_dataclass/size_dict)*100:.1f}%")
        print(f"Dataclass vs Tuple: {(size_dataclass/size_tuple)*100:.1f}%")


if __name__ == "__main__":
    # Spuštění benchmark testů
    print("🏎️ Spouštím benchmark testy...")
    pytest.main([__file__, "-v", "-m", "benchmark"]) 