#!/usr/bin/env python3
"""
Skript pro opravu kategorií v Neo4j pro jabkolevne tenant.
Aktualizuje zjednodušené kategorie na hierarchické názvy z Qdrant databáze.
"""

import os
import sys
import asyncio
import logging
from pathlib import Path
from dotenv import load_dotenv
from collections import defaultdict, Counter

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '.'))
sys.path.insert(0, project_root)

# Načtení .env
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# Konfigurace logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

from core.clients import get_qdrant_async_client
from neo4j import GraphDatabase, basic_auth

def get_neo4j_driver():
    """Vytvoří Neo4j driver."""
    neo4j_uri = os.getenv("NEO4J_URI", "bolt://localhost:7687")
    neo4j_user = os.getenv("NEO4J_USER", "neo4j")
    neo4j_password = os.getenv("NEO4J_PASSWORD", "password")
    
    driver = GraphDatabase.driver(
        neo4j_uri,
        auth=basic_auth(neo4j_user, neo4j_password)
    )
    return driver

async def analyze_qdrant_categories(tenant_id: str = "jabkolevne"):
    """
    Analyzuje kategorie v Qdrant databázi a vytvoří mapování.
    
    Analógia: Jako když děláme inventuru skladu - projdeme všechny regály
    a vytvoříme seznam, co kde je uloženo a jak se to jmenuje.
    """
    logger.info(f"🔍 Analyzuji kategorie v Qdrant pro tenant: {tenant_id}")
    
    qdrant_client = get_qdrant_async_client()
    products_collection = f"real_products_{tenant_id}"
    
    try:
        # Získání všech kategorií z Qdrant
        logger.info("📊 Načítám všechny produkty z Qdrant...")
        
        all_categories = set()
        offset = None
        processed = 0
        
        while True:
            scroll_result, next_offset = await qdrant_client.scroll(
                collection_name=products_collection,
                limit=100,
                offset=offset,
                with_payload=True
            )
            
            if not scroll_result:
                break
            
            for point in scroll_result:
                category = point.payload.get("category", "")
                if category:
                    all_categories.add(category)
            
            processed += len(scroll_result)
            offset = next_offset
            
            if processed % 200 == 0:
                logger.info(f"   Zpracováno {processed} produktů, nalezeno {len(all_categories)} unikátních kategorií")
            
            if not next_offset:
                break
        
        logger.info(f"✅ Celkem zpracováno {processed} produktů")
        logger.info(f"✅ Nalezeno {len(all_categories)} unikátních kategorií")
        
        # Analýza hierarchie kategorií
        category_hierarchy = defaultdict(list)
        base_categories = set()
        
        for category in all_categories:
            if " > " in category:
                parts = category.split(" > ")
                base_cat = parts[-1]  # Poslední část (např. "iPhone 11 Pro")
                
                # Hledání základní kategorie
                if "iPhone" in base_cat:
                    main_cat = "iPhone"
                elif "iPad" in base_cat:
                    main_cat = "iPad"
                elif "MacBook" in base_cat or "Mac" in base_cat:
                    main_cat = "MacBook"
                elif "Watch" in base_cat:
                    main_cat = "Apple Watch"
                elif "AirPods" in base_cat:
                    main_cat = "AirPods"
                elif "kabel" in base_cat.lower() or "Lightning" in base_cat or "USB" in base_cat:
                    main_cat = "Kabely (Apple)"
                elif "nabíjec" in base_cat.lower() or "adaptér" in base_cat.lower():
                    main_cat = "Nabíječky (Apple)"
                elif "powerbank" in base_cat.lower():
                    main_cat = "Powerbanky (pro Apple)"
                elif "sklo" in base_cat.lower() or "fólie" in base_cat.lower():
                    if "iPhone" in category:
                        main_cat = "Ochranná skla pro iPhone"
                    elif "iPad" in category:
                        main_cat = "Ochranná skla pro iPad"
                    else:
                        main_cat = "Ochranná skla"
                elif "kryt" in base_cat.lower() or "pouzdro" in base_cat.lower():
                    if "iPhone" in category:
                        main_cat = "Pouzdra pro iPhone"
                    elif "iPad" in category:
                        main_cat = "Pouzdra pro iPad"
                    else:
                        main_cat = "Pouzdra"
                elif "řemínek" in base_cat.lower():
                    main_cat = "Řemínky pro Apple Watch"
                else:
                    main_cat = base_cat  # Použij originální název
                
                category_hierarchy[main_cat].append(category)
                base_categories.add(main_cat)
            else:
                # Kategorie bez hierarchie
                base_categories.add(category)
                category_hierarchy[category].append(category)
        
        logger.info(f"📋 Identifikované základní kategorie:")
        for base_cat in sorted(base_categories):
            count = len(category_hierarchy[base_cat])
            logger.info(f"   {base_cat}: {count} variant")
        
        return category_hierarchy, all_categories
        
    finally:
        await qdrant_client.close()

def update_neo4j_categories(category_mapping: dict, tenant_id: str = "jabkolevne"):
    """
    Aktualizuje kategorie v Neo4j podle mapování z Qdrant.
    
    Analógia: Jako když přejmenováváme složky v počítači podle nové organizace -
    zachováme obsah, ale změníme názvy pro lepší přehlednost.
    """
    logger.info(f"🔄 Aktualizuji kategorie v Neo4j pro tenant: {tenant_id}")
    
    driver = get_neo4j_driver()
    
    try:
        with driver.session() as session:
            # Zjistíme současné kategorie v Neo4j
            logger.info("📊 Načítám současné kategorie z Neo4j...")
            result = session.run(
                f"MATCH (c:Category_{tenant_id}) RETURN c.name as name ORDER BY c.name"
            )
            neo4j_categories = [record["name"] for record in result]
            logger.info(f"✅ Současné Neo4j kategorie: {neo4j_categories}")
            
            # Vytvoříme mapování starých názvů na nové
            updates_needed = []
            
            for old_name in neo4j_categories:
                # Najdeme nejlepší shodu v category_mapping
                best_match = None
                best_score = 0
                
                for base_cat, full_categories in category_mapping.items():
                    # Počet produktů v této kategorii
                    score = len(full_categories)
                    
                    # Bonus za přesnou shodu nebo částečnou shodu
                    if old_name == base_cat:
                        score += 1000  # Přesná shoda
                        best_match = base_cat
                        best_score = score
                        break
                    elif old_name in base_cat or base_cat in old_name:
                        score += 100  # Částečná shoda
                        if score > best_score:
                            best_match = base_cat
                            best_score = score
                
                if best_match and best_match != old_name:
                    updates_needed.append((old_name, best_match))
                    logger.info(f"💡 Mapování: '{old_name}' -> '{best_match}'")
            
            if not updates_needed:
                logger.info("✅ Žádné aktualizace kategorií nejsou potřeba")
                return
            
            # Provedeme aktualizace
            logger.info(f"🔄 Provádím {len(updates_needed)} aktualizací kategorií...")
            
            for old_name, new_name in updates_needed:
                logger.info(f"   Aktualizuji: '{old_name}' -> '{new_name}'")
                
                # Aktualizace názvu kategorie
                session.run(
                    f"MATCH (c:Category_{tenant_id} {{name: $old_name}}) SET c.name = $new_name",
                    old_name=old_name, new_name=new_name
                )
            
            # Ověření aktualizací
            logger.info("✅ Ověřuji provedené aktualizace...")
            result = session.run(
                f"MATCH (c:Category_{tenant_id}) RETURN c.name as name ORDER BY c.name"
            )
            updated_categories = [record["name"] for record in result]
            logger.info(f"📊 Aktualizované kategorie: {updated_categories}")
            
            # Počet vztahů po aktualizaci
            result = session.run(
                f"MATCH (c1:Category_{tenant_id})-[r:RELATED_TO]->(c2:Category_{tenant_id}) RETURN count(r) as total"
            )
            relationship_count = result.single()["total"]
            logger.info(f"📊 Celkem vztahů po aktualizaci: {relationship_count}")
            
    except Exception as e:
        logger.error(f"❌ Chyba při aktualizaci Neo4j kategorií: {e}")
        raise
    finally:
        driver.close()
        logger.info("🔒 Neo4j připojení uzavřeno")

async def main():
    """
    Hlavní funkce pro opravu kategorií.
    
    Analógia: Jako generální reorganizace knihovny - nejprve projdeme všechny knihy,
    roztřídíme je podle témat a pak přeorganizujeme katalog.
    """
    logger.info("🚀 Spouštím opravu kategorií pro jabkolevne...")
    
    try:
        # 1. Analýza Qdrant kategorií
        category_mapping, all_categories = await analyze_qdrant_categories("jabkolevne")
        
        # 2. Ukázka nejčastějších kategorií
        logger.info("🏆 Top kategorie podle počtu variant:")
        sorted_categories = sorted(category_mapping.items(), key=lambda x: len(x[1]), reverse=True)
        for base_cat, variants in sorted_categories[:10]:
            logger.info(f"   {base_cat}: {len(variants)} variant")
            if len(variants) <= 3:
                for variant in variants:
                    logger.info(f"      - {variant}")
        
        # 3. Aktualizace Neo4j
        update_neo4j_categories(category_mapping, "jabkolevne")
        
        logger.info("🎉 === OPRAVA KATEGORIÍ DOKONČENA ===")
        logger.info("💡 Nyní by ultra skript měl najít shody kategorií!")
        
    except Exception as e:
        logger.error(f"💥 Kritická chyba: {e}")
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main()) 