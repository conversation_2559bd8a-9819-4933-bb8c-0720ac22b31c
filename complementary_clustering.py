#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import logging
import random
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Optional

from qdrant_client import QdrantClient
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, Distance, VectorParams, PayloadSchemaType

# Import potřebných knihoven pro klastrování
from sklearn.cluster import KMeans
import numpy as np


def get_products_from_category_with_clustering(self, category: str, limit: int = 50, num_clusters: int = 5) -> List[Dict]:
    """
    Získá produkty z dané kategorie a komplementárních kategorií s použitím klastrování.
    Vytvoří klastry v každé komplementární kategorii a vybere reprezentativní produkty z každého klastru.
    
    Args:
        category: Název kategorie
        limit: Maximální počet produktů (výchozí 50)
        num_clusters: <PERSON>čet klastrů v ka<PERSON><PERSON><PERSON> kategorii (výchozí 5)
        
    Returns:
        List[Dict]: Seznam produktů
    """
    logger = logging.getLogger(f"RealFeedQdrantInitializer_{self.tenant_id}")
    
    try:
        # Získání komplementárních kategorií
        relationship_collection = f"category_relationships_{self.tenant_id}"
        relationships = self.client.scroll(
            collection_name=relationship_collection,
            scroll_filter=Filter(
                must=[FieldCondition(key="source_category", match=MatchValue(value=category))]
            ),
            limit=10,
            with_payload=True
        )
        
        # Seznam všech relevantních kategorií (původní + komplementární)
        all_categories = [category]
        if relationships[0]:
            for rel in relationships[0]:
                comp_category = rel.payload.get("complementary_category") or rel.payload.get("target_category")
                if comp_category:
                    all_categories.append(comp_category)
        
        logger.info(f"Pro kategorii '{category}' nalezeno {len(all_categories)} relevantních kategorií")
        
        # Výběr produktů z každé kategorie s využitím klastrování
        final_products = []
        categories_processed = 0
        
        # Počet produktů k výběru z každé kategorie - rozdělíme limit rovnoměrně
        products_per_category = max(1, limit // len(all_categories))
        
        for cat in all_categories:
            # Najdeme produkty v této kategorii
            cat_products = [p for p in self.products if p.get("category") == cat]
            
            if not cat_products:
                logger.warning(f"Žádné produkty nenalezeny pro kategorii: {cat}")
                continue
                
            logger.info(f"Nalezeno {len(cat_products)} produktů v kategorii '{cat}'")
            
            # Pokud máme příliš málo produktů, nemá smysl klastrovat
            if len(cat_products) <= products_per_category:
                logger.info(f"Příliš málo produktů pro klastrování, beru všechny {len(cat_products)} produktů")
                final_products.extend(cat_products)
                categories_processed += 1
                continue
                
            # Nejprve zjistíme, kolik produktů má embedding
            products_with_embedding = [p for p in cat_products if p.get("embedding") is not None]
            
            if len(products_with_embedding) < 10:  # Příliš málo produktů s embeddingem
                logger.warning(f"Příliš málo produktů s embeddingem v kategorii '{cat}', používám náhodný výběr")
                selected_products = random.sample(cat_products, min(products_per_category, len(cat_products)))
                final_products.extend(selected_products)
                categories_processed += 1
                continue
            
            # Vytvoříme matici embeddingů pro klastrování
            embeddings_matrix = np.array([p["embedding"] for p in products_with_embedding])
            
            # Určíme optimální počet klastrů - použijeme min(počet_produktů/5, num_clusters)
            actual_num_clusters = min(len(products_with_embedding) // 5, num_clusters)
            actual_num_clusters = max(2, actual_num_clusters)  # Minimálně 2 klastry
            
            logger.info(f"Provádím klastrování na {len(products_with_embedding)} produktech do {actual_num_clusters} klastrů")
            
            # Aplikujeme K-means klastrování
            try:
                kmeans = KMeans(n_clusters=actual_num_clusters, random_state=42, n_init=10)
                cluster_labels = kmeans.fit_predict(embeddings_matrix)
                
                # Přiřadíme každému produktu label jeho klastru
                for i, product in enumerate(products_with_embedding):
                    product["cluster"] = int(cluster_labels[i])
                
                # Pro každý klastr vybereme reprezentativní produkty
                cluster_products = []
                
                # Počet produktů k výběru z každého klastru
                products_per_cluster = max(1, products_per_category // actual_num_clusters)
                
                for cluster_id in range(actual_num_clusters):
                    # Najdeme produkty v tomto klastru
                    cluster_members = [p for p in products_with_embedding if p.get("cluster") == cluster_id]
                    
                    if not cluster_members:
                        continue
                        
                    logger.info(f"Klastr {cluster_id} obsahuje {len(cluster_members)} produktů")
                    
                    # Vybereme reprezentativní produkty z klastru
                    # Můžeme použít různé strategie:
                    # 1. Náhodný výběr
                    # 2. Produkty nejblíže centroidu klastru
                    # 3. Kombinace obou přístupů
                    
                    # Zde implementujeme strategii 2 - produkty nejblíže centroidu
                    if len(cluster_members) <= products_per_cluster:
                        # Pokud je málo produktů, vezmeme všechny
                        selected_from_cluster = cluster_members
                    else:
                        # Jinak najdeme produkty nejblíže centroidu
                        centroid = kmeans.cluster_centers_[cluster_id]
                        
                        # Výpočet vzdáleností od centroidu
                        for product in cluster_members:
                            embedding = np.array(product["embedding"])
                            # Kosinová vzdálenost (1 - podobnost, takže menší = bližší)
                            product["centroid_distance"] = 1 - np.dot(embedding, centroid) / (
                                np.linalg.norm(embedding) * np.linalg.norm(centroid)
                            )
                        
                        # Seřadíme podle vzdálenosti (vzestupně)
                        cluster_members.sort(key=lambda p: p.get("centroid_distance", float('inf')))
                        
                        # Vybereme nejbližší produkty + náhodný výběr pro diverzitu
                        top_count = max(1, products_per_cluster // 2)  # První polovina nejbližších
                        random_count = products_per_cluster - top_count  # Zbytek náhodně
                        
                        # Nejbližší produkty
                        top_products = cluster_members[:top_count]
                        
                        # Náhodný výběr ze zbývajících
                        remaining = cluster_members[top_count:]
                        random_products = random.sample(remaining, min(random_count, len(remaining))) if remaining else []
                        
                        selected_from_cluster = top_products + random_products
                    
                    cluster_products.extend(selected_from_cluster)
                
                # Vyčistíme pomocné vlastnosti, které jsme přidali
                for product in cluster_products:
                    if "cluster" in product:
                        del product["cluster"]
                    if "centroid_distance" in product:
                        del product["centroid_distance"]
                
                final_products.extend(cluster_products)
                categories_processed += 1
                
                logger.info(f"Z kategorie '{cat}' vybráno {len(cluster_products)} produktů pomocí klastrování")
                
            except Exception as e:
                logger.error(f"Chyba při klastrování kategorie '{cat}': {str(e)}")
                # Fallback na náhodný výběr při chybě
                selected_products = random.sample(cat_products, min(products_per_category, len(cat_products)))
                final_products.extend(selected_products)
                categories_processed += 1
        
        # Pokud nemáme dostatek produktů, doplníme je náhodným výběrem z hlavní kategorie
        if len(final_products) < limit and len(all_categories) > 0:
            main_cat_products = [p for p in self.products if p.get("category") == category]
            
            if main_cat_products:
                # Vyfiltrujeme produkty, které už máme ve výsledcích
                existing_ids = {p.get("id") for p in final_products}
                remaining_products = [p for p in main_cat_products if p.get("id") not in existing_ids]
                
                # Kolik produktů ještě potřebujeme
                remaining_limit = limit - len(final_products)
                
                if remaining_products and remaining_limit > 0:
                    additional_products = random.sample(
                        remaining_products, 
                        min(remaining_limit, len(remaining_products))
                    )
                    final_products.extend(additional_products)
                    logger.info(f"Doplněno dalších {len(additional_products)} produktů z hlavní kategorie")
        
        # Odebrání duplikátů podle ID
        unique_products = {}
        for product in final_products:
            product_id = product.get("id")
            if product_id and product_id not in unique_products:
                unique_products[product_id] = product
        
        final_products = list(unique_products.values())
        
        # Omezení na požadovaný počet produktů
        if len(final_products) > limit:
            final_products = random.sample(final_products, limit)
        
        logger.info(f"Celkem vybráno {len(final_products)} produktů z {categories_processed} kategorií")
        
        return final_products
        
    except Exception as e:
        logger.error(f"Chyba při výběru produktů s klastrováním pro kategorii '{category}': {str(e)}")
        logger.error(traceback.format_exc())
        
        # Fallback na původní metodu v případě chyby
        logger.info("Používám fallback na původní metodu výběru produktů")
        return self._get_products_from_category(category, limit)


def generate_complementary_products_by_llm_with_clustering(self, products_per_category: int = 10, batch_size: int = 50, max_workers: int = 2, max_retries: int = 3, num_clusters: int = 5) -> bool:
    """
    Generuje komplementární produkty pomocí LLM s využitím klastrování pro výběr reprezentativních produktů.
    
    Vytváří klastry v každé komplementární kategorii a vybírá reprezentativní produkty z každého klastru.
    Tento přístup zajistuje větší diverzitu a reprezentativnost vybraných produktů.
    
    Args:
        products_per_category: Počet produktů z každé kategorie k zpracování
        batch_size: Velikost dávky produktů pro paralelní zpracování
        max_workers: Maximální počet pracovních vláken
        max_retries: Maximální počet pokusů při selhání
        num_clusters: Počet klastrů v každé kategorii (výchozí 5)
        
    Returns:
        bool: True, pokud bylo generování úspěšné
    """
    logger = logging.getLogger(f"RealFeedQdrantInitializer_{self.tenant_id}")
    
    try:
        # Kontrola, zda máme API klíč pro Anthropic
        if not self.anthropic_api_key:
            logger.error("Není nastavený API klíč pro Anthropic, nelze generovat komplementární produkty")
            return False
        
        logger.info(f"Generuji komplementární produkty pomocí LLM s klastrováním (produktů na kategorii: {products_per_category}, velikost dávky: {batch_size}, klastrů: {num_clusters})")
        
        # Vytvoření kolekce pro komplementární produkty, pokud neexistuje
        complementary_collection_name = f"complementary_products_{self.tenant_id}"
        
        try:
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]
            
            if complementary_collection_name not in collection_names:
                logger.info(f"Vytvářím kolekci {complementary_collection_name}")
                
                self.client.create_collection(
                    collection_name=complementary_collection_name,
                    vectors_config={
                        "default": VectorParams(
                            size=1536,
                            distance=Distance.COSINE
                        )
                    }
                )
                
                # Vytvoření schématu payload pro optimalizaci vyhledávání
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="main_product_id",
                    field_schema=PayloadSchemaType.KEYWORD
                )
                
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="main_product_category",
                    field_schema=PayloadSchemaType.KEYWORD
                )
                
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="complementary_product_ids",
                    field_schema=PayloadSchemaType.KEYWORD
                )
                
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="tenant_id",
                    field_schema=PayloadSchemaType.KEYWORD
                )
            else:
                logger.info(f"Kolekce {complementary_collection_name} již existuje")
        except Exception as e:
            logger.error(f"Chyba při vytváření kolekce: {str(e)}")
            return False
        
        # Extrakce kategorií z produktů
        categories = {}
        for product in self.products:
            category = product.get("category", "").strip()
            if category:
                if category not in categories:
                    categories[category] = []
                categories[category].append(product)
        
        if not categories:
            logger.error("Nebyly nalezeny žádné kategorie")
            return False
        
        logger.info(f"Nalezeno {len(categories)} unikátních kategorií")
        
        # Výběr reprezentativních produktů z každé kategorie pomocí klastrování
        selected_products = []
        
        for category, products in categories.items():
            # Použijeme metodu s klastrováním pro výběr produktů
            category_products = get_products_from_category_with_clustering(
                self, 
                category, 
                limit=products_per_category,
                num_clusters=num_clusters
            )
            
            logger.info(f"Kategorie {category}: vybráno {len(category_products)}/{len(products)} produktů pomocí klastrování")
            selected_products.extend(category_products)
        
        # Výpis několika příkladů produktů pro logování
        product_examples = [p.get("id", "unknown") for p in selected_products[:5]]
        logger.info(f"Příklady vybraných produktů: {', '.join(product_examples)}")
        
        # Závěrečný počet produktů
        logger.info(f"Celkem vybráno {len(selected_products)} produktů pro zpracování")
        
        # Uložíme seznam všech produktů, které mají být zpracovány
        all_product_ids = {p.get("id") for p in selected_products}
        processed_product_ids = set()  # Inicializace sady zpracovaných produktů
        
        # Rozdělení produktů do dávek
        product_batches = [selected_products[i:i + batch_size] for i in range(0, len(selected_products), batch_size)]
        logger.info(f"Rozděleno do {len(product_batches)} dávek po {batch_size} produktů (max)")
        
        # Zpracování dávek paralelně (omezení na max_workers kvůli API limitům)
        logger.info(f"Zpracovávám dávky paralelně s max. {max_workers} vlákny")
        
        # Pro každou dávku zavoláme metodu _process_products_batch_with_llm
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Vytvoříme futures pro první kolo zpracování
            futures = {executor.submit(self._process_products_batch_with_llm, batch, max_retries): batch 
                      for batch in product_batches}
            
            # Zpracováváme výsledky
            for future in as_completed(futures):
                batch = futures[future]
                batch_ids = [p.get("id") for p in batch]
                
                try:
                    result = future.result()
                    if result:
                        logger.info(f"Dávka {len(batch)} produktů úspěšně zpracována")
                        # Přidáme zpracované produkty do sady
                        processed_product_ids.update(batch_ids)
                    else:
                        logger.error(f"Chyba při zpracování dávky {len(batch)} produktů")
                except Exception as e:
                    logger.error(f"Výjimka při zpracování dávky: {str(e)}")
                    logger.error(traceback.format_exc())

        # Zkontrolujeme, zda byly zpracovány všechny produkty
        missing_product_ids = all_product_ids - processed_product_ids
        if missing_product_ids:
            logger.warning(f"Nebyly zpracovány všechny produkty. Chybí {len(missing_product_ids)} produktů")
            
            # Zkusíme zpracovat chybějící produkty v samostatné dávce
            if len(missing_product_ids) > 0:
                logger.info(f"Pokusím se zpracovat chybějící produkty ({len(missing_product_ids)}) v druhém kole")
                
                # Najdeme produkty podle ID
                missing_products = [p for p in selected_products if p.get("id") in missing_product_ids]
                
                # Zpracujeme chybějící produkty v menších dávkách
                small_batch_size = min(25, len(missing_products))
                missing_batches = [missing_products[i:i + small_batch_size] 
                                  for i in range(0, len(missing_products), small_batch_size)]
                
                logger.info(f"Rozděleno do {len(missing_batches)} dávek po {small_batch_size} produktů (max)")
                
                # Zpracujeme chybějící dávky
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = {executor.submit(self._process_products_batch_with_llm, batch, max_retries): batch 
                              for batch in missing_batches}
                    
                    for future in as_completed(futures):
                        batch = futures[future]
                        batch_ids = [p.get("id") for p in batch]
                        
                        try:
                            result = future.result()
                            if result:
                                logger.info(f"Dodatečná dávka {len(batch)} produktů úspěšně zpracována")
                                # Přidáme zpracované produkty do sady
                                processed_product_ids.update(batch_ids)
                            else:
                                logger.error(f"Chyba při zpracování dodatečné dávky {len(batch)} produktů")
                        except Exception as e:
                            logger.error(f"Výjimka při zpracování dodatečné dávky: {str(e)}")
        
        # Výsledek: Kolik produktů bylo zpracováno
        final_missing_product_ids = all_product_ids - processed_product_ids
        logger.info(f"Z celkového počtu {len(all_product_ids)} produktů bylo zpracováno {len(processed_product_ids)}")
        logger.info(f"Nezpracováno zůstalo {len(final_missing_product_ids)} produktů")
        
        # Získáme počet záznamů v kolekci pro reportování
        try:
            collection_info = self.client.get_collection(complementary_collection_name)
            logger.info(f"Kolekce {complementary_collection_name} nyní obsahuje {collection_info.points_count} záznamů")
        except Exception as e:
            logger.error(f"Nepodařilo se získat informace o kolekci: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Neočekávaná chyba při generování komplementárních produktů s klastrováním: {str(e)}")
        logger.error(traceback.format_exc())
        return False


# Přejmenujeme původní metodu, aby se zabránilo kolizi jmen
def _internal_generate_complementary_products_by_llm_with_clustering(self, products_per_category: int = 10, batch_size: int = 50, max_workers: int = 2, max_retries: int = 3, num_clusters: int = 5) -> bool:
    """
    Generuje komplementární produkty pomocí LLM s využitím klastrování pro výběr reprezentativních produktů.
    
    Vytváří klastry v každé komplementární kategorii a vybírá reprezentativní produkty z každého klastru.
    Tento přístup zajišťuje větší diverzitu a reprezentativnost vybraných produktů.
    
    Args:
        products_per_category: Počet produktů z každé kategorie k zpracování
        batch_size: Velikost dávky produktů pro paralelní zpracování
        max_workers: Maximální počet pracovních vláken
        max_retries: Maximální počet pokusů při selhání
        num_clusters: Počet klastrů v každé kategorii (výchozí 5)
        
    Returns:
        bool: True, pokud bylo generování úspěšné
    """
    # Tělo metody zůstává stejné jako v původní definici
    logger = logging.getLogger(f"RealFeedQdrantInitializer_{self.tenant_id}")
    
    try:
        # Kontrola, zda máme API klíč pro Anthropic
        if not self.anthropic_api_key:
            logger.error("Není nastavený API klíč pro Anthropic, nelze generovat komplementární produkty")
            return False
        
        logger.info(f"Generuji komplementární produkty pomocí LLM s klastrováním (produktů na kategorii: {products_per_category}, velikost dávky: {batch_size}, klastrů: {num_clusters})")
        
        # Vytvoření kolekce pro komplementární produkty, pokud neexistuje
        complementary_collection_name = f"complementary_products_{self.tenant_id}"
        
        try:
            collections = self.client.get_collections().collections
            collection_names = [collection.name for collection in collections]
            
            if complementary_collection_name not in collection_names:
                logger.info(f"Vytvářím kolekci {complementary_collection_name}")
                
                self.client.create_collection(
                    collection_name=complementary_collection_name,
                    vectors_config={
                        "default": VectorParams(
                            size=1536,
                            distance=Distance.COSINE
                        )
                    }
                )
                
                # Vytvoření schématu payload pro optimalizaci vyhledávání
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="main_product_id",
                    field_schema=PayloadSchemaType.KEYWORD
                )
                
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="main_product_category",
                    field_schema=PayloadSchemaType.KEYWORD
                )
                
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="complementary_product_ids",
                    field_schema=PayloadSchemaType.KEYWORD
                )
                
                self.client.create_payload_index(
                    collection_name=complementary_collection_name,
                    field_name="tenant_id",
                    field_schema=PayloadSchemaType.KEYWORD
                )
            else:
                logger.info(f"Kolekce {complementary_collection_name} již existuje")
        except Exception as e:
            logger.error(f"Chyba při vytváření kolekce: {str(e)}")
            return False
        
        # Extrakce kategorií z produktů
        categories = {}
        for product in self.products:
            category = product.get("category", "").strip()
            if category:
                if category not in categories:
                    categories[category] = []
                categories[category].append(product)
        
        if not categories:
            logger.error("Nebyly nalezeny žádné kategorie")
            return False
        
        logger.info(f"Nalezeno {len(categories)} unikátních kategorií")
        
        # Výběr reprezentativních produktů z každé kategorie pomocí klastrování
        selected_products = []
        
        for category, products in categories.items():
            # Použijeme metodu s klastrováním pro výběr produktů
            category_products = get_products_from_category_with_clustering(
                self, 
                category, 
                limit=products_per_category,
                num_clusters=num_clusters
            )
            
            logger.info(f"Kategorie {category}: vybráno {len(category_products)}/{len(products)} produktů pomocí klastrování")
            selected_products.extend(category_products)
        
        # Výpis několika příkladů produktů pro logování
        product_examples = [p.get("id", "unknown") for p in selected_products[:5]]
        logger.info(f"Příklady vybraných produktů: {', '.join(product_examples)}")
        
        # Závěrečný počet produktů
        logger.info(f"Celkem vybráno {len(selected_products)} produktů pro zpracování")
        
        # Uložíme seznam všech produktů, které mají být zpracovány
        all_product_ids = {p.get("id") for p in selected_products}
        processed_product_ids = set()  # Inicializace sady zpracovaných produktů
        
        # Rozdělení produktů do dávek
        product_batches = [selected_products[i:i + batch_size] for i in range(0, len(selected_products), batch_size)]
        logger.info(f"Rozděleno do {len(product_batches)} dávek po {batch_size} produktů (max)")
        
        # Zpracování dávek paralelně (omezení na max_workers kvůli API limitům)
        logger.info(f"Zpracovávám dávky paralelně s max. {max_workers} vlákny")
        
        # Pro každou dávku zavoláme metodu _process_products_batch_with_llm
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Vytvoříme futures pro první kolo zpracování
            futures = {executor.submit(self._process_products_batch_with_llm, batch, max_retries): batch 
                      for batch in product_batches}
            
            # Zpracováváme výsledky
            for future in as_completed(futures):
                batch = futures[future]
                batch_ids = [p.get("id") for p in batch]
                
                try:
                    result = future.result()
                    if result:
                        logger.info(f"Dávka {len(batch)} produktů úspěšně zpracována")
                        # Přidáme zpracované produkty do sady
                        processed_product_ids.update(batch_ids)
                    else:
                        logger.error(f"Chyba při zpracování dávky {len(batch)} produktů")
                except Exception as e:
                    logger.error(f"Výjimka při zpracování dávky: {str(e)}")
                    logger.error(traceback.format_exc())

        # Zkontrolujeme, zda byly zpracovány všechny produkty
        missing_product_ids = all_product_ids - processed_product_ids
        if missing_product_ids:
            logger.warning(f"Nebyly zpracovány všechny produkty. Chybí {len(missing_product_ids)} produktů")
            
            # Zkusíme zpracovat chybějící produkty v samostatné dávce
            if len(missing_product_ids) > 0:
                logger.info(f"Pokusím se zpracovat chybějící produkty ({len(missing_product_ids)}) v druhém kole")
                
                # Najdeme produkty podle ID
                missing_products = [p for p in selected_products if p.get("id") in missing_product_ids]
                
                # Zpracujeme chybějící produkty v menších dávkách
                small_batch_size = min(25, len(missing_products))
                missing_batches = [missing_products[i:i + small_batch_size] 
                                  for i in range(0, len(missing_products), small_batch_size)]
                
                logger.info(f"Rozděleno do {len(missing_batches)} dávek po {small_batch_size} produktů (max)")
                
                # Zpracujeme chybějící dávky
                with ThreadPoolExecutor(max_workers=max_workers) as executor:
                    futures = {executor.submit(self._process_products_batch_with_llm, batch, max_retries): batch 
                              for batch in missing_batches}
                    
                    for future in as_completed(futures):
                        batch = futures[future]
                        batch_ids = [p.get("id") for p in batch]
                        
                        try:
                            result = future.result()
                            if result:
                                logger.info(f"Dodatečná dávka {len(batch)} produktů úspěšně zpracována")
                                # Přidáme zpracované produkty do sady
                                processed_product_ids.update(batch_ids)
                            else:
                                logger.error(f"Chyba při zpracování dodatečné dávky {len(batch)} produktů")
                        except Exception as e:
                            logger.error(f"Výjimka při zpracování dodatečné dávky: {str(e)}")
        
        # Výsledek: Kolik produktů bylo zpracováno
        final_missing_product_ids = all_product_ids - processed_product_ids
        logger.info(f"Z celkového počtu {len(all_product_ids)} produktů bylo zpracováno {len(processed_product_ids)}")
        logger.info(f"Nezpracováno zůstalo {len(final_missing_product_ids)} produktů")
        
        # Získáme počet záznamů v kolekci pro reportování
        try:
            collection_info = self.client.get_collection(complementary_collection_name)
            logger.info(f"Kolekce {complementary_collection_name} nyní obsahuje {collection_info.points_count} záznamů")
        except Exception as e:
            logger.error(f"Nepodařilo se získat informace o kolekci: {str(e)}")
        
        return True
        
    except Exception as e:
        logger.error(f"Neočekávaná chyba při generování komplementárních produktů s klastrováním: {str(e)}")
        logger.error(traceback.format_exc())
        return False


def generate_complementary_products_by_llm_with_clustering(initializer, products_per_category: int = 10, batch_size: int = 50, max_workers: int = 2, max_retries: int = 3, num_clusters: int = 5) -> bool:
    """
    Wrapper funkce pro volání metody generate_complementary_products_by_llm_with_clustering instance třídy.
    
    Tato funkce slouží jako interface pro skript run_complementary_only.py.
    """
    # Voláme přejmenovanou metodu instance initializer
    return _internal_generate_complementary_products_by_llm_with_clustering.__get__(initializer)(
        products_per_category=products_per_category,
        batch_size=batch_size,
        max_workers=max_workers,
        max_retries=max_retries,
        num_clusters=num_clusters
    )
