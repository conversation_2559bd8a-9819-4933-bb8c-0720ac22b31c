#!/bin/bash
# run_sync_products.sh
# Skript pro spuštění synchronizace produktů mezi feedem a databází

# Výchozí hodnoty
TENANT="filsonstore"
BATCH_SIZE=50
WORKERS=10
FORCE=true

# Zpracování parametrů
while [[ $# -gt 0 ]]; do
  case $1 in
    -t|--tenant)
      TENANT="$2"
      shift 2
      ;;
    -b|--batch-size)
      BATCH_SIZE="$2"
      shift 2
      ;;
    -w|--workers)
      WORKERS="$2"
      shift 2
      ;;
    -f|--force)
      FORCE=true
      shift
      ;;
    -h|--help)
      echo "Použití: $0 [MOŽNOSTI]"
      echo "Synchronizuje produkty mezi feedem a databází."
      echo ""
      echo "Možnosti:"
      echo "  -t, --tenant TENANT_ID    ID tenanta (výchozí: filsonstore)"
      echo "  -b, --batch-size POČET    Velikost dávky pro zpracování produktů (výchozí: 50)"
      echo "  -w, --workers POČET       Počet paralelních workerů pro generování embeddingů (výchozí: 10)"
      echo "  -f, --force               Vynutit aktualizaci všech produktů"
      echo "  -h, --help                Zobrazí tuto nápovědu"
      exit 0
      ;;
    *)
      echo "Neznámý parametr: $1"
      echo "Použijte --help pro zobrazení nápovědy."
      exit 1
      ;;
  esac
done

# Příprava parametrů pro skript
FORCE_PARAM=""
if [ "$FORCE" = true ]; then
  FORCE_PARAM="--force"
fi

# Synchronizace produktů
echo "=== Synchronizace produktů mezi feedem a databází ==="
echo "Tenant: $TENANT"
echo "Batch size: $BATCH_SIZE"
echo "Workers: $WORKERS"
echo "Force update: $FORCE"
echo ""

# Prevence uspání systému během dlouhého běhu
caffeinate -s python sync_products.py --tenant "$TENANT" --batch-size "$BATCH_SIZE" --workers "$WORKERS" $FORCE_PARAM

echo ""
echo "=== Synchronizace produktů dokončena ==="
