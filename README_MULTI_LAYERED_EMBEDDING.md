# Multi-Tenant Processor pro Multi-Vrstvé Embeddingy

Tento skript zpracovává data z různých e-shopů (tenantů) a vytváří multi-vrstvé embeddingy pro doporučování komplementárních produktů.

## Funkce

- Načtení dat z feedu podle typu tenantu
- Vytvoření multi-vrstvých embeddingů s váženými komponentami:
  - Název produktu (váha: 30%)
  - Popis produktu (váha: 40%)
  - Kategorie (váha: 20%)
  - <PERSON><PERSON>čka (váha: 10%)
- Clustering produktů s podobností ≥0.85
- V<PERSON>b<PERSON>r reprezentativních produktů pro každý klastr
- Identifikace komplementárních produktů na základě:
  - Vztahů mezi kategoriemi z předem definovaného grafu
  - Podobnosti embeddingů
- Volitelné zpřesnění doporučení pomocí LLM (Claude nebo OpenAI)
- Uložení výsledků do Qdrantu

## Požadavky

- Python 3.8+
- Přístup k Qdrant serveru na http://localhost:6333
- API klíče pro OpenAI (pro embeddingy)
- Volitelně API klíče pro Anthropic (pro LLM zpřesnění)
- Graf vztahů mezi kategoriemi ve formátu JSON

## Použití

Spusťte skript pomocí bash skriptu s parametry:

```bash
./run_multi_tenant_processing.sh -t [tenant_id] [další parametry]
```

### Parametry

- `-t, --tenant TENANT_ID` - ID tenantu (výchozí: filsonstore)
- `-f, --force` - Vynutit přeindexování, i když kolekce existuje
- `-s, --skip-llm` - Přeskočit zpřesnění pomocí LLM
- `-n, --sample NUMBER` - Omezit počet produktů na zadaný počet (pro testování)
- `-g, --graph-file FILE` - Vlastní cesta k souboru s grafem vztahů kategorií
- `-p, --llm-provider PROVIDER` - Poskytovatel LLM (anthropic/openai, výchozí: anthropic)
- `-m, --openai-model MODEL` - Model OpenAI (gpt4o/gpt35/o3mini, výchozí: gpt4o)
- `-h, --help` - Zobrazí nápovědu

### Příklady

Zpracování dat pro Filsonstore s výchozím nastavením:
```bash
./run_multi_tenant_processing.sh -t filsonstore
```

Zpracování dat pro Avenberg s vynuceným přeindexováním a použitím OpenAI:
```bash
./run_multi_tenant_processing.sh -t avenberg -f -p openai -m gpt35
```

Testovací běh s omezeným počtem produktů a přeskočením LLM:
```bash
./run_multi_tenant_processing.sh -t filsonstore -n 100 -s
```

## Požadovaný formát dat

### Graf vztahů kategorií

Soubor s grafem vztahů kategorií musí být ve formátu JSON a obsahovat vztahy mezi kategoriemi s hodnotami síly vztahu (0-1.0):

```json
{
  "Kategorie1": {
    "Kategorie2": 0.8,
    "Kategorie3": 0.6
  },
  "Kategorie2": {
    "Kategorie1": 0.8,
    "Kategorie4": 0.7
  }
}
```

Skript hledá soubor grafu v následujících umístěních:
1. Explicitně zadaná cesta pomocí parametru `--graph-file`
2. `[tenant_id]_graph_data.json`
3. `category_relationships_[tenant_id].json`
4. `data/state/category_relationships/[tenant_id]_graph_data.json`

## Výstup

Skript vytváří dvě kolekce v Qdrantu:
- `real_products_[tenant_id]` - Kolekce produktů s embeddingy
- `complementary_recommendations_[tenant_id]` - Kolekce doporučení komplementárních produktů

## Logování

Logy jsou uloženy v adresáři `logs` s názvem souboru `multi_layered_embeddings_[tenant_id].log`. 