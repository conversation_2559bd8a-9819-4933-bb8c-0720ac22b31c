api:
  enabled: true
  key_env_prefix: JABKOLEVNE
cache:
  postgres:
    cleanup_interval_hours: 24
    max_age_days: 7
  redis:
    db: 0
    host: redis
    keys:
      embeddings: jabkolevne:{product_id}:embeddings
      metadata: jabkolevne:{product_id}:metadata
      recommendations: jabkolevne:{product_id}:recommendations
    listener:
      batch_size: 100
      enabled: true
      retry_delay: 5
    port: 6379
    ttl: 3600
feed:
  feed_url: https://www.jabkolevne.cz/google.xml
  format: xml
  type: jabkolevne
recommender:
  min_similarity: 0.3
  similar_products_count: 5
