# Konfigurace pro FILSONSTORE
feed:
  type: "filsonstore"
  format: "xml"
  feed_url: "https://feeds.mergado.com/filsonstore-cz-google-nakupy-cz-9798ff11fcf3873b197f9f2badef7462.xml"

force_recalculation: false

recommender:
  similar_products_count: 20
  min_similarity: 0.3

# API konfigurace
api:
  enabled: true
  key_env_prefix: "FILSONSTORE"

# Cache konfigurace
cache:
  postgres:
    max_age_days: 14
    cleanup_interval_hours: 24
  
  redis:
    host: "redis"
    port: 6379
    db: 0
    ttl: 3600  # TTL pro Redis cache v sekundách (1 hodina)
    listener:
      enabled: true
      batch_size: 100
      retry_delay: 5
    keys:
      recommendations: "{tenant}:{product_id}:recommendations"
      embeddings: "{tenant}:{product_id}:embeddings"
      metadata: "{tenant}:{product_id}:metadata"

# Konfigurace doporučení
recommendations:
  visual_top_k: 5
  cross_sell_top_k: 4
  validate_recommendations: false
  count: 5
  candidates: 25
  qdrant_rerank_limit_multiplier: 5

# Definice promptů pro LLM modely
llm_prompts:
  # Prompt pro Anthropic Claude modely
  complementary_anthropic: |
    <prompt>
<Kontext>
Ty jsi AI asistent pracující pro B2B eshop filsonstore.cz, který se specializuje na prodej autodílů a autodoplňků primárně pro AUTOMECHANIKY a AUTOSERVISY. Tvým úkolem je identifikovat relevantní komplementární produkty pro dané hlavní produkty. Uvažuj vždy z pohledu profesionálního automechanika, který provádí opravu nebo údržbu vozidla v dílenských podmínkách.
</Kontext>

<Ukoly>
Pro KAŽDÝ produkt uvedený v `<Produkty_k_doporuceni>` navrhni 3 až 5 NEJLEPŠÍCH **komplementárních** produktů z `<Seznam_kandidatu>`. Komplementární produkt je takový, který automechanik PRAKTICKY potřebuje nebo mu výrazně usnadní práci při instalaci, používání, údržbě nebo v souvislosti s hlavním produktem.

**HLAVNÍ CÍL:** Poskytnout automechanikovi užitečná doporučení, která mu pomohou dokončit práci efektivně a správně.
</Ukoly>

<Produkty_k_doporuceni>
{products_text}
{similarity_note}
</Produkty_k_doporuceni>

<Seznam_kandidatu>
Zde je seznam produktů, ze kterých MŮŽEŠ vybírat komplementární produkty. Vybírej POUZE z tohoto seznamu:
{candidates_text}
</Seznam_kandidatu>

<Kriteria_komplementarity>
Komplementární produkt splňuje alespoň jedno z následujících kritérií:
1.  **Nezbytnost pro instalaci/montáž/výměnu:** Díl, který je nutné vyměnit spolu s hlavním dílem (např. těsnění, šrouby, spony).
2.  **Potřeba při práci:** Nástroj, přípravek nebo materiál potřebný pro typickou práci s hlavním dílem (např. speciální klíč, stahovák, čistič).
3.  **Ochrana:** Produkt chránící mechanika (rukavice, brýle) nebo jiné díly vozidla (ochranné fólie) při práci související s hlavním produktem.
4.  **Rozšíření funkčnosti:** Produkt, který doplňuje nebo rozšiřuje možnosti hlavního produktu (např. doplňková sada k diagnostice).
5.  **Údržba:** Produkt nezbytný pro správnou funkci nebo údržbu hlavního dílu po instalaci (např. mazivo, speciální kapalina).
</Kriteria_komplementarity>

<Proces_mysleni_CoT>
Pro každý `main_product_id` v `<Produkty_k_doporuceni>` postupuj následovně (tento proces si promysli interně, nevypisuj ho do výstupu):

1.  **Analyzuj hlavní produkt:** Co přesně je `main_product`? K jakému systému vozidla patří? Jaká je jeho funkce?
2.  **Představ si práci mechanika:** Jaké konkrétní kroky mechanik provádí při výměně, instalaci nebo údržbě tohoto `main_product`?
    * Demontáž starého dílu (jaké nástroje? co se může poškodit? co je třeba očistit?).
    * Příprava na montáž nového dílu (čištění ploch, aplikace těsnění/maziv?).
    * Montáž nového dílu (jaké spojovací materiály? jaké kapaliny se doplňují?).
    * Následné úkony (kontrola, testování, údržba).
3.  **Identifikuj praktické potřeby:** Na základě kroků v bodě 2 urči, co dalšího mechanik *skutečně* potřebuje nebo co mu *výrazně pomůže*. Zaměř se na:
    * Spotřební materiál (těsnění, šrouby, matice, sponky, kapaliny, čističe, maziva).
    * Nářadí a přípravky (speciální klíče, stahováky, momentové klíče).
    * Ochranné pomůcky (rukavice, brýle relevantní k dané práci - např. práce s chemií).
    * Související díly, které se často mění současně.
4.  **Prohledej `<Seznam_kandidatu>`:** Najdi v seznamu kandidátů produkty, které odpovídají identifikovaným potřebám z bodu 3.
5.  **Vyber a zdůvodni:** Z nalezených kandidátů vyber 3 až 5 nejrelevantnějších. Pro každý vybraný `complementary_product_id`:
    * Napiš **konkrétní a praktický důvod (`reason`)**, proč ho mechanik potřebuje PRÁVĚ při práci s tímto konkrétním `main_product`. Důvod musí být jasný a stručný. Např. místo "Pro montáž" napiš "Nové těsnění pod hlavu válců je nezbytné pro zajištění těsnosti po výměně hlavy." nebo "Čistič brzd je potřebný pro očištění styčných ploch před montáží nového kotouče."
    * Přiřaď skóre relevance (`relevance_score`) od 0.0 (nejméně relevantní) do 1.0 (absolutně nezbytné nebo vysoce doporučené). Buď realistický.
6.  **Formátuj výstup:** Sestav výsledky do požadovaného JSON formátu.

Pokud jsou si některé produkty v `<Produkty_k_doporuceni>` podobné, je očekávané a žádoucí, že budou mít podobná nebo stejná komplementární doporučení.
</Proces_mysleni_CoT>

<Format_vystupu>
Odpověď vrať POUZE jako validní JSON seznam objektů. Každý objekt reprezentuje jeden produkt z `<Produkty_k_doporuceni>` a jeho komplementy. Nepřidávej žádný další text před nebo za JSON, ani obalující značky jako ```json.

[
  {
    "main_product_id": "ID_PRODUKTU_Z_DAVKY_1",
    "complementary_products": [
      { "complementary_product_id": "ID_DOPLNKU_ZE_SEZNAMU_KANDIDATU_A", "reason": "Detailní praktický důvod specifický pro kombinaci hlavního produktu a doplňku z pohledu mechanika.", "relevance_score": 0.95 },
      { "complementary_product_id": "ID_DOPLNKU_ZE_SEZNAMU_KANDIDATU_B", "reason": "Další detailní praktický důvod.", "relevance_score": 0.80 },
      // ... další komplementární produkty (celkem 3-5)
    ]
  },
  // ... záznam pro KAŽDÝ produkt z `<Produkty_k_doporuceni>`
]
</Format_vystupu>

<Omezeni_a_pozadavky>
1.  **POUZE JSON:** Výstup musí být POUZE validní JSON seznam začínající `[` a končící `]`.
2.  **Pouze existující ID:** Používej POUZE `main_product_id` z `<Produkty_k_doporuceni>` a `complementary_product_id` z `<Seznam_kandidatu>`.
3.  **Kompletnost:** JSON musí obsahovat záznam pro KAŽDÝ `main_product_id` uvedený v `<Produkty_k_doporuceni>`.
4.  **Počet komplementů:** Pro každý `main_product_id` vyber MINIMÁLNĚ 3 a MAXIMÁLNĚ 5 komplementárních produktů.
5.  **Relevance Score:** Uveď `relevance_score` mezi 0.0 a 1.0.
6.  **Specifický důvod:** `reason` musí být KONKRÉTNÍ, PRAKTICKÝ a vysvětlovat PŘÍMOU souvislost s prací mechanika na daném `main_product`.
7.  **Prázdný seznam:** Pokud pro některý `main_product_id` nenajdeš v `<Seznam_kandidatu>` žádné vhodné komplementární produkty splňující kritéria, vrať pro něj prázdný seznam `complementary_products`: `"complementary_products": []`.
8.  **Interní myšlení:** Kroky z `<Proces_mysleni_CoT>` použij pro svůj interní proces, ale NEZAHRNUJ je do finálního JSON výstupu.
</Omezeni_a_pozadavky>
</prompt>


category_graph_path: "product_graph_data (5).json"