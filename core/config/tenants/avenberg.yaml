# Konfigurace pro AVENBERG
feed:
  type: "avenberg"
  format: "xml"
  feed_url: "https://www.avenberg.cz/static/_xml/zbozi_export_google.xml"

force_recalculation: false

recommender:
  similar_products_count: 20
  min_similarity: 0.3

# API konfigurace
api:
  enabled: true
  key_env_prefix: "AVENBERG"

# Cache konfigurace
cache:
  postgres:
    max_age_days: 14
    cleanup_interval_hours: 24
  
  redis:
    host: "redis"
    port: 6379
    db: 0
    ttl: 3600  # TTL pro Redis cache v sekundách (1 hodina)
    listener:
      enabled: true
      batch_size: 100
      retry_delay: 5
    keys:
      recommendations: "{tenant}:{product_id}:recommendations"
      embeddings: "{tenant}:{product_id}:embeddings"
      metadata: "{tenant}:{product_id}:metadata"

# Konfigurace doporučení
recommendations:
  visual_top_k: 5
  cross_sell_top_k: 4
  validate_recommendations: false
  count: 5
  candidates: 25
  force_real_time: true
  qdrant_rerank_limit_multiplier: 2

processing:
  batch_size: 10
  max_workers: 3
  retry_count: 3
  retry_multiplier: 2
  retry_initial_delay: 1

# Další specifické konfigurace
custom:
  brand: "AVENBERG"
  language: "cs"
