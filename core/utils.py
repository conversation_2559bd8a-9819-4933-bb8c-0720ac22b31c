# utils.py
import hashlib
import uuid
import logging
import re
import os
import json
from typing import Union, List, Dict, Any, Optional, Tuple
import asyncio
import random
import time
import traceback
import numpy as np
from qdrant_client import AsyncQdrantClient, models
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, MatchAny, NamedVector, PointStruct, ScoredPoint, Distance, RecommendRequest, SearchRequest, QueryRequest, Query
from embedding_strategies import MultiEmbeddingStrategy

# LangChain imports
# from langchain_qdrant import Qdrant
# from langchain_openai import OpenAIEmbeddings
# from langchain_core.documents import Document

logger = logging.getLogger(__name__)

def create_qdrant_id(original_id: str) -> Union[str, int]:
    """
    Vytváří unikátní a platné ID pro Qdrant z původního ID produktu.
    Preferuje UUID, pokud původní ID není integer.
    """
    if isinstance(original_id, int):
        return original_id
    if isinstance(original_id, str) and original_id.isdigit():
        return int(original_id)

    # Použití UUIDv5 pro konzistentní UUID z řetězce
    # Používáme NAMESPACE_DNS jako běžný namespace
    generated_uuid = uuid.uuid5(uuid.NAMESPACE_DNS, str(original_id))
    return str(generated_uuid) # Qdrant podporuje UUID jako string

def normalize_category(category_text: str) -> str:
    """
    Základní normalizace textu kategorie.
    Odstraní přebytečné mezery a standardizuje oddělovač.
    V praxi může být potřeba složitější logika na základě analýzy dat.
    """
    if not category_text:
        return ""
    # Nahradit různé oddělovače za standardní " > "
    normalized = re.sub(r'\s*>\s*|\s*\|\s*|\s*-\s*', ' > ', category_text)
    # Odstranit mezery na začátku/konci a více mezer uvnitř
    normalized = ' '.join(normalized.split()).strip()
    return normalized

def get_parent_category(category_string: Optional[str], delimiter: str = ' > ') -> Optional[str]:
    """Vrátí nadřazenou kategorii (vše kromě poslední úrovně)."""
    if not category_string:
        return None
    parts = category_string.split(delimiter)
    if len(parts) > 1:
        return delimiter.join(parts[:-1])
    return None # Pokud je jen jedna úroveň nebo žádná

def get_category_level(category_string: Optional[str], level: int) -> Optional[str]:
    """
    Získá kategorii na dané úrovni z řetězce kategorií.
    Předpokládá oddělovač ' | ' nebo '>'.

    Args:
        category_string: Řetězec kategorií (např. "Auto > Díly > Brzdy").
        level: Požadovaná úroveň (0 pro první, 1 pro druhou, atd.).

    Returns:
        Název kategorie na dané úrovni nebo None, pokud neexistuje nebo je vstup neplatný.
    """
    if not category_string:
        return None
    
    # Zkusíme různé oddělovače
    parts = []
    if ' | ' in category_string:
        parts = [p.strip() for p in category_string.split(' | ')]
    elif ' > ' in category_string:
         parts = [p.strip() for p in category_string.split(' > ')]
    elif '>' in category_string: # Fallback pro '>' bez mezer
         parts = [p.strip() for p in category_string.split('>')]
    else:
         parts = [category_string.strip()] # Pokud není oddělovač, je to jedna úroveň

    if 0 <= level < len(parts):
        return parts[level]
    else:
        return None

def get_second_last_category(category_string: Optional[str], delimiter: str = ' > ') -> Optional[str]:
    """Vrátí PŘEDPOSLEDNÍ úroveň kategorie (pokud existuje)."""
    if not category_string:
        return None
    parts = category_string.split(delimiter)
    if len(parts) >= 2:
        return parts[-2].strip() # Vrátí předposlední prvek
    return None # Kategorie nemá alespoň 2 úrovně

async def run_with_retry(coro, max_retries: int = 5, initial_delay: float = 2.0, max_delay: float = 120.0, allowed_exceptions=(Exception,)):
    """
    Spustí asynchronní funkci s exponenciálním backoffem při chybě.
    Vylepšeno pro lepší zacházení s rate limity.
    """
    delay = initial_delay
    for attempt in range(1, max_retries + 1):
        try:
            return await coro
        except allowed_exceptions as e:
            # Speciální zacházení s rate limit chybami
            is_rate_limit = "429" in str(e) or "rate limit" in str(e).lower() or "too many requests" in str(e).lower()

            if attempt == max_retries:
                logger.error(f"Max retries ({max_retries}) reached. Error: {e}")
                raise
            else:
                # Pro rate limit chyby použijeme delší základní backoff
                if is_rate_limit:
                    # Použijeme delší zpoždění pro rate limit chyby
                    rate_limit_delay = delay * 2
                    logger.warning(f"Rate limit error on attempt {attempt}/{max_retries}: {e}. Retrying in {rate_limit_delay:.2f} seconds...")
                    await asyncio.sleep(rate_limit_delay)
                    # Agresivnější exponenciální backoff pro rate limit chyby
                    delay = min(max_delay, delay * 3 * (1 + random.uniform(0, 0.2)))
                else:
                    logger.warning(f"Attempt {attempt}/{max_retries} failed: {e}. Retrying in {delay:.2f} seconds...")
                    await asyncio.sleep(delay)
                    # Standardní exponenciální backoff s jitterem
                    delay = min(max_delay, delay * 2 * (1 + random.uniform(-0.1, 0.1)))
        except Exception as e: # Neočekávaná chyba
             logger.error(f"Unexpected error on attempt {attempt}: {e}")
             raise # Neočekávané chyby neopakujeme

async def process_batches_concurrently(items, process_func, batch_size=10, concurrency_limit=5):
    """
    Zpracovává položky v dávkách paralelně s omezením současného zpracování.

    Args:
        items: Seznam položek ke zpracování
        process_func: Asynchronní funkce, která zpracuje dávku
        batch_size: Velikost jedné dávky
        concurrency_limit: Maximální počet současně zpracovávaných dávek

    Returns:
        Seznam výsledků zpracování jednotlivých dávek
    """
    if not items:
        logger.warning("Prázdný seznam položek k zpracování.")
        return []

    # Kontrola vstupních parametrů
    if batch_size <= 0:
        logger.warning(f"Neplatná velikost dávky: {batch_size}, použije se výchozí hodnota 10")
        batch_size = 10

    if concurrency_limit <= 0:
        logger.warning(f"Neplatný limit paralelizmu: {concurrency_limit}, použije se výchozí hodnota 5")
        concurrency_limit = 5

    semaphore = asyncio.Semaphore(concurrency_limit)
    tasks = []
    results = []

    # Vytvoření dávek z položek
    batches = [items[i:i + batch_size] for i in range(0, len(items), batch_size)]
    batch_count = len(batches)
    item_count = len(items)
    logger.info(f"Zpracovávám {item_count} položek v {batch_count} dávkách s omezením paralelizmu {concurrency_limit}")

    # Statistiky pro reportování
    successful_batches = 0
    failed_batches = 0
    total_processed_items = 0

    # Procesní start time
    start_time = time.time()

    async def task_wrapper(batch_index, batch):
        nonlocal successful_batches, failed_batches, total_processed_items

        # Start time pro dávku
        batch_start_time = time.time()
        batch_size = len(batch)

        async with semaphore:
            try:
                logger.debug(f"Začínám zpracování dávky {batch_index+1}/{batch_count} ({batch_size} položek)")
                result = await process_func(batch)

                # Měření času
                batch_duration = time.time() - batch_start_time
                logger.info(f"Dokončena dávka {batch_index+1}/{batch_count} za {batch_duration:.2f}s ({batch_size} položek)")

                # Kontrola výsledku
                if result is None:
                    logger.warning(f"Dávka {batch_index+1} vrátila None, ale nezpůsobila výjimku")
                    # I když je None, pokud nedošlo k výjimce, považujeme za "neúspěšný pokus",
                    # ale ne za selhání, které by mělo inkrementovat failed_batches globálně
                    # failed_batches += 1 # Odstraněno - failed_batches se inkrementuje pouze při výjimce
                else:
                    successful_batches += 1
                    # Pokud výsledek obsahuje seznam, připočteme jeho délku
                    if isinstance(result, list):
                        total_processed_items += len(result)
                    else:
                        # Jinak počítáme jako jednu položku
                        total_processed_items += 1

                return result
            except Exception as e:
                # Detailní loggování při chybě
                batch_duration = time.time() - batch_start_time
                failed_batches += 1 # Inkrementujeme POUZE zde, při výjimce

                logger.error(f"Chyba při zpracování dávky {batch_index+1}/{batch_count} po {batch_duration:.2f}s: {e}")
                logger.error(f"Typ položek v dávce: {type(batch).__name__}")
                try:
                    if isinstance(batch, list) and batch:
                        logger.error(f"První položka v dávce je typu: {type(batch[0]).__name__}")
                except Exception:
                    logger.error("Nelze získat informace o první položce v dávce")

                logger.error(traceback.format_exc())
                return None

    # Vytvoření úloh pro všechny dávky
    for i, batch in enumerate(batches):
        tasks.append(task_wrapper(i, batch))

    # Paralelní zpracování všech úloh
    batch_results = await asyncio.gather(*tasks, return_exceptions=False)

    # Měření celkového času
    total_duration = time.time() - start_time

    # Závěrečné loggování
    logger.info(f"Dokončeno zpracování {batch_count} dávek za {total_duration:.2f}s")
    logger.info(f"Úspěšných dávek: {successful_batches}, neúspěšných: {failed_batches}")
    logger.info(f"Úspěšně zpracováno přibližně {total_processed_items} položek")

    # Procento úspěšnosti
    if batch_count > 0:
        success_rate = (successful_batches / batch_count) * 100
        logger.info(f"Úspěšnost zpracování: {success_rate:.1f}%")

    return batch_results

async def find_complementary_products_multi_embedding(
    client: AsyncQdrantClient,
    collection_name: str,
    product_data: Dict[str, Any],
    embedding_strategy,
    limit: int = 10,
    exclude_id: Optional[Union[str, int]] = None,
    tenant_id: Optional[str] = None,
    category_graph_path: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Najde komplementární produkty k danému produktu pomocí multi-embeddingového přístupu.

    Args:
        client: Asynchronní klient Qdrant.
        collection_name: Název kolekce v Qdrant.
        product_data: Slovník s daty zdrojového produktu (musí obsahovat 'product_id', 'name', 'category').
        embedding_strategy: Instance strategie pro tvorbu embeddingů.
        limit: Maximální počet doporučených produktů.
        exclude_id: ID produktu, které má být vyloučeno (nepoužívá se zde, vylučuje se zdrojový).
        tenant_id: ID tenanta pro logování.
        category_graph_path: Cesta k souboru JSON s grafem kategorií. Pokud není zadána, filtrování podle grafu se nepoužije.

    Returns:
        Seznam doporučených produktů (slovníky s payloadem a skóre).
    """
    source_product_id = product_data.get('product_id')
    source_product_name = product_data.get('name', 'N/A')
    source_category_full = product_data.get('category')
    # source_second_last_category = get_second_last_category(source_category)

    # --- Načtení grafu kategorií (pokud je zadána cesta) ---
    category_graph = {}
    if category_graph_path: # << Načítáme graf, jen pokud byla cesta poskytnuta
        try:
            with open(category_graph_path, 'r') as f:
                category_graph = json.load(f)
            logger.debug(f"Úspěšně načten graf kategorií z {category_graph_path}")
        except FileNotFoundError:
            logger.error(f"Soubor s grafem kategorií nebyl nalezen: {category_graph_path}")
        except json.JSONDecodeError:
            logger.error(f"Chyba při dekódování JSON v souboru: {category_graph_path}")
        except Exception as e:
            logger.error(f"Neočekávaná chyba při načítání grafu kategorií z {category_graph_path}: {e}")
    else:
        logger.warning("Nebyla poskytnuta cesta ke grafu kategorií (category_graph_path). Filtrování podle grafu nebude použito.")
    # -------------------------------------------------------

    # Získání hlavní kategorie zdrojového produktu
    source_main_category = get_category_level(source_category_full, 0)
    # << ÚPRAVA: Hledání komplementárních kategorií s fallbackem na rodiče >>
    allowed_complementary_categories_with_weights: Dict[str, float] = {}
    found_rules_for_cat = None # Kategorie, pro kterou jsme našli pravidla

    if category_graph and source_category_full:
        current_cat_to_check = source_category_full
        while current_cat_to_check: # Dokud máme co kontrolovat
            if current_cat_to_check in category_graph:
                # Našli jsme pravidla!
                allowed_complementary_categories_with_weights = category_graph[current_cat_to_check]
                found_rules_for_cat = current_cat_to_check
                if found_rules_for_cat == source_category_full:
                    if allowed_complementary_categories_with_weights:
                         logger.info(f"Pro zdrojovou kategorii '{source_category_full}' nalezeny tyto komplementární kategorie s váhami: {allowed_complementary_categories_with_weights}")
                    else:
                         logger.info(f"Zdrojová kategorie '{source_category_full}' je v grafu, ale nemá definované žádné komplementární kategorie.")
                else:
                    # Použili jsme fallback
                    if allowed_complementary_categories_with_weights:
                         logger.info(f"Pro zdrojovou kategorii '{source_category_full}' nebyla nalezena pravidla. Používám fallback na rodičovskou kategorii '{found_rules_for_cat}' s pravidly: {allowed_complementary_categories_with_weights}")
                    else:
                         logger.info(f"Pro zdrojovou kategorii '{source_category_full}' nebyla nalezena pravidla. Fallback na rodičovskou kategorii '{found_rules_for_cat}' také nemá definovaná pravidla.")
                break # Ukončíme hledání, našli jsme nejlepší možný match
            else:
                # Přesun na rodičovskou kategorii
                parent_cat = get_parent_category(current_cat_to_check)
                current_cat_to_check = parent_cat
        
        # Pokud jsme prošli celou hierarchii a nic nenašli
        if not found_rules_for_cat:
             logger.warning(f"Pro zdrojovou kategorii '{source_category_full}' ani žádnou její nadřazenou kategorii nebyla nalezena pravidla v grafu {category_graph_path}. Filtrování podle grafu nebude použito.")

    elif category_graph and not source_category_full:
        logger.warning("Zdrojový produkt nemá definovanou kategorii. Filtrování podle grafu nebude použito.")
    # else: # Logování pro případ, že graf nebyl načten, je již výše
    # ------------------------------------------------------------------------------

    # Kontrola, zda se podařilo získat ID
    if source_product_id is None:
        logger.error(f"Chybí 'product_id' v datech zdrojového produktu: {product_data.get('name', 'N/A')}")
        return []

    qdrant_id = create_qdrant_id(source_product_id)

    logger.info(f"Hledám komplementární produkty pro {tenant_id or ''} produkt: {source_product_name} (ID: {source_product_id}, QdrantID: {qdrant_id}, Cat: {source_category_full})")

    # 1. Získání embedding konfigurací
    try:
        embedding_configs = await embedding_strategy.get_embedding_configs()
        logger.info(f"Použiji {len(embedding_configs)} typů embeddingů: {[cfg['vector_name'] for cfg in embedding_configs]}")
    except Exception as e:
        logger.error(f"Nepodařilo se získat embedding konfigurace: {e}")
        return []

    # 2. Získání embeddingů pro zdrojový produkt - prioritně z Qdrant
    query_embeddings_dict = None
    try:
        # Pokus o načtení existujících vektorů z Qdrant
        retrieved_points = await client.retrieve(
            collection_name=collection_name,
            ids=[qdrant_id],
            with_payload=False,
            with_vectors=True
        )
        if retrieved_points and hasattr(retrieved_points[0], 'vector') and retrieved_points[0].vector:
            # Přidána kontrola hasattr pro případ, že by vektor úplně chyběl
            # Qdrant vrací buď list (pro jeden vektor) nebo dict (pro pojmenované)
            if isinstance(retrieved_points[0].vector, dict):
                query_embeddings_dict = retrieved_points[0].vector
                logger.debug(f"Úspěšně načteny existující multi-embeddingy pro produkt {source_product_id} (QdrantID: {qdrant_id})")
            else:
                logger.warning(f"Nalezeny vektory pro produkt {source_product_id}, ale nejsou ve formátu slovníku (pojmenované vektory). Budou přegenerovány.")
        else:
            logger.warning(f"Nebyly nalezeny žádné existující vektory pro produkt {source_product_id} (QdrantID: {qdrant_id}) v Qdrant. Budou vygenerovány.")

    except Exception as e:
        logger.warning(f"Chyba při pokusu o načtení existujících vektorů pro {source_product_id} (QdrantID: {qdrant_id}): {e}. Budou vygenerovány.")

    # Fallback: Pokud se nepodařilo načíst vektory z Qdrant, vygenerujeme je
    if query_embeddings_dict is None:
        logger.info(f"Generuji embeddingy pro produkt {source_product_id} on-the-fly...")
        try:
            query_embeddings_dict = await embedding_strategy.create_embeddings(product_data)
            if not query_embeddings_dict:
                logger.warning(f"Nebyly vygenerovány žádné embeddingy (on-the-fly) pro produkt {source_product_id}")
                return []
        except Exception as e:
            logger.error(f"Chyba při generování embeddingů (on-the-fly) pro produkt {source_product_id}: {e}", exc_info=True)
            return []

    # 3. Paralelní vyhledávání pro každý typ embeddingu
    search_tasks = []
    valid_embedding_configs = [] # Uchováme jen ty konfigurace, pro které máme embedding

    for config in embedding_configs:
        vector_name = config['vector_name']
        query_embedding = query_embeddings_dict.get(vector_name)

        if query_embedding is None:
            logger.warning(f"Chybí query embedding pro typ '{vector_name}', tento typ bude přeskočen.")
            continue

        valid_embedding_configs.append(config) # Přidáme konfiguraci, protože embedding existuje

        # Příprava filtru - VYLOUČENÍ zdrojového produktu
        qdrant_exclude_id = create_qdrant_id(source_product_id)
        search_filter = models.Filter(
            must_not=[
                models.HasIdCondition(has_id=[qdrant_exclude_id])
            ]
        )

        # Zvýšíme limit pro vyhledávání, abychom měli více kandidátů pro filtrování
        search_limit = limit * 10 + 10 # Zvýšen násobitel z 5 na 10

        # Vytvoření korutiny pro vyhledávání
        # Použijeme NamedVector pro specifikaci jména vektoru
        named_query_vector = models.NamedVector(name=vector_name, vector=query_embedding)

        search_request = models.SearchRequest(
            vector=named_query_vector,
            filter=search_filter,
            limit=search_limit,
            with_payload=True, # Chceme payload pro filtrování a zobrazení
            with_vector=False # Samotný vektor nepotřebujeme
        )

        # Přidání úlohy pro run_with_retry
        coro = client.search(
            collection_name=collection_name, 
            query_vector=named_query_vector,
            limit=search_limit,
            with_payload=True
        )
        search_tasks.append(run_with_retry(coro))
        logger.debug(f"Připravena úloha pro vyhledávání pomocí '{vector_name}'")

    # Spuštění vyhledávacích úloh paralelně
    search_results_list = await asyncio.gather(*search_tasks, return_exceptions=True)

    # 4. Agregace a filtrování výsledků
    combined_results: Dict[Union[str, int], Dict[str, Any]] = {}
    processed_count = 0

    for idx, result in enumerate(search_results_list):
        vector_name = valid_embedding_configs[idx]['vector_name'] # Získáme název vektoru pro daný výsledek
        weight = valid_embedding_configs[idx].get('weight', 1.0) # Získáme váhu, výchozí 1.0

        if isinstance(result, Exception):
            logger.error(f"Vyhledávání pomocí '{vector_name}' selhalo: {result}")
            continue

        if not isinstance(result, list):
             logger.warning(f"Očekáván seznam ScoredPoint pro '{vector_name}', ale obdrženo: {type(result)}")
             continue

        logger.debug(f"Zpracovávám {len(result)} výsledků z '{vector_name}' (váha: {weight})")
        processed_count += len(result)

        for scored_point in result:
            if not isinstance(scored_point, ScoredPoint):
                logger.warning(f"Přeskakuji neplatný výsledek typu {type(scored_point)} ve výsledcích '{vector_name}'")
                continue

            point_id = scored_point.id
            score = scored_point.score * weight # Aplikujeme váhu na skóre
            payload = scored_point.payload if scored_point.payload else {}

            # Přeskočíme, pokud payload neobsahuje potřebná data (např. kategorii)
            if not payload or 'category' not in payload:
                # logger.warning(f"Produkt s ID {point_id} nemá payload nebo kategorii, bude přeskočen.")
                continue

            # -- Filtrování podle grafu kategorií --
            complementary_category_full = payload.get('category')
            # Získáme hlavní kategorii doporučeného produktu
            complementary_main_category = get_category_level(complementary_category_full, 0)

            # << ÚPRAVA: Flexibilnější filtrování podle grafu pomocí startswith >>
            category_weight = 1.0
            is_allowed_by_graph = False # Defaultně předpokládáme, že neprojde
            apply_graph_filter = bool(category_graph and allowed_complementary_categories_with_weights)

            if apply_graph_filter:
                # Projdeme všechny povolené kategorie a jejich váhy z grafu
                for allowed_cat, weight in allowed_complementary_categories_with_weights.items():
                    # Zkontrolujeme, zda doporučená kategorie začíná povolenou kategorií
                    # (nebo je s ní shodná)
                    if complementary_category_full == allowed_cat or \
                       complementary_category_full.startswith(allowed_cat + ' > '):
                        category_weight = weight # Použijeme váhu nalezené (nadřazené) kategorie
                        is_allowed_by_graph = True # Produkt prošel grafem
                        logger.debug(f"Produkt ID {point_id} (Kategorie: '{complementary_category_full}') povolen grafem přes '{allowed_cat}' s váhou {category_weight:.2f}.")
                        break # Našli jsme shodu, můžeme přestat hledat

                # Pokud produkt neprošel kontrolou grafu (nezačínal žádnou povolenou kategorií)
                if not is_allowed_by_graph:
                    logger.debug(f"Filtruji produkt ID {point_id} (Name: {payload.get('name', 'N/A')}), jeho kategorie '{complementary_category_full}' nespadá pod žádnou povolenou komplementární kategorii pro '{source_category_full}' podle grafu.")
                    continue # Přeskočíme tento produkt
            # else: # Pokud se filtrování grafem neaplikuje, produkt automaticky prochází
            #     is_allowed_by_graph = True # Není třeba explicitně nastavovat, prostě nepoužijeme `continue`
            #     logger.debug(f"Produkt ID {point_id}: Filtrování podle komplementárních kategorií z grafu není aktivní.")

            # -- Původní filtrování podle předposlední kategorie (Nyní zakomentováno/nahrazeno) --
            # complementary_second_last_category = get_second_last_category(complementary_category_full)
            # if source_second_last_category and complementary_second_last_category and \
            #    source_second_last_category == complementary_second_last_category:
            #     # logger.debug(f"Filtruji produkt ID {point_id} (Name: {payload.get('name', 'N/A')}), shoda předposlední kategorie: '{source_second_last_category}'")
            #     continue # Přeskočíme tento produkt
            # ----------------------------------------------------------------------------------

            # Agregace výsledků
            if point_id not in combined_results:
                combined_results[point_id] = {
                    'payload': payload,
                    'total_score': score,
                    'hit_count': 1,
                    'scores': {vector_name: score}, # Ukládáme i jednotlivá skóre (pro ladění)
                    'category_weight': category_weight # << Přidána váha kategorie z grafu
                }
            else:
                combined_results[point_id]['total_score'] += score
                combined_results[point_id]['hit_count'] += 1
                combined_results[point_id]['scores'][vector_name] = score
                # Aktualizujeme váhu kategorie, pokud je tento produkt nalezen vícekrát?
                # Pravděpodobně ne, váha by měla být stejná pro daný produkt.
                # Pokud by se mohla lišit (což by neměla), můžeme přidat logiku:
                # combined_results[point_id]['category_weight'] = max(combined_results[point_id].get('category_weight', 0), category_weight)

    logger.info(f"Celkem zpracováno {processed_count} surových výsledků z {len(valid_embedding_configs)} typů embeddingů.")
    logger.info(f"Po agregaci a filtrování kategorií máme {len(combined_results)} unikátních kandidátů.")

    # 5. Seřazení a výběr top N produktů
    # Řadíme primárně podle počtu zásahů (sestupně), sekundárně podle (skóre * váha_kategorie)
    # Použijeme .get s výchozí hodnotou 1.0 pro category_weight pro jistotu
    sorted_results = sorted(
        combined_results.values(),
        key=lambda x: (x['hit_count'], x['total_score'] * x.get('category_weight', 1.0)),
        reverse=True
    )

    # Formátování finálního výstupu
    final_recommendations = []
    for item in sorted_results[:limit]:
        formatted_item = item['payload'] # Základem je payload
        # << Změna: Přepočet skóre pro výpis - můžeme ukázat původní skóre embeddingů NEBO upravené skóre? >>
        # Ukážeme původní součet skóre embeddingů a přidáme váhu kategorie zvlášť?
        # Nebo ukážeme finální řadící skóre?
        # Varianta A: Ukázat finální řadící skóre
        # final_score = item['total_score'] * item.get('category_weight', 1.0)
        # formatted_item['score'] = final_score
        # Varianta B: Ukázat původní skóre a váhu zvlášť (pro lepší přehled)
        formatted_item['embedding_score'] = item['total_score']
        formatted_item['category_weight'] = item.get('category_weight', 1.0)

        formatted_item['hit_count'] = item['hit_count'] # Přidáme počet zásahů
        # formatted_item['individual_scores'] = item['scores'] # Můžeme přidat pro ladění
        final_recommendations.append(formatted_item)

    logger.info(f"Nalezeno {len(final_recommendations)} komplementárních produktů po seřazení a limitu.")

    return final_recommendations

async def find_similar_products(
    client: AsyncQdrantClient,
    collection_name: str,
    product_id: Union[str, int],
    embedding_strategy: MultiEmbeddingStrategy, # Potřebujeme pro případné generování
    limit: int = 10,
    tenant_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Najde podobné produkty k danému produktu na základě kosinové podobnosti
    jejich "pure_description" embeddingů.
    
    Args:
        client: Asynchronní klient Qdrant.
        collection_name: Název kolekce v Qdrant.
        product_id: ID zdrojového produktu.
        embedding_strategy: Instance strategie pro tvorbu embeddingů (pro fallback generování).
        limit: Maximální počet doporučených produktů.
        tenant_id: ID tenanta pro logování.
        
    Returns:
        Seznam podobných produktů (slovníky s payloadem a skóre podobnosti).
    """
    qdrant_id = create_qdrant_id(product_id)
    vector_name = "pure_description" # Specifický vektor pro popis

    logger.info(f"Hledám podobné produkty (similar) pro {tenant_id or ''} produkt ID: {product_id} (QdrantID: {qdrant_id}) pomocí vektoru '{vector_name}'")

    # 1. Získání embeddingu popisu pro zdrojový produkt
    source_embedding = None
    try:
        # Pokus o načtení existujícího vektoru z Qdrant
        retrieved_points = await client.retrieve(
                        collection_name=collection_name,
            ids=[qdrant_id],
            with_payload=False,
            with_vectors=[vector_name] # Chceme jen specifický vektor
        )
        if retrieved_points and hasattr(retrieved_points[0], 'vector') and retrieved_points[0].vector and vector_name in retrieved_points[0].vector:
            source_embedding = retrieved_points[0].vector[vector_name]
            logger.debug(f"Úspěšně načten existující '{vector_name}' embedding pro produkt {product_id} (QdrantID: {qdrant_id})")
        else:
            logger.warning(f"Nenalezen existující vektor '{vector_name}' pro produkt {product_id} (QdrantID: {qdrant_id}). Bude vygenerován.")

    except Exception as e:
        logger.warning(f"Chyba při pokusu o načtení '{vector_name}' vektoru pro {product_id} (QdrantID: {qdrant_id}): {e}. Bude vygenerován.")

    # Fallback: Pokud se nepodařilo načíst, vygenerujeme ho
    if source_embedding is None:
        logger.info(f"Generuji '{vector_name}' embedding pro produkt {product_id} on-the-fly...")
        try:
            # Potřebujeme načíst data produktu pro generování
            # TODO: Efektivnější by bylo mít funkci, která vrátí jen potřebná data
            source_product_data_points = await client.retrieve(
                collection_name=collection_name,
                ids=[qdrant_id],
                with_payload=True, # Potřebujeme payload pro generování
                with_vectors=False
            )
            if not source_product_data_points or not source_product_data_points[0].payload:
                 logger.error(f"Nepodařilo se načíst payload pro produkt {product_id} (QdrantID: {qdrant_id}) pro on-the-fly generování.")
                 return []
            
            source_payload = source_product_data_points[0].payload
            
            # Vygenerujeme všechny embeddingy (aktuální strategie to dělá najednou)
            all_embeddings = await embedding_strategy.create_embeddings(source_payload)
            if not all_embeddings or vector_name not in all_embeddings:
                logger.warning(f"Nebyly vygenerovány žádné embeddingy nebo chybí '{vector_name}' pro produkt {product_id}")
                return []
            source_embedding = all_embeddings[vector_name]
        except Exception as e:
            logger.error(f"Chyba při generování '{vector_name}' embeddingu (on-the-fly) pro produkt {product_id}: {e}", exc_info=True)
            return []

    if not source_embedding:
        logger.error(f"Nepodařilo se získat ani vygenerovat '{vector_name}' embedding pro {product_id}")
        return []

    # 2. Vyhledávání podobných produktů pomocí kosinové podobnosti
    search_filter = models.Filter(
        must_not=[
            models.HasIdCondition(has_id=[qdrant_id]) # Vyloučení sebe sama
        ]
        # TODO: Možná přidat filtr na dostupnost (např. must=[FieldCondition(key='availability', match=MatchValue(value='in stock'))])?
    )

    # Použijeme NamedVector pro specifikaci jména vektoru
    named_query_vector = models.NamedVector(name=vector_name, vector=source_embedding)

    try:
        search_result = await client.search(
            collection_name=collection_name,
            query_vector=named_query_vector,
            query_filter=search_filter,
            limit=limit,
            # Explicitně můžeme specifikovat kosinovou vzdálenost, pokud není defaultní pro vektor
            # search_params=models.SearchParams(hnsw_ef=128, exact=False, distance=Distance.COSINE), # Odkomentovat pokud potřeba
            with_payload=True, 
            with_vector=False 
        )

        # Formátování výsledků
        similar_products = []
        for hit in search_result:
            formatted_hit = hit.payload # Základem je payload
            formatted_hit['score'] = hit.score # Přidáme skóre podobnosti
            similar_products.append(formatted_hit)

        logger.info(f"Nalezeno {len(similar_products)} podobných produktů pro {product_id}.")
        return similar_products

    except Exception as e:
        logger.error(f"Chyba při vyhledávání podobných produktů pro {product_id}: {e}", exc_info=True)
        return []