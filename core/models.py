#!/usr/bin/env python3
# core/models.py
from typing import Dict, List, Any, Optional
from pydantic import BaseModel, Field

class ProductInternal(BaseModel):
    """
    Interní reprezentace produktu pro zpracování v systému.
    """
    id: str
    name: str
    description: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    price: Optional[float] = None
    image_url: Optional[str] = None
    url: Optional[str] = None
    tenant_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

class ComplementaryProductRecord(BaseModel):
    """
    Záznam o komplementárním produktu.
    """
    complementary_product_id: str
    name: Optional[str] = None
    category: Optional[str] = None
    brand: Optional[str] = None
    price: Optional[float] = None
    image_url: Optional[str] = None
    url: Optional[str] = None
    relevance_score: float = 0.0
    reason: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

class ProductEmbedding(BaseModel):
    """
    Model pro uložení embeddingů produktu.
    """
    product_id: str
    combined: List[float]
    name_brand: Optional[List[float]] = None
    pure_description: Optional[List[float]] = None
    category_hierarchy: Optional[List[float]] = None
    brand_category: Optional[List[float]] = None
    tenant_id: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)

class ProductRelationship(BaseModel):
    """
    Model pro uložení vztahu mezi produkty.
    """
    source_product_id: str
    target_product_id: str
    relationship_type: str  # např. "complementary", "similar", "alternative"
    score: float
    metadata: Dict[str, Any] = Field(default_factory=dict)

class LLMComplementaryRelationship(BaseModel):
    """
    Model pro uložení vztahu mezi produkty vygenerovaného pomocí LLM.
    """
    source_product_id: str
    target_product_id: str
    source_name: Optional[str] = None
    target_name: Optional[str] = None
    source_category: Optional[str] = None
    target_category: Optional[str] = None
    relevance_score: float = 0.0
    reason: Optional[str] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)
