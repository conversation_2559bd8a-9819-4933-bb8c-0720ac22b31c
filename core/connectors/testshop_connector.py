#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import logging
import pandas as pd
from typing import List, Dict, Any
from .base_connector import BaseConnector


class TestshopConnector(BaseConnector):
    """
    Konektor pro načítání produktů z CSV souboru pro testshop.
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.logger = logging.getLogger("TestshopConnector")
        
        # Získání URL feedu z konfigurace
        feed_config = config.get("feed", {})
        self.feed_url = feed_config.get("feed_url", "")
        
        # Pokud URL začíná prefixem file://, jedná se o lokální soubor
        if self.feed_url.startswith("file://"):
            self.file_path = self.feed_url[7:]  # Odstranění prefixu file://
        else:
            self.file_path = self.feed_url
            
        self.logger.info(f"Inicializován TestshopConnector s cestou k souboru: {self.file_path}")
    
    def get_feed(self) -> List[Dict[str, Any]]:
        """
        Načte data z CSV souboru a vrátí je jako seznam slovníků.
        
        Returns:
            Seznam produktů jako slovníky
        """
        self.logger.info(f"Načítám CSV soubor: {self.file_path}")
        
        try:
            # Ověříme, zda soubor existuje
            if not os.path.exists(self.file_path):
                self.logger.error(f"CSV soubor neexistuje: {self.file_path}")
                return []
            
            # Načteme CSV soubor do pandas DataFrame
            df = pd.read_csv(self.file_path)
            self.logger.info(f"Načteno {len(df)} řádků z CSV souboru")
            
            # Zkontrolujeme přítomnost povinných sloupců
            required_fields = self.get_required_fields()
            missing_fields = [field for field in required_fields if field not in df.columns]
            
            if missing_fields:
                self.logger.error(f"V CSV souboru chybí povinné sloupce: {', '.join(missing_fields)}")
                return []
            
            # Převedeme DataFrame na seznam slovníků
            products = df.to_dict(orient="records")
            
            # Normalizace dat
            for product in products:
                # Převedeme všechny hodnoty na string, pokud nejsou None
                for key, value in product.items():
                    if pd.notna(value):
                        product[key] = str(value)
                    else:
                        product[key] = ""
                
                # Ujistíme se, že id je string
                if "id" in product:
                    product["id"] = str(product["id"])
            
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání CSV souboru: {e}")
            return []
    
    @staticmethod
    def get_required_fields() -> List[str]:
        """
        Vrátí seznam povinných polí v CSV souboru.
        
        Returns:
            Seznam názvů povinných polí
        """
        return ["id", "name", "category", "description"] 