from typing import Dict, List, Any, Optional
import logging
import pandas as pd
import sys
import os
import requests
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup

# Přidáme cestu k root adresáři
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .base_connector import BaseConnector


class FilsonstoreConnector(BaseConnector):
    """
    Konektor pro zpracování XML feedu z filsonstore.cz.
    
    Tento konektor zpracovává produktový feed ve formátu RSS XML s Google prvky,
    dostupný přes Mergado feeds.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Inicializuje konektor s danou konfigurací.
        
        Args:
            config: Konfigurace konektoru, která musí obsahovat:
                - feed_url: URL k XML feedu
        """
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")
        
        # Namespace pro Google feed
        self.ns = {'g': 'http://base.google.com/ns/1.0'}

    def get_feed(self) -> List[Dict[str, Any]]:
        """
        Získá a zpracuje XML feed z Filsonstore.
        
        Returns:
            List slovníků s daty o produktech ve standardizovaném formátu.
        """
        self.logger.info(f"Začínám získávat data z feedu")
        
        try:
            # Stažení XML feedu
            feed_content = self._fetch_feed()
            
            # Parsování XML
            products = self._parse_rss_feed(feed_content)
            
            self.logger.info(f"Úspěšně načteno a transformováno {len(products)} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při zpracování feedu: {e}")
            raise

    def _fetch_feed(self) -> str:
        """
        Stáhne XML feed z URL.
        
        Returns:
            Obsah XML feedu jako string.
        """
        self.logger.info(f"Fetching feed from {self.feed_url}")
        response = requests.get(self.feed_url)
        response.raise_for_status()  # Vyvolá výjimku, pokud není odpověď 2xx
        self.logger.info("Feed fetched successfully")
        return response.text

    def _parse_rss_feed(self, xml_content: str) -> List[Dict[str, Any]]:
        """
        Parsuje RSS XML feed s Google atributy.
        
        Args:
            xml_content: Obsah XML feedu jako string.
            
        Returns:
            List slovníků s daty o produktech.
        """
        self.logger.info("Parsování RSS XML feedu")
        
        try:
            # Parsování XML
            products = []
            soup = BeautifulSoup(xml_content, 'lxml-xml')
            
            # Najdeme všechny položky v feedu
            items = soup.find_all('item')
            self.logger.info(f"Nalezeno {len(items)} položek v RSS XML")
            
            for item in items:
                try:
                    # Extrakce základních údajů
                    product_id = item.find('g:id').text.strip() if item.find('g:id') else None
                    if not product_id:
                        self.logger.warning("Produkt přeskočen - chybí ID")
                        continue
                    
                    # Extrakce a mapování údajů na náš interní formát s ošetřením kódování
                    product = {
                        'ID': product_id,  # Požadované velké písmeno
                        'Name': self._normalize_text(item.find('title').text.strip() if item.find('title') else ''),
                        'Category': self._normalize_text(item.find('g:product_type').text.strip() if item.find('g:product_type') else ''),
                        'Description': self._normalize_text(item.find('g:description').text.strip() if item.find('g:description') else ''),
                        'price': self._parse_price(item.find('g:price').text.strip()) if item.find('g:price') else None,
                        'image_url': item.find('g:image_link').text.strip() if item.find('g:image_link') else '',
                        'product_url': item.find('link').text.strip() if item.find('link') else '',
                        'brand': self._normalize_text(item.find('g:brand').text.strip() if item.find('g:brand') else ''),
                        'Availability': item.find('g:availability').text.strip() if item.find('g:availability') else '',
                        'condition': item.find('g:condition').text.strip() if item.find('g:condition') else '',
                        'mpn': item.find('g:mpn').text.strip() if item.find('g:mpn') else '',
                        'gtin': item.find('g:gtin').text.strip() if item.find('g:gtin') else '',
                    }
                    
                    products.append(product)
                    
                except Exception as e:
                    self.logger.warning(f"Chyba při zpracování položky {product_id if 'product_id' in locals() else 'unknown'}: {e}")
                    continue
            
            # Kontrola formátu dat
            if products:
                # Vytvoření DataFrame pro kontrolu
                df = pd.DataFrame(products)
                
                # Kontrola povinných sloupců
                required_columns = ['ID', 'Name', 'Category']
                missing_columns = [col for col in required_columns if col not in df.columns]
                
                if missing_columns:
                    self.logger.warning(f"Ve feedu chybí tyto sloupce: {missing_columns}")
            
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při parsování RSS feedu: {e}")
            raise
            
    def _normalize_text(self, text: str) -> str:
        """
        Normalizuje text pro správné zobrazení českých znaků.
        
        Args:
            text: Vstupní text k normalizaci
            
        Returns:
            Normalizovaný text s opravenými českými znaky
        """
        if not text:
            return ""
            
        try:
            # Pokud text obsahuje znaky, které naznačují problém s kódováním
            if 'Å' in text or 'Ã' in text or 'Ä' in text or '\xad' in text or '\xbd' in text:
                # Nejprve zkusíme převést znaky reprezentující UTF-8 zakódované v ISO-8859-1/latin1
                try:
                    # Tento přístup předpokládá, že text byl špatně dekódován z UTF-8 do latin1
                    corrected = text.encode('latin1').decode('utf-8', errors='replace')
                    # Kontrola, zda výsledek vypadá lépe (např. obsahuje české znaky)
                    if 'ě' in corrected or 'č' in corrected or 'ř' in corrected or 'š' in corrected:
                        return corrected
                except Exception as e:
                    self.logger.debug(f"První metoda opravy kódování selhala: {e}")
                
                # Druhý pokus: zkusíme převést z Windows CP1250
                try:
                    corrected = text.encode('cp1250').decode('utf-8', errors='replace')
                    if 'ě' in corrected or 'č' in corrected or 'ř' in corrected or 'š' in corrected:
                        return corrected
                except Exception as e:
                    self.logger.debug(f"Druhá metoda opravy kódování selhala: {e}")
                
                # Třetí pokus: některé znaky se specificky nahrazují
                text = text.replace('Å¡', 'š')
                text = text.replace('Å™', 'ř')
                text = text.replace('Å½', 'ž')
                text = text.replace('Ä›', 'ě')
                text = text.replace('Ä\x8d', 'č')
                text = text.replace('Ã¡', 'á')
                text = text.replace('Ã­', 'í')
                text = text.replace('Ãº', 'ú')
                text = text.replace('Å¯', 'ů')
                text = text.replace('Ã½', 'ý')
                text = text.replace('Å\x99', 'ř')
                
            # Další čištění textu
            text = text.replace('\xa0', ' ')  # Nahrazení non-breaking space normálním
            
            return text
        except Exception as e:
            self.logger.warning(f"Chyba při normalizaci textu: {e}")
            return text

    def _parse_price(self, price_text: str) -> Optional[float]:
        """
        Převede textovou cenu (např. '279 CZK') na float.
        
        Args:
            price_text: Textový řetězec ceny.
            
        Returns:
            Cena jako float, nebo None pokud převod selže.
        """
        if not price_text:
            return None
        try:
            # Odstraníme měnu a mezery, nahradíme desetinnou čárku tečkou
            price_str = price_text.split(' ')[0].replace(',', '.') 
            return float(price_str)
        except (ValueError, IndexError) as e:
            self.logger.warning(f"Nepodařilo se převést cenu '{price_text}' na float: {e}")
            return None