# clients.py
import asyncio
import logging
from openai import AsyncOpenAI
from anthropic import AsyncAnthropic
from core.config import settings
from qdrant_client import AsyncQdrantClient, QdrantClient # Async i sync pro různ<PERSON> pot<PERSON>
from typing import Optional
import os
import cohere
import google.generativeai as genai

logger = logging.getLogger(__name__)

# Proměnné pro cachování klientů
_qdrant_async_client: Optional[AsyncQdrantClient] = None
_qdrant_sync_client: Optional[QdrantClient] = None
_openai_client: Optional[AsyncOpenAI] = None
_anthropic_client: Optional[AsyncAnthropic] = None
_cohere_client: cohere.Client = None
_gemini_client: Optional[genai.GenerativeModel] = None

def get_qdrant_async_client() -> AsyncQdrantClient:
    """Vrací (nebo vytváří) asynchronního Qdrant klienta."""
    global _qdrant_async_client
    if _qdrant_async_client is None:
        logger.info(f"Creating AsyncQdrantClient for URL: {settings.qdrant_url} with increased timeout.")
        _qdrant_async_client = AsyncQdrantClient(url=str(settings.qdrant_url), timeout=30.0)
    return _qdrant_async_client

def get_qdrant_sync_client() -> QdrantClient:
    """Vrací (nebo vytváří) synchronního Qdrant klienta."""
    global _qdrant_sync_client
    if _qdrant_sync_client is None:
        logger.info(f"Creating QdrantClient for URL: {settings.qdrant_url}")
        _qdrant_sync_client = QdrantClient(url=str(settings.qdrant_url))
    return _qdrant_sync_client

def get_openai_client() -> AsyncOpenAI:
    """Vrací (nebo vytváří) asynchronního OpenAI klienta."""
    global _openai_client
    if _openai_client is None:
        if not settings.openai_api_key:
            logger.error("OPENAI_API_KEY is not configured.")
            raise ValueError("OPENAI_API_KEY must be set")
        logger.info("Creating AsyncOpenAI client.")
        _openai_client = AsyncOpenAI(api_key=settings.openai_api_key)
    return _openai_client

def get_anthropic_client() -> AsyncAnthropic:
    """Vrací (nebo vytváří) asynchronního Anthropic klienta."""
    global _anthropic_client
    if _anthropic_client is None:
        if not settings.anthropic_api_key:
            logger.error("ANTHROPIC_API_KEY is not configured.")
            raise ValueError("ANTHROPIC_API_KEY must be set")
        logger.info("Creating AsyncAnthropic client.")
        # Poznámka: Prompt Caching (beta) se zde explicitně nepovoluje,
        # pokud to API klienta vyžaduje globálně, bylo by to zde.
        # Zdá se, že novější API to řeší přes parametry volání.
        _anthropic_client = AsyncAnthropic(api_key=settings.anthropic_api_key)
    return _anthropic_client

def get_cohere_client() -> Optional[cohere.Client]:
    """Vrací instanci synchronního Cohere klienta nebo None, pokud není nakonfigurován."""
    global _cohere_client
    if _cohere_client is None:
        api_key = os.getenv("COHERE_API_KEY")
        if not api_key:
            logger.warning("COHERE_API_KEY not found in environment variables. Reranking nebude k dispozici.")
            return None
        logger.info("Initializing Cohere client.")
        _cohere_client = cohere.Client(api_key=api_key)
    return _cohere_client

def get_gemini_client() -> genai.GenerativeModel:
    """Vrací (nebo vytváří) klienta pro Gemini model."""
    global _gemini_client
    if _gemini_client is None:
        api_key = os.getenv("GOOGLE_API_KEY")
        if not api_key:
            logger.error("GOOGLE_API_KEY not found in environment variables.")
            raise ValueError("Missing GOOGLE_API_KEY")
        logger.info("Initializing Google Generative AI client (Gemini).")
        genai.configure(api_key=api_key)
        # Vytvoříme klienta pro specifický model, který budeme používat
        # Změna na požadovaný model 'gemini-2.0-flash'
        _gemini_client = genai.GenerativeModel('gemini-2.0-flash')
        logger.info(f"Gemini client initialized for model: {_gemini_client.model_name}")
    return _gemini_client

async def close_clients():
    """Zavře otevřené asynchronní klienty."""
    global _qdrant_async_client, _openai_client, _anthropic_client, _gemini_client
    if _qdrant_async_client:
        await _qdrant_async_client.close()
        _qdrant_async_client = None
        logger.info("AsyncQdrantClient closed.")
    if _openai_client:
        await _openai_client.close()
        _openai_client = None
        logger.info("AsyncOpenAI client closed.")
    if _anthropic_client:
        await _anthropic_client.close()
        _anthropic_client = None
        logger.info("AsyncAnthropic client closed.")

    # Gemini klient (google-generativeai) nemá explicitní async close metodu.
    # Spojení jsou spravována interně. Stačí uvolnit referenci.
    if _gemini_client:
        _gemini_client = None
        logger.info("Gemini client reference released.")

    # Synchronní Qdrant klient nemá async close
    global _qdrant_sync_client
    if _qdrant_sync_client:
        # qdrant_client library sama spravuje spojení, explicitní close není standardně nutné
        _qdrant_sync_client = None
        logger.info("QdrantClient reference released.")