# state_store.py
from abc import ABC, abstractmethod
from typing import List, Dict, Optional
from data_models import CategoryRelationship, FunctionalContext
import json
import os
import logging
import aiofiles # Pro asynchronní práci se soubory

logger = logging.getLogger(__name__)

class StateStore(ABC):
    """Abstraktní třída pro ukládání a načítání stavu."""

    @abstractmethod
    async def save_category_relationships(self, tenant_id: str, relationships: List[CategoryRelationship]):
        pass

    @abstractmethod
    async def load_category_relationships(self, tenant_id: str) -> List[CategoryRelationship]:
        pass

    @abstractmethod
    async def check_category_relationships_exist(self, tenant_id: str) -> bool:
        pass

    @abstractmethod
    async def save_functional_contexts(self, tenant_id: str, contexts: List[FunctionalContext]):
        pass

    @abstractmethod
    async def load_functional_contexts(self, tenant_id: str) -> List[FunctionalContext]:
        pass

    @abstractmethod
    async def check_functional_contexts_exist(self, tenant_id: str) -> bool:
        pass

class FileStateStore(StateStore):
    """Implementace StateStore používající lokální JSON soubory."""

    def __init__(self, base_dir: str):
        self.base_dir = base_dir
        self.rel_dir = os.path.join(base_dir, "category_relationships")
        self.ctx_dir = os.path.join(base_dir, "functional_contexts")
        os.makedirs(self.rel_dir, exist_ok=True)
        os.makedirs(self.ctx_dir, exist_ok=True)
        logger.info(f"FileStateStore initialized with base directory: {base_dir}")

    def _get_rel_path(self, tenant_id: str) -> str:
        return os.path.join(self.rel_dir, f"{tenant_id}_relationships.json")

    def _get_ctx_path(self, tenant_id: str) -> str:
        return os.path.join(self.ctx_dir, f"{tenant_id}_contexts.json")

    async def save_category_relationships(self, tenant_id: str, relationships: List[CategoryRelationship]):
        path = self._get_rel_path(tenant_id)
        try:
            # Konverze Pydantic modelů na dict pro JSON
            data_to_save = [rel.model_dump(mode='json') for rel in relationships]
            async with aiofiles.open(path, "w", encoding="utf-8") as f:
                await f.write(json.dumps(data_to_save, ensure_ascii=False, indent=2))
            logger.info(f"Saved {len(relationships)} category relationships to {path}")
        except Exception as e:
            logger.error(f"Failed to save category relationships to {path}: {e}")
            raise # Znovu vyhodit výjimku, aby volající věděl o selhání

    async def load_category_relationships(self, tenant_id: str) -> List[CategoryRelationship]:
        path = self._get_rel_path(tenant_id)
        if not os.path.exists(path):
            logger.info(f"Category relationships file not found for tenant {tenant_id} at {path}")
            return []
        try:
            async with aiofiles.open(path, "r", encoding="utf-8") as f:
                content = await f.read()
                data = json.loads(content)
            
            # Upraveno pro zpracování formátu dat, kde data jsou ve struktuře:
            # { "tenant_id": "...", "relationships": { "kategorie_A": [ {"complementary_category": "kategorie_B", ...} ] } }
            relationships = []
            
            if isinstance(data, dict) and "relationships" in data:
                # Nový formát s vnořenou strukturou
                rel_dict = data.get("relationships", {})
                for source_category, targets in rel_dict.items():
                    for target_info in targets:
                        rel = CategoryRelationship(
                            source_category=source_category,
                            target_category=target_info.get("complementary_category"),
                            strength=target_info.get("strength", 0.5),
                            reason=target_info.get("reason", "")
                        )
                        relationships.append(rel)
            # Přímý mapping ze souboru {source: {target: strength, ...}, ...}
            elif isinstance(data, dict):
                for source_category, targets in data.items():
                    # Skip if not a mapping to strengths
                    if not isinstance(targets, dict):
                        continue
                    for target_category, strength in targets.items():
                        try:
                            weight = float(strength)
                        except Exception:
                            logger.warning(f"Skipping invalid strength for relationship {source_category} -> {target_category}: {strength}")
                            continue
                        rel = CategoryRelationship(
                            source_category=source_category,
                            target_category=target_category,
                            strength=weight,
                            reason=""
                        )
                        relationships.append(rel)
                logger.info(f"Loaded {len(relationships)} category relationships from direct mapping JSON")
            elif isinstance(data, list):
                # Původní formát, kde každý záznam je samostatný CategoryRelationship
                try:
                    relationships = [CategoryRelationship(**item) for item in data]
                except Exception as e:
                    logger.error(f"Chyba při převodu položek na CategoryRelationship: {e}")
                    return []
            else:
                logger.error(f"Neznámý formát dat v souboru {path}")
                return []
                
            logger.info(f"Loaded {len(relationships)} category relationships from {path}")
            return relationships
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON from {path}: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to load category relationships from {path}: {e}")
            return [] # Vracíme prázdný seznam při chybě

    async def check_category_relationships_exist(self, tenant_id: str) -> bool:
        path = self._get_rel_path(tenant_id)
        return os.path.exists(path)

    async def save_functional_contexts(self, tenant_id: str, contexts: List[FunctionalContext]):
        path = self._get_ctx_path(tenant_id)
        try:
            data_to_save = [ctx.model_dump(mode='json') for ctx in contexts]
            async with aiofiles.open(path, "w", encoding="utf-8") as f:
                await f.write(json.dumps(data_to_save, ensure_ascii=False, indent=2))
            logger.info(f"Saved {len(contexts)} functional contexts to {path}")
        except Exception as e:
            logger.error(f"Failed to save functional contexts to {path}: {e}")
            raise

    async def load_functional_contexts(self, tenant_id: str) -> List[FunctionalContext]:
        path = self._get_ctx_path(tenant_id)
        if not os.path.exists(path):
            logger.info(f"Functional contexts file not found for tenant {tenant_id} at {path}")
            return []
        try:
            async with aiofiles.open(path, "r", encoding="utf-8") as f:
                content = await f.read()
                data = json.loads(content)
            ctxs = [FunctionalContext(**item) for item in data]
            logger.info(f"Loaded {len(ctxs)} functional contexts from {path}")
            return ctxs
        except json.JSONDecodeError as e:
            logger.error(f"Failed to decode JSON from {path}: {e}")
            return []
        except Exception as e:
            logger.error(f"Failed to load functional contexts from {path}: {e}")
            return []

    async def check_functional_contexts_exist(self, tenant_id: str) -> bool:
        path = self._get_ctx_path(tenant_id)
        return os.path.exists(path)

# V budoucnu můžete přidat DBStateStore(StateStore): ...