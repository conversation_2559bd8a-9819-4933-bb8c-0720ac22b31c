# embedding_service.py
import asyncio
import logging
from typing import List, Tuple, Optional, Dict, Any
import unicodedata

from openai import AsyncOpenAI
from qdrant_client import AsyncQdrantClient
from qdrant_client.http.models import PointStruct

from core.config import settings
from core.utils import run_with_retry, process_batches_concurrently
from core.models import ProductInternal
from core.clients import get_openai_client

logger = logging.getLogger(__name__)

class EmbeddingService:
    """
    Třída pro práci s embeddingy produktů.
    Obaluje funkce pro generování embeddingů pomocí OpenAI API.
    """
    
    def __init__(self, openai_client: AsyncOpenAI = None):
        """
        Inicializuje službu pro embeddingy.
        
        Args:
            openai_client: Klient pro OpenAI API, nebo None pro pozdější inicializaci
        """
        self.openai_client = openai_client
        
    async def generate_and_update_embeddings(self, 
                                            products: List[ProductInternal]
    ) -> Dict[str, List[float]]:
        """
        Generuje embeddingy pro produkty, které je nemají.
        
        Args:
            products: Seznam produktů pro generování embeddingů
            
        Returns:
            Slovník mapující ID produktů na jejich embeddingy
        """
        if not self.openai_client:
            raise ValueError("OpenAI client is not initialized")
            
        return await generate_and_update_embeddings(
            openai_client=self.openai_client,
            products=products
        )
        
    async def get_embedding(self, text: str) -> List[float]:
        """
        Získá embedding pro jeden text.
        
        Args:
            text: Text pro který chceme embedding
            
        Returns:
            Vektor embeddingu
        """
        if not self.openai_client:
            raise ValueError("OpenAI client is not initialized")
            
        # Použijeme existující funkci pro dávku, ale s jedním textem
        result = await _generate_embeddings_batch_openai(
            client=self.openai_client,
            texts=[text]
        )
        
        # Pokud je výsledek platný, vrátíme ho, jinak dummy embedding
        if result and result[0]:
            return result[0]
        else:
            logger.warning(f"Failed to generate embedding for text: {text[:50]}...")
            # Vrátíme dummy embedding
            return [0.1] * settings.embedding_dimensions

def _normalize_for_embedding(text: str) -> str:
    """Normalizuje text pro embedding (lowercase, odstranění diakritiky)."""
    # Převod na malá písmena
    text = text.lower()
    # Odstranění diakritiky
    text = ''.join(c for c in unicodedata.normalize('NFD', text) if unicodedata.category(c) != 'Mn')
    return text

def _create_text_for_embedding(product: ProductInternal) -> str:
    """Vytvoří textovou reprezentaci produktu pro embedding."""
    parts = [
        f"Název: {product.name}",
        f"Kategorie: {product.category}",
    ]
    if product.brand:
        parts.append(f"Značka: {product.brand}")
    if product.description:
        # Omezení délky popisu pro embedding
        desc = product.description[:500] + "..." if len(product.description) > 500 else product.description
        parts.append(f"Popis: {desc}")

    full_text = "\n".join(parts)
    return _normalize_for_embedding(full_text)

async def _generate_embeddings_batch_openai(
    client: AsyncOpenAI,
    texts: List[str]
) -> List[Optional[List[float]]]:
    """Generuje embeddings pro dávku textů pomocí OpenAI API."""
    if not texts:
        return []

    logger.debug(f"Generating embeddings for batch of {len(texts)} texts.")
    try:
        # Použití funkce s retry logikou
        response = await run_with_retry(
            client.embeddings.create(
                model=settings.embedding_model,
                input=texts
            ),
            allowed_exceptions=(openai.RateLimitError, openai.APIConnectionError, openai.APITimeoutError, openai.InternalServerError)
        )
        # Získání embeddings z odpovědi
        embeddings = [item.embedding for item in response.data]
        if len(embeddings) != len(texts):
             logger.warning(f"Mismatch in embedding results count: expected {len(texts)}, got {len(embeddings)}")
             # Tuto situaci by API nemělo dopustit, ale pro jistotu - vrátíme None pro chybějící
             # V praxi spíše vyhodit chybu
             return [None] * len(texts)
        logger.debug(f"Successfully generated {len(embeddings)} embeddings.")
        return embeddings
    except Exception as e:
        logger.error(f"Failed to generate embeddings batch after retries: {e}")
        # Vrátíme None pro všechny texty v dávce při selhání
        return [None] * len(texts)

async def generate_and_update_embeddings(
    openai_client: AsyncOpenAI,
    products: List[ProductInternal]
) -> Dict[str, List[float]]:
    """
    Generuje embeddings pro produkty, které je nemají, a vrací slovník {product.id: embedding}.
    Používá dummy embedding jako fallback.
    """
    embeddings_map: Dict[str, List[float]] = {}
    products_to_embed: List[ProductInternal] = []
    dummy_embedding = [0.1] * settings.embedding_dimensions

    # Identifikace produktů bez embeddingu
    for product in products:
        if product.embedding is None:
            products_to_embed.append(product)
        else:
            # Pokud už embedding má, použijeme ho
            embeddings_map[product.id] = product.embedding

    if not products_to_embed:
        logger.info("All products already have embeddings.")
        return embeddings_map

    logger.info(f"Generating embeddings for {len(products_to_embed)} products.")

    product_texts = {product.id: _create_text_for_embedding(product) for product in products_to_embed}
    product_ids_ordered = [product.id for product in products_to_embed]
    texts_ordered = [product_texts[pid] for pid in product_ids_ordered]

    # Asynchronní funkce pro zpracování jedné dávky textů
    async def process_embedding_batch(text_batch: List[str]) -> List[Optional[List[float]]]:
        return await _generate_embeddings_batch_openai(openai_client, text_batch)

    # Zpracování v dávkách s omezenou konkurencí
    batch_results_lists = await process_batches_concurrently(
        items=texts_ordered,
        process_func=process_embedding_batch,
        batch_size=settings.embedding_batch_size,
        concurrency_limit=settings.embedding_max_workers # Limit konkurentních API volání
    )

    # Zpracování výsledků
    all_embeddings_ordered: List[Optional[List[float]]] = []
    for batch_result in batch_results_lists:
        if batch_result: # Pokud dávka neselhal celá
             all_embeddings_ordered.extend(batch_result)
        else:
             # Pokud dávka selhala (process_batches_concurrently vrátilo None), přidáme None pro všechny
             # Velikost dávky by zde byla potřeba, nebo lépe upravit process_batches_concurrently
             logger.error("An embedding batch failed entirely.")
             # Musíme odhadnout velikost selhané dávky - zjednodušení:
             # Předpokládáme, že selhání se projeví jako None pro jednotlivé položky ve flat listu
             # Realističtější by bylo vracet strukturovanější info o chybě z process_batches_concurrently
             pass # V _generate_embeddings_batch_openai už vracíme None pro selhání

    if len(all_embeddings_ordered) != len(product_ids_ordered):
        logger.error(f"Mismatch after processing batches: expected {len(product_ids_ordered)} results, got {len(all_embeddings_ordered)}")
        # Zkusíme mapovat, co jde, zbytek dostane dummy
        processed_count = 0
        for i, product_id in enumerate(product_ids_ordered):
             if i < len(all_embeddings_ordered) and all_embeddings_ordered[i]:
                 embeddings_map[product_id] = all_embeddings_ordered[i]
                 processed_count +=1
             else:
                 logger.warning(f"Using dummy embedding for product {product_id} due to processing error.")
                 embeddings_map[product_id] = dummy_embedding
        logger.warning(f"Successfully processed only {processed_count} embeddings due to errors.")
    else:
        # Mapování výsledků zpět na ID produktů
        successful_count = 0
        failed_count = 0
        for i, product_id in enumerate(product_ids_ordered):
            embedding = all_embeddings_ordered[i]
            if embedding:
                embeddings_map[product_id] = embedding
                successful_count += 1
            else:
                logger.warning(f"Failed to generate embedding for product {product_id}, using dummy embedding.")
                embeddings_map[product_id] = dummy_embedding
                failed_count += 1
        logger.info(f"Embeddings generated: {successful_count} successful, {failed_count} failed (used dummy).")

    # Aktualizace Pydantic modelů (volitelné, pokud je chceme mít aktuální)
    for product in products_to_embed:
        product.embedding = embeddings_map.get(product.id)

    return embeddings_map