from qdrant_client import QdrantClient

# Získání kategorií z Qdrantu
client = QdrantClient(host='localhost', port=6333)
results = client.scroll(
    collection_name='real_products_filsonstore',
    limit=1000,
    with_payload=['category']
)

qdrant_categories = set()
qdrant_categories_without_prefix = set()
for point in results[0]:
    category = point.payload.get('category')
    if category:
        qdrant_categories.add(category)
        # Odstraníme prefix "Autodoplňky > " pro porovnání
        if category.startswith("Autodoplňky > "):
            category_without_prefix = category[len("Autodoplňky > "):]
            qdrant_categories_without_prefix.add(category_without_prefix)

print(f"V Qdrantu je celkem {len(qdrant_categories)} kategorií.")
print(f"Po odstranění prefixu 'Autodoplňky > ' máme {len(qdrant_categories_without_prefix)} unikátních hodnot.")

# Simulace kategorií z Neo4j (z předchozího výstupu)
neo4j_categories = {
    "Nosiče lodí a paddleboardů", "Příčníky na hagusy", "Střešní nosiče",
    "Příslušenství k nosičům a boxům", "Autokosmetika", "Cestování s dětmi",
    "Vany do zavazadlového prostoru", "Elektrokoloběžky", "Elektrické doplňky",
    "Interiér vozidla", "Nosiče lyží a snowboardů", "Autokoberce",
    "Provozní kapaliny", "Autostany a kemping", "Povinná a doporučená výbava",
    "Nosiče kol na střechu", "Zimní výbava", "Autobaterie",
    "Exteriér vozidla", "Oleje a maziva", "Nosiče kol na zadní dveře",
    "Střešní boxy", "Stěrače", "Autopotahy", "Autožárovky",
    "Dům a zahrada", "Doplňky pro motocykly", "Vůně a parfémy do auta",
    "Dílna a garáž", "Doplňky pro nákladní vozy", "Nosiče kol na tažné"
}

# Kategorie, které jsou v Neo4j a odpovídají kategoriím v Qdrantu (bez prefixu)
matching_categories = neo4j_categories.intersection(qdrant_categories_without_prefix)
print(f"\nKategorie, které jsou jak v Neo4j, tak v Qdrantu (po odstranění prefixu): {len(matching_categories)}")
for category in sorted(matching_categories):
    print(f"- {category}")

# Kategorie, které jsou v Neo4j, ale nebyly nalezeny v Qdrantu
neo4j_only_categories = neo4j_categories - qdrant_categories_without_prefix
print(f"\nKategorie, které jsou jen v Neo4j: {len(neo4j_only_categories)}")
for category in sorted(neo4j_only_categories):
    print(f"- {category}")

# Generování Cypher dotazu pro aktualizaci kategorií
update_query = """
MATCH (c:Category_filsonstore)
WHERE c.name IN [{}]
SET c.name = "Autodoplňky > " + c.name
RETURN c.name AS UpdatedCategory
""".format(", ".join([f'"{c}"' for c in matching_categories]))

print("\nCypher dotaz pro aktualizaci kategorií:")
print(update_query)

# Ukládáme dotaz do souboru
with open("update_categories.cypher", "w") as f:
    f.write(update_query) 