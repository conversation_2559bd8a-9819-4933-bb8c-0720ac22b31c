# qdrant_ops.py
import logging
from typing import List, Dict, Optional, Union, Tuple, Any
from qdrant_client import AsyncQdrantClient
from qdrant_client.http import models
from qdrant_client.http.models import VectorParams, Distance, PointStruct, PayloadSchemaType, Filter, FieldCondition, MatchValue, PointIdsList, MatchAny, CollectionStatus, UpdateStatus
from data_models import ProductInternal, QdrantPayload, ComplementaryProductRecord, LLMComplementaryRelationship
from config import settings
from utils import create_qdrant_id

logger = logging.getLogger(__name__)

async def collection_exists(client: AsyncQdrantClient, collection_name: str) -> bool:
    """Zkontroluje, zda kolekce existuje."""
    try:
        collections = await client.get_collections()
        return collection_name in [c.name for c in collections.collections]
    except Exception as e:
        logger.error(f"Failed to check existence of collection {collection_name}: {e}")
        return False # Bezpečn<PERSON><PERSON><PERSON><PERSON>, že ne<PERSON>, pokud kontrola selže

async def delete_collection(client: AsyncQdrantClient, collection_name: str):
    """<PERSON><PERSON><PERSON><PERSON> kole<PERSON>."""
    logger.warning(f"Deleting collection: {collection_name}")
    try:
        await client.delete_collection(collection_name=collection_name, timeout=60)
        logger.info(f"Collection {collection_name} deleted successfully.")
    except Exception as e:
        logger.error(f"Failed to delete collection {collection_name}: {e}")
        raise # Chybu při mazání je lepší signalizovat dál

async def create_product_collection(client: AsyncQdrantClient, collection_name: str):
    """Vytvoří kolekci pro produkty."""
    logger.info(f"Creating product collection: {collection_name}")
    try:
        await client.create_collection(
            collection_name=collection_name,
            vectors_config=models.VectorParams(
                size=settings.embedding_dimensions,
                distance=models.Distance.COSINE
            )
            # Můžeme přidat konfiguraci HNSW, kvantizaci atd. pro optimalizaci
        )
        logger.info(f"Collection {collection_name} created. Creating payload indices...")
        # Vytvoření indexů pro payload - důležité pro filtrování
        await client.create_payload_index(collection_name=collection_name, field_name="tenant_id", field_schema=PayloadSchemaType.KEYWORD)
        await client.create_payload_index(collection_name=collection_name, field_name="product_id", field_schema=PayloadSchemaType.KEYWORD)
        await client.create_payload_index(collection_name=collection_name, field_name="category", field_schema=PayloadSchemaType.KEYWORD)
        await client.create_payload_index(collection_name=collection_name, field_name="brand", field_schema=PayloadSchemaType.KEYWORD)
        await client.create_payload_index(collection_name=collection_name, field_name="availability", field_schema=PayloadSchemaType.KEYWORD)
        await client.create_payload_index(collection_name=collection_name, field_name="functional_contexts", field_schema=PayloadSchemaType.KEYWORD) # Index pro seznam kontextů
        logger.info(f"Payload indices for {collection_name} created.")
    except Exception as e:
        logger.error(f"Failed to create collection or indices for {collection_name}: {e}")
        raise

async def create_complementary_collection(client: AsyncQdrantClient, collection_name: str):
    """Vytvoří kolekci pro komplementární produkty."""
    logger.info(f"Creating complementary products collection: {collection_name}")
    try:
        # Tato kolekce může nebo nemusí potřebovat vektory, záleží na použití
        # Pro jednoduchost přidáme dummy vektor, i když ho primárně nepoužíváme pro hledání
        await client.create_collection(
            collection_name=collection_name,
             vectors_config=models.VectorParams(
                 size=settings.embedding_dimensions, # Stejná dimenze jako produkty
                 distance=models.Distance.COSINE
             )
        )
        logger.info(f"Collection {collection_name} created. Creating payload indices...")
        await client.create_payload_index(collection_name=collection_name, field_name="tenant_id", field_schema=PayloadSchemaType.KEYWORD)
        await client.create_payload_index(collection_name=collection_name, field_name="main_product_id", field_schema=PayloadSchemaType.KEYWORD)
        await client.create_payload_index(collection_name=collection_name, field_name="main_product_category", field_schema=PayloadSchemaType.KEYWORD)
        # Index na seznam ID nemusí být super efektivní, ale umožní hledání
        await client.create_payload_index(collection_name=collection_name, field_name="complementary_product_ids", field_schema=PayloadSchemaType.KEYWORD)
        logger.info(f"Payload indices for {collection_name} created.")
    except Exception as e:
        logger.error(f"Failed to create collection or indices for {collection_name}: {e}")
        raise

async def upsert_product_points(client: AsyncQdrantClient, collection_name: str, products: List[ProductInternal], batch_size: int = 100):
    """
    Nahraje (nebo aktualizuje) produkty jako body do Qdrantu.
    Očekává produkty již obohacené o `qdrant_id` a `embedding`.
    """
    points_to_upsert: List[PointStruct] = []
    processed_count = 0
    failed_count = 0

    for product in products:
        if product.embedding is None:
            logger.warning(f"Skipping product {product.id} - missing embedding.")
            failed_count += 1
            continue
        if not product.qdrant_id:
             logger.warning(f"Skipping product {product.id} - missing qdrant_id.")
             failed_count += 1
             continue

        try:
            payload = QdrantPayload(
                original_id=product.id,
                product_id=product.id, # Opakujeme pro filtrování
                name=product.name,
                category=product.category,
                description=product.description,
                price=product.price,
                image_url=product.image_url,
                product_url=product.product_url,
                brand=product.brand,
                availability=product.availability,
                functional_contexts=product.functional_contexts,
                tenant_id=product.qdrant_id.split("_")[0] if isinstance(product.qdrant_id, str) else collection_name.split("_")[-1] # Odhad tenant_id z qdrant_id nebo collection_name
            )

            point = PointStruct(
                id=product.qdrant_id,
                vector=product.embedding, # Přímo použijeme embedding
                payload=payload.model_dump(exclude_none=True) # Uložíme payload
            )
            points_to_upsert.append(point)
            processed_count += 1
        except Exception as e:
            logger.error(f"Failed to create PointStruct for product {product.id}: {e}", exc_info=True)
            failed_count += 1

    if not points_to_upsert:
        logger.warning("No valid points to upsert.")
        return False

    logger.info(f"Upserting {len(points_to_upsert)} points to collection {collection_name}...")
    try:
        # Qdrant client zvládá interní batching, ale můžeme i zde pro jistotu
        for i in range(0, len(points_to_upsert), batch_size):
            batch = points_to_upsert[i : i + batch_size]
            await client.upsert(collection_name=collection_name, points=batch, wait=True) # wait=True pro potvrzení
            logger.info(f"Upserted batch {i//batch_size + 1}/{(len(points_to_upsert)-1)//batch_size + 1}")

        logger.info(f"Successfully upserted {len(points_to_upsert)} points.")
        if failed_count > 0:
             logger.warning(f"Failed to prepare {failed_count} points for upsert.")
        return True
    except Exception as e:
        logger.error(f"Failed to upsert points to {collection_name}: {e}")
        return False

async def get_products_without_embedding(client: AsyncQdrantClient, collection_name: str, limit: int = 1000) -> List[Tuple[Union[int, str], Dict]]:
    """
    Získá ID a payload produktů, které ještě nemají platný vektor (např. mají dummy vektor).
    Poznámka: Qdrant nemá přímý filtr na "neexistence vektoru" nebo na hodnoty vektoru.
    Tato funkce je zjednodušená - načte body a zkontroluje vektor v kódu. Pro velké kolekce neefektivní.
    Lepší přístup: Ukládat flag 'has_embedding' do payloadu a filtrovat podle něj.
    """
    logger.warning("Getting products without embedding by scrolling - potentially inefficient for large collections.")
    products_to_process = []
    offset = None
    dummy_vector_start = [0.1] * 5 # Jen prvních pár prvků pro kontrolu

    try:
        while True:
            results, next_offset = await client.scroll(
                collection_name=collection_name,
                limit=limit,
                offset=offset,
                with_payload=True,
                with_vectors=True # Potřebujeme vektor pro kontrolu
            )
            if not results:
                break

            for point in results:
                # Zjednodušená kontrola na dummy vektor (začíná [0.1, 0.1, ...])
                if point.vector is None or (isinstance(point.vector, list) and point.vector[:5] == dummy_vector_start):
                    products_to_process.append((point.id, point.payload))

            offset = next_offset
            if offset is None:
                break # Všechny body byly načteny

        logger.info(f"Found {len(products_to_process)} products potentially needing embedding update.")
        return products_to_process

    except Exception as e:
        logger.error(f"Failed to scroll through {collection_name} to find products without embeddings: {e}")
        return []

async def update_product_embeddings_batch(client: AsyncQdrantClient, collection_name: str, updates: Dict[Union[int, str], List[float]]):
    """Aktualizuje vektory pro dané body."""
    points_to_update = [
        PointStruct(id=point_id, vector=embedding)
        for point_id, embedding in updates.items()
    ]
    if not points_to_update:
        return

    logger.info(f"Updating embeddings for {len(points_to_update)} points in {collection_name}")
    try:
        # Použijeme upsert, což aktualizuje vektor, pokud bod existuje
        await client.upsert(collection_name=collection_name, points=points_to_update, wait=True)
        logger.info(f"Successfully updated embeddings for {len(points_to_update)} points.")
    except Exception as e:
        logger.error(f"Failed to update embeddings in {collection_name}: {e}")
        # Můžeme zde přidat logiku pro opakování selhaných updatů

async def update_product_contexts_batch(client: AsyncQdrantClient, collection_name: str, updates: Dict[Union[int, str], List[str]]):
    """Aktualizuje pole 'functional_contexts' v payloadu pro dané body."""
    points_to_update = [
        models.PointPayload(
            id=point_id,
            payload={"functional_contexts": contexts}
        ) for point_id, contexts in updates.items()
    ]
    if not points_to_update:
        return

    logger.info(f"Updating functional contexts for {len(points_to_update)} points in {collection_name}")
    try:
        # Aktualizace pouze části payloadu
        await client.set_payload(
            collection_name=collection_name,
            payload=updates, # {point_id: {"functional_contexts": [...]}}
            points=list(updates.keys()), # Seznam ID bodů k aktualizaci
            wait=True
        )
        # Poznámka: set_payload s více ID může být méně efektivní než upsert, ale nemění vektor.
        # Alternativa: client.overwrite_payload(...) nebo patch_payload
        logger.info(f"Successfully updated functional contexts for {len(points_to_update)} points.")
    except Exception as e:
        logger.error(f"Failed to update functional contexts in {collection_name}: {e}")

async def update_product_payloads_batch(client: AsyncQdrantClient, collection_name: str, updates: Dict[Union[int, str], Dict[str, Any]]):
    """Aktualizuje specifická pole v payloadu pro dané body."""
    if not updates:
        return

    logger.info(f"Updating payload fields for {len(updates)} points in {collection_name}")
    # Qdrant client.set_payload vyžaduje seznam ID a payload pro každý bod
    # Náš updates dict má formát {point_id: {field: value, ...}}
    points_to_update = list(updates.keys())
    payloads_to_set = [updates[pid] for pid in points_to_update]
    
    # POZOR: set_payload přepisuje daná pole, takže musíme předat celý nový slovník pro každé pole
    # Toto není ideální API pro batch update různých polí najednou. 
    # Lepší by mohl být PATCH, ale ten také vyžaduje specific point ID.
    # Zkusíme iterativní PATCH prozatím.
    updated_count = 0
    try:
        for point_id, payload_patch in updates.items():
             await client.set_payload(
                 collection_name=collection_name,
                 payload=payload_patch, # např. {"price": 99.9, "availability": "skladem"}
                 points=[point_id],
                 wait=True
             )
             updated_count += 1
        logger.info(f"Successfully updated payloads for {updated_count} points.")
    except Exception as e:
        logger.error(f"Failed during payload update in {collection_name}: {e}")

async def upsert_complementary_records(client: AsyncQdrantClient, collection_name: str, records: List[ComplementaryProductRecord], batch_size: int = 100):
    """Nahraje záznamy o komplementárních produktech."""
    points_to_upsert: List[PointStruct] = []
    for record in records:
         # ID bodu = ID hlavního produktu
        qdrant_id = create_qdrant_id(record.main_product_id)
        # Vektor můžeme nastavit na dummy nebo průměr vektoru hlavního produktu, pokud ho známe
        # Zde použijeme dummy vektor, protože primárně nehledáme podle něj
        dummy_vector = [0.0] * settings.embedding_dimensions

        point = PointStruct(
            id=qdrant_id,
            vector=dummy_vector,
            payload=record.model_dump(exclude_none=True)
        )
        points_to_upsert.append(point)

    if not points_to_upsert:
        logger.warning("No complementary records to upsert.")
        return False

    logger.info(f"Upserting {len(points_to_upsert)} complementary records to collection {collection_name}...")
    try:
        for i in range(0, len(points_to_upsert), batch_size):
            batch = points_to_upsert[i : i + batch_size]
            await client.upsert(collection_name=collection_name, points=batch, wait=True)
            logger.info(f"Upserted batch {i//batch_size + 1}/{(len(points_to_upsert)-1)//batch_size + 1}")

        logger.info(f"Successfully upserted {len(points_to_upsert)} complementary records.")
        return True
    except Exception as e:
        logger.error(f"Failed to upsert complementary records to {collection_name}: {e}")
        return False

async def get_all_product_qdrant_ids(client: AsyncQdrantClient, collection_name: str, batch_size: int = 1000) -> set[Union[int, str]]:
    """Získá všechna Qdrant ID z dané produktové kolekce."""
    all_ids = set()
    offset = None
    logger.info(f"Fetching all point IDs from {collection_name}...")
    try:
        while True:
            # Použijeme scroll s limit=0 a with_vector=False pro získání jen ID
            results, next_offset = await client.scroll(
                collection_name=collection_name,
                limit=batch_size, # Načítáme po dávkách
                offset=offset,
                with_payload=False, # Payload nepotřebujeme
                with_vectors=False  # Vektory nepotřebujeme
            )
            if not results:
                break
                
            for point in results:
                 all_ids.add(point.id)
                 
            offset = next_offset
            if offset is None:
                 break
        logger.info(f"Found {len(all_ids)} point IDs in {collection_name}.")
        return all_ids
    except Exception as e:
        logger.error(f"Failed to fetch all IDs from {collection_name}: {e}")
        return set() # Vrátíme prázdný set v případě chyby

async def delete_products_by_id(client: AsyncQdrantClient, collection_name: str, ids_to_delete: List[Union[int, str]]):
    """Smaže body z kolekce podle seznamu jejich Qdrant ID."""
    if not ids_to_delete:
        return
    logger.warning(f"Deleting {len(ids_to_delete)} points from {collection_name}...")
    try:
        await client.delete(
            collection_name=collection_name,
            points_selector=PointIdsList(points=ids_to_delete),
            wait=True
        )
        logger.info(f"Successfully deleted {len(ids_to_delete)} points.")
    except Exception as e:
        logger.error(f"Failed to delete points from {collection_name}: {e}")

async def find_similar_product(client: AsyncQdrantClient, collection_name: str, vector: List[float], top_k: int = 1) -> Optional[Union[int, str]]:
    """Najde ID nejpodobnějšího produktu (kromě sebe sama)."""
    try:
        search_result = await client.search(
            collection_name=collection_name,
            query_vector=vector,
            limit=top_k + 1 # Hledáme top K+1, abychom mohli případně vyloučit sebe sama
            # Můžeme přidat query_filter pro vyloučení sebe sama, pokud známe ID
        )
        # Předpokládáme, že první výsledek může být samotný dotazovaný bod (pokud už je v DB)
        # Pokud chceme striktně jiný bod, musíme přidat logiku pro vyloučení nebo kontrolu ID
        if len(search_result) > 0:
            # Jednoduchá logika: vezmeme první výsledek, pokud jich je víc než 0
            # Pro robustnější řešení bychom porovnali ID nebo hledali s filtrem
            return search_result[0].id
        else:
            return None
    except Exception as e:
        logger.error(f"Similarity search failed in {collection_name}: {e}")
        return None

async def get_complementary_record_by_id(client: AsyncQdrantClient, collection_name: str, main_product_qdrant_id: Union[int, str]) -> Optional[ComplementaryProductRecord]:
    """Načte záznam komplementárních produktů pro dané ID hlavního produktu."""
    try:
        # Použijeme retrieve pro získání jednoho bodu podle ID
        records = await client.retrieve(
            collection_name=collection_name,
            ids=[main_product_qdrant_id],
            with_payload=True
        )
        if records:
            # Převedeme payload zpět na náš Pydantic model
            payload_dict = records[0].payload
            # Ujistíme se, že klíčové pole existuje
            if payload_dict and 'main_product_id' in payload_dict:
                 return ComplementaryProductRecord(**payload_dict)
            else:
                 logger.warning(f"Retrieved record for {main_product_qdrant_id} has missing or invalid payload: {payload_dict}")
                 return None
        else:
            return None # Záznam nenalezen
    except Exception as e:
        logger.error(f"Failed to retrieve complementary record for {main_product_qdrant_id} from {collection_name}: {e}")
        return None

async def check_complementary_exists(client: AsyncQdrantClient, collection_name: str, product_ids: List[str]) -> List[str]:
    """Zkontroluje, pro které produkty již existuje záznam v komplementární kolekci."""
    existing_ids = []
    qdrant_ids_to_check = [create_qdrant_id(pid) for pid in product_ids]

    # Rozdělení na menší dávky pro retrieve, pokud je jich hodně
    batch_size = 200
    for i in range(0, len(qdrant_ids_to_check), batch_size):
        batch_qdrant_ids = qdrant_ids_to_check[i : i + batch_size]
        try:
            # Retrieve je efektivnější pro získání bodů podle ID než scroll s filtrem
            results = await client.retrieve(
                collection_name=collection_name,
                ids=batch_qdrant_ids,
                with_payload=False, # Nepotřebujeme payload, jen vědět, zda existují
                with_vectors=False
            )
            # Získáme původní ID z existujících Qdrant ID
            found_qdrant_ids = {point.id for point in results}
            # Mapování zpět na původní ID (může být složitější, pokud qdrant_id není deterministické)
            # Pro UUID je to ok. Pro hash by bylo lepší hledat podle payload.main_product_id
            original_id_map = {create_qdrant_id(pid): pid for pid in product_ids[i : i + len(batch_qdrant_ids)]}
            for qid in found_qdrant_ids:
                if qid in original_id_map:
                    existing_ids.append(original_id_map[qid])

        except Exception as e:
            logger.error(f"Failed to check existence of complementary records batch: {e}")
            # Pokračujeme s další dávkou

    return existing_ids

async def find_complementary_candidates(
    client: AsyncQdrantClient,
    collection_name: str,
    vector: List[float],
    limit: int,
    exclude_category: Optional[str] = None,
    include_categories: Optional[List[str]] = None,
    exclude_id: Optional[Union[str, int]] = None
) -> List[Dict]:
    """
    Najde kandidáty pro komplementární produkty s možností vyloučení/zahrnutí kategorií a ID.

    Args:
        client: Asynchronní Qdrant klient.
        collection_name: Název kolekce produktů.
        vector: Embedding vektor hlavního produktu.
        limit: Maximální počet kandidátů k vrácení.
        exclude_category: Kategorie, která má být vyloučena z výsledků.
        include_categories: Seznam kategorií, na které se má vyhledávání omezit.
        exclude_id: Qdrant ID, které má být vyloučeno z výsledků (např. ID hlavního produktu).

    Returns:
        Seznam payloadů (slovníků) nalezených kandidátů.
    """
    must_conditions = []
    must_not_conditions = []

    if include_categories:
        # Přidáme podmínku pro zahrnutí pouze specifikovaných kategorií
        must_conditions.append(
            FieldCondition(key="category", match=MatchAny(any=include_categories))
        )
        logger.debug(f"Searching candidates, including categories: {include_categories}")

    if exclude_category:
        # Přidáme podmínku pro vyloučení specifikované kategorie
        must_not_conditions.append(
            FieldCondition(key="category", match=MatchValue(value=exclude_category))
        )
        logger.debug(f"Searching candidates, excluding category: {exclude_category}")

    if exclude_id:
         # Přidáme podmínku pro vyloučení specifického ID
         # Qdrant ID jsou buď int nebo str (UUID), musíme použít PointIdsList
         # Filtr 'ids' se dává mimo must/must_not sekce Filter objektu
         pass # ID filter se řeší níže, ne přes must_not FieldCondition

    # Sestavení finálního filtru
    search_filter = None
    if must_conditions or must_not_conditions:
        search_filter = Filter(
            must=must_conditions if must_conditions else None,
            must_not=must_not_conditions if must_not_conditions else None
        )

    # Přidání filtru pro ID (pokud je specifikováno)
    # Qdrant nepodporuje vyloučení ID přímo ve Filter objektu jednoduše.
    # Musíme buď načíst více výsledků a filtrovat ID dodatečně,
    # nebo použít složitější filtr, pokud by API podporovalo `ids` v `must_not`.
    # Pro jednoduchost načteme o 1 více a odfiltrujeme exclude_id, pokud se objeví.
    query_limit = limit + 1 if exclude_id else limit

    logger.debug(f"Searching candidates in {collection_name} with limit {query_limit} and filter: {search_filter}")

    try:
        search_result = await client.search(
            collection_name=collection_name,
            query_vector=vector,
            query_filter=search_filter,
            limit=query_limit,
            with_payload=True # Potřebujeme payload
        )

        candidates = []
        for hit in search_result:
            # Vyloučíme exclude_id, pokud je nastaveno a shoduje se
            if exclude_id is not None and hit.id == exclude_id:
                continue
            if hit.payload:
                 # Přidáme payload do výsledků
                 # Odstraníme embedding z payloadu, pokud tam je, abychom neposílali velká data
                 payload_copy = hit.payload.copy()
                 payload_copy.pop('embedding', None) # Odstraní klíč 'embedding', pokud existuje
                 candidates.append(payload_copy)
            # Omezíme na požadovaný počet, pokud jsme načetli více kvůli exclude_id
            if len(candidates) >= limit:
                break

        logger.debug(f"Found {len(candidates)} candidates after filtering.")
        return candidates
    except Exception as e:
        logger.error(f"Error searching for candidates in {collection_name}: {e}", exc_info=True)
        return []

async def upsert_llm_complementary_batch(
    client: AsyncQdrantClient,
    collection_name: str,
    relationships: List[LLMComplementaryRelationship],
    batch_size: int = 100
) -> bool:
    """
    Nahraje (upsert) dávku LLM vygenerovaných komplementárních vztahů do Qdrantu.
    Každý bod reprezentuje hlavní produkt a payload obsahuje seznamy komplementů.
    """
    if not relationships:
        logger.info("No complementary relationships to upsert in this batch.")
        return True

    points_to_upsert: List[PointStruct] = []
    processed_count = 0
    failed_count = 0

    # Vytvoření prázdného vektoru pro komplementární kolekci
    # Qdrant vyžaduje vektor správné velikosti, takže použijeme velikost 1536 (standardní OpenAI embedding)
    empty_vector = [0.0] * 1536

    for rel in relationships:
        try:
            # Použijeme create_qdrant_id pro vytvoření správného formátu ID
            point_id = create_qdrant_id(rel.main_product_id)
            payload = {
                "main_product_id": rel.main_product_id,
                "complementary_product_ids": [comp.complementary_product_id for comp in rel.complementary_products],
                "reasons": [comp.reason for comp in rel.complementary_products],
                "relevance_scores": [comp.relevance_score for comp in rel.complementary_products]
            }
            points_to_upsert.append(PointStruct(
                id=point_id,
                vector=empty_vector,  # Místo None použijeme prázdný vektor
                payload=payload
            ))
            processed_count += 1
        except Exception as e:
            logger.error(f"Failed to create PointStruct for main_product_id {rel.main_product_id}: {e}")
            failed_count += 1
            
    if failed_count > 0:
        logger.warning(f"Failed to prepare {failed_count} points for upsert.")
        
    if not points_to_upsert:
        logger.warning("No valid points were prepared for upsert after processing relationships.")
        return True 

    logger.info(f"Upserting {len(points_to_upsert)} complementary relationship points to collection '{collection_name}'...")
    
    all_successful = True
    for i in range(0, len(points_to_upsert), batch_size):
        batch = points_to_upsert[i : i + batch_size]
        try:
            response = await client.upsert(
                collection_name=collection_name,
                points=batch,
                wait=True
            )
            if response.status != UpdateStatus.COMPLETED:
                logger.error(f"Qdrant upsert for complementary batch {i//batch_size + 1} failed with status: {response.status}")
                all_successful = False
        except Exception as e:
            logger.error(f"Exception during Qdrant upsert for complementary batch {i//batch_size + 1}: {e}", exc_info=True)
            all_successful = False

    if all_successful:
        logger.info(f"Successfully upserted {len(points_to_upsert)} complementary relationship points for this batch.")
    else:
        logger.error("Errors occurred during upserting complementary relationship points for this batch.")

    return all_successful

# --- Funkce pro práci s kategoriálními vztahy v Qdrantu --- #

async def create_category_relationships_collection(
    client,
    tenant_id: str,
    vector_size: int = 1536
):
    """
    Vytvoří kolekci pro kategoriální vztahy v Qdrantu
    """
    collection_name = f"category_relationships_{tenant_id}"
    
    try:
        # Kontrola, zda kolekce již existuje
        collections = await client.get_collections()
        if collection_name in [c.name for c in collections.collections]:
            logger.info(f"Kolekce {collection_name} již existuje.")
            return True
            
        # Vytvoření kolekce pro kategoriální vztahy
        await client.create_collection(
            collection_name=collection_name,
            vectors_config={
                "category_embedding": {
                    "size": vector_size,
                    "distance": "Cosine"
                }
            }
        )
        
        # Vytvoření indexů pro rychlejší vyhledávání
        await client.create_payload_index(
            collection_name=collection_name,
            field_name="source_category",
            field_schema="keyword"
        )
        
        await client.create_payload_index(
            collection_name=collection_name,
            field_name="target_category",
            field_schema="keyword"
        )
        
        await client.create_payload_index(
            collection_name=collection_name,
            field_name="normalized_source",
            field_schema="keyword"
        )
        
        await client.create_payload_index(
            collection_name=collection_name,
            field_name="normalized_target",
            field_schema="keyword"
        )
        
        await client.create_payload_index(
            collection_name=collection_name,
            field_name="tenant_id",
            field_schema="keyword"
        )
        
        logger.info(f"Vytvořena kolekce {collection_name} pro kategoriální vztahy.")
        return True
    except Exception as e:
        logger.error(f"Chyba při vytváření kolekce {collection_name}: {e}")
        return False

def normalize_category_name(category_name):
    """
    Normalizuje název kategorie pro lepší vyhledávání
    """
    if not category_name:
        return ""
        
    # Pomocný import - ujistěte se, že je nainstalovaný unidecode
    try:
        from unidecode import unidecode
    except ImportError:
        # Pokud není unidecode k dispozici, použijeme alespoň základní normalizaci
        text = category_name.lower()
        for char in [">", "/", "\\", "|"]:
            text = text.replace(char, " ")
        return text.strip()
    
    # Převod na malá písmena
    text = category_name.lower()
    # Odstranění diakritiky
    text = unidecode(text)
    # Náhrada oddělovačů
    for char in [">", "/", "\\", "|"]:
        text = text.replace(char, " ")
    # Odstranění speciálních znaků
    import re
    text = re.sub(r'[^a-z0-9\s]', '', text)
    # Normalizace mezer
    text = re.sub(r'\s+', ' ', text)
    return text.strip()

async def import_category_relationships_to_qdrant(
    client,
    json_file_path: str, 
    tenant_id: str,
    embedding_service=None
):
    """
    Importuje kategoriální vztahy z JSON do Qdrantu
    """
    import json
    import os
    
    collection_name = f"category_relationships_{tenant_id}"
    
    # Kontrola existence kolekce
    try:
        collections = await client.get_collections()
        if collection_name not in [c.name for c in collections.collections]:
            await create_category_relationships_collection(client, tenant_id)
    except Exception as e:
        logger.error(f"Chyba při kontrole existence kolekce: {e}")
        return 0
    
    try:
        # Načtení JSON souboru
        if not os.path.exists(json_file_path):
            logger.error(f"Soubor {json_file_path} neexistuje.")
            return 0
            
        with open(json_file_path, "r", encoding="utf-8") as f:
            data = json.load(f)
        
        relationships = data.get("relationships", {})
        points = []
        import uuid
        import random
        
        # Vytvoření fiktivních embeddingů, pokud nemáme embedding_service
        def get_dummy_embedding(size=1536):
            return [random.uniform(-1, 1) for _ in range(size)]
        
        # Zpracování všech vztahů
        for source_category, targets in relationships.items():
            # Získání embedding pro zdrojovou kategorii
            if embedding_service:
                try:
                    source_embedding = await embedding_service.get_embedding(source_category)
                except:
                    source_embedding = get_dummy_embedding()
            else:
                source_embedding = get_dummy_embedding()
            
            # Normalizace pro vyhledávání
            normalized_source = normalize_category_name(source_category)
            
            for target in targets:
                target_category = target.get("complementary_category")
                strength = target.get("strength", 0.5)
                reason = target.get("reason", "")
                
                # Normalizace cílové kategorie
                normalized_target = normalize_category_name(target_category)
                
                # Generování unikátního ID pro vztah
                point_id = str(uuid.uuid4())
                
                # Vytvoření bodu pro Qdrant
                point = {
                    "id": point_id,
                    "vector": {
                        "category_embedding": source_embedding
                    },
                    "payload": {
                        "source_category": source_category,
                        "target_category": target_category,
                        "strength": strength,
                        "reason": reason,
                        "tenant_id": tenant_id,
                        "normalized_source": normalized_source,
                        "normalized_target": normalized_target
                    }
                }
                points.append(point)
        
        # Uložení do Qdrantu po dávkách
        if points:
            batch_size = 100
            for i in range(0, len(points), batch_size):
                batch = points[i:i+batch_size]
                await client.upsert(
                    collection_name=collection_name,
                    points=batch,
                    wait=True
                )
                logger.info(f"Naimportováno {min(i+batch_size, len(points))}/{len(points)} kategoriálních vztahů do Qdrantu pro tenant {tenant_id}")
            
            logger.info(f"Dokončen import {len(points)} kategoriálních vztahů do Qdrantu pro tenant {tenant_id}")
            return len(points)
        
        return 0
    
    except Exception as e:
        logger.error(f"Chyba při importu kategoriálních vztahů do Qdrantu: {e}")
        return 0

async def get_connected_categories_from_qdrant(
    client,
    product_category: str,
    tenant_id: str,
    embedding_service = None,
    limit: int = 10
) -> list:
    """
    Najde propojené kategorie v Qdrantu pomocí tří metod
    """
    collection_name = f"category_relationships_{tenant_id}"
    connected_categories = []
    
    try:
        # Kontrola existence kolekce
        collections = await client.get_collections()
        if collection_name not in [c.name for c in collections.collections]:
            logger.warning(f"Kolekce {collection_name} neexistuje v Qdrantu.")
            return []
            
        # Metoda 1: Přesné vyhledávání podle source_category
        filter_query = {
            "must": [
                {"key": "tenant_id", "match": {"value": tenant_id}},
                {"key": "source_category", "match": {"value": product_category}}
            ]
        }
        
        results = await client.scroll(
            collection_name=collection_name,
            scroll_filter=filter_query,
            limit=limit
        )
        
        if results and len(results) > 0 and results[0]:
            for point in results[0]:
                connected_categories.append({
                    "category": point.payload.get("target_category"),
                    "strength": point.payload.get("strength", 0.5),
                    "reason": point.payload.get("reason", "")
                })
            return connected_categories
        
        # Metoda 2: Vyhledávání podle normalizovaného názvu
        normalized_product_category = normalize_category_name(product_category)
        
        filter_query = {
            "must": [
                {"key": "tenant_id", "match": {"value": tenant_id}},
                {"key": "normalized_source", "match": {"value": normalized_product_category}}
            ]
        }
        
        results = await client.scroll(
            collection_name=collection_name,
            scroll_filter=filter_query,
            limit=limit
        )
        
        if results and len(results) > 0 and results[0]:
            for point in results[0]:
                connected_categories.append({
                    "category": point.payload.get("target_category"),
                    "strength": point.payload.get("strength", 0.5),
                    "reason": point.payload.get("reason", "")
                })
            return connected_categories
            
        # Metoda 3: Vyhledání pomocí hlavní kategorie (první část před >)
        if ">" in product_category:
            main_category = product_category.split(">")[0].strip()
            normalized_main = normalize_category_name(main_category)
            
            filter_query = {
                "must": [
                    {"key": "tenant_id", "match": {"value": tenant_id}},
                    {"key": "normalized_source", "match": {"value": normalized_main}}
                ]
            }
            
            results = await client.scroll(
                collection_name=collection_name,
                scroll_filter=filter_query,
                limit=limit
            )
            
            if results and len(results) > 0 and results[0]:
                for point in results[0]:
                    connected_categories.append({
                        "category": point.payload.get("target_category"),
                        "strength": point.payload.get("strength", 0.5),
                        "reason": point.payload.get("reason", "")
                    })
                return connected_categories
                
        # Metoda 4: Vektorové vyhledávání podobných kategorií, pokud máme embedding_service
        if embedding_service and not connected_categories:
            try:
                product_category_embedding = await embedding_service.get_embedding(product_category)
                
                results = await client.search(
                    collection_name=collection_name,
                    query_vector={"category_embedding": product_category_embedding},
                    query_filter={"must": [{"key": "tenant_id", "match": {"value": tenant_id}}]},
                    limit=limit
                )
                
                if results:
                    for hit in results:
                        connected_categories.append({
                            "category": hit.payload.get("target_category"),
                            "strength": hit.payload.get("strength", 0.5),
                            "reason": hit.payload.get("reason", "")
                        })
            except Exception as e:
                logger.error(f"Chyba při vektorovém vyhledávání kategorií: {e}")
        
        return connected_categories
    
    except Exception as e:
        logger.error(f"Chyba při získávání propojených kategorií z Qdrantu: {e}")
        return []
        
async def build_category_graph_from_qdrant(client, tenant_id: str) -> dict:
    """
    Vytvoří slovník kategoriálních vztahů z Qdrantu
    """
    collection_name = f"category_relationships_{tenant_id}"
    graph = {}
    
    try:
        # Kontrola existence kolekce
        collections = await client.get_collections()
        if collection_name not in [c.name for c in collections.collections]:
            logger.warning(f"Kolekce {collection_name} neexistuje v Qdrantu.")
            return {}
            
        # Načtení všech vztahů pro daného tenanta
        filter_query = {
            "must": [
                {"key": "tenant_id", "match": {"value": tenant_id}}
            ]
        }
        
        offset = None
        limit = 100
        all_points = []
        
        # Načtení všech bodů postupně po stránkách
        while True:
            results = await client.scroll(
                collection_name=collection_name,
                scroll_filter=filter_query,
                limit=limit,
                offset=offset
            )
            
            if not results or not results[0]:
                break
                
            all_points.extend(results[0])
            
            if len(results[0]) < limit:
                break
                
            offset = results[1]  # next_page_offset je druhý prvek tuple
        
        # Vytvoření grafu ze získaných bodů
        for point in all_points:
            source = point.payload.get("source_category")
            target = point.payload.get("target_category")
            
            if source not in graph:
                graph[source] = set()
                
            graph[source].add(target)
        
        logger.info(f"Načteno {len(graph)} zdrojových kategorií z Qdrantu pro tenant {tenant_id}.")
        return graph
    
    except Exception as e:
        logger.error(f"Chyba při vytváření grafu kategorií z Qdrantu: {e}")
        return {}