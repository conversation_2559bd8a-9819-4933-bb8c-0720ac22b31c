import asyncio
import time
import os
import json
from typing import Dict, List, Any, Optional

# Import our search implementations
from fulltext import CzechFulltextRetriever
from semantic import search_semantic_products
from hybrid_search import search_hybrid_products, ensemble_retrievers

# Import clients
from qdrant_client.async_qdrant_client import AsyncQdrantClient
from core.clients import get_qdrant_async_client, get_openai_client, get_cohere_client
from openai import AsyncOpenAI
import cohere

# Colored output for better readability
class Colors:
    HEADER = '\033[95m'
    BLUE = '\033[94m'
    CYAN = '\033[96m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    END = '\033[0m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'

# Helper functions for displaying results
def print_header(text: str):
    print(f"\n{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.END}")
    print(f"{Colors.HEADER}{Colors.BOLD}{text.center(80)}{Colors.END}")
    print(f"{Colors.HEADER}{Colors.BOLD}{'=' * 80}{Colors.END}\n")

def print_section(text: str):
    print(f"\n{Colors.BLUE}{Colors.BOLD}{text}{Colors.END}")
    print(f"{Colors.BLUE}{'-' * len(text)}{Colors.END}\n")

def print_result(index: int, result: Dict[str, Any], source: str = None):
    title = result.get("name", result.get("title", "[No Title]"))
    product_id = result.get("product_id", result.get("original_id", result.get("id", "[No ID]")))
    score = result.get("score", 0.0)
    
    if source:
        color = Colors.GREEN if source == "fulltext" else Colors.CYAN if source == "semantic" else Colors.YELLOW
        source_text = f" [{color}{source.upper()}{Colors.END}]"
    else:
        source_text = ""
        
    print(f"{index+1:2d}. {Colors.BOLD}{title[:60]}{Colors.END}{' ...' if len(title) > 60 else ''}")
    print(f"   ID: {product_id} | Score: {score:.4f}{source_text}")
    
    # Print additional details if available
    if "original_score_fulltext" in result and "original_score_semantic" in result:
        print(f"   Original scores: Fulltext={result['original_score_fulltext']:.4f}, Semantic={result['original_score_semantic']:.4f}")

async def load_products_from_qdrant(client: AsyncQdrantClient, collection_name: str) -> List[Dict[str, Any]]:
    """Load all products from a Qdrant collection."""
    limit = 1000  # Adjust based on collection size
    offset = 0
    all_products = []
    
    while True:
        results = await client.scroll(
            collection_name=collection_name,
            limit=limit,
            offset=offset,
            with_payload=True
        )
        
        points = results[0]
        if not points:
            break
            
        all_products.extend([point.payload for point in points])
        offset += len(points)
        
        if len(points) < limit:
            break
    
    return all_products

async def test_search_methods(tenant_id: str, collection_name: str, query: str, limit: int = 10):
    print_header(f"HYBRID SEARCH TEST FOR '{query}'")
    
    # Initialize clients
    qdrant_client = get_qdrant_async_client()
    openai_client = get_openai_client()
    cohere_client = get_cohere_client()
    
    products_collection_name = collection_name  # Use the actual collection name
    
    # 1. Run fulltext search
    print_section("Testing Fulltext Search (BM25)")
    fulltext_retriever = CzechFulltextRetriever()
    
    # Load products for BM25
    print("Loading products from Qdrant...")
    t_start = time.time()
    products = await load_products_from_qdrant(qdrant_client, products_collection_name)
    t_end = time.time()
    print(f"Loaded {len(products)} products in {t_end - t_start:.2f} seconds")
    
    # Add documents to fulltext retriever
    print("Indexing products for BM25...")
    t_start = time.time()
    fulltext_retriever.add_documents(products)
    t_end = time.time()
    print(f"Indexed {len(products)} products in {t_end - t_start:.2f} seconds")
    
    # Run fulltext search
    print(f"\nSearching for '{query}' with BM25...")
    t_start = time.time()
    fulltext_results = fulltext_retriever.search(query, top_k=limit)
    t_end = time.time()
    print(f"Found {len(fulltext_results)} results in {t_end - t_start:.4f} seconds")
    
    # Print fulltext results
    print("\nTop BM25 results:")
    for i, result in enumerate(fulltext_results[:10]):
        print_result(i, result, "fulltext")
        
    # 2. Run semantic search
    print_section("Testing Semantic Search (Embeddings)")
    print(f"Searching for '{query}' with embeddings...")
    t_start = time.time()
    semantic_results = await search_semantic_products(
        client=qdrant_client,
        collection_name=products_collection_name,
        query_text=query,
        openai_client=openai_client,
        cohere_client=None,  # Disable reranking for this test
        limit=limit,
        enable_reranking=False
    )
    t_end = time.time()
    print(f"Found {len(semantic_results)} results in {t_end - t_start:.4f} seconds")
    
    # Print semantic results
    print("\nTop Semantic results:")
    for i, result in enumerate(semantic_results[:10]):
        print_result(i, result, "semantic")
    
    # 3. Run hybrid search
    print_section("Testing Hybrid Search (Combined)")
    rerank_limit_multiplier = 4
    final_limit = 10
    fulltext_ratio = 0.25
    semantic_ratio = 0.75
    ensemble_alpha = 0.5
    
    print(f"Searching for '{query}' with hybrid approach...")
    print(f"Parameters: fulltext_ratio={fulltext_ratio}, semantic_ratio={semantic_ratio}, ensemble_alpha={ensemble_alpha}")
    t_start = time.time()
    hybrid_results = await search_hybrid_products(
        client=qdrant_client,
        collection_name=products_collection_name,
        query_text=query,
        openai_client=openai_client,
        cohere_client=None,  # Disable reranking for this test to see raw hybrid results
        limit=final_limit,
        rerank_limit_multiplier=rerank_limit_multiplier,
        fulltext_ratio=fulltext_ratio,
        semantic_ratio=semantic_ratio,
        ensemble_alpha=ensemble_alpha,
        enable_reranking=False,
        fulltext_retriever=fulltext_retriever
    )
    t_end = time.time()
    print(f"Found {len(hybrid_results)} results in {t_end - t_start:.4f} seconds")
    
    # Print hybrid results
    print("\nTop Hybrid results (without reranking):")
    for i, result in enumerate(hybrid_results[:10]):
        print_result(i, result)
    
    # 4. Run hybrid search with reranking
    if cohere_client:
        print_section("Testing Hybrid Search with Cohere Reranking")
        print(f"Searching for '{query}' with hybrid approach + reranking...")
        t_start = time.time()
        hybrid_reranked_results = await search_hybrid_products(
            client=qdrant_client,
            collection_name=products_collection_name,
            query_text=query,
            openai_client=openai_client,
            cohere_client=cohere_client,
            limit=final_limit,
            rerank_limit_multiplier=rerank_limit_multiplier,
            fulltext_ratio=fulltext_ratio,
            semantic_ratio=semantic_ratio,
            ensemble_alpha=ensemble_alpha,
            enable_reranking=True,
            fulltext_retriever=fulltext_retriever
        )
        t_end = time.time()
        print(f"Found {len(hybrid_reranked_results)} results in {t_end - t_start:.4f} seconds")
        
        # Print reranked results
        print("\nTop Hybrid results (with Cohere reranking):")
        for i, result in enumerate(hybrid_reranked_results[:10]):
            print_result(i, result)
    
    # 5. Analyze result overlap
    print_section("Result Analysis")
    
    # Extract IDs for comparison
    fulltext_ids = [r.get("product_id", r.get("original_id", r.get("id"))) for r in fulltext_results[:limit]]
    semantic_ids = [r.get("product_id", r.get("original_id", r.get("id"))) for r in semantic_results[:limit]]
    hybrid_ids = [r.get("product_id", r.get("original_id", r.get("id"))) for r in hybrid_results[:limit]]
    
    # Calculate overlaps
    fulltext_semantic_overlap = set(fulltext_ids).intersection(set(semantic_ids))
    fulltext_hybrid_overlap = set(fulltext_ids).intersection(set(hybrid_ids))
    semantic_hybrid_overlap = set(semantic_ids).intersection(set(hybrid_ids))
    
    # Print overlaps
    print(f"Overlap between Fulltext and Semantic: {len(fulltext_semantic_overlap)} results")
    print(f"Overlap between Fulltext and Hybrid: {len(fulltext_hybrid_overlap)} results")
    print(f"Overlap between Semantic and Hybrid: {len(semantic_hybrid_overlap)} results")
    
    # Print results only in one method
    print(f"\nResults unique to Fulltext: {len(set(fulltext_ids) - set(semantic_ids) - set(hybrid_ids))}")
    print(f"Results unique to Semantic: {len(set(semantic_ids) - set(fulltext_ids) - set(hybrid_ids))}")
    print(f"Results unique to Hybrid: {len(set(hybrid_ids) - set(fulltext_ids) - set(semantic_ids))}")
    
    # Calculate contribution percentages in hybrid results
    print("\nSource contribution in hybrid results:")
    fulltext_in_hybrid = sum(1 for id in hybrid_ids if id in fulltext_ids)
    semantic_in_hybrid = sum(1 for id in hybrid_ids if id in semantic_ids)
    both_in_hybrid = sum(1 for id in hybrid_ids if id in fulltext_ids and id in semantic_ids)
    unique_in_hybrid = sum(1 for id in hybrid_ids if id not in fulltext_ids and id not in semantic_ids)
    
    print(f"From Fulltext only: {fulltext_in_hybrid - both_in_hybrid} results ({(fulltext_in_hybrid - both_in_hybrid) / len(hybrid_ids) * 100:.1f}%)")
    print(f"From Semantic only: {semantic_in_hybrid - both_in_hybrid} results ({(semantic_in_hybrid - both_in_hybrid) / len(hybrid_ids) * 100:.1f}%)")
    print(f"From Both: {both_in_hybrid} results ({both_in_hybrid / len(hybrid_ids) * 100:.1f}%)")
    print(f"Unique to Hybrid: {unique_in_hybrid} results ({unique_in_hybrid / len(hybrid_ids) * 100:.1f}%)")

async def list_collections(client: AsyncQdrantClient):
    """List all available collections in Qdrant."""
    collections_response = await client.get_collections()
    collections = collections_response.collections
    return [collection.name for collection in collections]

async def extract_tenant_id_from_collection(collection_name: str) -> Optional[str]:
    """Extract tenant ID from collection name if it follows the pattern real_products_{tenant_id}."""
    if collection_name.startswith("real_products_"):
        return collection_name[len("real_products_"):]
    return None

async def main():
    # Initialize client to list collections
    qdrant_client = get_qdrant_async_client()
    
    # List available collections
    print_section("Available Collections")
    collections = await list_collections(qdrant_client)
    product_collections = []
    
    for i, collection in enumerate(collections):
        tenant_id = await extract_tenant_id_from_collection(collection)
        if tenant_id:
            product_collections.append((tenant_id, collection))
            print(f"{i+1}. {collection} (tenant_id: {tenant_id})")
    
    if not product_collections:
        print(f"{Colors.RED}No product collections found. Available collections:{Colors.END}")
        for collection in collections:
            print(f" - {collection}")
        return
    
    # Vybrat tenanta filsonstore
    filsonstore_idx = None
    for idx, (tenant, collection) in enumerate(product_collections):
        if tenant == 'filsonstore':
            filsonstore_idx = idx
            break
    
    if filsonstore_idx is None:
        print(f"{Colors.RED}Tenant filsonstore nebyl nalezen. Použiju první kolekci.{Colors.END}")
        filsonstore_idx = 0
    else:
        print(f"\nVybrán tenant filsonstore.")
    
    tenant_id, collection_name = product_collections[filsonstore_idx]
    print(f"\nUsing tenant: {Colors.GREEN}{tenant_id}{Colors.END} with collection: {Colors.GREEN}{collection_name}{Colors.END}")
    
    # Test queries
    queries = [
        "iPhone",
        "notebook",
        "televizor 55 palců",
        "bezdrátová sluchátka",
        "čistička vzduchu"
    ]
    
    # Použít zadaný kód produktu jako dotaz
    custom_query = "3870101"  # Kód produktu pro filsonstore
    print(f"\nUsing product code as query: '{custom_query}'")
    
    # Run test for the query
    await test_search_methods(tenant_id, collection_name, custom_query)

if __name__ == "__main__":
    asyncio.run(main())
