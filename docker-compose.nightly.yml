version: '3'

services:
  nightly_process:
    build:
      context: .
      dockerfile: Dockerfile.nightly
    container_name: nightly_process
    volumes:
      - ./logs:/app/logs
    networks:
      - gallitec_network
    env_file:
      - .env
    # Standardně bez parametrů - spustí pro všechny tenanty
    # Pro spuštění s parametry lze použít např.:
    # command: --tenants avenberg --skip-sync

networks:
  gallitec_network:
    external: true
    name: recommendatio_and_semantic_default # Upravte podle názvu vaší existující Docker sítě 