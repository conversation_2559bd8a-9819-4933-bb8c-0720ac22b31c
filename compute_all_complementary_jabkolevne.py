#!/usr/bin/env python3
"""
Kompletní skript pro výpočet všech komplementárních produktů jabkolevne.cz
Tato verze používá realistické mapování kategorií založené na skutečné struktuře dat.

Analogie: <PERSON><PERSON><PERSON> man<PERSON> obchodu, který z<PERSON> každý produkt a dokáže vytvořit 
kompletní systém doporučení pro celý sortiment.

Autor: <PERSON>: 2025-01-17
"""

import json
import logging
import os
import time
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict, Counter
import re

from dotenv import load_dotenv
from neo4j import GraphDatabase
from qdrant_client import QdrantClient
from qdrant_client.http.models import Filter, FieldCondition, MatchValue

# Nastavení loggingu
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Načtení proměnných prostředí
load_dotenv()

# Konfigurace
TENANT_ID = "jabkolevne"
QDRANT_COLLECTION = f"real_products_{TENANT_ID}"
CATEGORY_LABEL = f"Category_{TENANT_ID}"

# Neo4j konfigurace
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "Graph2025Secure!")

# Qdrant konfigurace
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333

# Realistické mapování založené na skutečných kategoriích v databázi
CATEGORY_MAPPING_RULES = {
    "iPhone": {
        "contains": ["iPhone >", "> iPhone"],
        "keywords": ["iPhone"],
        "exclude": ["Příslušenství", "Kryty", "Tvrzená skla"]
    },
    "iPad": {
        "contains": ["iPad >", "> iPad"],
        "keywords": ["iPad"],
        "exclude": ["Příslušenství", "Kryty", "Tvrzená skla"]
    },
    "MacBook": {
        "contains": ["MacBook >", "> MacBook", "Mac >", "> Mac"],
        "keywords": ["MacBook", "Mac"],
        "exclude": ["Příslušenství"]
    },
    "Apple Watch": {
        "contains": ["Watch >", "> Watch", "Apple Watch"],
        "keywords": ["Watch"],
        "exclude": ["Příslušenství", "Řemínky"]
    },
    "AirPods": {
        "contains": ["AirPods >", "> AirPods"],
        "keywords": ["AirPods"],
        "exclude": ["Příslušenství", "Pouzdra"]
    },
    "Nabíječky (Apple)": {
        "contains": ["Nabíječky >", "> Nabíječky", "nabíječky >"],
        "keywords": ["nabíječk", "adaptér", "kabel", "Lightning", "USB-C"],
        "exclude": []
    },
    "Kabely (Apple)": {
        "contains": ["Kabely >", "> Kabely", "kabel"],
        "keywords": ["kabel", "Lightning", "USB"],
        "exclude": []
    },
    "Pouzdra pro iPhone": {
        "contains": ["Kryty na mobilní telefony Apple >", "iPhone"],
        "keywords": ["kryt", "pouzdro"],
        "include_all": ["Příslušenství", "iPhone"]
    },
    "Ochranná skla pro iPhone": {
        "contains": ["Tvrzená skla", "skla"],
        "keywords": ["sklo", "tvrzené"],
        "include_all": ["iPhone"]
    },
    "Pouzdra pro iPad": {
        "contains": ["Kryty", "Pouzdra"],
        "keywords": ["kryt", "pouzdro"],
        "include_all": ["iPad"]
    },
    "Ochranná skla pro iPad": {
        "contains": ["Tvrzená skla", "skla"],
        "keywords": ["sklo", "tvrzené"],
        "include_all": ["iPad"]
    },
    "Řemínky pro Apple Watch": {
        "contains": ["Řemínky >", "> Řemínky"],
        "keywords": ["řemínek", "náramek"],
        "include_all": ["Watch"]
    },
    "Bezdrátové nabíječky (Apple)": {
        "contains": ["Bezdrátové nabíječky", "bezdrátové"],
        "keywords": ["bezdrátov", "wireless"],
        "exclude": []
    },
    "Powerbanky (pro Apple)": {
        "contains": ["Powerbanky >", "> Powerbanky"],
        "keywords": ["powerbanka", "záložní"],
        "exclude": []
    },
    "Apple Pencil": {
        "contains": ["Apple Pencil", "Pencil"],
        "keywords": ["pencil", "pero"],
        "exclude": []
    },
    "AirTag": {
        "contains": ["AirTag"],
        "keywords": ["airtag"],
        "exclude": []
    },
    "Pouzdra pro AirPods": {
        "contains": ["Pouzdra na sluchátka", "sluchátka"],
        "keywords": ["pouzdro"],
        "include_all": ["AirPods"]
    }
}

class ComprehensiveComplementaryCalculator:
    """
    Kompletní kalkulátor komplementárních produktů - jako AI systém,
    který zná celý obchod a vytváří personalizovaná doporučení.
    """
    
    def __init__(self):
        self.neo4j_driver = None
        self.qdrant_client = None
        self.category_cache = {}
        self.all_categories_sample = {}
        
    def connect_to_databases(self) -> bool:
        """Připojí se k databázím s robustním error handlingem."""
        try:
            logger.info("🔗 Připojuji se k Neo4j a Qdrant databázím...")
            
            # Neo4j
            self.neo4j_driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
            with self.neo4j_driver.session() as session:
                result = session.run("RETURN 1 AS test")
                assert result.single()["test"] == 1
            
            # Qdrant
            self.qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
            collections = self.qdrant_client.get_collections().collections
            collection_names = [col.name for col in collections]
            if QDRANT_COLLECTION not in collection_names:
                logger.error(f"❌ Kolekce {QDRANT_COLLECTION} neexistuje")
                return False
                
            logger.info("✅ Připojení k databázím úspěšné")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při připojování: {e}")
            return False
    
    def analyze_all_categories(self, sample_size: int = 500) -> Dict[str, int]:
        """Analyzuje všechny kategorie v databázi pro lepší pochopení struktury."""
        try:
            logger.info(f"🔍 Analyzuji {sample_size} produktů pro mapování kategorií...")
            
            results = self.qdrant_client.scroll(
                collection_name=QDRANT_COLLECTION,
                limit=sample_size,
                with_payload=True,
                with_vectors=False
            )
            
            points, _ = results
            category_counts = Counter()
            
            for point in points:
                category = point.payload.get('category', 'Bez kategorie')
                category_counts[category] += 1
            
            # Uložíme pro pozdější použití
            self.all_categories_sample = dict(category_counts)
            
            logger.info(f"📈 Analyzováno {len(category_counts)} unikátních kategorií")
            logger.info("🏆 Top 15 nejčastějších kategorií:")
            for i, (category, count) in enumerate(category_counts.most_common(15), 1):
                logger.info(f"   {i:2d}. {category}: {count} produktů")
                
            return dict(category_counts)
            
        except Exception as e:
            logger.error(f"❌ Chyba při analýze kategorií: {e}")
            return {}
    
    def get_complementary_relationships(self) -> Dict[str, List[Tuple[str, float]]]:
        """Načte komplementární vztahy z Neo4j grafu."""
        try:
            with self.neo4j_driver.session() as session:
                query = f"""
                MATCH (source:{CATEGORY_LABEL})-[r:COMPLEMENTARY_TO]->(target:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN source.name as source_category, target.name as target_category, r.weight as weight
                ORDER BY source.name, r.weight DESC
                """
                
                result = session.run(query)
                relationships = defaultdict(list)
                
                for record in result:
                    source = record["source_category"]
                    target = record["target_category"]
                    weight = record["weight"]
                    relationships[source].append((target, weight))
                
                logger.info(f"📊 Načteno {len(relationships)} kategorií s komplementárními vztahy")
                
                # Detailní výpis vztahů
                logger.info("🔗 Komplementární vztahy:")
                for source, targets in relationships.items():
                    target_list = ", ".join([f"{target}({weight})" for target, weight in targets[:3]])
                    logger.info(f"   {source} -> {target_list}")
                
                return dict(relationships)
                
        except Exception as e:
            logger.error(f"❌ Chyba při načítání komplementárních vztahů: {e}")
            return {}
    
    def find_products_by_smart_matching(self, simplified_category: str, limit: int = 20) -> List[Dict]:
        """
        Inteligentní vyhledávání produktů pomocí pokročilých pravidel mapování.
        """
        cache_key = f"{simplified_category}:{limit}"
        if cache_key in self.category_cache:
            return self.category_cache[cache_key]
        
        rules = CATEGORY_MAPPING_RULES.get(simplified_category, {})
        contains_patterns = rules.get("contains", [])
        keywords = rules.get("keywords", [])
        exclude_patterns = rules.get("exclude", [])
        include_all_patterns = rules.get("include_all", [])
        
        matching_products = []
        
        try:
            # Procházíme všechny kategorie ze vzorku
            matching_categories = []
            
            for category, count in self.all_categories_sample.items():
                category_lower = category.lower()
                is_match = False
                
                # Kontrola obsahuje vzorce
                for pattern in contains_patterns:
                    if pattern.lower() in category_lower:
                        is_match = True
                        break
                
                # Kontrola klíčových slov
                if not is_match:
                    for keyword in keywords:
                        if keyword.lower() in category_lower:
                            is_match = True
                            break
                
                # Kontrola "musí obsahovat všechny"
                if include_all_patterns:
                    all_present = all(pattern.lower() in category_lower for pattern in include_all_patterns)
                    if all_present:
                        is_match = True
                
                # Kontrola vyloučení
                if is_match and exclude_patterns:
                    for exclude_pattern in exclude_patterns:
                        if exclude_pattern.lower() in category_lower:
                            is_match = False
                            break
                
                if is_match:
                    matching_categories.append((category, count))
            
            # Seřadíme kategorie podle četnosti
            matching_categories.sort(key=lambda x: x[1], reverse=True)
            
            logger.info(f"   🎯 '{simplified_category}': nalezeno {len(matching_categories)} odpovídajících kategorií")
            
            # Načteme produkty z nejčastějších kategorií
            products_per_category = max(1, limit // max(1, len(matching_categories)))
            
            for category, count in matching_categories[:5]:  # Top 5 kategorií
                try:
                    results = self.qdrant_client.scroll(
                        collection_name=QDRANT_COLLECTION,
                        limit=products_per_category,
                        with_payload=True,
                        with_vectors=False,
                        scroll_filter=Filter(
                            must=[
                                FieldCondition(
                                    key="category",
                                    match=MatchValue(value=category)
                                )
                            ]
                        )
                    )
                    
                    points, _ = results
                    for point in points:
                        product_data = {
                            'id': point.id,
                            'name': point.payload.get('name', 'Neznámý produkt'),
                            'category': point.payload.get('category', ''),
                            'price': point.payload.get('price', 0),
                            'url': point.payload.get('url', ''),
                            'availability': point.payload.get('availability', 'Neznámá'),
                            'brand': point.payload.get('brand', ''),
                            'match_category': category,
                            'category_frequency': count
                        }
                        matching_products.append(product_data)
                        
                except Exception as e:
                    logger.debug(f"   ⚠️ Chyba při načítání produktů z kategorie '{category}': {e}")
                    continue
            
            # Odstranění duplicit a omezení
            unique_products = {}
            for product in matching_products:
                unique_products[product['id']] = product
            
            result_products = list(unique_products.values())[:limit]
            
            # Uložíme do cache
            self.category_cache[cache_key] = result_products
            
            logger.info(f"   ✅ '{simplified_category}': nalezeno {len(result_products)} produktů")
            return result_products
            
        except Exception as e:
            logger.error(f"❌ Chyba při inteligentním vyhledávání pro '{simplified_category}': {e}")
            return []
    
    def calculate_complementary_for_product(self, product: Dict, relationships: Dict) -> List[Dict]:
        """Vypočítá komplementární produkty pro jeden produkt pomocí smart matching."""
        product_category = product.get('category', '')
        
        # Najdeme odpovídající zjednodušenou kategorii
        matching_simplified_category = None
        best_match_score = 0
        
        for simplified_cat, rules in CATEGORY_MAPPING_RULES.items():
            score = 0
            category_lower = product_category.lower()
            
            # Hodnocení podle různých pravidel
            contains_patterns = rules.get("contains", [])
            for pattern in contains_patterns:
                if pattern.lower() in category_lower:
                    score += 3
            
            keywords = rules.get("keywords", [])
            for keyword in keywords:
                if keyword.lower() in category_lower:
                    score += 2
            
            include_all_patterns = rules.get("include_all", [])
            if include_all_patterns:
                if all(pattern.lower() in category_lower for pattern in include_all_patterns):
                    score += 5
            
            exclude_patterns = rules.get("exclude", [])
            for exclude_pattern in exclude_patterns:
                if exclude_pattern.lower() in category_lower:
                    score -= 10  # Silné vyloučení
            
            if score > best_match_score:
                best_match_score = score
                matching_simplified_category = simplified_cat
        
        if not matching_simplified_category or best_match_score <= 0:
            logger.debug(f"   ⚠️ Produkt '{product['name']}' ('{product_category}') nemá odpovídající kategorii")
            return []
        
        # Získáme komplementární kategorie
        complementary_categories = relationships.get(matching_simplified_category, [])
        
        if not complementary_categories:
            logger.debug(f"   ⚠️ Kategorie '{matching_simplified_category}' nemá komplementární vztahy")
            return []
        
        # Najdeme produkty pro komplementární kategorie
        all_complementary_products = []
        for target_category, weight in complementary_categories:
            complementary_products = self.find_products_by_smart_matching(target_category, limit=8)
            
            for comp_product in complementary_products:
                comp_product['complementary_weight'] = weight
                comp_product['complementary_category'] = target_category
                comp_product['source_product_id'] = product['id']
                comp_product['source_product_name'] = product['name']
                comp_product['source_category'] = matching_simplified_category
                comp_product['match_score'] = best_match_score
                all_complementary_products.append(comp_product)
        
        # Seřadíme podle váhy a kvality shody
        all_complementary_products.sort(
            key=lambda x: (x['complementary_weight'], x.get('category_frequency', 0)), 
            reverse=True
        )
        
        if all_complementary_products:
            logger.info(f"   ✅ '{product['name'][:50]}...' ({matching_simplified_category}) -> {len(all_complementary_products)} doporučení")
        
        return all_complementary_products
    
    def calculate_all_complementary_products(self) -> Dict:
        """Vypočítá komplementární produkty pro celý katalog."""
        logger.info("🚀 === VÝPOČET KOMPLEMENTÁRNÍCH PRODUKTŮ PRO CELÝ KATALOG ===")
        
        # 1. Analýza kategorií
        logger.info("🔍 Analyzuji strukturu kategorií...")
        self.analyze_all_categories(sample_size=1000)
        
        # 2. Načtení komplementárních vztahů
        logger.info("📊 Načítám komplementární vztahy...")
        relationships = self.get_complementary_relationships()
        if not relationships:
            logger.error("❌ Žádné komplementární vztahy nenalezeny")
            return {}
        
        # 3. Načtení všech produktů
        logger.info(f"📦 Načítám všechny produkty z kolekce {QDRANT_COLLECTION}...")
        all_products = []
        offset = None
        batch_size = 200
        
        try:
            while True:
                results = self.qdrant_client.scroll(
                    collection_name=QDRANT_COLLECTION,
                    limit=batch_size,
                    offset=offset,
                    with_payload=True,
                    with_vectors=False
                )
                
                points, next_offset = results
                
                for point in points:
                    product_data = {
                        'id': point.id,
                        'name': point.payload.get('name', 'Neznámý produkt'),
                        'category': point.payload.get('category', ''),
                        'price': point.payload.get('price', 0),
                        'url': point.payload.get('url', ''),
                        'availability': point.payload.get('availability', 'Neznámá'),
                        'brand': point.payload.get('brand', ''),
                        'description': point.payload.get('description', '')
                    }
                    all_products.append(product_data)
                
                if next_offset is None or len(points) == 0:
                    break
                    
                offset = next_offset
                
                if len(all_products) % 500 == 0:
                    logger.info(f"   📦 Načteno {len(all_products)} produktů...")
        
        except Exception as e:
            logger.error(f"❌ Chyba při načítání produktů: {e}")
            return {}
        
        logger.info(f"✅ Celkem načteno {len(all_products)} produktů")
        
        # 4. Výpočet komplementárních produktů
        logger.info("🔄 Počítám komplementární produkty pro všechny produkty...")
        
        all_results = {}
        processed_count = 0
        successful_count = 0
        start_time = time.time()
        
        for i, product in enumerate(all_products):
            try:
                complementary_products = self.calculate_complementary_for_product(product, relationships)
                
                if complementary_products:
                    all_results[product['id']] = {
                        'source_product': product,
                        'complementary_products': complementary_products,
                        'total_recommendations': len(complementary_products)
                    }
                    successful_count += 1
                
                processed_count += 1
                
                # Progress report
                if processed_count % 100 == 0:
                    elapsed_time = time.time() - start_time
                    rate = processed_count / elapsed_time
                    remaining_time = (len(all_products) - processed_count) / rate
                    
                    logger.info(f"   📊 {processed_count}/{len(all_products)} ({processed_count/len(all_products)*100:.1f}%) | "
                              f"Úspěšných: {successful_count} | "
                              f"Rychlost: {rate:.1f} produktů/s | "
                              f"Zbývá: {remaining_time/60:.1f} min")
                    
            except Exception as e:
                logger.error(f"   ❌ Chyba u produktu {product.get('name', 'neznámý')}: {e}")
                continue
        
        total_time = time.time() - start_time
        logger.info(f"🎉 Výpočet dokončen za {total_time/60:.1f} minut!")
        logger.info(f"📊 {successful_count} z {len(all_products)} produktů má komplementární doporučení")
        
        return all_results
    
    def save_comprehensive_results(self, results: Dict, filename: str = "all_complementary_jabkolevne.json"):
        """Uloží kompletní výsledky do JSON souboru."""
        try:
            total_recommendations = sum(item['total_recommendations'] for item in results.values())
            
            json_data = {
                'metadata': {
                    'tenant': TENANT_ID,
                    'calculation_type': 'comprehensive',
                    'total_products_with_recommendations': len(results),
                    'total_recommendations': total_recommendations,
                    'average_recommendations_per_product': total_recommendations / len(results) if results else 0,
                    'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'version': '1.0'
                },
                'complementary_products': {}
            }
            
            for product_id, data in results.items():
                json_data['complementary_products'][str(product_id)] = {
                    'source_product': data['source_product'],
                    'complementary_products': data['complementary_products'],
                    'total_recommendations': data['total_recommendations']
                }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            file_size_mb = os.path.getsize(filename) / 1024 / 1024
            logger.info(f"💾 Kompletní výsledky uloženy do: {filename}")
            logger.info(f"📁 Velikost souboru: {file_size_mb:.2f} MB")
            
        except Exception as e:
            logger.error(f"❌ Chyba při ukládání kompletních výsledků: {e}")
    
    def generate_comprehensive_statistics(self, results: Dict):
        """Vygeneruje detailní statistiky."""
        try:
            if not results:
                logger.info("📊 Žádné výsledky pro statistiky")
                return
            
            total_products = len(results)
            total_recommendations = sum(item['total_recommendations'] for item in results.values())
            avg_recommendations = total_recommendations / total_products
            
            # Statistiky podle zdrojových kategorií
            source_category_stats = defaultdict(lambda: {'count': 0, 'total_recommendations': 0})
            target_category_stats = defaultdict(int)
            weight_distribution = defaultdict(int)
            
            for data in results.values():
                if data['complementary_products']:
                    source_cat = data['complementary_products'][0].get('source_category', 'Neznámá')
                    source_category_stats[source_cat]['count'] += 1
                    source_category_stats[source_cat]['total_recommendations'] += data['total_recommendations']
                    
                    for comp_product in data['complementary_products']:
                        target_category_stats[comp_product['complementary_category']] += 1
                        weight = comp_product['complementary_weight']
                        weight_distribution[weight] += 1
            
            logger.info("📊 === KOMPLETNÍ STATISTIKY KOMPLEMENTÁRNÍCH PRODUKTŮ ===")
            logger.info(f"📦 Celkem produktů s doporučeními: {total_products:,}")
            logger.info(f"🔗 Celkem doporučení: {total_recommendations:,}")
            logger.info(f"📈 Průměr doporučení na produkt: {avg_recommendations:.2f}")
            
            logger.info("🎯 Statistiky podle zdrojových kategorií:")
            for category, stats in sorted(source_category_stats.items(), key=lambda x: x[1]['count'], reverse=True):
                avg_per_product = stats['total_recommendations'] / stats['count']
                logger.info(f"   - {category}: {stats['count']:,} produktů, průměr {avg_per_product:.1f} doporučení/produkt")
            
            logger.info("🏆 Top 10 nejdoporučovanějších kategorií:")
            for category, count in sorted(target_category_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
                logger.info(f"   - {category}: {count:,} doporučení")
            
            logger.info("⚖️ Distribuce vah vztahů:")
            for weight, count in sorted(weight_distribution.items(), reverse=True):
                logger.info(f"   - Váha {weight}: {count:,} doporučení")
                
        except Exception as e:
            logger.error(f"❌ Chyba při generování statistik: {e}")
    
    def close_connections(self):
        """Uzavře všechna připojení."""
        if self.neo4j_driver:
            self.neo4j_driver.close()
        if self.qdrant_client:
            self.qdrant_client.close()
        logger.info("🔒 Všechna připojení uzavřena")

def main():
    """
    Hlavní funkce pro výpočet komplementárních produktů pro celý katalog.
    """
    calculator = ComprehensiveComplementaryCalculator()
    
    try:
        # Připojení k databázím
        if not calculator.connect_to_databases():
            logger.error("❌ Nepodařilo se připojit k databázím")
            return False
        
        # Výpočet všech komplementárních produktů
        results = calculator.calculate_all_complementary_products()
        
        if not results:
            logger.error("❌ Žádné komplementární produkty nebyly vypočítány")
            return False
        
        # Uložení výsledků
        calculator.save_comprehensive_results(results)
        
        # Generování statistik
        calculator.generate_comprehensive_statistics(results)
        
        logger.info("🎉 === KOMPLETNÍ VÝPOČET ÚSPĚŠNĚ DOKONČEN ===")
        return True
        
    except Exception as e:
        logger.error(f"❌ Kritická chyba: {e}")
        return False
        
    finally:
        calculator.close_connections()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 