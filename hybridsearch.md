# Hybridní vyhledávací systém (Fulltext + Semantic Search)

Implementace pokročilého hybridního vyhledávacího systému kombinujícího fulltextové a sémantické vyhledávání s následným rerankingem kandidátů.

## Architektura systému

```
┌─────────────────┐     ┌────────────────┐     ┌───────────────┐
│  Vstupní dotaz  ├────►│ 1. Retrievery  ├────►│  3. Reranker  ├─────► Finální výsledky
└─────────────────┘     └────────┬───────┘     └───────┬───────┘
                                 │                     ▲
                        ┌────────┴───────┐            │
                        │                │            │
                 ┌──────▼──────┐  ┌──────▼───────┐   │
                 │ 1a. Fulltext│  │ 1b. Semantic │   │
                 └──────┬──────┘  └──────┬───────┘   │
                        │                │           │
                        └────────┬───────┘           │
                                 │                   │
                                 ▼                   │
                        ┌────────────────┐           │
                        │  2. Ensemble   ├───────────┘
                        └────────────────┘
```

## Implementační detaily

### 1. Retrieval systémy

#### 1a. Fulltextový retriever

Pro kvalitní fulltextové vyhledávání v češtině:

```python
from rank_bm25 import BM25Okapi
import re
from pymorphy2 import MorphAnalyzer
from nltk.corpus import stopwords

class CzechFulltextRetriever:
    def __init__(self, documents):
        # Přípra<PERSON> stop slov pro češtinu
        self.stop_words = set(stopwords.words('czech'))
        
        # Inicializace morfologického analyzátoru
        self.morph = MorphAnalyzer(lang='cz')
        
        # Tokenizace a předzpracování dokumentů
        tokenized_docs = [self._preprocess_text(doc) for doc in documents]
        
        # Inicializace BM25
        self.bm25 = BM25Okapi(tokenized_docs)
        self.documents = documents
        
    def _preprocess_text(self, text):
        # Odstranění interpunkce a konverze na malá písmena
        text = re.sub(r'[^\w\s]', ' ', text.lower())
        
        # Tokenizace a odstranění stop slov
        tokens = [token for token in text.split() if token not in self.stop_words]
        
        # Lemmatizace (základní tvary slov)
        tokens = [self.morph.parse(token)[0].normal_form for token in tokens]
        
        return tokens
        
    def search(self, query, top_k=10):
        # Předzpracování dotazu
        query_tokens = self._preprocess_text(query)
        
        # Získání skóre pro každý dokument
        scores = self.bm25.get_scores(query_tokens)
        
        # Získání top-k výsledků
        top_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)[:top_k]
        
        results = [
            {
                "document": self.documents[idx],
                "score": scores[idx],
                "index": idx,
                "method": "fulltext"
            }
            for idx in top_indices if scores[idx] > 0
        ]
        
        return results
```

#### 1b. Sémantický retriever

```python
from sentence_transformers import SentenceTransformer
import numpy as np
import faiss

class SemanticRetriever:
    def __init__(self, documents, model_name="paraphrase-multilingual-mpnet-base-v2"):
        # Načtení mnohojazyčného modelu se silnou podporou češtiny
        self.model = SentenceTransformer(model_name)
        
        # Enkódování dokumentů
        self.documents = documents
        self.embeddings = self.model.encode(documents, show_progress_bar=True)
        
        # Vytvoření FAISS indexu pro rychlé vyhledávání
        self.dimension = self.embeddings.shape[1]
        self.index = faiss.IndexFlatIP(self.dimension)  # Inner product index (kosinová podobnost)
        self.index.add(self.embeddings)
        
    def search(self, query, top_k=10):
        # Enkódování dotazu
        query_vector = self.model.encode([query])[0].reshape(1, -1)
        
        # Vyhledání nejbližších sousedů
        scores, indices = self.index.search(query_vector, top_k)
        
        # Formátování výsledků
        results = [
            {
                "document": self.documents[idx],
                "score": float(scores[0][i]),
                "index": int(idx),
                "method": "semantic"
            }
            for i, idx in enumerate(indices[0])
        ]
        
        return results
```

### 2. Ensemble metoda

Pro kombinování výsledků z různých retrieverů:

```python
def ensemble_retrievers(fulltext_results, semantic_results, alpha=0.5):
    """
    Kombinuje výsledky z fulltextového a sémantického vyhledávání pomocí váženého skóre.
    
    Args:
        fulltext_results: Výsledky z fulltextového vyhledávání
        semantic_results: Výsledky ze sémantického vyhledávání
        alpha: Váha pro sémantické výsledky (1-alpha pro fulltext)
        
    Returns:
        Seznam kombinovaných výsledků
    """
    combined_dict = {}
    
    # Přidání fulltext výsledků s normalizací skóre
    max_ft_score = max([r["score"] for r in fulltext_results]) if fulltext_results else 1.0
    for result in fulltext_results:
        idx = result["index"]
        normalized_score = result["score"] / max_ft_score if max_ft_score > 0 else 0
        combined_dict[idx] = {
            "document": result["document"],
            "fulltext_score": normalized_score,
            "semantic_score": 0.0,
            "index": idx
        }
    
    # Přidání sémantických výsledků s normalizací skóre
    max_sem_score = max([r["score"] for r in semantic_results]) if semantic_results else 1.0
    for result in semantic_results:
        idx = result["index"]
        normalized_score = result["score"] / max_sem_score if max_sem_score > 0 else 0
        
        if idx in combined_dict:
            combined_dict[idx]["semantic_score"] = normalized_score
        else:
            combined_dict[idx] = {
                "document": result["document"],
                "fulltext_score": 0.0,
                "semantic_score": normalized_score,
                "index": idx
            }
    
    # Výpočet kombinovaného skóre
    for item in combined_dict.values():
        item["combined_score"] = (1 - alpha) * item["fulltext_score"] + alpha * item["semantic_score"]
    
    # Seřazení podle kombinovaného skóre
    combined_results = sorted(list(combined_dict.values()), 
                             key=lambda x: x["combined_score"], 
                             reverse=True)
    
    return combined_results
```

### 3. Reranker

Pro závěrečné přehodnocení kandidátů:

```python
from transformers import AutoModelForSequenceClassification, AutoTokenizer
import torch

class CzechReranker:
    def __init__(self, model_name="cross-encoder/ms-marco-MiniLM-L-6-v2"):
        # Načtení modelu a tokenizeru
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.model = AutoModelForSequenceClassification.from_pretrained(model_name)
        self.model.to(self.device)
        self.tokenizer = AutoTokenizer.from_pretrained(model_name)
        
    def rerank(self, query, candidates, top_k=10):
        # Příprava párů dotaz-dokument
        pairs = [[query, candidate["document"]] for candidate in candidates]
        
        # Tokenizace
        features = self.tokenizer(
            pairs,
            padding=True,
            truncation=True,
            return_tensors="pt"
        ).to(self.device)
        
        # Výpočet skóre
        with torch.no_grad():
            scores = self.model(**features).logits.flatten().cpu().numpy()
        
        # Přidání reranker skóre do výsledků
        for i, candidate in enumerate(candidates):
            candidate["rerank_score"] = float(scores[i])
        
        # Seřazení podle reranker skóre
        reranked = sorted(candidates, key=lambda x: x["rerank_score"], reverse=True)
        
        return reranked[:top_k]
```

## Kompletní implementace

Celý systém můžete zkombinovat takto:

```python
class HybridSearch:
    def __init__(self, documents):
        # Inicializace retrievers
        self.fulltext = CzechFulltextRetriever(documents)
        self.semantic = SemanticRetriever(documents)
        self.reranker = CzechReranker()
        
    def search(self, query, top_k=10, alpha=0.5, rerank=True):
        # 1. Získání kandidátů z fulltextového vyhledávače
        fulltext_results = self.fulltext.search(query, top_k=top_k*2)
        
        # 2. Získání kandidátů ze sémantického vyhledávače
        semantic_results = self.semantic.search(query, top_k=top_k*2)
        
        # 3. Kombinace výsledků
        combined_results = ensemble_retrievers(fulltext_results, semantic_results, alpha=alpha)
        
        # 4. Reranking (volitelný)
        if rerank:
            final_results = self.reranker.rerank(query, combined_results, top_k=top_k)
        else:
            final_results = combined_results[:top_k]
            
        return final_results
```

## Využití v praxi

```python
# Příprava dokumentů
documents = [
    "Fantom Dětské softshellové kalhoty do nápletu s fleecem šedé velikost 86",
    "Splash About Dětská plovací vesta Go Splash Up and Away velikost 2-4 roky",
    "Expres menu Hovězí guláš 2 porce",
    # ... další dokumenty
]

# Inicializace vyhledávače
hybrid_search = HybridSearch(documents)

# Vyhledávání
results = hybrid_search.search("dětské kalhoty softshell", top_k=5)
```

## Výhody tohoto přístupu

1. **Robustnější vyhledávání** - kombinace lexikálního a sémantického přístupu lépe zachytí relevantní výsledky
2. **Pokročilá česká podpora** - využití lemmatizace a českých jazykových specifik
3. **Škálovatelnost** - FAISS index umožňuje efektivní vyhledávání i ve velkých kolekcích
4. **Adaptabilita** - parametr alpha umožňuje ladit důraz na sémantiku versus přesné shody
5. **Přesnost** - finální reranking zajišťuje, že nejrelevantnější výsledky budou na prvních pozicích

## Závěr

Implementace je modularizovaná, takže můžete jednotlivé komponenty snadno nahradit nebo upravit podle potřeby. Pro produkční prostředí doporučujeme použití vektorových databází jako je Chroma, Milvus nebo Weaviate pro ukládání embedingů namísto přímého použití FAISS.

## Závislosti

```
pip install rank_bm25 pymorphy2 pymorphy2-dicts-cs sentence-transformers faiss-cpu nltk transformers torch
```

A pro načtení českých stop slov:

```python
import nltk
nltk.download('stopwords')
```
