<!DOCTYPE html>
<html lang="cs">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Interaktivní graf produktov<PERSON>ch kategorií</title>
  <style>
    body {
      font-family: 'Inter', Arial, sans-serif; /* Using Inter font */
      margin: 0;
      padding: 0;
      overflow: hidden;
      height: 100vh;
      display: flex;
      flex-direction: column;
      background-color: #f0f2f5; /* Light background for the body */
    }

    .header {
      background-color: #1a3a5f; /* Dark blue header */
      color: white;
      padding: 15px 20px; /* Increased padding */
      text-align: center;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Subtle shadow */
      border-bottom: 3px solid #f28e2c; /* Accent border */
    }

    .header h1 {
        margin: 0;
        font-size: 1.5em; /* Larger title */
        font-weight: 600; /* Semi-bold */
    }

    .container {
      display: flex;
      flex: 1;
      overflow: hidden; /* Important for layout */
    }

    .sidebar {
      width: 320px; /* Slightly wider sidebar */
      flex-shrink: 0; /* Prevent sidebar from shrinking */
      background-color: #ffffff; /* White sidebar */
      padding: 20px;
      overflow-y: auto;
      box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1); /* Enhanced shadow */
      border-right: 1px solid #e0e0e0; /* Subtle border */
      z-index: 10; /* Ensure sidebar is above graph if overlapping */
    }

    .main {
      flex: 1;
      position: relative;
      overflow: hidden; /* Important for SVG bounds */
      background-color: #f8f9fa; /* Lighter background for graph area */
      min-width: 0; /* Prevent flex item from overflowing */
    }

    .graph-container {
      width: 100%;
      height: 100%;
      position: absolute; /* Ensure it fills the main area */
      top: 0;
      left: 0;
    }
     /* Style for the "no data" message */
     .graph-container p {
        text-align: center;
        padding-top: 50px;
        color: #666;
        font-size: 1.1em;
     }


    .section {
      margin-bottom: 25px; /* Increased spacing */
      padding: 20px;
      background-color: #fff;
      border-radius: 8px; /* More rounded corners */
      box-shadow: 0 2px 5px rgba(0,0,0,0.08); /* Softer shadow */
      border: 1px solid #e5e7eb; /* Light border */
    }

    .section-title {
      font-weight: 600; /* Semi-bold title */
      margin-bottom: 18px;
      font-size: 1.1em; /* Slightly larger title */
      border-bottom: 2px solid #1a3a5f; /* Header color border */
      padding-bottom: 10px;
      color: #1a3a5f; /* Match header color */
    }

    .slider-group, .toggle-container {
      margin-bottom: 18px; /* Consistent spacing */
    }

     .slider-label, .toggle-container span:first-child {
        display: block; /* Make label block */
        margin-bottom: 8px; /* Space below label */
        font-size: 0.9em;
        color: #333;
        font-weight: 500;
    }

    .slider-label {
        display: flex;
        justify-content: space-between;
        align-items: center; /* Align value vertically */
    }

    .slider-label span:last-child {
        font-weight: 600;
        color: #1a3a5f;
    }


    .slider {
      width: 100%;
      cursor: pointer;
      accent-color: #1a3a5f; /* Match theme color */
    }

    .toggle-container {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .source-selector {
      margin-bottom: 15px;
    }
    
    .radio-container {
      display: block;
      margin-bottom: 10px;
      cursor: pointer;
    }
    
    .radio-label {
      margin-left: 8px;
      font-size: 14px;
      color: #333;
    }

    /* Nicer Toggle Switch */
    .toggle-switch {
      position: relative;
      display: inline-block;
      width: 44px; /* Slightly wider */
      height: 24px; /* Slightly taller */
    }

    .toggle-switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .toggle-slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
      border-radius: 24px; /* Fully rounded */
    }

    .toggle-slider:before {
      position: absolute;
      content: "";
      height: 18px; /* Larger knob */
      width: 18px;
      left: 3px; /* Adjust position */
      bottom: 3px; /* Adjust position */
      background-color: white;
      transition: .4s;
      border-radius: 50%;
      box-shadow: 0 1px 3px rgba(0,0,0,0.2);
    }

    input:checked + .toggle-slider {
      background-color: #1a3a5f; /* Theme color when checked */
    }

     input:focus + .toggle-slider {
        box-shadow: 0 0 1px #1a3a5f; /* Focus indicator */
     }

    input:checked + .toggle-slider:before {
      transform: translateX(20px); /* Adjust slide distance */
    }

    /* Button Styling */
    button {
      background-color: #1a3a5f;
      color: white;
      border: none;
      padding: 10px 15px; /* More padding */
      border-radius: 6px; /* Rounded corners */
      cursor: pointer;
      width: 100%;
      margin-bottom: 12px;
      font-size: 0.95em; /* Slightly larger font */
      font-weight: 500;
      transition: background-color 0.3s ease, transform 0.1s ease; /* Smooth transitions */
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    button:hover {
      background-color: #132f4c; /* Darker on hover */
      box-shadow: 0 4px 6px rgba(0, 0, 0, 0.15);
    }

     button:active {
        transform: translateY(1px); /* Press effect */
        box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
     }

     /* Specific button styles */
     #remove-relation {
        background-color: #e15759; /* Red for remove */
     }
     #remove-relation:hover {
        background-color: #c74243; /* Darker red */
     }
     #export-button {
         background-color: #59a14f; /* Green for export */
         margin-top: 10px; /* Space above export */
     }
     #export-button:hover {
         background-color: #447c3c; /* Darker green */
     }


    select {
      width: 100%;
      padding: 10px; /* More padding */
      margin-bottom: 15px; /* More spacing */
      border-radius: 6px;
      border: 1px solid #ccc; /* Lighter border */
      background-color: #fff;
      font-size: 0.9em;
      box-shadow: inset 0 1px 2px rgba(0,0,0,0.05);
      appearance: none; /* Remove default arrow */
      background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20width%3D%22292.4%22%20height%3D%22292.4%22%3E%3Cpath%20fill%3D%22%231a3a5f%22%20d%3D%22M287%2069.4a17.6%2017.6%200%200%200-13-5.4H18.4c-5%200-9.3%201.8-12.9%205.4A17.6%2017.6%200%200%200%200%2082.2c0%205%201.8%209.3%205.4%2012.9l128%20127.9c3.6%203.6%207.8%205.4%2012.8%205.4s9.2-1.8%2012.8-5.4L287%2095c3.5-3.5%205.4-7.8%205.4-12.8%200-5-1.9-9.2-5.5-12.8z%22%2F%3E%3C%2Fsvg%3E'); /* Updated fill color */
      background-repeat: no-repeat;
      background-position: right 10px top 50%;
      background-size: 10px auto;
      padding-right: 30px; /* Space for custom arrow */
    }

     select:focus {
        outline: none;
        border-color: #1a3a5f;
        box-shadow: 0 0 0 2px rgba(26, 58, 95, 0.2);
     }

    /* Graph Styles */
    .node {
      cursor: pointer;
      transition: transform 0.2s ease; /* Smooth transition for potential scaling */
    }

    .node circle {
      stroke: #fff;
      stroke-width: 2.5px; /* Slightly thicker stroke */
      transition: fill 0.3s ease, stroke 0.3s ease, r 0.3s ease; /* Smooth transitions */
      filter: drop-shadow(0px 1px 2px rgba(0,0,0,0.3)); /* Add subtle shadow */
    }

    .node:hover circle {
      fill: #f28e2c; /* Orange hover */
      stroke: #e6e6e6;
    }

    .node.selected circle {
      fill: #e41a1c; /* Red selected */
      stroke: #333; /* Darker stroke when selected */
      stroke-width: 3.5px;
    }

    /* Cluster Colors - Defined here for getNodeColor fallback */
    .node.cluster1 circle { fill: #4e79a7; } /* Blue */
    .node.cluster2 circle { fill: #59a14f; } /* Green */
    .node.cluster3 circle { fill: #e15759; } /* Red */
    .node.cluster4 circle { fill: #76b7b2; } /* Teal */
    .node.outlier circle { fill: #9c755f; } /* Brown */

    /* Node Text */
    .node text {
      font-size: 10px;
      fill: #333;
      text-anchor: middle;
      pointer-events: none;
      paint-order: stroke; /* Ensure text is readable over lines */
      stroke: white; /* White outline */
      stroke-width: 2px;
      stroke-linecap: butt;
      stroke-linejoin: miter;
      font-weight: 500;
      opacity: 0; /* Start hidden, fade in */
      transition: opacity 0.3s ease;
    }
     .node text.visible {
        opacity: 1;
     }


    /* Link Styles */
    .link {
      stroke: #adb5bd; /* Lighter gray links */
      stroke-opacity: 0.6;
      transition: stroke 0.3s ease, stroke-opacity 0.3s ease; /* Smooth transitions */
    }

    .link.highlighted {
      stroke: #f28e2c !important; /* Orange highlight */
      stroke-opacity: 1 !important;
    }

    /* Tooltip */
    .tooltip {
      position: absolute;
      background-color: rgba(40, 40, 40, 0.9); /* Darker tooltip */
      color: white;
      padding: 10px 15px;
      border-radius: 6px;
      border: none; /* Remove border */
      pointer-events: none;
      font-size: 13px; /* Slightly smaller */
      box-shadow: 0 3px 8px rgba(0,0,0,0.2); /* More pronounced shadow */
      display: none;
      z-index: 100;
      max-width: 250px; /* Limit width */
      white-space: normal; /* Allow wrapping */
      transition: opacity 0.2s ease; /* Fade in/out */
    }
    .tooltip strong {
        color: #f28e2c; /* Accent color for category name */
    }

    /* Legend */
    .legend {
      position: absolute;
      bottom: 25px;
      left: 25px;
      background-color: rgba(255, 255, 255, 0.95); /* Slightly more opaque */
      padding: 15px;
      border-radius: 8px;
      font-size: 12px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.15);
      z-index: 50;
      max-width: 220px; /* Limit width */
    }

    .legend-title {
      font-weight: 600;
      margin-bottom: 8px;
      font-size: 1.1em;
      color: #1a3a5f;
    }

    .legend-item, .color-legend {
      display: flex;
      align-items: center;
      margin-bottom: 6px;
    }

    .legend-line {
      width: 30px;
      margin-right: 10px;
      border-radius: 1px; /* Slightly rounded ends */
    }

    .color-dot {
      width: 14px; /* Larger dots */
      height: 14px;
      border-radius: 50%;
      margin-right: 10px;
      border: 1px solid rgba(0,0,0,0.1); /* Subtle border */
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .container {
            flex-direction: column;
            overflow: hidden; /* Hide overflow on main container */
        }
        .sidebar {
            width: 100%;
            max-height: 50vh; /* Allow slightly more height */
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            border-right: none;
            border-bottom: 1px solid #e0e0e0;
            padding: 15px;
            box-sizing: border-box; /* Include padding in width/height */
        }
        .main {
            height: auto; /* Allow main content to take remaining height */
            flex-grow: 1; /* Ensure it fills space */
            overflow: hidden; /* Ensure graph stays within bounds */
        }
        .header h1 {
            font-size: 1.2em;
        }
        .legend {
            bottom: 15px;
            left: 15px;
            padding: 10px;
            max-width: 180px; /* Adjust legend size */
            font-size: 11px;
        }
    }
     @media (max-width: 480px) {
         .sidebar {
             padding: 10px;
             max-height: 60vh; /* More height on small screens */
         }
         button {
             padding: 8px 12px;
             font-size: 0.9em;
         }
         select {
             padding: 8px;
             font-size: 0.85em;
         }
         .section-title {
             font-size: 1em;
         }
         .legend {
            display: none; /* Hide legend on very small screens */
         }
     }

  </style>
</head>
<body>
  <div class="header">
    <h1>Interaktivní graf produktových kategorií</h1>
  </div>

  <div class="container">
    <div class="sidebar">
      <div class="section">
        <div class="section-title">Datový zdroj</div>

        <div class="source-selector">
          <label class="radio-container">
            <input type="radio" name="data-source" value="filsonstore_graph_data.json" checked>
            <span class="radio-label">Filsonstore data</span>
          </label>
          <label class="radio-container">
            <input type="radio" name="data-source" value="category_relationships_avenberg.json">
            <span class="radio-label">Avenberg data</span>
          </label>
        </div>
        <button id="load-data-btn">Načíst data</button>
      </div>

      <div class="section">
        <div class="section-title">Nastavení zobrazení</div>

        <div class="toggle-container">
          <span>Zobrazit popisky</span>
          <label class="toggle-switch">
            <input type="checkbox" id="show-labels" checked>
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="toggle-container">
          <span>Barevné skupiny</span>
          <label class="toggle-switch">
            <input type="checkbox" id="show-clusters" checked>
            <span class="toggle-slider"></span>
          </label>
        </div>

        <div class="slider-group">
          <div class="slider-label">
            <span>Minimální síla vztahu:</span>
            <span id="strength-value">0.30</span>
          </div>
          <input type="range" id="min-strength" class="slider" min="0" max="1" step="0.05" value="0.3">
        </div>

        <div class="slider-group">
          <div class="slider-label">
            <span>Velikost uzlů:</span>
            <span id="node-size-value">7</span>
          </div>
          <input type="range" id="node-size" class="slider" min="3" max="15" step="1" value="7">
        </div>

        <button id="reset-view">Resetovat pohled</button>
      </div>

      <div class="section">
        <div class="section-title">Úprava vztahů</div>

        <select id="source-category">
          <option value="">Vyberte výchozí kategorii</option>
        </select>

        <select id="target-category">
          <option value="">Vyberte cílovou kategorii</option>
        </select>

        <div class="slider-group">
          <div class="slider-label">
            <span>Síla vztahu:</span>
            <span id="relation-strength-value">0.50</span>
          </div>
          <input type="range" id="relation-strength" class="slider" min="0.1" max="1" step="0.05" value="0.5">
        </div>

        <div class="toggle-container">
          <span>Obousměrný vztah</span>
          <label class="toggle-switch">
            <input type="checkbox" id="bidirectional" checked>
            <span class="toggle-slider"></span>
          </label>
        </div>

        <button id="add-relation">Přidat/Upravit vztah</button>
        <button id="remove-relation">Odebrat vztah</button>
      </div>

      <button id="export-button">Exportovat data</button>
    </div>

    <div class="main">
      <div class="graph-container" id="graph">
          </div>
      <div class="tooltip"></div>
      <div class="legend">
        <div class="legend-title">Síla vztahu</div>
        <div class="legend-item">
          <div class="legend-line" style="background-color: #999; height: 1px;"></div>
          <span>0.1 - 0.3 (Slabý)</span>
        </div>
        <div class="legend-item">
          <div class="legend-line" style="background-color: #666; height: 2px;"></div>
          <span>0.4 - 0.6 (Střední)</span>
        </div>
        <div class="legend-item">
          <div class="legend-line" style="background-color: #333; height: 3px;"></div>
          <span>0.7 - 0.9 (Silný)</span>
        </div>
        <div class="legend-item">
          <div class="legend-line" style="background-color: #000; height: 4px;"></div>
          <span>1.0 (Maximální)</span>
        </div>

        <div class="legend-title" style="margin-top: 15px;">Produktové skupiny</div>
        <div id="cluster-legend">
            </div>
      </div>
    </div>
  </div>

  <script src="https://cdnjs.cloudflare.com/ajax/libs/d3/7.8.5/d3.min.js"></script>
  <script>
    // Wait for the DOM to be fully loaded before running the script
    document.addEventListener('DOMContentLoaded', () => {
        console.log("DOM fully loaded and parsed");

        // --- Data ---
        // Data budou načtena ze souboru
        let graphData = {}; // Prázdný objekt pro načtení dat

        // Product clusters definition pro Avenberg data
        const productClusters = {
          "cluster1": { name: "Zahradní nábytek", color: "#4e79a7", categories: [
             "Zahradní sedací soupravy", "Sety zahradního nábytku", "Lehátka a houpačky", 
             "Zahradní lavice", "Zahradní židle", "Zahradní relaxační křesla", "Ratanový nábytek"
          ] },
          "cluster2": { name: "Grilování a ohřev", color: "#59a14f", categories: [
             "Příslušenství ke grilům", "Grily na dřevěné uhlí", "Krby a ohniště", 
             "Malé grily", "Stolní a kempingové grily", "Přenosné a skládací grily", 
             "Plynové grily", "Udírny", "Terasové ohřívače", "Velké luxusní grily"
          ] },
          "cluster3": { name: "Stínění a ochrana", color: "#e15759", categories: [
             "Altány a slunečníky", "Pergoly", "Markýzy", "Ochranné plachty", 
             "Přístřešky na auto"
          ] },
          "cluster4": { name: "Dílna a nářadí", color: "#76b7b2", categories: [
             "Nářadí (Dílna)", "Zahradní nářadí a příslušenství", "Domky na nářadí", 
             "Vybavení do dílny", "Vozíky (Dílna)", "Domky na dřevo", "Přístřešky na dřevo"
          ] },
          "cluster5": { name: "Bydlení", color: "#f28e2c", categories: [
             "Bytové doplňky", "Pohovky a sofa (Bydlení)", "Relaxační křesla a lenošky (Bydlení)", 
             "Sedací soupravy (Bydlení)", "Jídelní židle a stoly", "Jídelní sety", "Mobilita (Bydlení)"
          ] },
          "cluster6": { name: "Ostatní", color: "#9c755f", categories: [
             "Bazénové příslušenství", "Solární ohřevy a sprchy", "Solární ohřevy vody",
             "Venkovní osvětlení a makety kamer", "Skleníky a pařníky", "Květináče a dekorace",
             "Úložné boxy", "Kancelářské židle"
          ] }
        };
        
        // Asynchronně načteme data ze zvoleného souboru
        async function loadGraphData(sourceFile) {
            try {
                // Použij parametr sourceFile nebo výchozí hodnotu
                const dataSource = sourceFile || document.querySelector('input[name="data-source"]:checked').value;
                console.log('Načítám data ze souboru:', dataSource);
                
                const response = await fetch(dataSource);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                graphData = await response.json();
                console.log('Data načtena:', Object.keys(graphData).length, 'kategorií');
                
                // Inicializace grafu po načtení dat
                populateSelects();
                populateClusterLegend();
                initializeGraph();
                
                // Aktualizace nadpisu stránky s informací o zdroji dat
                const sourceLabel = dataSource.includes('avenberg') ? 'Avenberg' : 'Filsonstore';
                document.querySelector('.header h1').textContent = `Interaktivní graf produktových kategorií (${sourceLabel})`;
            } catch (error) {
                console.error('Chyba při načítání dat:', error);
                graphContainer.innerHTML = `<p style='color: red; text-align: center; padding: 20px;'>Nepodařilo se načíst data grafu (${error.message}). Zkontrolujte soubor '${dataSource}'.</p>`;
            }
        }

        // --- Global Variables & Settings ---
        let minStrength = 0.3;
        let baseNodeSize = 7;
        let showLabels = true;
        let showClusters = true;
        let selectedNode = null;
        let simulation;
        let svg, g, zoom; // D3 elements
        let linkElements, nodeElements; // D3 selections
        const tooltip = d3.select(".tooltip");
        const graphContainer = document.getElementById('graph'); // Get graph container element

        // --- DOM Elements ---
        const strengthSlider = document.getElementById('min-strength');
        const strengthValueSpan = document.getElementById('strength-value');
        const nodeSizeSlider = document.getElementById('node-size');
        const nodeSizeValueSpan = document.getElementById('node-size-value');
        const showLabelsToggle = document.getElementById('show-labels');
        const showClustersToggle = document.getElementById('show-clusters');
        const resetButton = document.getElementById('reset-view');
        const sourceSelect = document.getElementById('source-category');
        const targetSelect = document.getElementById('target-category');
        const relationStrengthSlider = document.getElementById('relation-strength');
        const relationStrengthValueSpan = document.getElementById('relation-strength-value');
        const bidirectionalToggle = document.getElementById('bidirectional');
        const addRelationButton = document.getElementById('add-relation');
        const removeRelationButton = document.getElementById('remove-relation');
        const exportButton = document.getElementById('export-button');
        const clusterLegendDiv = document.getElementById('cluster-legend');

        // --- Helper Functions ---

        function populateSelects() {
            console.log("Populating selects...");
            const categories = Object.keys(graphData).sort();
            sourceSelect.innerHTML = '<option value="">Vyberte výchozí kategorii</option>';
            targetSelect.innerHTML = '<option value="">Vyberte cílovou kategorii</option>';
            categories.forEach(category => {
                const sourceOption = document.createElement('option');
                sourceOption.value = category; sourceOption.textContent = category;
                sourceSelect.appendChild(sourceOption);
                const targetOption = document.createElement('option');
                targetOption.value = category; targetOption.textContent = category;
                targetSelect.appendChild(targetOption);
            });
            console.log("Selects populated.");
        }

        function populateClusterLegend() {
            console.log("Populating cluster legend...");
            clusterLegendDiv.innerHTML = '';
            Object.entries(productClusters).forEach(([clusterId, clusterInfo]) => {
                const legendItem = document.createElement('div'); legendItem.className = 'color-legend';
                const colorDot = document.createElement('div'); colorDot.className = `color-dot`; colorDot.style.backgroundColor = clusterInfo.color;
                const textSpan = document.createElement('span'); textSpan.textContent = clusterInfo.name;
                legendItem.appendChild(colorDot); legendItem.appendChild(textSpan);
                clusterLegendDiv.appendChild(legendItem);
            });
            console.log("Cluster legend populated.");
        }

        function prepareGraphData() {
            console.log(`Preparing graph data with minStrength: ${minStrength}`);
            // Add initial x/y to nodes for potentially better stability
            const width = graphContainer.clientWidth || 600; // Fallback width
            const height = graphContainer.clientHeight || 400; // Fallback height
            const nodes = Object.keys(graphData).map(category => {
                let group = "outlier";
                for (const [clusterId, clusterInfo] of Object.entries(productClusters)) {
                    if (clusterInfo.categories.includes(category)) { group = clusterId; break; }
                }
                return {
                    id: category, label: category, group: group,
                    x: Math.random() * width, // Random initial position
                    y: Math.random() * height
                };
            });

            const links = [];
            const addedLinks = new Set();
            
            // Vypisování pro debugging
            console.log('graphData keys:', Object.keys(graphData).length);
            
            Object.entries(graphData).forEach(([source, relations]) => {
                if (typeof relations !== 'object') {
                    console.warn(`Neplatná relace pro ${source}:`, relations);
                    return;
                }
                
                Object.entries(relations).forEach(([target, value]) => {
                    if (!graphData.hasOwnProperty(target)) {
                        console.warn(`Cílová kategorie neexistuje: ${target}`);
                        return;
                    }
                    
                    if (value < minStrength) return;
                    
                    // Create a consistent key regardless of source/target order
                    const linkKey = [source, target].sort().join('--');
                    if (!addedLinks.has(linkKey)) {
                        links.push({ source: source, target: target, value: value });
                        addedLinks.add(linkKey);
                    }
                });
            });

            // Filter out nodes that have no links after filtering by minStrength
            const participatingNodeIds = new Set();
            links.forEach(link => {
                participatingNodeIds.add(link.source); // Use string IDs here as simulation hasn't run yet
                participatingNodeIds.add(link.target);
            });

            // Include only nodes that participate in at least one link *at the current strength*
            // or nodes that have relations defined *even if filtered out* (optional, depends on desired behavior)
            const filteredNodes = nodes.filter(node => participatingNodeIds.has(node.id) || Object.keys(graphData[node.id] || {}).length > 0); // Keep nodes with potential links too

            console.log(`Prepared ${filteredNodes.length} nodes and ${links.length} links.`);
            return { nodes: filteredNodes, links };
        }


        function calculateNodeCentrality(nodes, links) {
          const centrality = {};
          nodes.forEach(node => { centrality[node.id] = 0; });
          links.forEach(link => {
            const sourceId = typeof link.source === 'object' ? link.source.id : link.source;
            const targetId = typeof link.target === 'object' ? link.target.id : link.target;
            const value = link.value || 0;
            if (centrality.hasOwnProperty(sourceId)) { centrality[sourceId] += value; }
            if (centrality.hasOwnProperty(targetId)) { centrality[targetId] += value; }
          });
          return centrality;
        }

        function calculateNodeRadius(node, centralityMap) {
          const centralityValue = centralityMap[node.id] || 0;
          return baseNodeSize + Math.sqrt(centralityValue) * 1.5;
        }

        function getLinkStroke(value) {
            if (value >= 0.7) return "#333";
            if (value >= 0.4) return "#666";
            return "#999";
        }

        function getLinkStrokeWidth(value) {
            return 1 + value * 3.5;
        }

        // --- D3 Graph Initialization and Update ---

        function initializeGraph() {
            console.log("Initializing graph...");
            const { nodes, links } = prepareGraphData();

            // Clear previous graph/message
            graphContainer.innerHTML = ''; // Clear container

            if (nodes.length === 0) {
                console.warn("No nodes to display based on current filter settings.");
                graphContainer.innerHTML = "<p>Žádná data k zobrazení pro aktuální nastavení síly vztahu.</p>";
                // Clear global refs if graph is removed
                svg = g = simulation = linkElements = nodeElements = null;
                return;
            }

            const nodeCentrality = calculateNodeCentrality(nodes, links);
            const width = graphContainer.clientWidth;
            const height = graphContainer.clientHeight;
            console.log(`Graph container size: ${width}x${height}`);

            svg = d3.select(graphContainer)
                .append("svg")
                .attr("width", "100%")
                .attr("height", "100%")
                .attr("viewBox", [0, 0, width, height])
                .style("background-color", "#f8f9fa");
            console.log("SVG element created.");

            svg.on("click", () => { /* ... SVG click handler ... */
                if (selectedNode) {
                    console.log("SVG clicked, deselecting node.");
                    selectedNode = null;
                    if (nodeElements) nodeElements.classed("selected", false).select('circle').attr('stroke', '#fff');
                    if (linkElements) linkElements.style("stroke-opacity", 0.6).classed("highlighted", false);
                    if (nodeElements) nodeElements.style("opacity", 1);
                    sourceSelect.value = ""; targetSelect.value = "";
                    relationStrengthSlider.value = 0.5; relationStrengthValueSpan.textContent = '0.50';
                }
            });

            zoom = d3.zoom().scaleExtent([0.1, 4]).on("zoom", (event) => { g.attr("transform", event.transform); });
            svg.call(zoom);

            g = svg.append("g"); // Main group for zoom/pan

            // Připravíme obal pro odkazy
            const linksGroup = g.append("g").attr("class", "links");
            
            // Přidáme jednotlivé odkazy (lines)
            linkElements = linksGroup.selectAll("line")
                .data(links)
                .enter()
                .append("line")
                .attr("class", "link")
                .attr("stroke", d => getLinkStroke(d.value))
                .attr("stroke-width", d => getLinkStrokeWidth(d.value))
                .attr("stroke-opacity", 0.6);
                
            console.log("Links připraveny, počet:", linkElements.size());
            console.log(`Created ${linkElements.size()} link elements.`);

            // Připravíme obal pro uzly
            const nodesGroup = g.append("g").attr("class", "nodes");
            
            // Vytvoříme jednotlivé uzly
            nodeElements = nodesGroup.selectAll("g.node")
                .data(nodes)
                .enter()
                .append("g")
                .attr("class", d => `node ${showClusters ? d.group : ''}`)
                .call(drag(simulation)) // Přidáme funkčnost táhnutí zde
                .on("mouseover", handleMouseOver)
                .on("mouseout", handleMouseOut)
                .on("click", handleClick);

            // Přidáme kruh k uzlům
            nodeElements.append("circle")
                .attr("r", d => calculateNodeRadius(d, nodeCentrality))
                .attr("fill", d => getNodeColor(d));

            // Přidáme text k uzlům
            nodeElements.append("text")
                .attr("dy", ".35em")
                .text(d => d.label || d.id) // Použijeme label, nebo fallback na id
                .classed("visible", showLabels);
                
            console.log("Nodes připraveny, počet:", nodeElements.size()); // Use class for visibility control

            console.log(`Created ${nodeElements.size()} node elements (groups).`);

            console.log('Spouštím simulaci s', nodes.length, 'uzly a', links.length, 'vazbami');
            
            // Nejprve vytvořím simulaci
            simulation = d3.forceSimulation()
                .force('link', d3.forceLink().id(d => d.id).distance(70))
                .force('charge', d3.forceManyBody().strength(-120 - baseNodeSize * 18))
                .force('center', d3.forceCenter(width / 2, height / 2))
                .force('collide', d3.forceCollide().radius(d => calculateNodeRadius(d, nodeCentrality) + 8))
                .on('tick', ticked);
                
            // Pak nastavím data simulace
            simulation.nodes(nodes);
            simulation.force('link').links(links);
            
            // Nastavím sílu vazeb podle hodnoty
            simulation.force('link').strength(link => link.value * 0.4 + 0.1);
            
            console.log('Simulace spuštěna');
            
            // Již není potřeba přidávat drag, protože jsme to udělali výše
            updateGraphStyles(); // Apply initial styles
        }

        function updateGraph() {
            console.log("Updating graph...");
            if (!graphContainer) {
                console.error("Graph container not found!");
                return;
            }
            const { nodes, links } = prepareGraphData();

            // --- Handle empty graph state ---
            if (nodes.length === 0) {
                console.warn("Update resulted in 0 nodes. Clearing graph.");
                if (simulation) simulation.stop(); // Stop existing simulation
                graphContainer.innerHTML = "<p>Žádná data k zobrazení pro aktuální nastavení síly vztahu.</p>";
                svg = g = simulation = linkElements = nodeElements = null; // Clear refs
                return;
            } else if (!simulation || !svg) {
                // If graph was previously empty or doesn't exist, initialize fully
                console.log("Graph was empty or non-existent, re-initializing.");
                initializeGraph();
                return;
            } else {
                 // Clear any "no data" message if graph is now visible
                 const noDataMsg = graphContainer.querySelector("p");
                 if (noDataMsg) noDataMsg.remove();
            }

            console.log(`Updating with ${nodes.length} nodes and ${links.length} links.`);
            const nodeCentrality = calculateNodeCentrality(nodes, links);
            const width = graphContainer.clientWidth;
            const height = graphContainer.clientHeight;

            // --- Update Simulation Data ---
            simulation.nodes(nodes); // Update nodes
            simulation.force("link").links(links); // Update links
            simulation.force("charge").strength(-120 - baseNodeSize * 18);
            simulation.force("collide").radius(d => calculateNodeRadius(d, nodeCentrality) + 8);
            simulation.force("center", d3.forceCenter(width / 2, height / 2)); // Re-center if needed

            // --- Update Links ---
            linkElements = g.select(".links")
                .selectAll("line")
                .data(links, d => {
                    const sourceId = typeof d.source === 'object' ? d.source.id : d.source;
                    const targetId = typeof d.target === 'object' ? d.target.id : d.target;
                    return `${sourceId}-${targetId}`;
                })
                .join(
                    enter => enter.append("line")
                        .attr("class", "link")
                        .attr("stroke", d => getLinkStroke(d.value))
                        .attr("stroke-width", 0).attr("stroke-opacity", 0)
                        .attr("x1", d => d.source.x || width / 2).attr("y1", d => d.source.y || height / 2) // Start at current/center pos
                        .attr("x2", d => d.target.x || width / 2).attr("y2", d => d.target.y || height / 2),
                    update => update,
                    exit => exit.transition("exit").duration(300) // Named transition
                        .attr("stroke-opacity", 0).attr("stroke-width", 0)
                        .remove()
                )
                .call(update => update.transition("update").duration(500)
                    .attr("stroke", d => getLinkStroke(d.value))
                    .attr("stroke-width", d => getLinkStrokeWidth(d.value))
                    .attr("stroke-opacity", 0.6)
                );
            console.log(`Updated link elements selection size: ${linkElements.size()}`);

            // --- Update Nodes ---
            nodeElements = g.select(".nodes")
                .selectAll("g.node")
                .data(nodes, d => d.id)
                .join(
                    enter => {
                        const newNodeGroup = enter.append("g")
                            .attr("class", d => `node ${showClusters ? d.group : ''}`)
                            .call(drag(simulation))
                            .on("mouseover", handleMouseOver).on("mouseout", handleMouseOut).on("click", handleClick)
                            .style("opacity", 0)
                            .attr("transform", d => `translate(${d.x || width / 2}, ${d.y || height / 2})`); // Use initial/center pos

                        newNodeGroup.append("circle").attr("r", 0).attr("fill", d => getNodeColor(d));
                        newNodeGroup.append("text").attr("dy", ".35em").text(d => d.label).classed("visible", showLabels);
                        return newNodeGroup;
                    },
                    update => update,
                    exit => exit.transition("exit").duration(300) // Named transition
                         .style("opacity", 0)
                         .select("circle").attr("r", 0)
                         .end()
                         .then(() => exit.remove())
                         .catch(err => console.error("Error during node exit transition:", err)) // Catch potential errors
                );

            // Apply transitions to all node elements
            nodeElements.transition("update").duration(500)
                .style("opacity", 1)
                .attr("class", d => `node ${showClusters ? d.group : ''} ${selectedNode && selectedNode.id === d.id ? 'selected' : ''}`);
            
            // Apply transitions to circles separately
            nodeElements.select("circle").transition("update").duration(500)
                .attr("r", d => calculateNodeRadius(d, nodeCentrality))
                .attr("fill", d => getNodeColor(d));
            nodeElements.select("text").classed("visible", showLabels); // Update label visibility class immediately

            console.log(`Updated node elements selection size: ${nodeElements.size()}`);

            simulation.alpha(0.6).restart(); // Restart simulation
            console.log("Simulation restarted.");
            updateGraphStyles(); // Apply style changes
        }


        function ticked() {
            // Přidání více diagnostických výpisů a kontrola dat
            if (!simulation) {
                console.warn("Tick: Simulace neexistuje");
                return;
            }

            try {
                // Kontrola a aktualizace pozic vazeb
                if (linkElements && linkElements.size() > 0) {
                    linkElements
                        .attr("x1", d => {
                            if (!d.source) console.warn("Link bez source:", d);
                            return d.source.x;
                        })
                        .attr("y1", d => d.source.y)
                        .attr("x2", d => d.target.x)
                        .attr("y2", d => d.target.y);
                } else {
                    console.warn("Tick: linkElements chybí", linkElements ? linkElements.size() : 0);
                }

                // Kontrola a aktualizace pozic uzlů
                if (nodeElements && nodeElements.size() > 0) {
                    nodeElements.attr("transform", d => {
                        if (!d.x || !d.y) console.warn("Node bez souřadnic:", d);
                        return `translate(${d.x || 0},${d.y || 0})`;
                    });
                } else {
                    console.warn("Tick: nodeElements chybí", nodeElements ? nodeElements.size() : 0);
                }
            } catch (err) {
                console.error("Chyba v ticked():", err);
            }
        }

        function updateGraphStyles() {
            if (!nodeElements || !simulation) {
                 console.warn("updateGraphStyles: nodeElements or simulation not ready.");
                 return; // Exit if elements aren't ready
            }
            console.log("Updating graph styles...");

            const nodeCentrality = calculateNodeCentrality(simulation.nodes(), simulation.force("link").links());

            nodeElements.attr("class", d => `node ${showClusters ? d.group : ''} ${selectedNode && selectedNode.id === d.id ? 'selected' : ''}`);
            nodeElements.select("circle")
                .transition().duration(300)
                .attr("r", d => calculateNodeRadius(d, nodeCentrality))
                .attr("fill", d => getNodeColor(d));

            // Use the class for text visibility
            nodeElements.select("text").classed("visible", showLabels);

            // Update simulation forces only if needed (e.g., if baseNodeSize changed)
            // simulation.force("charge").strength(...);
            // simulation.force("collide").radius(...);
            // if (simulation.alpha() < 0.1) { simulation.alpha(0.1).restart(); }
        }

        function getNodeColor(d) {
            if (showClusters && productClusters[d.group]) { return productClusters[d.group].color; }
            else { return '#4e79a7'; } // Default blue
        }

        // --- Interaction Handlers ---
        function drag(simulationInstance) { /* ... drag functions ... */
            function dragstarted(event, d) { if (!event.active) simulationInstance.alphaTarget(0.3).restart(); d.fx = d.x; d.fy = d.y; }
            function dragged(event, d) { d.fx = event.x; d.fy = event.y; }
            function dragended(event, d) { if (!event.active) simulationInstance.alphaTarget(0); d.fx = null; d.fy = null; }
            return d3.drag().on("start", dragstarted).on("drag", dragged).on("end", dragended);
        }

        function handleMouseOver(event, d) { /* ... mouseover handler ... */
            if (!nodeElements || !linkElements) return;
            tooltip.style("display", "block").style("opacity", 1)
                   .html(`<strong>${d.label}</strong><br>Skupina: ${productClusters[d.group]?.name || 'N/A'}`)
                   .style("left", (event.pageX + 15) + "px").style("top", (event.pageY - 10) + "px");
            nodeElements.style("opacity", n => isNeighbor(d, n) ? 1 : 0.3);
            linkElements.style("stroke-opacity", l => linkSourceTargetMatch(l, d) ? 1 : 0.1)
                        .classed("highlighted", l => linkSourceTargetMatch(l, d));
            d3.select(event.currentTarget).style("opacity", 1).select('circle').attr('stroke', '#f28e2c');
        }

        function handleMouseOut(event, d) { /* ... mouseout handler ... */
             if (!nodeElements || !linkElements) return;
             tooltip.style("opacity", 0).transition().delay(200).style("display", "none");
             if (!selectedNode) {
                nodeElements.style("opacity", 1);
                linkElements.style("stroke-opacity", 0.6).classed("highlighted", false);
                d3.select(event.currentTarget).select('circle').attr('stroke', '#fff');
             } else {
                 highlightNeighbors(selectedNode); // Re-highlight selected neighbors
                 // Reset stroke only if not the selected node
                 if (selectedNode.id !== d.id) {
                     d3.select(event.currentTarget).select('circle').attr('stroke', '#fff');
                 } else {
                     d3.select(event.currentTarget).select('circle').attr('stroke', '#333'); // Keep selected stroke
                 }
             }
        }

        function handleClick(event, d) { /* ... click handler ... */
            event.stopPropagation();
            console.log(`Node clicked: ${d.id}`);
            if (selectedNode && selectedNode.id === d.id) {
                console.log("Deselecting node.");
                selectedNode = null;
                nodeElements.classed("selected", false).select('circle').attr('stroke', '#fff');
                nodeElements.style("opacity", 1);
                linkElements.style("stroke-opacity", 0.6).classed("highlighted", false);
            } else {
                console.log("Selecting node.");
                selectedNode = d;
                nodeElements.classed("selected", n => n.id === d.id);
                nodeElements.select('circle').attr('stroke', n => n.id === d.id ? '#333' : '#fff');
                highlightNeighbors(d);
                sourceSelect.value = d.id; targetSelect.value = "";
                updateRelationStrengthUI();
            }
        }

        function highlightNeighbors(node) { /* ... highlight neighbors ... */
            if (!nodeElements || !linkElements) return;
            nodeElements.style("opacity", n => isNeighbor(node, n) ? 1 : 0.3);
            nodeElements.filter(n => n.id === node.id).style("opacity", 1);
            linkElements.style("stroke-opacity", l => linkSourceTargetMatch(l, node) ? 1 : 0.1)
                        .classed("highlighted", l => linkSourceTargetMatch(l, node));
        }

        // Helper to check link source/target matching node
        function linkSourceTargetMatch(link, node) {
            return link.source === node || link.target === node || link.source.id === node.id || link.target.id === node.id;
        }

        function isNeighbor(node1, node2) { /* ... is neighbor check ... */
            if (!node1 || !node2 || !simulation) return false;
            if (node1.id === node2.id) return true;
            const currentLinks = simulation.force("link").links();
            return currentLinks.some(link =>
                (link.source.id === node1.id && link.target.id === node2.id) ||
                (link.source.id === node2.id && link.target.id === node1.id)
            );
        }

        function updateRelationStrengthUI() { /* ... update relation UI ... */
            const sourceCat = sourceSelect.value; const targetCat = targetSelect.value;
            if (sourceCat && targetCat && sourceCat !== targetCat) {
                const sf = graphData[sourceCat]?.[targetCat]; const sb = graphData[targetCat]?.[sourceCat];
                if (sf !== undefined) { relationStrengthSlider.value = sf; relationStrengthValueSpan.textContent = sf.toFixed(2); bidirectionalToggle.checked = (sb !== undefined && sb === sf); }
                else if (sb !== undefined) { relationStrengthSlider.value = sb; relationStrengthValueSpan.textContent = sb.toFixed(2); bidirectionalToggle.checked = false; }
                else { relationStrengthSlider.value = 0.5; relationStrengthValueSpan.textContent = '0.50'; bidirectionalToggle.checked = true; }
            } else { relationStrengthSlider.value = 0.5; relationStrengthValueSpan.textContent = '0.50'; bidirectionalToggle.checked = true; }
        }

        function addOrUpdateRelation() { /* ... add/update relation ... */
            const sourceCat = sourceSelect.value; const targetCat = targetSelect.value;
            const strength = parseFloat(relationStrengthSlider.value); const isBidirectional = bidirectionalToggle.checked;
            if (!sourceCat || !targetCat || sourceCat === targetCat) { alert("Prosím vyberte dvě různé kategorie."); return; }
            if (isNaN(strength) || strength < 0.1 || strength > 1) { alert("Prosím zadejte platnou sílu vztahu (0.1 - 1.0)."); return; }
            if (!graphData[sourceCat]) graphData[sourceCat] = {}; graphData[sourceCat][targetCat] = strength;
            if (isBidirectional) { if (!graphData[targetCat]) graphData[targetCat] = {}; graphData[targetCat][sourceCat] = strength; }
            else { if (graphData[targetCat]?.[sourceCat]) { delete graphData[targetCat][sourceCat]; } }
            console.log(`Vztah ${sourceCat} <=> ${targetCat} upraven/přidán se silou ${strength.toFixed(2)} ${isBidirectional ? '(obousměrný)' : ''}`);
            updateGraph();
        }

        function removeRelation() { /* ... remove relation ... */
            const sourceCat = sourceSelect.value; const targetCat = targetSelect.value;
            if (!sourceCat || !targetCat || sourceCat === targetCat) { alert("Prosím vyberte dvě různé kategorie pro odebrání vztahu."); return; }
            let relationRemoved = false;
            if (graphData[sourceCat]?.[targetCat]) { delete graphData[sourceCat][targetCat]; relationRemoved = true; }
            if (graphData[targetCat]?.[sourceCat]) { delete graphData[targetCat][sourceCat]; relationRemoved = true; }
            if (relationRemoved) { console.log(`Vztah mezi ${sourceCat} a ${targetCat} odebrán.`); updateGraph(); updateRelationStrengthUI(); }
            else { alert("Mezi vybranými kategoriemi neexistuje žádný vztah."); }
        }

        function exportData() { /* ... export data ... */
            const dataStr = JSON.stringify(graphData, null, 2); const blob = new Blob([dataStr], { type: "application/json" });
            const url = URL.createObjectURL(blob); const a = document.createElement("a");
            a.href = url; a.download = "product_graph_data.json"; document.body.appendChild(a);
            a.click(); document.body.removeChild(a); URL.revokeObjectURL(url); console.log("Data exportována.");
        }

        // --- Event Listeners ---
        strengthSlider.addEventListener('input', (e) => {
          minStrength = parseFloat(e.target.value); strengthValueSpan.textContent = minStrength.toFixed(2);
          clearTimeout(strengthSlider.timer); strengthSlider.timer = setTimeout(updateGraph, 250); // Debounce
        });
        nodeSizeSlider.addEventListener('input', (e) => { baseNodeSize = parseInt(e.target.value); nodeSizeValueSpan.textContent = baseNodeSize; updateGraphStyles(); });
        showLabelsToggle.addEventListener('change', (e) => { showLabels = e.target.checked; updateGraphStyles(); });
        showClustersToggle.addEventListener('change', (e) => { showClusters = e.target.checked; updateGraphStyles(); });
        resetButton.addEventListener('click', () => { if (svg && zoom) { svg.transition().duration(750).call(zoom.transform, d3.zoomIdentity); } });
        relationStrengthSlider.addEventListener('input', (e) => { relationStrengthValueSpan.textContent = parseFloat(e.target.value).toFixed(2); });
        sourceSelect.addEventListener('change', updateRelationStrengthUI); targetSelect.addEventListener('change', updateRelationStrengthUI);
        addRelationButton.addEventListener('click', addOrUpdateRelation); removeRelationButton.addEventListener('click', removeRelation); exportButton.addEventListener('click', exportData);

        let resizeTimer;
        window.addEventListener('resize', () => {
            clearTimeout(resizeTimer);
            resizeTimer = setTimeout(() => {
                console.log("Window resized, updating graph layout.");
                if (svg && simulation && graphContainer) {
                    const width = graphContainer.clientWidth; const height = graphContainer.clientHeight;
                    if (width > 0 && height > 0) { // Ensure dimensions are valid
                        svg.attr("viewBox", [0, 0, width, height]);
                        simulation.force("center", d3.forceCenter(width / 2, height / 2));
                        if (simulation.alpha() < 0.1) { simulation.alpha(0.1).restart(); }
                         else { simulation.restart(); } // Ensure restart even if alpha is high
                    } else {
                         console.warn("Resize detected but container dimensions are invalid.");
                    }
                } else if (!simulation && graphContainer && Object.keys(graphData).length > 0) {
                    console.log("Graph not initialized, attempting init after resize.");
                    initializeGraph(); // Try to init if it wasn't before
                }
            }, 250); // Debounce resize
        });

        // Event listener pro načtení dat z vybraného zdroje
        const loadDataBtn = document.getElementById('load-data-btn');
        loadDataBtn.addEventListener('click', () => {
            const selectedSource = document.querySelector('input[name="data-source"]:checked').value;
            loadGraphData(selectedSource);
        });
        
        // --- Initial Setup ---
        try {
            // Načtení výchozích dat - filsonstore data
            loadGraphData('filsonstore_graph_data.json'); // Načti výchozí datový soubor
        } catch (error) {
             console.error("Error during initial setup:", error);
             // Display error message to the user
             graphContainer.innerHTML = `<p style='color: red; text-align: center; padding: 20px;'>Došlo k chybě při inicializaci grafu. Zkontrolujte prosím konzoli (F12) pro více detailů.</p>`;
        }

    }); // End of DOMContentLoaded
  </script>
</body>
</html>
