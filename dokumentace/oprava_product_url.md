# Oprava chybějícího atributu product_url

## Problém
V logu synchronizace produktů se objevuje velké množství chyb typu:
```
Atribut 'product_url' chybí v payloadu produktu XXXXX, považováno za změnu.
```

Tyto chyby nastávají, protože v souboru `feed_processor.py` jsou URL produktů z feedu mapovány pouze na atribut `url`, ale ne na atribut `product_url`. Toto způsobuje, že u mnoha produktů chybí atribut `product_url`, což vede k zbytečným aktualizacím produktů při každé synchronizaci.

## Analýza problému
1. V modelu `ProductInternal` (soubor `core/models.py`) jsou definovány dva atributy:
   ```python
   url: Optional[str] = None
   product_url: Optional[str] = None  # Přidáno pro kompatibilitu s data_models.ProductInternal
   ```

2. V modelu `Product` (soubor `data_models.py`) je definován atribut `product_url` místo `url`.

3. V souboru `feed_processor.py` se při transformaci produktů z feedu používá pouze atribut `url`, ale ne `product_url`:
   ```python
   product = ProductInternal(
       id=product_id,
       name=str(raw_product.get('Name', '')).strip(),
       # ... další atributy
       url=str(raw_product.get('ProductUrl', '')).strip() or None,
       # chybí product_url
       # ... další atributy
   )
   ```

4. V souboru `sync_products.py` se porovnávají produkty z feedu s produkty v databázi, a pokud některé atributy chybí, jsou považovány za změněné:
   ```python
   if any attribute missing:
       logger.info(f"Atribut '{attribut}' chybí v payloadu produktu {product.id}, považováno za změnu.")
   ```

## Oprava
1. Vytvořili jsme zálohu původního souboru:
   ```
   feed_processor.py.bak_20250505
   ```

2. Upravili jsme soubor `feed_processor.py` tak, aby při vytváření `ProductInternal` nastavoval URL produktu na oba atributy `url` a `product_url`:
   ```python
   product_url = str(raw_product.get('ProductUrl', '')).strip() or None
   
   product = ProductInternal(
       id=product_id,
       # ... další atributy
       url=product_url,
       product_url=product_url,  # Přidáno pro řešení problému chybějícího product_url
       # ... další atributy
   )
   ```

## Ověření opravy
1. Po nasazení opravy a spuštění synchronizace produktů jsme ověřili, že nové a aktualizované produkty mají správně vyplněný atribut `product_url`:
   ```json
   "url":"https://www.filsonstore.cz/tazny-popruh-na-auto-dodavky-suv-nakladni-10000kg-10-tun-4m",
   "product_url":"https://www.filsonstore.cz/tazny-popruh-na-auto-dodavky-suv-nakladni-10000kg-10-tun-4m"
   ```

2. Během probíhající synchronizace jsou hlášeny chyby pro existující produkty, ale po dokončení této synchronizace budou všechny produkty mít správně nastavený atribut `product_url`, což zamezí zbytečným aktualizacím při další synchronizaci.

## Očekávaný výsledek
Po dokončení synchronizace a dalším spuštění synchronizačního procesu by se již neměly objevovat hlášení o chybějícím atributu `product_url`, což povede k rychlejší synchronizaci a menšímu množství zbytečných aktualizací. 