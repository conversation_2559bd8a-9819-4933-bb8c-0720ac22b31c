from neo4j import GraphDatabase

# Přihlašovací údaje k Neo4j
URI = "bolt://localhost:7687"
AUTH = ("neo4j", "Graph2025Secure!")

def test_connection():
    """Test připojení k Neo4j databázi."""
    with GraphDatabase.driver(URI, auth=AUTH) as driver:
        # <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> připojen<PERSON>
        driver.verify_connectivity()
        print("Připojení k Neo4j úspěšně navázáno!")
        
        # Jed<PERSON>duchý test dotazu - vrátí "Hello, World!"
        result = driver.execute_query("RETURN 'Hello, World!' AS message", database_="neo4j")
        print(result.records[0]["message"])

if __name__ == "__main__":
    try:
        test_connection()
    except Exception as e:
        print(f"Chyba při připojení k Neo4j: {e}") 