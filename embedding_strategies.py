#!/usr/bin/env python3
# embedding_strategies.py
import logging
import asyncio
from typing import Dict, List, Any, Optional
import openai
from openai import AsyncOpenAI
import tiktoken

logger = logging.getLogger(__name__)

# Konstanta pro maximální počet tokenů (s rezervou)
MAX_TOKENS = 8000

class MultiEmbeddingStrategy:
    """
    Třída pro generování různých typů embeddingů pro produkty.
    
    Implementuje 5 různých typů embeddingů:
    1. combined - kombinovaný embedding (30% název, 40% popis, 20% kategorie, 10% značka)
    2. name_brand - embedding zaměřený na název a značku produktu
    3. pure_description - embedding zaměřený na detailní popis produktu
    4. category_hierarchy - embedding zaměřený na kategorie produktů
    5. brand_category - embedding zaměřený na značku a kategorii
    """
    
    def __init__(self, openai_client: AsyncOpenAI):
        """
        Inicializuje strategii pro generování embeddingů.
        
        Args:
            openai_client: Klient pro OpenAI API
        """
        self.openai_client = openai_client
        self.embedding_model = "text-embedding-3-small"
        self.embedding_dimensions = 1536
        
        # Zkusíme načíst enkodér pro model, pokud selže, logujeme chybu
        try:
            self.tokenizer = tiktoken.encoding_for_model(self.embedding_model)
        except Exception as e:
            logger.error(f"Nepodařilo se načíst tiktoken enkodér pro model {self.embedding_model}: {e}. Zkracování nebude fungovat správně.")
            self.tokenizer = None
        
        # Váhy pro kombinovaný embedding
        self.weights = {
            "name": 0.3,       # 30%
            "description": 0.4, # 40%
            "category": 0.2,    # 20%
            "brand": 0.1        # 10%
        }
    
    async def create_embeddings(self, product_data: Dict[str, Any]) -> Dict[str, List[float]]:
        """
        Vytvoří různé typy embeddingů pro produkt.
        Zkrátí příliš dlouhé vstupy, aby nedošlo k překročení limitu tokenů OpenAI.

        Args:
            product_data: Slovník s daty produktu (musí obsahovat 'id' pro logování)
            
        Returns:
            Slovník mapující typy embeddingů na jejich vektory
        """
        # Kontrola, zda product_data není None
        if product_data is None:
            logger.error("Funkce create_embeddings obdržela None místo product_data.")
            # Vrátíme prázdné embeddingy jako fallback
            # Použijeme původní klíče z `texts` definované níže (nebo fixní seznam, pokud to selže)
            default_keys = ["combined", "name_brand", "pure_description", "category_hierarchy", "brand_category"]
            return {key: [0.0] * self.embedding_dimensions for key in default_keys}

        product_id = product_data.get("id", "N/A") # Získání ID pro logování

        # Extrakce dat produktu a ošetření None hodnot před voláním .lower()
        name_val = product_data.get("name").lower()
        desc_val = product_data.get("description").lower()
        cat_val = product_data.get("category").lower()
        brand_val = product_data.get("brand")

        name = name_val.lower() if isinstance(name_val, str) else ""
        description = desc_val.lower() if isinstance(desc_val, str) else ""
        category = cat_val.lower() if isinstance(cat_val, str) else ""
        brand = brand_val.lower() if isinstance(brand_val, str) else ""
        
        # Pokud je product_data string, použijeme ho jako popis (tato logika se zdá být redundantní, pokud očekáváme Dict)
        # Pokud by product_data mohl být string, musela by kontrola a extrakce proběhnout jinak.
        # Prozatím předpokládáme, že product_data je Dict nebo None.
        # if isinstance(product_data, str):
        #     description = product_data.lower() # I zde normalizujeme
        #     name = ""
        #     category = ""
        #     brand = ""
        
        # Příprava textů pro různé typy embeddingů (již používají normalizované proměnné)
        texts = {
            "name_brand": f"{name} {brand}".strip(),
            "pure_description": description,
            "category_hierarchy": category,
            "brand_category": f"{brand} {category}".strip(),
            "combined": self._create_combined_text(name, description, category, brand)
        }

        # Zkrácení textů, pokud je potřeba a pokud je k dispozici tokenizer
        truncated_texts = {}
        if self.tokenizer:
            for key, text in texts.items():
                tokens = self.tokenizer.encode(text)
                if len(tokens) > MAX_TOKENS:
                    truncated_tokens = tokens[:MAX_TOKENS]
                    truncated_text = self.tokenizer.decode(truncated_tokens)
                    truncated_texts[key] = truncated_text
                    logger.warning(f"Text pro embedding '{key}' produktu '{product_id}' byl zkrácen z {len(tokens)} na {len(truncated_tokens)} tokenů.")
                else:
                    truncated_texts[key] = text
        else:
            # Fallback, pokud tokenizer není k dispozici - nekontrolujeme délku
            truncated_texts = texts
            logger.warning("Tiktoken tokenizer není dostupný, délka textu pro embedding nebyla zkontrolována.")

        # Generování embeddingů pro všechny typy
        embeddings = {}
        
        try:
            # Generování embeddingů pomocí OpenAI API s potenciálně zkrácenými texty
            response = await self.openai_client.embeddings.create(
                model=self.embedding_model,
                input=[truncated_texts[key] for key in truncated_texts.keys()] # Použijeme zkrácené texty
            )
            
            # Mapování výsledků na typy embeddingů
            for i, key in enumerate(truncated_texts.keys()):
                embeddings[key] = response.data[i].embedding
                
            logger.debug(f"Vygenerovány embeddingy pro produkt '{product_id}'")
            return embeddings
            
        except Exception as e:
            # Vylepšené logování chyby s ID produktu
            logger.error(f"Chyba při generování embeddingů pro produkt '{product_id}': {e}")
            # Vrátíme prázdné embeddingy jako fallback
            # Použijeme původní klíče z `texts`, abychom měli jistotu, že vrátíme všechny
            return {key: [0.1] * self.embedding_dimensions for key in texts.keys()}
    
    def _create_combined_text(self, name: str, description: str, category: str, brand: str) -> str:
        """
        Vytvoří kombinovaný text pro embedding s váženými komponenty.
        (Vráceno k původní implementaci)

        Args:
            name: Název produktu
            description: Popis produktu
            category: Kategorie produktu
            brand: Značka produktu

        Returns:
            Kombinovaný text pro embedding
        """
        # Opakování textu podle vah pro simulaci vážení
        name_repeat = max(1, int(self.weights["name"] * 10))
        desc_repeat = max(1, int(self.weights["description"] * 10))
        category_repeat = max(1, int(self.weights["category"] * 10))
        brand_repeat = max(1, int(self.weights["brand"] * 10))

        # Původní sestavení textu
        combined_text = " ".join([
            f"{name} " * name_repeat,
            f"{description} " * desc_repeat,
            f"{category} " * category_repeat,
            f"{brand} " * brand_repeat
        ]).strip()

        # Logování se vrátí, pokud bylo původně
        # logger.debug(f"Combined text length: {len(combined_text)}")

        return combined_text

    async def get_embedding_configs(self) -> List[Dict[str, Any]]:
        """Vrátí konfiguraci pro jednotlivé typy embeddingů."""
        # Názvy musí odpovídat klíčům v metodě create_embeddings
        # Váhy jsou použity ve find_complementary_products_multi_embedding
        # TODO: Váhy jsou nastaveny orientačně, bude potřeba je doladit experimentálně.
        return [
            {'vector_name': 'combined', 'weight': 1.0},           # Hlavní kombinovaný embedding
            {'vector_name': 'name_brand', 'weight': 0.8},         # Silný signál pro podobné produkty
            {'vector_name': 'pure_description', 'weight': 0.6},   # Důležitý pro funkční podobnost, ale méně pro komplementaritu?
            {'vector_name': 'category_hierarchy', 'weight': 0.7}, # Důležité pro navigaci ve struktuře
            {'vector_name': 'brand_category', 'weight': 0.7},     # Kombinace značky a kategorie
        ]
