# config.py
import os
from pydantic_settings import BaseSettings
from pydantic import Field, HttpUrl, validator, BaseModel
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Načtení .env souboru
load_dotenv()

class FeedSettings(BaseModel):
    type: str
    config: Dict[str, Any] = Field(default_factory=dict) # Flexibilní config pro různé typy feedů

class TenantSettings(BaseModel):
    tenant_id: str
    feed: FeedSettings
    # Můžete přidat další specifická nastavení pro tenanta

class Settings(BaseSettings):
    qdrant_url: HttpUrl = Field(default='http://localhost:6333')
    openai_api_key: Optional[str] = Field(default=None, validation_alias='OPENAI_API_KEY')
    anthropic_api_key: Optional[str] = Field(default=None, validation_alias='ANTHROPIC_API_KEY')
    log_level: str = Field(default='INFO')
    state_store_dir: str = Field(default='data/state') # Adresář pro FileStateStore

    # Konfigurace tenantů - ideálně načítat z DB nebo konfiguračního souboru
    # Zde pro ukázku pevně daná, v praxi by byla dynamická
    tenant_configs: Dict[str, TenantSettings] = {
        "tenant1": TenantSettings(
            tenant_id="tenant1",
            feed=FeedSettings(type="xml", config={"url": "http://example.com/feed.xml"})
        ),
        "avenberg": TenantSettings(
            tenant_id="avenberg",
            feed=FeedSettings(type="avenberg", config={"feed_url": "https://www.avenberg.cz/static/_xml/zbozi_export_google.xml"})
        ),
        "coolpohyb": TenantSettings(
            tenant_id="coolpohyb",
            feed=FeedSettings(type="coolpohyb", config={"feed_url": "https://www.coolpohyb.cz/google.xml"})
        ),
        "filsonstore": TenantSettings(
            tenant_id="filsonstore",
            feed=FeedSettings(type="filsonstore", config={"feed_url": "https://feeds.mergado.com/filsonstore-cz-google-nakupy-cz-9798ff11fcf3873b197f9f2badef7462.xml"})
        ),
        # Přidat další tenanty...
    }

    # Embedding model
    embedding_model: str = "text-embedding-3-small"
    embedding_dimensions: int = 1536

    # LLM modely pro Anthropic
    anthropic_model_generation: str = "claude-3-7-sonnet-20250219" # Pro generování (vztahy, kontexty)
    anthropic_model_fast: str = "claude-3-5-haiku-20241022" # Pro rychlejší úlohy (přiřazení kontextů, komplementární)
    anthropic_model_complementary: str = "claude-3-5-sonnet-20240620" # Pro generování komplementárních produktů
    
    # LLM modely pro OpenAI 
    openai_model_complementary: str = "gpt-4o-2024-05-13" # Pro generování komplementárních produktů
    openai_model_complementary_cheap: str = "gpt-3.5-turbo-0125" # Levnější alternativa pro komplementární produkty
    openai_model_complementary_mini: str = "o3-mini" # Nejlevnější alternativa (o3-mini)
    
    # Nový parametr pro Qdrant search u komplementárních produktů
    complementary_candidate_limit: int = 50 # Kolik kandidátů načíst z Qdrantu pro LLM
    get_similar_limit: int = 5 # Kolik podobných produktů získat pro kandidáty
    
    # Paralelizace a batching
    embedding_max_workers: int = 10 # Souvisí s API limity OpenAI
    embedding_batch_size: int = 50
    context_assignment_batch_size: int = 50 # Max produktů v jednom LLM volání pro přiřazení kontextů
    complementary_llm_batch_size: int = 25 # Max produktů v jednom LLM volání pro komplementární
    complementary_llm_max_workers: int = 1 # Omezení kvůli Anthropic API A QDRANT ZÁTĚŽI

    class Config:
        env_file = '.env'
        env_file_encoding = 'utf-8'
        extra = 'ignore' # Ignorovat extra proměnné v .env

# Globální instance nastavení
settings = Settings()

def get_tenant_config(tenant_id: str) -> Optional[TenantSettings]:
    """Získá konfiguraci pro daného tenanta."""
    return settings.tenant_configs.get(tenant_id)