#!/usr/bin/env python3
"""
Skript pro generování komplementárních vztahů mezi produkty.
"""

import os
import logging
from qdrant_client import QdrantClient
from qdrant_client.http.models import PointStruct
import json
from dotenv import load_dotenv

# Načtení proměnných prostředí
load_dotenv()

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"generate_complementary.log")
    ]
)

logger = logging.getLogger("generate_complementary")

def create_complementary_collection(tenant_id="avenberg"):
    """
    Vytvoří kolekci pro komplementární produkty.
    
    Args:
        tenant_id: ID tenanta
    """
    client = QdrantClient(url=os.getenv("QDRANT_URL", "http://localhost:6333"))
    collection_name = f"complementary_products_{tenant_id}"
    
    try:
        # Kontrola existence kolekce
        collections = client.get_collections().collections
        collection_names = [collection.name for collection in collections]
        
        if collection_name in collection_names:
            logger.info(f"Kolekce {collection_name} již existuje")
            return True
            
        # Vytvoření nové kolekce
        client.create_collection(
            collection_name=collection_name,
            vectors_config={
                "default": {
                    "size": 1536,  # Velikost vektoru pro text-embedding-3-small
                    "distance": "Cosine"
                }
            }
        )
        logger.info(f"Vytvořena nová kolekce {collection_name}")
        return True
        
    except Exception as e:
        logger.error(f"Chyba při vytváření kolekce: {str(e)}")
        return False

def generate_complementary_relationships(tenant_id="avenberg"):
    """
    Generuje komplementární vztahy mezi produkty.
    
    Args:
        tenant_id: ID tenanta
    """
    client = QdrantClient(url=os.getenv("QDRANT_URL", "http://localhost:6333"))
    products_collection = f"real_products_{tenant_id}"
    complementary_collection = f"complementary_products_{tenant_id}"
    
    try:
        # Načtení všech produktů
        results = client.scroll(
            collection_name=products_collection,
            limit=1000,  # Dostatečně velký limit pro načtení všech produktů
            with_payload=True
        )
        
        products = results[0]
        logger.info(f"Načteno {len(products)} produktů")
        
        # Generování komplementárních vztahů
        complementary_points = []
        
        for product in products:
            product_id = product.payload.get("product_id") or product.payload.get("original_id") or str(product.id)
            category = product.payload.get("category", "")
            
            # Hledání komplementárních produktů
            complementary_products = []
            complementary_reasons = {}
            
            # Příklad pravidel pro komplementární produkty
            if "Zahradní nábytek" in category:
                # Pro zahradní nábytek hledáme doplňky jako polštáře, deky, atd.
                for other_product in products:
                    if other_product.id != product.id:
                        other_category = other_product.payload.get("category", "")
                        if "Polštáře" in other_category or "Deky" in other_category:
                            comp_id = other_product.payload.get("product_id") or other_product.payload.get("original_id") or str(other_product.id)
                            complementary_products.append(comp_id)
                            complementary_reasons[comp_id] = "Doplněk pro pohodlné sezení"
            
            elif "Bazén" in category:
                # Pro bazény hledáme chemii, krycí plachty, atd.
                for other_product in products:
                    if other_product.id != product.id:
                        other_category = other_product.payload.get("category", "")
                        if "Bazénová chemie" in other_category or "Krycí plachty" in other_category:
                            comp_id = other_product.payload.get("product_id") or other_product.payload.get("original_id") or str(other_product.id)
                            complementary_products.append(comp_id)
                            complementary_reasons[comp_id] = "Doplněk pro údržbu bazénu"
            
            # Vytvoření bodu pro komplementární produkty
            if complementary_products:
                point = PointStruct(
                    id=product_id,
                    vector={"default": [0.0] * 1536},  # Dummy vektor, protože nepotřebujeme vektorové vyhledávání
                    payload={
                        "main_product_id": product_id,
                        "complementary_product_ids": complementary_products,
                        "complementary_product_reasons": complementary_reasons
                    }
                )
                complementary_points.append(point)
        
        # Uložení komplementárních vztahů do Qdrantu
        if complementary_points:
            client.upsert(
                collection_name=complementary_collection,
                points=complementary_points,
                wait=True
            )
            logger.info(f"Uloženo {len(complementary_points)} komplementárních vztahů")
            
            # Uložení do JSON souboru pro zálohu
            with open(f"complementary_relationships_{tenant_id}.json", "w", encoding="utf-8") as f:
                json.dump(
                    [point.payload for point in complementary_points],
                    f,
                    ensure_ascii=False,
                    indent=2
                )
            logger.info(f"Záloha komplementárních vztahů uložena do souboru")
        
        return True
        
    except Exception as e:
        logger.error(f"Chyba při generování komplementárních vztahů: {str(e)}")
        return False

if __name__ == "__main__":
    # Vytvoření kolekce pro komplementární produkty
    if create_complementary_collection():
        # Generování komplementárních vztahů
        generate_complementary_relationships() 