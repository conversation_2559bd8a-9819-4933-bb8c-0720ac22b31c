#!/usr/bin/env python3
import json

with open("all_complementary_jabkolevne.json", 'r') as f:
    data = json.load(f)

print(f"Typ dat: {type(data)}")
print(f"Počet položek: {len(data)}")

if isinstance(data, dict):
    print("\nKlíče slovníku:")
    for key in data.keys():
        value_type = type(data[key])
        print(f"  - {key}: {value_type}")
        if value_type in [list, dict]:
            print(f"    Počet položek: {len(data[key])}")
    
    # Ukázka complementary_products
    if 'complementary_products' in data:
        comp_data = data['complementary_products']
        print(f"\nTyp complementary_products: {type(comp_data)}")
        
        if isinstance(comp_data, dict):
            # Ukázka prvních několika klíčů
            print("\nPrvních 5 klíčů:")
            for i, key in enumerate(list(comp_data.keys())[:5]):
                print(f"  {i+1}. {key}")
                print(f"     Hodnota: {json.dumps(comp_data[key], indent=6, ensure_ascii=False)[:300]}...")
        elif isinstance(comp_data, list):
            print(f"\nPrvní položka seznamu:")
            print(json.dumps(comp_data[0], indent=2, ensure_ascii=False)[:500] + "...")
    
elif isinstance(data, list):
    print("\nPrvní položka seznamu:")
    print(json.dumps(data[0], indent=2, ensure_ascii=False)[:500] + "...")

comp_products = data.get('complementary_products', {})

# Vezměme první položku
first_key = list(comp_products.keys())[0]
first_item = comp_products[first_key]

print(f"Struktura první položky (ID: {first_key}):")
print(json.dumps(first_item, indent=2, ensure_ascii=False)[:2000])
print("\n...")

# Zobrazíme klíče
print(f"\nKlíče v položce:")
for key in first_item.keys():
    print(f"  - {key}: {type(first_item[key])}")
    if isinstance(first_item[key], list) and len(first_item[key]) > 0:
        print(f"    Počet položek: {len(first_item[key])}")
        print(f"    První položka: {type(first_item[key][0])}") 