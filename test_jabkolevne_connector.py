#!/usr/bin/env python3
"""
Skript pro testování jabkolevne XML konektoru
"""

import sys
import yaml
from pathlib import Path

# Cesta k projektu
PROJECT_ROOT = Path(__file__).resolve().parent

def test_connector():
    """Testuje vytvořený konektor."""
    try:
        tenant_id = "jabkolevne"
        config_path = PROJECT_ROOT / "config" / "tenants" / f"{tenant_id}.yaml"
        
        # Import modulu
        sys.path.insert(0, str(PROJECT_ROOT))
        from connectors.connector_factory import ConnectorFactory
        
        # Načtení konfigurace
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Vytvoření konektoru
        print(f"Vytvářím konektor pro {tenant_id}...")
        connector = ConnectorFactory.create_connector(tenant_id, config['feed'])
        
        # Test načtení dat
        print(f"Načítám data z feedu {config['feed']['feed_url']}...")
        products = connector.get_feed()
        
        if products:
            print(f"✅ Načteno {len(products)} produktů")
            print("\nUkázka prvního produktu:")
            for key, value in products[0].items():
                print(f"  {key}: {value}")
            return True
        else:
            print("❌ Žádné produkty nenačteny")
            return False
    
    except Exception as e:
        print(f"❌ Chyba při testování konektoru: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_connector()
