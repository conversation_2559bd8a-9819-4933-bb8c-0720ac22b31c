#!/usr/bin/env python3
"""
Patch pro ultra skript - přidává smart matching logiku pro lepší nalezení komplementárních kategorií.
"""

import re
import os
import sys

def patch_ultra_script():
    """
    Aplikuje patch na ultra skript pro smart matching kategorií.
    
    Analógia: Jako k<PERSON>ž vylepšujeme vyhledávač v knihovně - místo hledání pouze
    přesných názvů začneme hledat i podle klíčových slov v názvech knih.
    """
    script_path = "batch_scripts/compute_complementary.py"
    
    print("🔧 Aplikuji smart matching patch na ultra skript...")
    
    # Čtení původního souboru
    with open(script_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Definice nové smart matching funkce
    smart_matching_function = '''
async def get_complementary_categories_cached_smart(source_category_name: str) -> List[Tuple[str, float]]:
    """Rychlé získání komplementárn<PERSON>ch kategorií z cache s SMART matching."""
    cache_age = time.time() - NEO4J_CACHE.get('_loaded_at', 0)
    if cache_age > NEO4J_CACHE_TTL:
        NEO4J_CACHE.clear()  # Cache expirovala
        return []
    
    # 1. Přímé hledání (nejrychlejší)
    if source_category_name in NEO4J_CACHE:
        return NEO4J_CACHE[source_category_name]
    
    # 2. Smart matching - extrakce klíčových slov z kategorie
    source_keywords = extract_category_keywords(source_category_name)
    if not source_keywords:
        return []
    
    # 3. Hledání podle klíčových slov
    all_results = []
    for cache_category, relationships in NEO4J_CACHE.items():
        if cache_category == '_loaded_at':
            continue
            
        # Kontrola, zda cache kategorie obsahuje nějaké klíčové slovo ze source
        for keyword in source_keywords:
            if keyword.lower() in cache_category.lower():
                all_results.extend(relationships)
                break  # Našli jsme shodu, pokračujeme na další cache kategorii
    
    # 4. Slučování a deduplikace výsledků podle cílové kategorie
    merged_results = {}
    for target_cat, weight in all_results:
        if target_cat in merged_results:
            merged_results[target_cat] = max(merged_results[target_cat], weight)  # Vezme vyšší váhu
        else:
            merged_results[target_cat] = weight
    
    # 5. Seřazení podle váhy
    final_results = sorted(merged_results.items(), key=lambda x: x[1], reverse=True)
    
    if final_results:
        logger.debug(f"🧠 Smart matching pro '{source_category_name}' našel {len(final_results)} kategorií pomocí klíčových slov: {source_keywords}")
    
    return final_results

def extract_category_keywords(category: str) -> List[str]:
    """
    Extrakce klíčových slov z kategorie pro smart matching.
    
    Analógia: Jako když z názvu knihy "Průvodce programováním v Pythonu pro začátečníky"
    vytáhneme klíčová slova "Python", "programování", aby bylo snadné najít podobné knihy.
    """
    if not category:
        return []
    
    # Definice významných klíčových slov pro Apple produkty
    important_keywords = [
        'iPhone', 'iPad', 'MacBook', 'Mac', 'Apple Watch', 'Watch', 'AirPods', 'AirTag', 
        'Apple TV', 'Apple Pencil', 'Pencil',
        'kryt', 'pouzdro', 'sklo', 'fólie', 'kabel', 'nabíjec', 'adaptér', 'powerbank',
        'řemínek', 'sluchátka', 'reproduktor', 'charger', 'lightning', 'usb'
    ]
    
    # Normalizace kategorie
    category_lower = category.lower()
    
    # Hledání klíčových slov v kategorii
    found_keywords = []
    for keyword in important_keywords:
        if keyword.lower() in category_lower:
            found_keywords.append(keyword)
    
    # Speciální případy - extrakce modelů iPhone
    iphone_models = ['iPhone 11', 'iPhone 12', 'iPhone 13', 'iPhone 14', 'iPhone 15', 'iPhone 16']
    for model in iphone_models:
        if model.lower() in category_lower and 'iPhone' not in found_keywords:
            found_keywords.append('iPhone')
            break
    
    # Speciální případy - extrakce modelů iPad
    ipad_models = ['iPad Pro', 'iPad Air', 'iPad Mini']
    for model in ipad_models:
        if model.lower() in category_lower and 'iPad' not in found_keywords:
            found_keywords.append('iPad')
            break
    
    # Pokud nenajdeme nic specifického, zkusíme základní klíčová slova
    if not found_keywords:
        # Hledání obecných klíčových slov
        basic_keywords = ['příslušenství', 'audio', 'kryty', 'tvrzená', 'nabíječky']
        for keyword in basic_keywords:
            if keyword.lower() in category_lower:
                found_keywords.append(keyword)
    
    return found_keywords'''
    
    # Nahrazení původní funkce novou smart funkcí
    old_function_pattern = r'async def get_complementary_categories_cached\(source_category_name: str\) -> List\[Tuple\[str, float\]\]:[^}]+?return final_results'
    
    # Hledání původní funkce
    match = re.search(old_function_pattern, content, re.DOTALL)
    if not match:
        print("❌ Nepodařilo se najít původní funkci get_complementary_categories_cached")
        return False
    
    # Nahrazení původní funkce novou
    content = content.replace(match.group(0), smart_matching_function.strip())
    
    # Přejmenování volání na novou funkci
    content = content.replace(
        'complementary_categories_with_weights = await get_complementary_categories_cached(source_category)',
        'complementary_categories_with_weights = await get_complementary_categories_cached_smart(source_category)'
    )
    
    # Zápis upraveného souboru
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print("✅ Smart matching patch úspěšně aplikován!")
    print("💡 Ultra skript nyní používá chytré vyhledávání kategorií")
    return True

if __name__ == "__main__":
    print("🚀 Spouštím smart matching patch pro ultra skript...")
    
    if patch_ultra_script():
        print("🎉 Patch dokončen! Ultra skript je připraven k testování.")
    else:
        print("💥 Patch selhal!")
        sys.exit(1) 