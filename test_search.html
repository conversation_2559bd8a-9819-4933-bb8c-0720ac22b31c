<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vyhledávání produktů</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }
        
        /* Vyhledávací přepínače */
        .search-type-controls {
            display: flex;
            flex-wrap: wrap;
            margin: 10px 0;
            gap: 10px;
            justify-content: center;
        }
        
        .search-type-controls label {
            display: inline-flex;
            align-items: center;
            background-color: #f0f0f0;
            border-radius: 20px;
            padding: 6px 12px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.2s ease;
            border: 1px solid #ddd;
        }
        
        .search-type-controls label:hover {
            background-color: #e0e0e0;
        }
        
        .search-type-controls input[type="radio"] {
            margin-right: 6px;
        }
        
        .search-type-controls input[type="radio"]:checked + label {
            background-color: #4a6dff;
            color: white;
            border-color: #3a5dff;
        }
        
        .search-container {
            display: flex;
            flex-direction: column;
            padding: 15px;
            background-color: white;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }
        
        .container {
            max-width: 1200px;
            margin: auto;
            background: #fff;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            display: flex;
            flex-direction: column;
        }
        
        header {
            padding: 20px;
            background-color: #fff;
            border-bottom: 1px solid #eee;
            position: sticky;
            top: 0;
            z-index: 1000;
            box-shadow: 0 2px 5px rgba(0,0,0,0.05);
        }
        
        .search-section {
            display: flex;
            gap: 10px;
            padding: 10px 20px;
        }
        
        .search-input {
            display: flex;
            flex-grow: 1;
            gap: 10px;
            position: relative;
        }
        
        .search-input-icon {
            position: absolute;
            left: 45px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
            pointer-events: none;
        }
        
        #tenantSelect {
            padding: 10px;
            border: 1px solid #ccc;
            border-radius: 4px;
            background-color: #fff;
            transition: border-color 0.3s;
        }
        
        #tenantSelect:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        #queryInput {
            flex-grow: 1;
            padding: 10px 10px 10px 35px;
            border: 1px solid #ccc;
            border-radius: 4px;
            transition: all 0.3s ease;
            font-size: 1em;
        }
        
        #queryInput:focus {
            border-color: #007bff;
            outline: none;
            box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
        }
        
        #searchButton {
            padding: 10px 15px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }
        
        #searchButton:hover {
            background-color: #0056b3;
        }
        
        .main-content {
            display: flex;
            min-height: calc(100vh - 150px);
        }
        
        .sidebar {
            width: 250px;
            padding: 20px;
            background-color: #f9f9f9;
            border-right: 1px solid #eee;
            flex-shrink: 0;
        }
        
        .content {
            flex-grow: 1;
            padding: 20px;
        }
        
        h1, h2, h3 {
            color: #333;
            margin-top: 0;
        }
        
        .filter-section {
            margin-bottom: 20px;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        
        .filter-section:last-child {
            border-bottom: none;
        }
        
        .filter-section h3 {
            margin-bottom: 10px;
            font-size: 16px;
            font-weight: bold;
        }
        
        .filter-option {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }
        
        .filter-option label {
            margin-left: 5px;
            cursor: pointer;
        }
        
        .top-product {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #eee;
            border-radius: 8px;
            background-color: #fff;
            display: flex;
            gap: 20px;
        }
        
        .top-product-image {
            width: 300px;
            height: 300px;
            object-fit: contain;
        }
        
        .top-product-details {
            flex-grow: 1;
        }
        
        .top-product-name {
            font-size: 24px;
            margin-bottom: 10px;
        }
        
        .top-product-price {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 15px;
        }
        
        .products-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }
        
        .product-card {
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 15px;
            background-color: #fff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
            text-align: center;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .product-card img {
            max-width: 100%;
            height: 150px;
            object-fit: contain;
            margin-bottom: 10px;
            border-radius: 4px;
        }
        
        .product-card h3 {
            font-size: 1em;
            margin: 10px 0 5px 0;
            color: #333;
            min-height: 3em;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        .product-card .price {
            font-weight: bold;
            color: #007bff;
            margin-top: auto;
        }
        
        .product-card .score {
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
        }
        
        .product-card a {
            text-decoration: none;
            color: inherit;
        }
        
        .show-all-btn {
            display: block;
            width: 100%;
            padding: 15px;
            text-align: center;
            background-color: #f0f0f0;
            color: #333;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
            margin-bottom: 20px;
        }
        
        .show-all-btn:hover {
            background-color: #e0e0e0;
        }
        
        .loading, .error {
            text-align: center;
            font-size: 1.1em;
            color: #666;
            margin: 30px 0;
        }
        
        .error {
            color: #dc3545;
        }
        
        #allProductsView {
            display: none; /* Skryto ve výchozím stavu */
        }
        
        #allProductsGrid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .category-tag {
            display: inline-block;
            background-color: #f0f0f0;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            color: #666;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .brand-tag {
            display: inline-block;
            background-color: #e0f0ff;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 0.8em;
            color: #0056b3;
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        .phrase-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 15px;
        }
        
        .phrase-tag {
            background-color: #f0f0f0;
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9em;
            color: #333;
            cursor: pointer;
            transition: all 0.2s ease;
            display: inline-block;
            border: 1px solid #ddd;
        }
        
        .phrase-tag:hover {
            background-color: #e0e0e0;
            border-color: #ccc;
        }
        
        .phrase-tag.active {
            background-color: #007bff;
            color: white;
            border-color: #0056b3;
            position: relative;
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        
        .phrase-tag.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background-color: #007bff;
            border-radius: 50%;
        }
        
        .btn-to-cart {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 15px;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
            transition: background-color 0.3s;
        }
        .btn-to-cart:hover {
            background-color: #218838;
        }
        .btn-to-cart.added {
            background-color: #61bd4f;
        }
        .btn-to-cart.added span {
            display: inline-block;
            margin-right: 5px;
            animation: pulse 1.5s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.2); }
            100% { transform: scale(1); }
        }
        
        /* Skeleton loader pro obrázky */
        .top-product-image-container {
            position: relative;
            width: 300px;
            height: 300px;
            overflow: hidden;
            border-radius: 8px;
        }
        .top-product-image-container::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, #f0f0f0 0%, #e0e0e0 50%, #f0f0f0 100%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            z-index: -1;
        }
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }
        
        /* Aktivní stav posuvníku */
        .price-slider-handle.active-handle {
            border-color: #007bff;
            background-color: #e6f2ff;
            transform: translate(-50%, -50%) scale(1.15);
            box-shadow: 0 0 5px #007bff;
        }
        
        /* Vylepšení indikátoru načítání */
        .loading {
            text-align: center;
            font-size: 1.1em;
            color: #666;
            margin: 30px 0;
            position: relative;
        }
        .loading::after {
            content: '';
            display: inline-block;
            width: 1em;
            height: 1em;
            border: 2px solid #666;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 0.8s linear infinite;
            margin-left: 10px;
            vertical-align: middle;
        }
        @keyframes spin {
            to { transform: rotate(360deg); }
        }
        
        /* Vylepšení tlačítek */
        #searchButton, .show-all-btn {
            transition: all 0.2s ease;
        }
        #searchButton:active, .show-all-btn:active {
            transform: scale(0.95);
        }
        
        /* Vylepšení zvýraznění aktivní fráze */
        .phrase-tag.active {
            background-color: #007bff;
            color: white;
            border-color: #0056b3;
            position: relative;
            box-shadow: 0 2px 5px rgba(0,123,255,0.3);
        }
        .phrase-tag.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 5px;
            height: 5px;
            background-color: #007bff;
            border-radius: 50%;
        }
        
        /* Tooltip pro cenu */
        .tooltip {
            position: relative;
            display: inline-block;
        }
        .tooltip .tooltip-text {
            visibility: hidden;
            width: 120px;
            background-color: #555;
            color: #fff;
            text-align: center;
            border-radius: 6px;
            padding: 5px;
            position: absolute;
            z-index: 1;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
        }
        .tooltip .tooltip-text::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #555 transparent transparent transparent;
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        
        /* Zpětná vazba na filtry */
        .filter-feedback {
            display: none;
            background-color: #f8f8f8;
            border-radius: 4px;
            padding: 5px 10px;
            font-size: 0.8em;
            color: #666;
            margin-top: 5px;
            transition: all 0.3s ease;
        }
        .filter-feedback.show {
            display: block;
            animation: fadeIn 0.3s ease;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        
        /* Tlačítko pro zobrazení mobilních filtrů */
        .mobile-filter-toggle {
            display: none;
            width: 100%;
            padding: 10px;
            background-color: #f0f0f0;
            border: none;
            border-radius: 4px;
            text-align: center;
            margin-bottom: 15px;
            cursor: pointer;
            font-weight: bold;
        }
        
        /* Vylepšení pro výkon na mobilních zařízeních */
        @media (max-width: 768px) {
            .mobile-filter-toggle {
                display: block;
            }
            .sidebar {
                display: none;
                width: 100%;
            }
            .sidebar.visible {
                display: block;
                animation: slideDown 0.3s ease;
            }
            @keyframes slideDown {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
        }
        
        /* Cenový filtr */
        .price-filter {
            display: flex;
            flex-direction: column;
            gap: 10px;
            padding: 10px 0;
        }
        .price-slider-container {
            position: relative;
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            margin: 10px 0;
        }
        .price-slider-range {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            background-color: #f0f0f0;
        }
        .price-slider-handle {
            position: absolute;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            background-color: #fff;
            border: 2px solid #666;
            border-radius: 50%;
            cursor: pointer;
            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
            z-index: 10;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .price-slider-handle:hover {
            background-color: #f8f8f8;
            border-color: #333;
            transform: translate(-50%, -50%) scale(1.1);
        }
        .price-slider-handle:active {
            box-shadow: 0 0 5px #007bff;
            transform: translate(-50%, -50%) scale(1.15);
        }
        .price-slider-values {
            display: flex;
            justify-content: space-between;
            font-size: 0.9em;
            color: #666;
            margin-bottom: 5px;
        }
        
        /* Histogram cen */
        .price-histogram-container {
            height: 35px;
            width: 100%;
            margin-top: 15px;
            position: relative;
        }
        .price-histogram {
            height: 100%;
            display: flex;
            align-items: flex-end;
            justify-content: center;
            gap: 3px;
        }
        .histogram-bar {
            width: 15px;
            background-color: #333;
            border-radius: 0;
            display: inline-block;
        }
        /* Různé výšky sloupců */
        .histogram-bar-height-1 { height: 25%; }
        .histogram-bar-height-2 { height: 50%; }
        .histogram-bar-height-3 { height: 75%; }
        .histogram-bar-height-4 { height: 100%; }
        
        /* Mezera uprostřed (zobrazena jako úzký sloupec) */
        .histogram-spacer {
            width: 15px;
            height: 4px;
            background-color: #333;
            margin-top: auto;
        }
        .no-price-data {
            width: 100%;
            text-align: center;
            font-size: 0.8em;
            color: #999;
            line-height: 50px;
        }
        .no-results {
            text-align: center;
            padding: 20px;
            background-color: #f8f8f8;
            border-radius: 5px;
            color: #666;
            margin: 15px 0;
        }
        
        /* Animace pro fráze */
        .phrase-tag.animate-in {
            animation: tagFadeIn 0.4s ease-out forwards;
        }
        @keyframes tagFadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Indikátor načítání dalších produktů */
        .loading-info {
            text-align: center;
            padding: 10px;
            font-size: 0.9em;
            color: #666;
            margin: 5px 0 15px 0;
        }
        
        /* Responzivní styly pro mobilní zařízení */
        @media (max-width: 768px) {
            .container {
                max-width: 100%;
            }
            
            .main-content {
                flex-direction: column;
            }
            
            .sidebar {
                width: 100%;
                border-right: none;
                border-bottom: 1px solid #eee;
                display: none;
            }
            
            .sidebar.visible {
                display: block;
                animation: slideDown 0.3s ease;
            }
            
            .search-section {
                flex-direction: column;
            }
            
            .search-input {
                margin-bottom: 10px;
            }
            
            .top-product {
                flex-direction: column;
            }
            
            .top-product-image {
                width: 100%;
                max-width: 300px;
                margin: 0 auto 20px;
            }
            
            .top-product-image-container {
                width: 100%;
                max-width: 300px;
                margin: 0 auto 20px;
            }
            
            .products-grid, #allProductsGrid {
                grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
            }
            
            .mobile-filter-toggle {
                display: block;
            }
            
            /* Vylepšení pro filtry na mobilních zařízeních */
            .filter-section {
                position: relative;
            }
            
            .filter-section h3 {
                cursor: pointer;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }
            
            .filter-section h3::after {
                content: '+';
                font-size: 20px;
            }
            
            .filter-section.expanded h3::after {
                content: '-';
            }
            
            .filter-content {
                max-height: 0;
                overflow: hidden;
                transition: max-height 0.3s ease;
            }
            
            .filter-section.expanded .filter-content {
                max-height: 500px;
            }
            
            @keyframes slideDown {
                from { opacity: 0; transform: translateY(-20px); }
                to { opacity: 1; transform: translateY(0); }
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <header>
            <div class="search-section">
                <div class="search-container">
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <select id="tenantSelect" class="dropdown" style="flex: 1;">
                            <option value="" disabled selected>Vyberte tenanta</option>
                            <option value="avenberg">Avenberg</option>
                            <option value="filsonstore">Filsonstore</option>
                            <option value="jabkolevne">Jabkolevne</option>
                        </select>
                        <button id="searchButton" class="search-button">Vyhledat</button>
                    </div>
                    <input type="text" id="queryInput" placeholder="Zadejte vyhledávací dotaz..." class="input-field">
                    <div class="search-type-controls">
                        <label><input type="radio" name="searchType" value="standard" checked> Standardní</label>
                        <label><input type="radio" name="searchType" value="fulltext"> BM25 Fulltext</label>
                        <label><input type="radio" name="searchType" value="qdrant-fulltext"> Qdrant Fulltext</label>
                        <label><input type="radio" name="searchType" value="hybrid-qdrant"> Hybrid Qdrant</label>
                    </div>
                </div>
            </div>
        </header>
        
        <div class="main-content">
            <!-- Přidat tlačítko pro zobrazení filtrů na mobilech -->
            <button id="mobileFilterToggle" class="mobile-filter-toggle">Zobrazit filtry</button>
            
            <aside class="sidebar">
                <div class="filter-section">
                    <h3>Fráze</h3>
                    <div id="phraseFilters" class="phrase-tags">
                        <!-- Dynamicky generované fráze podle kategorie -->
                    </div>
                </div>
                
                <div class="filter-section">
                    <h3>Cena</h3>
                    <div class="price-filter">
                        <div class="price-slider-values">
                            <span id="minPriceValue">0 Kč</span> - <span id="maxPriceValue">0 Kč</span>
                        </div>
                        <div class="price-slider-container">
                            <div id="priceSliderRange" class="price-slider-range"></div>
                            <div id="priceSliderHandleMin" class="price-slider-handle"></div>
                            <div id="priceSliderHandleMax" class="price-slider-handle"></div>
                        </div>
                        <div id="priceHistogram" class="price-histogram-container">
                            <!-- Histogram bude vygenerován JavaScript kódem -->
                        </div>
                    </div>
                </div>
                
                <div class="filter-section">
                    <h3>Kategorie</h3>
                    <div id="categoryFilters">
                        <!-- Bude dynamicky naplněno -->
                    </div>
                </div>
                
                <div class="filter-section">
                    <h3>Značky</h3>
                    <div id="brandFilters">
                        <!-- Bude dynamicky naplněno -->
                    </div>
                </div>
            </aside>
            
            <main class="content">
                <div id="defaultView">
                    <div id="status"></div>
                    
                    <div id="topProductSection" style="display: none;">
                        <h2>Top produkt</h2>
                        <div id="topProduct" class="top-product">
                            <!-- Top produkt bude zde -->
                        </div>
                    </div>
                    
                    <div id="productsSection" style="display: none;">
                        <h2>Produkty</h2>
                        <div id="productsGrid" class="products-grid">
                            <!-- Produkty budou zde -->
                        </div>
                        <button id="showAllBtn" class="show-all-btn">ZOBRAZIT VŠECHNY PRODUKTY</button>
                    </div>
                </div>
                
                <div id="allProductsView">
                    <h2>Výsledky vyhledávání</h2>
                    <button id="backToDefaultBtn" class="show-all-btn">← ZPĚT NA PŘEHLED</button>
                    <div id="allProductsGrid">
                        <!-- Všechny produkty budou zde -->
                    </div>
                </div>
            </main>
        </div>
    </div>

    <script>
        const queryInput = document.getElementById('queryInput');
        const searchButton = document.getElementById('searchButton');
        const tenantSelect = document.getElementById('tenantSelect');
        const statusDiv = document.getElementById('status');
        const topProductSection = document.getElementById('topProductSection');
        const topProductDiv = document.getElementById('topProduct');
        const productsSection = document.getElementById('productsSection');
        const productsGrid = document.getElementById('productsGrid');
        const showAllBtn = document.getElementById('showAllBtn');
        const allProductsView = document.getElementById('allProductsView');
        const defaultView = document.getElementById('defaultView');
        const allProductsGrid = document.getElementById('allProductsGrid');
        const backToDefaultBtn = document.getElementById('backToDefaultBtn');
        const phraseFilters = document.getElementById('phraseFilters');
        const categoryFilters = document.getElementById('categoryFilters');
        const brandFilters = document.getElementById('brandFilters');
        const priceSliderRange = document.getElementById('priceSliderRange');
        const priceSliderHandleMin = document.getElementById('priceSliderHandleMin');
        const priceSliderHandleMax = document.getElementById('priceSliderHandleMax');
        const minPriceValue = document.getElementById('minPriceValue');
        const maxPriceValue = document.getElementById('maxPriceValue');
        const priceHistogram = document.getElementById('priceHistogram');
        const mobileFilterToggle = document.getElementById('mobileFilterToggle');
        
        // Globální proměnná pro uložení všech výsledků
        let allSearchResults = [];
        let categories = new Set();
        let brands = new Set();
        let minProductPrice = 0;
        let maxProductPrice = 0;
        let currentMinPrice = 0;
        let currentMaxPrice = 0;
        let isDraggingMin = false;
        let isDraggingMax = false;
        let pendingFilters = false; // Příznak pro čekající filtry
        let searchTimeout = null; // Timeout pro debounced vyhledávání
        
        // Příznak pro tlumení změn během rychlého posunu slideru
        let debounceSliderChange = false;
        let sliderDebounceTimeout = null;
        // Timeout pro debouncing filtrování při pohybu slideru
        let filterDebounceTimeout = null;
        
        const czechStopwords = [
            "a", "aby", "abych", "abychom", "abys", "abyste", "ale", "anebo", "ani", "ano", "asi", "aspoň", 
            "být", "bez", "bude", "budem", "budeme", "budete", "budeš", "budou", "budu", "by", "byl", "byla", 
            "byli", "bylo", "byly", "bys", "čau", "chce", "chceme", "chcete", "chceš", "chci", "chtít", 
            "chtějí", "chtěl", "chtěla", "chtěli", "chtělo", "chtěly", "co", "čtrnáct", "čtyři", "dál", 
            "dále", "další", "deset", "devatenáct", "devět", "do", "dobrý", "docela", "dva", "dvacet", 
            "dvanáct", "dvě", "ho", "i", "jak", "jakmile", "jako", "já", "je", "jeden", "jedenáct", "jedna", 
            "jedno", "jeho", "jehož", "jej", "její", "jejich", "jemu", "jen", "jenom", "ještě", "jestli", 
            "jestliže", "jeśli", "jich", "jim", "jinak", "jiné", "již", "jsem", "jsi", "jsme", "jsou", "jste", 
            "k", "kam", "kde", "kdo", "kdopak", "kdy", "když", "ke", "kolik", "kromě", "která", "které", 
            "kterou", "který", "kteří", "kvůli", "mají", "málo", "mám", "máme", "máš", "máte", "mé", "mě", 
            "mezi", "mí", "mít", "mně", "mnou", "moc", "mohl", "mohla", "mohli", "mohlo", "mohou", "moje", 
            "moji", "možná", "můj", "mu", "musí", "může", "my", "na", "nad", "nade", "nám", "námi", "naproti", 
            "nás", "náš", "naše", "naši", "ne", "nebo", "nebyl", "nebyla", "nebyli", "nebylo", "nebyly", 
            "něco", "nedělá", "nedělají", "nedělám", "neděláme", "neděláš", "neděláte", "nějak", "někde", 
            "někdo", "nemají", "nemám", "nemáme", "nemáš", "nemáte", "nemůže", "není", "nestačí", "nevadí", 
            "než", "nic", "nich", "ním", "nimi", "nyní", "o", "od", "ode", "on", "ona", "oni", "ono", "ony", 
            "osm", "osmnáct", "pak", "patnáct", "pět", "po", "pod", "podél", "podle", "pokud", "polovina", 
            "potom", "pouze", "právě", "pravděpodobně", "prd", "pro", "proč", "prostě", "proti", "proto", 
            "protože", "první", "před", "přes", "přese", "přesto", "při", "rok", "roku", "s", "se", "sedm", 
            "sedmnáct", "šest", "šestnáct", "skoro", "smí", "smějí", "snad", "spolu", "jsa", "jsi", "jsme", 
            "jste", "sto", "strana", "své", "svých", "svým", "svými", "ta", "tak", "také", "takže", "tam", 
            "tamhle", "tamhleto", "tamto", "tato", "té", "tebe", "tebou", "teda", "tedy", "tě", "téměř", 
            "ten", "tento", "těch", "tím", "tim", "třeba", "tři", "třináct", "to", "tohle", "toho", "tohoto", 
            "tom", "tomto", "tomu", "toto", "tu", "tú", "tuto", "tvá", "tvé", "tvoje", "tvůj", "ty", "tyto", 
            "u", "už", "v", "vám", "vámi", "vás", "váš", "vaše", "vaši", "ve", "večer", "vedle", "velmi", 
            "více", "vlastně", "však", "všechno", "všichni", "vůbec", "vy", "vždy", "z", "za", "zatímco", 
            "ze", "že", "aby", "abych", "abychom", "abys", "abyste", "ale", "anebo", "ani", "ano", "asi", "aspoň",
            "bez", "ber", "bude", "budem", "budeme", "budete", "budeš", "budou", "budu", "by", "byl", "byla", "byli",
            "bylo", "byly", "bys", "byt", "ci", "clanek", "clanku", "clanky", "co", "com", "coz", "cz", "dab", "dal",
            "dalsi", "design", "detail", "dlouha", "dnes", "do", "email", "fi", "fotogalerie", "chtel", "i", "id",
            "info", "ja", "jak", "jako", "je", "jeden", "jeho", "jej", "jeji", "jejich", "jen", "jeste", "jenz", "ji",
            "jich", "jine", "jiz", "jsem", "jsi", "jsme", "jsou", "jste", "k", "kam", "kde", "kdo", "kdyz", "ke", "ktera",
            "ktere", "kteri", "kterou", "ktery", "ku", "ma", "mate", "me", "mezi", "mi", "mit", "mne", "mnou", "muj", "muze", "my",
            "na", "nad", "nam", "napiste", "nas", "nase", "nasi", "ne", "nebo", "nejsou", "neni", "net", "nez", "niz", "nove",
            "novy", "o", "od", "org", "pak", "po", "pod", "podle", "pokud", "pol", "pouze", "prave", "pred", "pres", "pri", "pro",
            "proc", "proto", "protoze", "prvni", "pta", "re", "s", "se", "si", "sice", "strana", "sve", "svuj", "svych", "svym",
            "svymi", "ta", "tak", "take", "takze", "tam", "tato", "te", "tedy", "tema", "ten", "tento", "teto", "text", "ti",
            "tim", "timto", "tipy", "to", "tohle", "toho", "tohoto", "tom", "tomto", "tomuto", "totiz", "tu", "tuto", "tvuj", "ty",
            "tyden", "tym", "tyto", "u", "uz", "v", "vam", "vas", "vase", "ve", "vice", "vsak", "vy", "z", "za", "zda", "zde", "ze",
            "zpet", "zpravy", "www", "kč", "czk", "eur", "pln", "s.r.o.", "s.r.o", "as", "a.s." 
            // Přidány specifické stopwords jako Kč, s.r.o. atd.
        ];
        
        // Event listenery
        searchButton.addEventListener('click', performSearch);
        queryInput.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                event.preventDefault();
                if (defaultView.style.display !== 'none') {
                    // Pokud jsme v defaultním pohledu a máme výsledky, přepneme na zobrazení všech
                    if (allSearchResults.length > 0) {
                        showAllProducts();
                        // Aplikovat čekající filtry při přepnutí na zobrazení všech produktů
                        if (pendingFilters) {
                            applyFilters();
                            pendingFilters = false;
                        }
                    } else {
                        // Jinak provedeme normální vyhledávání
                performSearch();
                    }
                } else {
                    // Pokud jsme ve view všech produktů, provedeme vyhledávání
                    performSearch();
                }
            }
        });
        
        // Vylepšený debounced vyhledávání při psaní
        queryInput.addEventListener('input', function() {
            // Zrušit předchozí timeout, pokud existuje
            if (searchTimeout) {
                clearTimeout(searchTimeout);
            }
            
            // Vizuální zpětná vazba - zobrazení indikátoru psaní
            if (queryInput.value.trim().length > 0) {
                statusDiv.textContent = 'Psaní...';
                statusDiv.className = 'loading';
            } else {
                statusDiv.textContent = '';
                statusDiv.className = '';
            }
            
            // Nastavit nový timeout pro vyhledávání
            searchTimeout = setTimeout(() => {
                // Pouze pokud input obsahuje alespoň 2 znaky
                if (queryInput.value.trim().length >= 2) {
                    // Změna statusu na "Vyhledávání..."
                    statusDiv.textContent = 'Vyhledávání...';
                    statusDiv.className = 'loading';
                    
                    // Krátká prodleva pro lepší UX
                    setTimeout(() => {
                        performSearch();
                    }, 100);
                } else if (queryInput.value.trim().length === 0) {
                    // Vyčistit výsledky, pokud je vstup prázdný
                    statusDiv.textContent = '';
                    statusDiv.className = '';
                    topProductSection.style.display = 'none';
                    productsSection.style.display = 'none';
                }
            }, 400);
        });
        
        showAllBtn.addEventListener('click', function() {
            showAllProducts();
            // Aplikovat čekající filtry při přepnutí na zobrazení všech produktů
            if (pendingFilters) {
                applyFilters();
                pendingFilters = false;
            }
        });
        
        backToDefaultBtn.addEventListener('click', function() {
            hideAllProducts();
        });

        // Event listener pro phrase tagy
        phraseFilters.addEventListener('click', function(event) {
            if (event.target.classList.contains('phrase-tag')) {
                const phrase = event.target.getAttribute('data-phrase');
                queryInput.value = phrase;
                performSearch();
            }
        });

        // Inicializace cenového slideru
        function initPriceSlider() {
            // Nastavení počátečních hodnot
            currentMinPrice = minProductPrice;
            currentMaxPrice = maxProductPrice;
            
            // Aktualizace UI
            updatePriceSliderUI();
            
            // Event listenery pro tahání posuvníků
            priceSliderHandleMin.addEventListener('mousedown', function(e) {
                isDraggingMin = true;
                // Přidat třídu pro vizuální zvýraznění aktivního držadla
                this.classList.add('active-handle');
                e.preventDefault(); // Zabránit defaultnímu chování
            });
            
            priceSliderHandleMax.addEventListener('mousedown', function(e) {
                isDraggingMax = true;
                // Přidat třídu pro vizuální zvýraznění aktivního držadla
                this.classList.add('active-handle');
                e.preventDefault();
            });

            // Přidat podporu pro dotykové ovládání na mobilních zařízeních
            priceSliderHandleMin.addEventListener('touchstart', function(e) {
                isDraggingMin = true;
                this.classList.add('active-handle');
                e.preventDefault();
            });
            
            priceSliderHandleMax.addEventListener('touchstart', function(e) {
                isDraggingMax = true;
                this.classList.add('active-handle');
                e.preventDefault();
            });
            
            document.addEventListener('touchmove', function(e) {
                if (!isDraggingMin && !isDraggingMax) return;
                
                // Získat dotykové souřadnice
                const touch = e.touches[0];
                const sliderRect = priceSliderRange.parentElement.getBoundingClientRect();
                const sliderWidth = sliderRect.width;
                const offsetX = touch.clientX - sliderRect.left;
                const percentage = Math.min(Math.max(offsetX / sliderWidth, 0), 1);
                
                const priceRange = maxProductPrice - minProductPrice;
                const newPrice = minProductPrice + percentage * priceRange;
                
                if (isDraggingMin) {
                    currentMinPrice = Math.min(newPrice, currentMaxPrice - priceRange * 0.05);
                }
                
                if (isDraggingMax) {
                    currentMaxPrice = Math.max(newPrice, currentMinPrice + priceRange * 0.05);
                }
                
                // Aktualizovat UI
                updatePriceSliderUI();
                
                // Filtrování během dotyku s debounce
                pendingFilters = true;
                clearTimeout(filterDebounceTimeout);
                filterDebounceTimeout = setTimeout(() => {
                    // PŮVODNÍ: if (defaultView.style.display === 'none') {
                    applyFilters(); // Aplikovat filtry vždy
                    pendingFilters = false; // Resetovat po aplikaci
                    // PŮVODNÍ: }
                }, 100);
                
                e.preventDefault();
            });
            
            document.addEventListener('touchend', function() {
                if (isDraggingMin || isDraggingMax) {
                    priceSliderHandleMin.classList.remove('active-handle');
                    priceSliderHandleMax.classList.remove('active-handle');
                    
                    isDraggingMin = false;
                    isDraggingMax = false;
                    
                    updatePriceSliderUI();
                    
                    clearTimeout(filterDebounceTimeout); // Zrušit případný debounced filtr
                    
                    // PŮVODNÍ: if (defaultView.style.display === 'none' && pendingFilters) {
                    if (pendingFilters) { // Aplikovat, pokud byly nějaké změny na slideru
                        applyFilters();
                        pendingFilters = false;
                    }
                }
            });
            
            document.addEventListener('mousemove', function(e) {
                if (!isDraggingMin && !isDraggingMax) return;
                
                const sliderRect = priceSliderRange.parentElement.getBoundingClientRect();
                const sliderWidth = sliderRect.width;
                const offsetX = e.clientX - sliderRect.left;
                const percentage = Math.min(Math.max(offsetX / sliderWidth, 0), 1);
                
                const priceRange = maxProductPrice - minProductPrice;
                const newPrice = minProductPrice + percentage * priceRange;
                
                if (isDraggingMin) {
                    currentMinPrice = Math.min(newPrice, currentMaxPrice - priceRange * 0.05);
                }
                
                if (isDraggingMax) {
                    currentMaxPrice = Math.max(newPrice, currentMinPrice + priceRange * 0.05);
                }
                
                // Aktualizovat UI pouze pokud není tlumení nebo po každých 50ms při rychlém posunu
                if (!debounceSliderChange) {
                    updatePriceSliderUI();
                    
                    // Nastavit tlumení, aby se aktualizace nespouštěla příliš často
                    debounceSliderChange = true;
                    clearTimeout(sliderDebounceTimeout);
                    sliderDebounceTimeout = setTimeout(() => {
                        debounceSliderChange = false;
                    }, 50);
                }
                
                // Označit, že filtry čekají na aplikaci
                pendingFilters = true;
                
                // Přidat debouncing pro filtrování během posunu slideru
                // Zrušit předchozí timeout, pokud existuje
                clearTimeout(filterDebounceTimeout);
                
                // Nastavit nový timeout pro filtrování
                filterDebounceTimeout = setTimeout(() => {
                    // PŮVODNÍ: Pouze aplikovat filtry, pokud jsme ve view všech produktů
                    // PŮVODNÍ: if (defaultView.style.display === 'none') {
                    applyFilters(); // Aplikovat filtry vždy
                    pendingFilters = false; // Resetovat po aplikaci
                    // PŮVODNÍ: }
                }, 100); // Interval 100ms pro filtrování během posunu
            });
            
            document.addEventListener('mouseup', function() {
                if (isDraggingMin || isDraggingMax) {
                    // Odstranění třídy pro vizuální zvýraznění
                    priceSliderHandleMin.classList.remove('active-handle');
                    priceSliderHandleMax.classList.remove('active-handle');
                    
                    isDraggingMin = false;
                    isDraggingMax = false;
                    
                    // Zajistit finální aktualizaci UI (pro případ, že byla tlumena)
                    updatePriceSliderUI();
                    
                    // Zrušit případný čekající timeout pro filtrování
                    clearTimeout(filterDebounceTimeout);
                    
                    // PŮVODNÍ: Pouze aplikovat filtry, pokud jsme ve view všech produktů
                    // PŮVODNÍ: if (defaultView.style.display === 'none' && pendingFilters) {
                    if (pendingFilters) { // Aplikovat, pokud byly nějaké změny na slideru
                        applyFilters();
                        pendingFilters = false;
                    }
                }
            });
            
            // Kliknutí přímo na slider (posune nejbližší posuvník)
            priceSliderRange.parentElement.addEventListener('click', function(e) {
                if (e.target === priceSliderHandleMin || e.target === priceSliderHandleMax) return;
                
                const sliderRect = priceSliderRange.parentElement.getBoundingClientRect();
                const sliderWidth = sliderRect.width;
                const offsetX = e.clientX - sliderRect.left;
                const percentage = Math.min(Math.max(offsetX / sliderWidth, 0), 1);
                
                const priceRange = maxProductPrice - minProductPrice;
                const newPrice = minProductPrice + percentage * priceRange;
                
                // Určit, který posuvník je blíže ke kliknutí a posunout jej
                const distanceToMin = Math.abs(newPrice - currentMinPrice);
                const distanceToMax = Math.abs(newPrice - currentMaxPrice);
                
                if (distanceToMin <= distanceToMax) {
                    currentMinPrice = newPrice;
                } else {
                    currentMaxPrice = newPrice;
                }
                
                updatePriceSliderUI();
                
                // Okamžitě aplikovat filtry, pokud jsme ve view všech produktů
                pendingFilters = true;
                // PŮVODNÍ: if (defaultView.style.display === 'none') {
                applyFilters(); // Aplikovat filtry vždy
                pendingFilters = false; // Resetovat po aplikaci
                // PŮVODNÍ: }
            });
        }
        
        // Aktualizace UI pro cenový slider
        function updatePriceSliderUI() {
            if (minProductPrice === maxProductPrice) return;
            
            const priceRange = maxProductPrice - minProductPrice;
            const minPercentage = (currentMinPrice - minProductPrice) / priceRange * 100;
            const maxPercentage = (currentMaxPrice - minProductPrice) / priceRange * 100;
            
            // Nastavení pozice posuvníků
            priceSliderHandleMin.style.left = `${minPercentage}%`;
            priceSliderHandleMax.style.left = `${maxPercentage}%`;
            
            // Nastavení vyznačeného rozsahu
            priceSliderRange.style.left = `${minPercentage}%`;
            priceSliderRange.style.width = `${maxPercentage - minPercentage}%`;
            
            // Aktualizace textových hodnot s formátováním
            const formattedMin = Math.round(currentMinPrice).toLocaleString('cs-CZ');
            const formattedMax = Math.round(currentMaxPrice).toLocaleString('cs-CZ');
            minPriceValue.textContent = `${formattedMin} Kč`;
            maxPriceValue.textContent = `${formattedMax} Kč`;
            
            // Přidat tooltip pro vizualizaci nastavené ceny
            if (!priceSliderHandleMin.querySelector('.tooltip-text')) {
                // Přidat tooltip k posuvníkům
                const minTooltip = document.createElement('span');
                minTooltip.className = 'tooltip-text';
                priceSliderHandleMin.classList.add('tooltip');
                priceSliderHandleMin.appendChild(minTooltip);
                
                const maxTooltip = document.createElement('span');
                maxTooltip.className = 'tooltip-text';
                priceSliderHandleMax.classList.add('tooltip');
                priceSliderHandleMax.appendChild(maxTooltip);
            }
            
            // Aktualizace hodnot v tooltip
            const minTooltip = priceSliderHandleMin.querySelector('.tooltip-text');
            const maxTooltip = priceSliderHandleMax.querySelector('.tooltip-text');
            
            if (minTooltip) minTooltip.textContent = `${formattedMin} Kč`;
            if (maxTooltip) maxTooltip.textContent = `${formattedMax} Kč`;
            
            // Zobrazit zpětnou vazbu při změně filtru
            showFilterFeedback();
            
            // Přidat vizuální efekt pro slider
            priceSliderRange.style.transition = 'all 0.2s ease-out';
            priceSliderRange.style.backgroundColor = '#007bff';
            setTimeout(() => {
                priceSliderRange.style.backgroundColor = '';
            }, 300);
        }
        
        // Přidání chybějící funkce showFilterFeedback
        function showFilterFeedback() {
            // Kontrola, zda již existuje element zpětné vazby
            let feedbackEl = document.querySelector('.filter-feedback');
            
            // Pokud neexistuje, vytvoříme ho
            if (!feedbackEl) {
                feedbackEl = document.createElement('div');
                feedbackEl.className = 'filter-feedback';
                const priceFilterSection = document.querySelector('.price-filter');
                if (priceFilterSection) {
                    priceFilterSection.appendChild(feedbackEl);
                }
            }
            
            // Nastavíme text zpětné vazby
            const formattedMin = Math.round(currentMinPrice).toLocaleString('cs-CZ');
            const formattedMax = Math.round(currentMaxPrice).toLocaleString('cs-CZ');
            feedbackEl.textContent = `Filtrování: ${formattedMin} - ${formattedMax} Kč`;
            feedbackEl.style.backgroundColor = '#e8f4ff';
            
            // Zobrazíme zpětnou vazbu a přidáme animaci
            feedbackEl.classList.add('show');
            
            // Přidáme animaci pulzování
            feedbackEl.style.animation = 'pulse 0.5s 2';
            
            // Nastavíme časovač pro skrytí zpětné vazby po 3 sekundách
            setTimeout(() => {
                feedbackEl.classList.remove('show');
                feedbackEl.style.animation = '';
            }, 3000);
        }
        
        function displayTopProduct(product) {
            topProductDiv.innerHTML = '';
            
            const img = document.createElement('img');
            img.src = product.image_url || 'https://via.placeholder.com/300?text=No+Image';
            img.alt = product.name || 'Produkt';
            img.className = 'top-product-image';
            img.loading = 'eager'; // Prioritní načtení
            img.onerror = function() {
                this.onerror = null;
                this.src = 'https://via.placeholder.com/300?text=Image+Error';
            };
            
            // Přidat skeleton loader během načítání obrázku
            const imgContainer = document.createElement('div');
            imgContainer.className = 'top-product-image-container';
            imgContainer.appendChild(img);
            
            // Efekt náběhu při zobrazení
            img.style.opacity = '0';
            setTimeout(() => {
                img.style.transition = 'opacity 0.3s ease';
                img.style.opacity = '1';
            }, 50);
            
            const detailsDiv = document.createElement('div');
            detailsDiv.className = 'top-product-details';
            
            const nameH3 = document.createElement('h3');
            nameH3.className = 'top-product-name';
            nameH3.textContent = product.name || 'Název není k dispozici';
            
            const priceP = document.createElement('p');
            priceP.className = 'top-product-price';
            priceP.textContent = product.price ? `${product.price} Kč` : '-';
            
            const categoryTag = document.createElement('div');
            categoryTag.className = 'category-tag';
            categoryTag.textContent = product.category || 'Bez kategorie';
            
            const brandTag = document.createElement('div');
            brandTag.className = 'brand-tag';
            brandTag.textContent = product.brand || 'Bez značky';
            
            const btnToCart = document.createElement('button');
            btnToCart.className = 'btn-to-cart';
            btnToCart.textContent = 'Do košíku';
            btnToCart.addEventListener('click', function() {
                this.innerHTML = '<span>✓</span> Přidáno';
                this.classList.add('added');
                setTimeout(() => {
                    this.innerHTML = 'Do košíku';
                    this.classList.remove('added');
                }, 2000);
            });
            
            const productLink = document.createElement('a');
            productLink.href = product.product_url || '#';
            productLink.target = '_blank';
            productLink.textContent = 'Zobrazit detail produktu';
            productLink.style.display = 'block';
            productLink.style.marginTop = '10px';
            
            detailsDiv.appendChild(nameH3);
            detailsDiv.appendChild(priceP);
            detailsDiv.appendChild(categoryTag);
            detailsDiv.appendChild(brandTag);
            detailsDiv.appendChild(document.createElement('br'));
            detailsDiv.appendChild(document.createElement('br'));
            detailsDiv.appendChild(btnToCart);
            detailsDiv.appendChild(productLink);
            
            topProductDiv.appendChild(imgContainer);
            topProductDiv.appendChild(detailsDiv);
            
            topProductSection.style.display = 'block';
        }
        
        function displayProducts(products, startIndex, count, targetElement) {
            targetElement.innerHTML = '';
            
            const endIndex = Math.min(startIndex + count, products.length);
            
            for (let i = startIndex; i < endIndex; i++) {
                const product = products[i];
                const productDiv = document.createElement('div');
                productDiv.className = 'product-card';

                const productLink = document.createElement('a');
                 productLink.href = product.product_url || '#'; 
                productLink.target = '_blank';

                const img = document.createElement('img');
                img.src = product.image_url || 'https://via.placeholder.com/150?text=No+Image'; 
                img.alt = product.name || 'Produkt';
                img.onerror = function() {
                    this.onerror = null;
                    this.src = 'https://via.placeholder.com/150?text=Image+Error';
                };
                productLink.appendChild(img);

                const nameH3 = document.createElement('h3');
                nameH3.textContent = product.name || 'Název není k dispozici';
                productLink.appendChild(nameH3);

                const priceP = document.createElement('p');
                priceP.className = 'price';
                 priceP.textContent = product.price ? `${product.price} Kč` : '-'; 
                productLink.appendChild(priceP);

                productDiv.appendChild(productLink);
                
                 const scoreP = document.createElement('p');
                 scoreP.className = 'score';
                 scoreP.textContent = `Score: ${product.score ? product.score.toFixed(4) : 'N/A'}`;
                productDiv.appendChild(scoreP);
                
                targetElement.appendChild(productDiv);
            }
            
            if (targetElement === productsGrid) {
                productsSection.style.display = endIndex > startIndex ? 'block' : 'none';
            }
        }
        
        function updateCategoryAndBrandFilters() {
            // Vyčistit stávající filtry
            categoryFilters.innerHTML = '';
            brandFilters.innerHTML = '';
            
            // Přidat filtry kategorií
            categories.forEach(category => {
                const filterDiv = document.createElement('div');
                filterDiv.className = 'filter-option';
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `category-${category}`;
                checkbox.setAttribute('data-category', category);
                
                // Změna: Pouze nastavit příznak pro čekající filtry, neaplikovat ihned
                checkbox.addEventListener('change', function() {
                    pendingFilters = true;
                    
                    // Pouze aplikovat hned, pokud jsme ve view všech produktů
                    if (defaultView.style.display === 'none') {
                        applyFilters();
                        pendingFilters = false;
                    }
                });
                
                const label = document.createElement('label');
                label.htmlFor = `category-${category}`;
                label.textContent = category;
                
                filterDiv.appendChild(checkbox);
                filterDiv.appendChild(label);
                categoryFilters.appendChild(filterDiv);
            });
            
            // Přidat filtry značek
            brands.forEach(brand => {
                const filterDiv = document.createElement('div');
                filterDiv.className = 'filter-option';
                
                const checkbox = document.createElement('input');
                checkbox.type = 'checkbox';
                checkbox.id = `brand-${brand}`;
                checkbox.setAttribute('data-brand', brand);
                
                // Změna: Pouze nastavit příznak pro čekající filtry, neaplikovat ihned
                checkbox.addEventListener('change', function() {
                    pendingFilters = true;
                    
                    // Pouze aplikovat hned, pokud jsme ve view všech produktů
                    if (defaultView.style.display === 'none') {
                        applyFilters();
                        pendingFilters = false;
                    }
                });
                
                const label = document.createElement('label');
                label.htmlFor = `brand-${brand}`;
                label.textContent = brand;
                
                filterDiv.appendChild(checkbox);
                filterDiv.appendChild(label);
                brandFilters.appendChild(filterDiv);
            });
            
            // Aktualizace cenového histogramu
            updatePriceHistogram();
        }
        
        function updatePriceHistogram() {
            // Nejprve najít minimální a maximální cenu produktů
            minProductPrice = Infinity;
            maxProductPrice = 0;
            
            allSearchResults.forEach(product => {
                // Extrahujeme číselnou hodnotu ceny
                let priceValue = 0;
                if (product.price) {
                    // Odstranit měnu a převést na číslo
                    const priceString = product.price.toString().replace(/[^\d.-]/g, '');
                    priceValue = parseFloat(priceString);
                }
                
                if (!isNaN(priceValue) && priceValue > 0) {
                    minProductPrice = Math.min(minProductPrice, priceValue);
                    maxProductPrice = Math.max(maxProductPrice, priceValue);
                }
            });
            
            // Inicializace slideru po zjištění min a max ceny
            currentMinPrice = minProductPrice;
            currentMaxPrice = maxProductPrice;
            
            // Formátování textu cenového rozsahu
            minPriceValue.textContent = `${Math.round(minProductPrice)} Kč`;
            maxPriceValue.textContent = `${Math.round(maxProductPrice)} Kč`;
            
            // Vytvořit histogram přesně podle vzoru
            const histogramContainer = document.getElementById('priceHistogram');
            histogramContainer.innerHTML = '';
            
            // Vytvoříme vnitřní kontejner pro sloupce
            const histogramBarsContainer = document.createElement('div');
            histogramBarsContainer.className = 'price-histogram';
            histogramContainer.appendChild(histogramBarsContainer);
            
            // Vzor výšek sloupců v histogramu (4 znamená nejvyšší sloupec, 1 nejnižší)
            // Podle ukázky z obrázku: vyšší sloupce vlevo, nižší uprostřed, pak mezera, pak střední výška vpravo
            const barHeights = [4, 3, 2, 1, 'spacer', 2, 2, 3];
            
            // Rozdělíme cenový rozsah
            const priceRange = maxProductPrice - minProductPrice;
            const stepSize = priceRange / barHeights.length;
            
            barHeights.forEach((height, index) => {
                if (height === 'spacer') {
                    // Vytvoříme mezeru (zobrazena jako nízký sloupec)
                    const spacer = document.createElement('div');
                    spacer.className = 'histogram-spacer';
                    histogramBarsContainer.appendChild(spacer);
            } else {
                    // Vytvoříme sloupec
                    const bar = document.createElement('div');
                    bar.className = `histogram-bar histogram-bar-height-${height}`;
                    histogramBarsContainer.appendChild(bar);
                    
                    // Výpočet cenového rozsahu pro tento sloupec
                    const minSloupecCena = Math.round(minProductPrice + (index * stepSize));
                    const maxSloupecCena = Math.round(minProductPrice + ((index + 1) * stepSize));
                    
                    // Tooltip s informací o cenovém rozsahu
                    bar.title = `${minSloupecCena} - ${maxSloupecCena} Kč`;
                    
                    // Přidat event listener pro kliknutí na sloupec histogramu
                    bar.addEventListener('click', function() {
                        // Nastavit cenový filtr na rozsah tohoto sloupce
                        currentMinPrice = minProductPrice + (index * stepSize);
                        currentMaxPrice = minProductPrice + ((index + 1) * stepSize);
                        updatePriceSliderUI();
                        applyFilters();
                    });
                }
            });
            
            // Inicializace slideru až po vytvoření histogramu
            initPriceSlider();
        }
        
        function applyFilters() {
            // Získat zaškrtnuté kategorie
            const selectedCategories = Array.from(document.querySelectorAll('#categoryFilters input:checked'))
                .map(input => input.getAttribute('data-category'));
            
            // Získat zaškrtnuté značky
            const selectedBrands = Array.from(document.querySelectorAll('#brandFilters input:checked'))
                .map(input => input.getAttribute('data-brand'));
            
            // Start měření výkonu
            const startTime = performance.now();
            
            // Filtrovat výsledky
            let filteredResults = [...allSearchResults];
            
            if (selectedCategories.length > 0) {
                filteredResults = filteredResults.filter(product => 
                    selectedCategories.includes(product.category)
                );
            }
            
            if (selectedBrands.length > 0) {
                filteredResults = filteredResults.filter(product => 
                    selectedBrands.includes(product.brand)
                );
            }
            
            // Filtrování podle ceny z posuvníků s využitím memoizace
            if (minProductPrice !== Infinity && maxProductPrice > 0) {
                filteredResults = memoizedPriceFiltering(filteredResults, currentMinPrice, currentMaxPrice);
            }
            
            // Konec měření výkonu
            const endTime = performance.now();
            
            // Aktualizovat počty v nadpisech
            const productCountText = `(${filteredResults.length})`;
            const productsHeading = document.querySelector('#productsSection h2');
            if (productsHeading) {
                productsHeading.textContent = `Produkty ${productCountText}`;
            }
            
            const allProductsHeading = document.querySelector('#allProductsView h2');
            if (allProductsHeading) {
                // Zachovat původní text dotazu, ale aktualizovat počet
                const headingText = allProductsHeading.textContent;
                const matchResults = headingText.match(/^(.+?)\s*\(\d+\)$/);
                if (matchResults) {
                    allProductsHeading.textContent = `${matchResults[1]} ${productCountText}`;
                } else {
                    allProductsHeading.textContent = `Výsledky ${productCountText}`;
                }
            }
            
            // Zobrazit debug informace v konzoli
            console.debug(`Filtrování dokončeno za ${(endTime - startTime).toFixed(2)}ms, nalezeno ${filteredResults.length} výsledků`);
            
            // Aktualizovat zobrazení podle aktuálního pohledu
            if (defaultView.style.display !== 'none') {
                // Jsme v defaultním pohledu
                if (filteredResults.length > 0) {
                    displayTopProduct(filteredResults[0]);
                    displayProducts(filteredResults, 1, 6, productsGrid);
                } else {
                    topProductSection.style.display = 'none';
                    productsSection.style.display = 'none';
                    statusDiv.textContent = 'Žádné produkty neodpovídají zadaným filtrům.';
                    statusDiv.className = 'error';
                }
            } else {
                // Jsme v pohledu všech produktů
                if (filteredResults.length > 0) {
                    // Použít virtuální scrolling pro zobrazení velkého počtu produktů
                    displayProductsOptimized(filteredResults, allProductsGrid);
                } else {
                    allProductsGrid.innerHTML = '<div class="no-results">Žádné produkty neodpovídají zadaným filtrům.</div>';
                }
            }
        }
        
        // Optimalizovaná funkce pro zobrazení velkého počtu produktů
        function displayProductsOptimized(products, targetElement) {
            // Vyčistit cílový element
            targetElement.innerHTML = '';
            
            // Pokud je málo produktů, použít standardní metodu
            if (products.length <= 50) {
                displayProducts(products, 0, products.length, targetElement);
                return; 
            }
            
            // Pro velký počet produktů použít optimalizované vykreslování
            const fragment = document.createDocumentFragment();
            
            // Vykreslit pouze prvních 50 produktů a přidat tlačítko "Načíst další"
            displayProducts(products.slice(0, 50), 0, 50, fragment);
            
            // Přidat informaci o počtu zobrazených produktů
            const infoDiv = document.createElement('div');
            infoDiv.className = 'loading-info';
            infoDiv.textContent = `Zobrazeno 50 z ${products.length} produktů`;
            fragment.appendChild(infoDiv);
            
            // Přidat tlačítko pro načtení dalších produktů
            const loadMoreBtn = document.createElement('button');
            loadMoreBtn.className = 'show-all-btn';
            loadMoreBtn.textContent = `NAČÍST DALŠÍ PRODUKTY`;
            loadMoreBtn.setAttribute('data-offset', '50');
            loadMoreBtn.setAttribute('data-total', products.length.toString());
            
            loadMoreBtn.addEventListener('click', function() {
                const offset = parseInt(this.getAttribute('data-offset'));
                const nextBatch = 30; // Počet dalších produktů na načtení
                
                // Přidat další produkty
                const nextProducts = products.slice(offset, offset + nextBatch);
                const tempContainer = document.createDocumentFragment();
                displayProducts(nextProducts, 0, nextProducts.length, tempContainer);
                
                // Vložit před tlačítko "Načíst další"
                this.parentNode.insertBefore(tempContainer, this);
                
                // Aktualizovat offset
                const newOffset = offset + nextBatch;
                this.setAttribute('data-offset', newOffset.toString());
                
                // Aktualizovat informaci o počtu zobrazených produktů
                infoDiv.textContent = `Zobrazeno ${Math.min(newOffset, products.length)} z ${products.length} produktů`;
                
                // Skrýt tlačítko, pokud jsou načteny všechny produkty
                if (newOffset >= products.length) {
                    this.style.display = 'none';
                }
            });
            
            fragment.appendChild(loadMoreBtn);
            targetElement.appendChild(fragment);
        }
        
        // Memoizace pro výpočet cenových rozsahů
        const memoizedPriceFiltering = (function() {
            let cache = {};
            let lastCacheKey = '';
            let lastResult = [];
            
            // Pomocná funkce pro extrakci číselné hodnoty ceny
            function extractPriceValue(price) {
                if (!price) return NaN;
                
                // Pokud je to číslo, vrátíme přímo
                if (typeof price === 'number') return price;
                
                // Pokud je to string, vyčistíme ho a převedeme
                const priceString = price.toString().replace(/[^\d.-]/g, '');
                const parsedPrice = parseFloat(priceString);
                // console.log(`extractPriceValue: original='${price}', string='${priceString}', parsed=${parsedPrice}`); // LADĚNÍ - dočasně pro detail
                return parsedPrice;
            }
            
            return function(products, minPrice, maxPrice) {
                // Zaokrouhlení cen na celá čísla pro lepší shodu v cache
                const roundedMinPrice = Math.floor(minPrice);
                const roundedMaxPrice = Math.ceil(maxPrice);
                const cacheKey = `${roundedMinPrice}-${roundedMaxPrice}`;
                
                // Rychlá kontrola - pokud je stejný klíč jako naposledy, vrátíme poslední výsledek
                if (cacheKey === lastCacheKey) {
                    return lastResult;
                }
                
                // Kontrola cache
                if (cache[cacheKey]) {
                    lastCacheKey = cacheKey;
                    lastResult = cache[cacheKey];
                    return cache[cacheKey];
                }
                
                console.time('priceFilter');
                
                // Filtrovat produkty podle ceny
                const result = products.filter(product => {
                    const priceValue = extractPriceValue(product.price);
                    
                    // LADĚNÍ: Zobrazit, co se porovnává
                    if (product.name && (product.name.includes("Gril") || product.name.includes("gril"))) { // Zobrazit jen pro relevantní produkty pro přehlednost
                        console.log(`Produkt: ${product.name}, Cena surová: ${product.price} (typ: ${typeof product.price}), Extraovaná cena: ${priceValue}, Min: ${roundedMinPrice}, Max: ${roundedMaxPrice}, Vyhovuje: ${!isNaN(priceValue) && priceValue >= roundedMinPrice && priceValue <= roundedMaxPrice}`);
                    }

                    // Pokud nemá platnou cenu, nezahrneme ho
                    if (isNaN(priceValue)) return false;
                    
                    // Kontrola rozsahu
                    return priceValue >= roundedMinPrice && priceValue <= roundedMaxPrice;
                });
                
                console.timeEnd('priceFilter');
                
                // Uložit výsledek do cache (pouze pokud není příliš velký)
                if (result.length < 1000) {
                    cache[cacheKey] = result;
                    
                    // Omezení velikosti cache (uchováme jen posledních 20 výsledků)
                    const cacheKeys = Object.keys(cache);
                    if (cacheKeys.length > 20) {
                        const oldestKey = cacheKeys[0];
                        delete cache[oldestKey];
                    }
                }
                
                // Uložit poslední výsledek pro rychlou kontrolu příště
                lastCacheKey = cacheKey;
                lastResult = result;
                
                return result;
            };
        })();
        
        function showAllProducts() {
            defaultView.style.display = 'none';
            allProductsView.style.display = 'block';
            
            // Načtení všech produktů a aplikace filtrů
            if (pendingFilters) {
                applyFilters();
                pendingFilters = false;
            } else {
                // Pokud nejsou čekající filtry, zobrazit všechny produkty
                displayProducts(allSearchResults, 0, allSearchResults.length, allProductsGrid);
            }
        }
        
        function hideAllProducts() {
            allProductsView.style.display = 'none';
            defaultView.style.display = 'block';
        }
        
        async function performSearch() {
            const query = queryInput.value.trim();
            const tenantId = tenantSelect.value;
            
            if (!query) {
                statusDiv.textContent = 'Prosím, zadejte vyhledávací dotaz.';
                statusDiv.className = 'error';
                topProductSection.style.display = 'none';
                productsSection.style.display = 'none';
                return;
            }

            topProductSection.style.display = 'none';
            productsSection.style.display = 'none';
            statusDiv.textContent = 'Načítám výsledky...';
            statusDiv.className = 'loading';

            // Získat aktuálně vybraný typ vyhledávání
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            // Kontrola cache se zohledněním typu vyhledávání
            const cacheKey = `${tenantId}:${searchType}:${query}`;
            const cachedResults = sessionStorage.getItem(cacheKey);
            
            if (cachedResults) {
                try {
                    // Použít data z cache pro okamžitou odezvu
                    const products = JSON.parse(cachedResults);
                    processSearchResults(products);
                    console.debug(`Výsledky pro ${searchType} načteny z cache`);
                    
                    // Přesto provést nové hledání na pozadí pro aktuální výsledky
                    fetchSearchResults(tenantId, query, true);
                    return;
                } catch (e) {
                    console.error('Chyba při čtení z cache:', e);
                    // Pokračovat s normálním vyhledáváním
                }
            }
            
            // Provést normální vyhledávání
            fetchSearchResults(tenantId, query);
        }
        
        // Oddělená funkce pro volání API
        async function fetchSearchResults(tenantId, query, isBackgroundFetch = false) {
            // Získat aktuálně vybraný typ vyhledávání
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            // Nastavení URL podle typu vyhledávání
            let apiUrl;
            switch(searchType) {
                case 'fulltext':
                    apiUrl = `http://localhost:8000/fulltext-search/${tenantId}?query=${encodeURIComponent(query)}`;
                    break;
                case 'qdrant-fulltext':
                    apiUrl = `http://localhost:8000/qdrant-fulltext-search/${tenantId}?query=${encodeURIComponent(query)}`;
                    break;
                case 'hybrid-qdrant':
                    apiUrl = `http://localhost:8000/hybrid-qdrant-search/${tenantId}?query=${encodeURIComponent(query)}&fulltext_ratio=0.3&semantic_ratio=0.7&ensemble_alpha=0.5`;
                    break;
                case 'standard':
                default:
                    apiUrl = `http://localhost:8000/search/${tenantId}?query=${encodeURIComponent(query)}`;
                    break;
            }
            
            console.log(`Vyhledávání: ${searchType}, URL: ${apiUrl}`);
            
            try {
                const response = await fetch(apiUrl);
                if (!response.ok) {
                    let errorMsg = `Chyba API: ${response.status} ${response.statusText}`;
                    try {
                         const errorData = await response.json();
                         if (errorData.detail) {
                             errorMsg += ` - ${errorData.detail}`;
                         }
                    } catch (e) {}
                    throw new Error(errorMsg);
                }
                
                const products = await response.json();
                
                // Uložit do cache pro budoucí použití včetně typu vyhledávání
                const cacheKey = `${tenantId}:${searchType}:${query}`;
                try {
                    sessionStorage.setItem(cacheKey, JSON.stringify(products));
                } catch (e) {
                    console.warn('Nepodařilo se uložit výsledky do cache:', e);
                }
                
                // Zpracovat výsledky, pokud nejde o vyhledávání na pozadí
                if (!isBackgroundFetch) {
                    processSearchResults(products);
                }
            } catch (error) {
                if (!isBackgroundFetch) {
                    console.error('Chyba při volání API:', error);
                    statusDiv.textContent = `Chyba: ${error.message}`;
                    statusDiv.className = 'error';
                    topProductSection.style.display = 'none';
                    productsSection.style.display = 'none';
                }
            }
        }
        
        // Funkce pro detekci kategorie z aktuálních výsledků vyhledávání
        function detectCategoryFromResults(results) {
            if (!results || results.length === 0) return 'default';
            
            // Počítání kategorií ve výsledcích
            const categoryCounter = {};
            
            results.forEach(product => {
                if (product.category) {
                    // Zjednodušení kategorie pro seskupování
                    let simpleCategory = product.category.toLowerCase();
                    
                    // Zkrácení kategorie na základní slovo
                    if (simpleCategory.includes('gril')) {
                        simpleCategory = 'grilování';
                    } else if (simpleCategory.includes('pergol')) {
                        simpleCategory = 'pergoly';
                    } else if (simpleCategory.includes('bazén') || simpleCategory.includes('bazn')) {
                        simpleCategory = 'bazény';
                    } else if (simpleCategory.includes('zahrad') || simpleCategory.includes('nábytek')) {
                        simpleCategory = 'zahrada';
                    }
                    
                    categoryCounter[simpleCategory] = (categoryCounter[simpleCategory] || 0) + 1;
                }
            });
            
            // Nalezení nejčastější kategorie
            let mostFrequent = 'default';
            let maxCount = 0;
            
            for (const [category, count] of Object.entries(categoryCounter)) {
                if (count > maxCount) {
                    mostFrequent = category;
                    maxCount = count;
                }
            }
            
            return mostFrequent;
        }
        
        // Zpracování výsledků vyhledávání
        function processSearchResults(products) {
            // Získat aktuálně vybraný typ vyhledávání pro informaci
            const searchType = document.querySelector('input[name="searchType"]:checked').value;
            
            statusDiv.textContent = `Nalezeno ${products.length} výsledků (${searchType})`;
            statusDiv.className = products.length > 0 ? 'success' : 'warning';
            
            // Uložit výsledky pro možné další filtrování
            allSearchResults = products;
            
            // Vyčistit předchozí výsledky
            topProductGrid.innerHTML = '';
            productsGrid.innerHTML = '';
            allProductsGrid.innerHTML = '';
            
            if (products.length > 0) {
                // Sbírat kategorie a značky pro filtry
                categories.clear();
                brands.clear();
                
                products.forEach(product => {
                    if (product.category) categories.add(product.category);
                    if (product.brand) brands.add(product.brand);
                });
                
                // Aktualizovat filtry
                updateCategoryAndBrandFilters();
                
                // Detekce kategorie z výsledků
                const detectedCategory = detectCategoryFromResults(products);
                
                // Aktualizace frází na základě výsledků vyhledávání
                if (typeof updatePhrases === 'function') {
                    updatePhrases(detectedCategory, products);
                }
                
                // Zobrazit top produkt (první výsledek)
                displayTopProduct(products[0]);
                
                // Zobrazit další produkty (2. až 7. výsledek)
                if (products.length > 1) {
                    displayProducts(products, 1, 6, productsGrid);
                }
                
                // Zobrazení defaultního pohledu (top produkt + 6 produktů)
                hideAllProducts();
                
                // Zobrazit sekce s výsledky
                topProductSection.style.display = 'block';
                productsSection.style.display = 'block';
            } else {
                statusDiv.textContent = 'Nebyly nalezeny žádné produkty.';
                statusDiv.className = 'warning';
                topProductSection.style.display = 'none';
                productsSection.style.display = 'none';
            }
        }

        // --- Animace placeholderu ---
        const exampleQueries = [
            "chtel bych nejaky gril kolem 2000",
            "sklenik do 5000",
            "ratanova sedacka od 10000 do 15000",
            "malý gril kolem 2000",
            "potreboval bych zahradni nabytek kolem 10000",
            "chtěl bych pergolu",
            "chtel bych nejake stineni na zahradu"
        ];
        let queryIndex = 0;
        let charIndex = 0;
        let isDeleting = false;
        let typingTimeout;
        const typingSpeed = 100;
        const deletingSpeed = 50;
        const pauseBetweenQueries = 1500;

        function typePlaceholder() {
            const currentQuery = exampleQueries[queryIndex];
            let displayedText;

            if (isDeleting) {
                displayedText = currentQuery.substring(0, charIndex - 1);
                charIndex--;
            } else {
                displayedText = currentQuery.substring(0, charIndex + 1);
                charIndex++;
            }

            queryInput.placeholder = displayedText + '|';

            let nextTimeoutDelay = isDeleting ? deletingSpeed : typingSpeed;

            if (!isDeleting && charIndex === currentQuery.length) {
                isDeleting = true;
                nextTimeoutDelay = pauseBetweenQueries;
                queryInput.placeholder = displayedText;
            }
            else if (isDeleting && charIndex === 0) {
                isDeleting = false;
                queryIndex = (queryIndex + 1) % exampleQueries.length;
                nextTimeoutDelay = 500;
                queryInput.placeholder = '|';
            }

            if (queryInput.dataset.userTyped !== 'true') {
                 typingTimeout = setTimeout(typePlaceholder, nextTimeoutDelay);
            } else {
                queryInput.placeholder = "Zadejte dotaz...";
            }
        }

        function stopAnimationAndResetPlaceholder() {
             clearTimeout(typingTimeout);
            queryInput.dataset.userTyped = 'true';
             if (queryInput.value === '') {
                  queryInput.placeholder = ""; 
             }
        }
         
        function checkToRestartAnimation() {
            if (queryInput.value === '' && document.activeElement !== queryInput) {
                 queryInput.dataset.userTyped = 'false'; 
                queryInput.placeholder = '|';
                clearTimeout(typingTimeout);
                typingTimeout = setTimeout(typePlaceholder, 500);
             } else if (queryInput.value === '' && document.activeElement === queryInput) {
                  queryInput.placeholder = '|'; 
                clearTimeout(typingTimeout);
             }
        }

            typingTimeout = setTimeout(typePlaceholder, 1000); 
        queryInput.addEventListener('input', stopAnimationAndResetPlaceholder);
         queryInput.addEventListener('focus', stopAnimationAndResetPlaceholder);
         queryInput.addEventListener('blur', checkToRestartAnimation);
         queryInput.addEventListener('input', checkToRestartAnimation);

        document.addEventListener('DOMContentLoaded', function() {
            // Získání dotazu z URL parametrů
            const urlParams = new URLSearchParams(window.location.search);
            const queryParam = urlParams.get('query');
            const tenantParam = urlParams.get('tenant');
            
            // Pokud máme parametr dotazu v URL, nastavíme ho do vyhledávacího pole
            if (queryParam) {
                queryInput.value = queryParam;
                
                // Pokud je zadaný také tenant, nastavíme ho
                if (tenantParam && ['avenberg', 'filsonstore'].includes(tenantParam)) {
                    tenantSelect.value = tenantParam;
                }
                
                // Spustíme vyhledávání s krátkým zpožděním (aby se načetla stránka)
                setTimeout(() => {
                    performSearch();
                }, 300);
            }
            
            // Kategorie produktů a související fráze pro vyhledávání - rozšířeno
            const categoryPhrases = {
                'default': [
                    'gril', 'pergola', 'zahradní nábytek', 'bazén',
                    'zahradní technika', 'stínění', 'markýza', 'skleník'
                ]
            };
            
            // Cenové rozsahy pro fráze
            const priceRangePhrases = [
                'do 5000 Kč', 'do 10000 Kč', 'nad 20000 Kč', 'levné', 'luxusní'
            ];
            
            // Výchozí kategorie při načtení stránky
            let currentCategory = 'default';
            let activePhrase = null;
            
            // Ukládání populárních frází z výsledků vyhledávání
            let popularPhrases = new Map(); // Tato mapa bude nyní hlavním zdrojem pro dynamické fráze
            
            // Funkce pro extrahování relevantních frází z výsledků vyhledávání
            function extractPhrasesFromResults(results) {
                if (!results || results.length === 0) return new Map(); // Vrátit prázdnou mapu
                
                const phraseCounts = new Map();

                results.forEach(product => {
                    const phrases = [];
                    if (product.category) {
                        // Kategorie jako celek může být fráze
                        phrases.push(product.category.trim().toLowerCase());
                        // Jednotlivá slova z kategorie
                        product.category.split(/[\s>]+/).forEach(word => phrases.push(word.trim().toLowerCase()));
                    }
                    
                    if (product.brand) {
                        phrases.push(product.brand.trim().toLowerCase());
                    }
                    
                    if (product.name) {
                        product.name.split(/[\s.,!?;:"'()[\]{}]+/).forEach(word => { // Rozdělení podle více oddělovačů
                            const cleanWord = word.trim().toLowerCase();
                            if (cleanWord.length > 2 && !czechStopwords.includes(cleanWord) && !/^[0-9]+$/.test(cleanWord)) { // Kontrola délky, stopwords a čistě číselných řetězců
                                phrases.push(cleanWord);
                            }
                        });
                    }

                    // Zvýšit počet pro každou unikátní frázi z tohoto produktu
                    new Set(phrases).forEach(phrase => {
                        if (phrase) { // Ujistit se, že fráze není prázdná
                             phraseCounts.set(phrase, (phraseCounts.get(phrase) || 0) + 1);
                        }
                    });
                });
                
                return phraseCounts; // Vrátit mapu frází a jejich počtů
            }
            
            // Funkce pro aktualizaci frází podle kategorie
            function updatePhrases(category, fromResults = null) { // Parametr 'category' se už moc nepoužije pro obsah
                const phraseContainer = document.getElementById('phraseFilters');
                if (!phraseContainer) return;
                
                phraseContainer.innerHTML = ''; // Vyčištění kontejneru
                
                let phrasesToShow = [];
                
                if (fromResults && fromResults.length > 0) {
                    const extractedPhraseCounts = extractPhrasesFromResults(fromResults);
                    
                    // Seřadit fráze podle počtu výskytů (sestupně) a vzít top N (např. 8)
                    const sortedPhrases = Array.from(extractedPhraseCounts.entries())
                        .sort((a, b) => b[1] - a[1])
                        .slice(0, 8) // Zobrazit top 8 frází
                        .map(entry => entry[0]);
                    
                    phrasesToShow.push(...sortedPhrases);
                }
                
                // Přidat statické cenové fráze (vždy, nebo jen pokud jsou nějaké dynamické?)
                // Prozatím je přidáme vždy, pokud nejsou už obsaženy (pro jistotu)
                priceRangePhrases.forEach(prp => {
                    if (!phrasesToShow.includes(prp.toLowerCase())) {
                        phrasesToShow.push(prp);
                    }
                });

                // Odstranit duplicity (pokud by nějaké vznikly) a prázdné hodnoty
                phrasesToShow = [...new Set(phrasesToShow.filter(phrase => phrase && phrase.trim()))];
                
                // Vytvoření a přidání tagů pro fráze
                phrasesToShow.forEach((phrase, index) => {
                    // Zpoždění pro animaci ponecháme, pokud chceme
                    setTimeout(() => {
                        addPhraseTag(phrase, phraseContainer, true);
                    }, index * 50);
                });
            }
            
            // Pomocná funkce pro vytvoření a přidání phrase tagu
            function addPhraseTag(phrase, container, animate = false) {
                const phraseTag = document.createElement('div');
                phraseTag.className = 'phrase-tag';
                if (animate) {
                    phraseTag.classList.add('animate-in');
                }
                if (activePhrase === phrase) {
                    phraseTag.classList.add('active');
                }
                phraseTag.setAttribute('data-phrase', phrase);
                phraseTag.textContent = phrase;
                
                // Přidání event listeneru pro kliknutí na frázi
                phraseTag.addEventListener('click', function() {
                    // Odstranit aktivní třídu ze všech frází
                    document.querySelectorAll('.phrase-tag').forEach(tag => {
                        tag.classList.remove('active');
                    });
                    
                    // Nastavit aktivní třídu pro tuto frázi
                    phraseTag.classList.add('active');
                    activePhrase = phrase;
                    
                    // Nastavit hodnotu vyhledávacího pole
                    document.getElementById('queryInput').value = phrase;
                    document.getElementById('searchButton').click();
                });
                
                container.appendChild(phraseTag);
            }
            
            // Detekce fráze z vyhledávacího dotazu
            function detectPhraseFromQuery(query) {
                query = query.toLowerCase().trim();
                
                // PŮVODNÍ: Procházet všechny kategorie a jejich fráze
                // PŮVODNÍ: for (const category in categoryPhrases) {
                // PŮVODNÍ:     for (const phrase of categoryPhrases[category]) {
                // PŮVODNÍ:         if (query === phrase.toLowerCase()) {
                // PŮVODNÍ:             return phrase;
                // PŮVODNÍ:         }
                // PŮVODNÍ:     }
                // PŮVODNÍ: }
                // Nyní se spoléháme na to, že aktivní fráze se nastaví dynamicky
                
                // Zkontrolovat cenové fráze
                for (const category in categoryPhrases) {
                    for (const phrase of categoryPhrases[category]) {
                        if (query === phrase.toLowerCase()) {
                            return phrase;
                        }
                    }
                }
                
                // Zkontrolovat cenové fráze
                for (const phrase of priceRangePhrases) {
                    if (query === phrase.toLowerCase()) {
                        return phrase;
                    }
                }
                
                return null;
            }
            
            // Nastavení frází podle výchozí kategorie
            updatePhrases(currentCategory);
            
            // PŮVODNÍ: updatePhrases(currentCategory); // Voláno na začátku
            // Nyní počkáme na první výsledky vyhledávání, nebo můžeme zobrazit jen cenové
            updatePhrases(null, null); // Na začátku zobrazí jen cenové fráze, pokud jsou definovány globálně
            
            // Přepsání event listeneru pro vyhledávání, aby aktualizoval fráze
            async function handleSearch() {
                const query = document.getElementById('queryInput').value;
                const tenant = document.getElementById('tenantSelect').value;
                
                if (query) {
                    try {
                        const response = await fetch(`/search/${tenant}?query=${encodeURIComponent(query)}`);
                        const data = await response.json();
                        
                        // Po získání výsledků detekovat kategorii
                        const detectedCategory = detectCategoryFromResults(data);
                        
                        // Aktualizovat fráze při každém vyhledávání z reálných výsledků
                        currentCategory = detectedCategory;
                        
                        // Detekovat, zda aktuální dotaz odpovídá nějaké frázi
                        activePhrase = query;
                        
                        // Aktualizovat zobrazení frází z reálných výsledků
                        updatePhrases(null, data); // První parametr (kategorie) už není pro obsah relevantní
                        
                        // Zobrazit výsledky vyhledávání
                        processSearchResults(data);
                    } catch (error) {
                        console.error('Chyba při vyhledávání:', error);
                    }
                }
            }
            
            // Přidání event listeneru pro provedení vyhledávání
            document.getElementById('searchButton').addEventListener('click', handleSearch);
            
            // Sledování změn v dotazovém poli
            document.getElementById('queryInput').addEventListener('input', function() {
                const query = this.value.trim().toLowerCase();
                
                // Kontrola, zda dotaz odpovídá některé z frází
                const allPhrases = document.querySelectorAll('.phrase-tag');
                allPhrases.forEach(phraseTag => {
                    const phraseText = phraseTag.getAttribute('data-phrase').toLowerCase();
                    
                    if (phraseText === query) {
                        // Nastavit aktivní třídu
                        allPhrases.forEach(tag => tag.classList.remove('active'));
                        phraseTag.classList.add('active');
                        activePhrase = phraseTag.getAttribute('data-phrase');
                    }
                });
            });

            // Inicializace rozbalovacích filtrů pro mobilní zařízení
            document.querySelectorAll('.filter-section h3').forEach(heading => {
                // Obalit obsah filtru do div pro animaci rozbalování
                const content = document.createElement('div');
                content.className = 'filter-content';
                
                // Přesunout všechny elementy mezi nadpisem a dalším nadpisem do nového divu
                let nextElement = heading.nextElementSibling;
                while (nextElement && nextElement.tagName !== 'H3') {
                    const temp = nextElement.nextElementSibling;
                    content.appendChild(nextElement);
                    nextElement = temp;
                }
                
                heading.parentNode.insertBefore(content, heading.nextElementSibling);
                
                // Přidat event listener pro přepínání rozbalení/zabalení
                heading.addEventListener('click', function() {
                    this.parentNode.classList.toggle('expanded');
                });
            });
            
            // Nastavit defaultní rozbalení první sekce filtrů
            if (document.querySelector('.filter-section')) {
                document.querySelector('.filter-section').classList.add('expanded');
            }

            // Synchronizace URL s vyhledávacím dotazem
            function updateUrlWithSearchParams() {
                const query = queryInput.value.trim();
                const tenant = tenantSelect.value;
                
                if (query) {
                    // Vytvořit novou URL s parametry
                    const url = new URL(window.location.href);
                    url.searchParams.set('query', query);
                    url.searchParams.set('tenant', tenant);
                    
                    // Aktualizovat URL bez přesměrování
                    window.history.replaceState({}, '', url);
                }
            }
            
            // Přidat aktualizaci URL při vyhledávání
            const originalPerformSearch = performSearch;
            performSearch = function() {
                originalPerformSearch.apply(this, arguments);
                updateUrlWithSearchParams();
            };

            // Přidat event listener pro mobilní zobrazení filtrů
            if (mobileFilterToggle) {
                const sidebar = document.querySelector('.sidebar');
                mobileFilterToggle.addEventListener('click', function() {
                    sidebar.classList.toggle('visible');
                    this.textContent = sidebar.classList.contains('visible') ? 'Skrýt filtry' : 'Zobrazit filtry';
                });
            }
        });
    </script>
</body>
</html> 


