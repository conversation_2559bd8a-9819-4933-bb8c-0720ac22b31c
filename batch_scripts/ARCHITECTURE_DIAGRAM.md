# 🏗️ Architektura Systému - Komplementární Produkty

## 🔧 Komponenty a <PERSON>tov<PERSON>

```mermaid
graph TB
    subgraph "Input Layer"
        CLI[CLI Arguments]
        Config[Tenant Config YAML]
        Env[Environment Variables]
    end
    
    subgraph "Processing Engine"
        Main[main_optimized]
        Sem[Semaphore Control]
        Batch[Batch Processor]
        Cache[Cache Manager]
    end
    
    subgraph "Data Sources"
        QP[Qdrant Products Collection]
        QC[Qdrant Cache Collection]
        Neo[Neo4j Category Graph]
    end
    
    subgraph "AI Services"
        Gemini[Gemini AI API]
        Embedding[OpenAI Embeddings]
    end
    
    subgraph "Output"
        Results[Computed Results]
        Metrics[Performance Metrics]
        Logs[Application Logs]
    end
    
    CLI --> Main
    Config --> Main
    Env --> Main
    
    Main --> Cache
    Main --> Batch
    Cache --> QC
    
    Batch --> Sem
    Sem --> QP
    Sem --> Neo
    Sem --> Gemini
    
    QP --> Embedding
    
    Gemini --> Results
    Results --> QC
    Results --> Metrics
    Results --> Logs
    
    style Main fill:#e3f2fd
    style Gemini fill:#fff3e0
    style QP fill:#f1f8e9
    style QC fill:#fce4ec
    style Neo fill:#f3e5f5
```

---

## 🔄 Detailní <PERSON> Model

### **1. Product Entity (Qdrant)**
```json
{
  "id": "qdrant_uuid",
  "vector": {
    "combined": [0.123, 0.456, ...] // 1536 dimensions
  },
  "payload": {
    "product_id": "12345",
    "name": "Product Name",
    "description": "Product description...",
    "category": "Electronics/Smartphones",
    "brand": "Apple",
    "price": 25999.0,
    "image_url": "https://...",
    "product_url": "https://...",
    "availability": "in stock"
  }
}
```

### **2. Category Relationships (Neo4j)**
```cypher
// Uzel kategorie
(:Category {
  name: "Electronics/Smartphones",
  level: 2,
  tenant_id: "filsonstore"
})

// Vztah komplementarity
(:Category)-[:COMPLEMENTS {
  weight: 0.8,
  confidence: 0.9,
  created_at: "2024-01-01T00:00:00Z"
}]->(:Category)
```

### **3. Cache Entry (Qdrant)**
```json
{
  "id": "product_qdrant_uuid",
  "vector": {}, // Empty for cache
  "payload": {
    "main_product_id": "12345",
    "complementary_ids": ["67890", "11111", "22222"],
    "scores": [0.95, 0.87, 0.79],
    "computed_at": 1704067200.0,
    "gemini_model": "gemini-1.5-pro",
    "total_candidates": 45,
    "categories_used": ["Accessories", "Cases"]
  }
}
```

---

## ⚡ Performance Optimalizace

### **1. Batch Processing Strategy**
```mermaid
gantt
    title Batch Processing Timeline
    dateFormat X
    axisFormat %s
    
    section Single Product Mode
    Product 1    :0, 2
    Product 2    :2, 4
    Product 3    :4, 6
    Product 4    :6, 8
    Product 5    :8, 10
    
    section Batch Mode (5 products)
    Batch Prep   :0, 1
    Gemini Call  :1, 3
    Save Results :3, 4
```

### **2. Memory Management**
```python
# Streaming vs Batch Loading
def memory_comparison():
    # Traditional: Load all IDs
    all_ids = load_all_product_ids()  # 50MB for 100k products
    
    # Streaming: Process in chunks
    for chunk in stream_product_ids(chunk_size=1000):  # 0.5MB per chunk
        process_chunk(chunk)
        del chunk  # Immediate cleanup
```

### **3. Concurrency Patterns**
```python
# Optimized Semaphore Usage
semaphore = asyncio.Semaphore(20)  # API rate limit

async def controlled_processing():
    async with semaphore:
        # Max 20 concurrent Gemini calls
        # Each call processes 5 products = 100 products/batch
        pass
```

---

## 🎯 Scaling Considerations

### **Horizontal Scaling**
```mermaid
graph LR
    subgraph "Instance 1"
        P1[Products 1-1000]
        G1[Gemini Client 1]
    end
    
    subgraph "Instance 2"  
        P2[Products 1001-2000]
        G2[Gemini Client 2]
    end
    
    subgraph "Shared Resources"
        QDB[(Qdrant Cluster)]
        NDB[(Neo4j Cluster)]
        Cache[(Shared Cache)]
    end
    
    P1 --> QDB
    P2 --> QDB
    P1 --> NDB
    P2 --> NDB
    G1 --> Cache
    G2 --> Cache
```

### **Resource Requirements**

| Component | CPU | Memory | Network | Storage |
|-----------|-----|--------|---------|---------|
| **Processing** | 4-8 cores | 8-16 GB | - | - |
| **Qdrant** | 2-4 cores | 16-32 GB | 1 Gbps | SSD |
| **Neo4j** | 2-4 cores | 8-16 GB | 1 Gbps | SSD |
| **Gemini API** | - | - | Stable | - |

---

## 🔍 Monitoring & Observability

### **Key Metrics**
```python
class SystemMetrics:
    # Throughput
    products_per_second: float
    batch_efficiency_ratio: float  # batch_time / (single_time * batch_size)
    
    # Quality
    cache_hit_rate: float  # Should be >90% on reruns
    error_rate: float      # Should be <1%
    
    # Resources
    memory_usage_mb: float
    gemini_api_quota_used: float
    
    # Latency
    avg_gemini_response_time: float
    avg_qdrant_search_time: float
    avg_neo4j_query_time: float
```

### **Alerting Thresholds**
- Cache hit rate < 80% (možný problém s cache TTL)
- Error rate > 5% (API problémy nebo data corruption)
- Products per second < 2 (performance degradace)
- Gemini response time > 10s (API throttling)

---

## 🚀 Deployment Pipeline

```mermaid
graph TD
    A[Git Push] --> B[CI/CD Trigger]
    B --> C[Run Tests]
    C --> D{Tests Pass?}
    D -->|No| E[Notify Developer]
    D -->|Yes| F[Build Container]
    F --> G[Deploy to Staging]
    G --> H[Integration Tests]
    H --> I{All Good?}
    I -->|No| J[Rollback]
    I -->|Yes| K[Deploy to Production]
    K --> L[Health Check]
    L --> M[Monitor Metrics]
```

### **Configuration Management**
```yaml
# docker-compose.yml
services:
  compute-complementary:
    image: gallitec/compute-complementary:latest
    environment:
      - QDRANT_URL=http://qdrant:6333
      - NEO4J_URI=bolt://neo4j:7687
      - GEMINI_API_KEY=${GEMINI_API_KEY}
    volumes:
      - ./config:/app/config
      - ./logs:/app/logs
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '4'
          memory: 8G
```

Tento technický workflow poskytuje kompletní pohled na celou architekturu systému! 🏗️ 