import os
import sys
import asyncio
import logging
from dotenv import load_dotenv
import time

# Pu0159idu00e1nu00ed cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Nastavenu00ed logovu00e1nu00ed
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_categories")

# Nau010dtenu00ed .env souboru
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# Import potu0159ebnu00fdch funkcu00ed
from batch_scripts.compute_complementary import (
    get_neo4j_driver,
    get_complementary_categories_cached,
    preload_neo4j_cache,
    NEO4J_CACHE
)

async def test_complementary_categories(tenant_id: str, category: str):
    """
    Testuje nau010du00edtu00e1nu00ed komplementu00e1rnu00edch kategoriu00ed z Neo4j cache.
    
    Args:
        tenant_id: ID tenanta
        category: Nu00e1zev kategorie k testovu00e1nu00ed
    """
    logger.info(f"Zau010du00ednu00e1m test komplementu00e1rnu00edch kategoriu00ed pro kategorii '{category}'")
    
    # Inicializace Neo4j driveru
    neo4j_driver = await get_neo4j_driver()
    
    try:
        # Preload cache - tento krok obvykle probu00edhu00e1 pu0159i startu aplikace
        logger.info("Nau010du00edtu00e1m cache pro Neo4j...")
        start_time = time.time()
        await preload_neo4j_cache(neo4j_driver, tenant_id)
        logger.info(f"Cache nau010dtena za {time.time() - start_time:.2f} sekund")
        
        # Zobrazenu00ed obsahu cache
        logger.info(f"Pou010det zu00e1znamu016f v NEO4J_CACHE: {len(NEO4J_CACHE)}")
        
        # Zu00edsku00e1nu00ed komplementu00e1rnu00edch kategoriu00ed
        logger.info(f"Hledu00e1m komplementu00e1rnu00ed kategorie pro: {category}")
        categories_start = time.time()
        complementary_categories = await get_complementary_categories_cached(category)
        
        # Vu00fdpis vu00fdsledku016f
        if complementary_categories:
            logger.info(f"Nalezeno {len(complementary_categories)} komplementu00e1rnu00edch kategoriu00ed za {time.time() - categories_start:.4f} sekund")
            logger.info("Komplementu00e1rnu00ed kategorie:")
            for i, (cat_name, weight) in enumerate(complementary_categories):
                logger.info(f"  {i+1}. {cat_name} (vu00e1ha: {weight:.2f})")
        else:
            logger.warning(f"Nebyly nalezeny u017eu00e1dnu00e9 komplementu00e1rnu00ed kategorie pro '{category}'")
            
            # Zkusme naju00edt nu011bjaku00e9 kategorie v cache
            cache_keys = list(NEO4J_CACHE.keys())
            if cache_keys:
                logger.info(f"Pu0159u00edklady kategoriu00ed v cache: {cache_keys[:5]}")
                
                # Pokus o u010du00e1steu010dnou shodu (podobnu011b jako v pu016fvodnu00ed implementaci)
                for cache_key in cache_keys:
                    if category.lower() in cache_key.lower() or cache_key.lower() in category.lower():
                        logger.info(f"Nalezena potenciu00e1lnu011b souviseju00edcu00ed kategorie v cache: {cache_key}")
    except Exception as e:
        logger.error(f"Dou0161lo k chybu011b pu0159i testovu00e1nu00ed: {e}")
    finally:
        # Uzavu0159enu00ed driveru
        await neo4j_driver.close()

if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test nau010du00edtu00e1nu00ed komplementu00e1rnu00edch kategoriu00ed")
    parser.add_argument("--tenant", type=str, default="filsonstore", help="Tenant ID")
    parser.add_argument("--category", type=str, required=True, help="Nu00e1zev kategorie k testovu00e1nu00ed")
    
    args = parser.parse_args()
    
    asyncio.run(test_complementary_categories(args.tenant, args.category))
