#!/usr/bin/env python3
"""
Skript pro opravu Neo4j vztahů - logičtější cross-sell doporučení
"""

from neo4j import GraphDatabase
import time

def update_filsonstore_relationships():
    """Aktualizuje Neo4j vztahy pro lepší cross-sell logiku."""
    
    # Nové logické vztahy pro automotive
    improved_relationships = {
        "Autodoplňky > Povinná a doporučená výbava": [
            ("Autodoplňky > Dílna a garáž", 0.7, "Nářadí pro nouzové opravy"),
            ("Autodoplňky > Interiér vozidla", 0.2, "Bezpečnostní prvky v interiéru"),
            ("Autodoplňky > Autokosmetika", 0.1, "Údržba výstražných prvků"),
            # Odstraněno: Autopotahy, Autokoberce - nelogické pro tažná lana
        ],
        
        "Autodoplňky > Oleje a maziva": [
            ("Autodoplň<PERSON> > D<PERSON>ln<PERSON> a gará<PERSON>", 0.9, "Nástroje pro výměnu olejů"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čistící prostředky pro údržbu"),
            # Zvýšena váha pro Dílna a garáž - logičtější
        ],
        
        "Autodoplňky > Dílna a garáž": [
            ("Autodoplňky > Oleje a maziva", 0.6, "Provozní kapaliny pro servis"),
            ("Autodoplňky > Povinná a doporučená výbava", 0.3, "Bezpečnostní prvky při opravách"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čistící prostředky"),
        ],
        
        "Autodoplňky > Autopotahy": [
            ("Autodoplňky > Autokoberce", 0.7, "Kompletní ochrana interiéru"),
            ("Autodoplňky > Interiér vozidla", 0.2, "Doplňky interiéru"),
            ("Autodoplňky > Autokosmetika", 0.1, "Péče o textilie"),
        ],
        
        "Autodoplňky > Autokoberce": [
            ("Autodoplňky > Autopotahy", 0.7, "Kompletní ochrana interiéru"),
            ("Autodoplňky > Vany do zavazadlového prostoru", 0.2, "Ochrana celého vozu"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čistící prostředky"),
        ]
    }
    
    print("=== NÁVRH ZLEPŠENÝCH VZTAHŮ ===")
    for source, targets in improved_relationships.items():
        print(f"\n📂 {source}:")
        total_weight = sum(weight for _, weight, _ in targets)
        
        for target, weight, reason in targets:
            percentage = (weight / total_weight) * 100
            candidates = max(1, int(round(80 * weight / total_weight)))
            print(f"   → {target}")
            print(f"     Váha: {weight} ({percentage:.1f}% = {candidates} kandidátů)")
            print(f"     Důvod: {reason}")
    
    print("\n=== KLÍČOVÉ ZMĚNY ===")
    print("✅ Povinná výbava → Autopotahy: ODSTRANĚNO (nelogické)")
    print("✅ Povinná výbava → Dílna: zvýšeno na 0.7 (nářadí pro nouzové opravy)")
    print("✅ Oleje → Dílna: zvýšeno na 0.9 (nástroje pro výměnu)")
    print("✅ Autopotahy/koberce: seskupeny logicky (ochrana interiéru)")
    
    return improved_relationships

def apply_to_neo4j():
    """Aplikuje změny do Neo4j databáze."""
    try:
        driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', 'gallitec'))
        
        with driver.session() as session:
            print("\n=== APLIKACE DO NEO4J ===")
            
            # Smazání stávajících vztahů
            session.run("MATCH ()-[r:RELATED_TO]->() DELETE r")
            print("✅ Stávající vztahy smazány")
            
            # Přidání nových vztahů
            relationships = update_filsonstore_relationships()
            
            for source, targets in relationships.items():
                for target, weight, reason in targets:
                    session.run("""
                        MERGE (s:Category_filsonstore {name: $source})
                        MERGE (t:Category_filsonstore {name: $target})
                        CREATE (s)-[:RELATED_TO {weight: $weight, reason: $reason}]->(t)
                    """, source=source, target=target, weight=weight, reason=reason)
            
            print("✅ Nové vztahy vytvořeny")
            
        driver.close()
        print("✅ Neo4j aktualizováno!")
        
    except Exception as e:
        print(f"❌ Chyba při aktualizaci Neo4j: {e}")
        print("💡 Spusť později když skončí rate limit")

if __name__ == "__main__":
    print("🔧 ANALÝZA A NÁVRH ZLEPŠENÍ NEO4J VZTAHŮ")
    update_filsonstore_relationships()
    
    choice = input("\nChceš aplikovat změny do Neo4j? (y/n): ")
    if choice.lower() == 'y':
        apply_to_neo4j()
    else:
        print("💾 Změny nejsou aplikovány, pouze zobrazeny")