#!/bin/bash

# 🚀 Ultra optimalizace compute_complementary.py - Quick Start Script
# Autor: AI Assistant
# Popis: <PERSON><PERSON><PERSON><PERSON><PERSON> script pro spuštění a testování ultra optimalizací

set -e  # Exit on any error

# Barvy pro lepší čitelnost
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# Funkce pro barevný výpis
echo_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

echo_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

echo_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

echo_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo_ultra() {
    echo -e "${PURPLE}🚀 $1${NC}"
}

# Banner
echo -e "${CYAN}"
echo "🚀🚀🚀 ULTRA OPTIMALIZACE COMPUTE_COMPLEMENTARY.PY 🚀🚀🚀"
echo "========================================================="
echo -e "${NC}"

# Kontrola, že jsme ve správném adresáři
if [ ! -f "compute_complementary.py" ]; then
    echo_error "Compute_complementary.py nenalezen! Spusťte z batch_scripts adresáře."
    exit 1
fi

# Default values
TENANT="filsonstore"
MODE="test"
BATCH_SIZE=15
CONCURRENCY=25
LIMIT=""

# Parsing argumentů
while [[ $# -gt 0 ]]; do
    case $1 in
        --tenant)
            TENANT="$2"
            shift 2
            ;;
        --mode)
            MODE="$2"
            shift 2
            ;;
        --batch-size)
            BATCH_SIZE="$2"
            shift 2
            ;;
        --concurrency)
            CONCURRENCY="$2"
            shift 2
            ;;
        --limit)
            LIMIT="$2"
            shift 2
            ;;
        --help|-h)
            echo "Použití: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --tenant TENANT        Tenant ID (default: filsonstore)"
            echo "  --mode MODE           Režim: test|ultra|compare|full (default: test)"
            echo "  --batch-size N        Batch size pro Gemini (default: 15)"
            echo "  --concurrency N       Concurrency level (default: 25)"
            echo "  --limit N             Limit produktů k zpracování"
            echo "  --help               Zobrazí tuto nápovědu"
            echo ""
            echo "Režimy:"
            echo "  test      - Rychlý test na vzorku 20 produktů"
            echo "  ultra     - Ultra optimalizovaná verze"
            echo "  compare   - Porovnání ultra vs standard optimalizace"
            echo "  full      - Zpracování všech produktů (POZOR: dlouhé!)"
            exit 0
            ;;
        *)
            echo_error "Neznámý parametr: $1"
            echo "Použijte --help pro nápovědu"
            exit 1
            ;;
    esac
done

echo_info "Konfigurace:"
echo "  Tenant: $TENANT"
echo "  Mode: $MODE"
echo "  Batch size: $BATCH_SIZE"
echo "  Concurrency: $CONCURRENCY"
if [ -n "$LIMIT" ]; then
    echo "  Limit: $LIMIT"
fi
echo ""

# Hlavní switch podle režimu
case $MODE in
    "test")
        echo_ultra "SPOUŠTÍM RYCHLÝ TEST ULTRA OPTIMALIZACÍ..."
        echo_info "Test porovná výkon ultra vs. standard optimalizace na vzorku 20 produktů"
        
        if [ -f "test_ultra_optimizations.py" ]; then
            python test_ultra_optimizations.py
        else
            echo_warning "test_ultra_optimizations.py nenalezen, spouštím ruční test..."
            LIMIT_ARG=""
            if [ -n "$LIMIT" ]; then
                LIMIT_ARG="--limit $LIMIT"
            else
                LIMIT_ARG="--limit 20"
            fi
            
            python compute_complementary.py \
                --tenant "$TENANT" \
                --ultra \
                --batch-size "$BATCH_SIZE" \
                --ultra-concurrency "$CONCURRENCY" \
                $LIMIT_ARG
        fi
        ;;
        
    "ultra")
        echo_ultra "SPOUŠTÍM ULTRA OPTIMALIZOVANOU VERZI..."
        echo_warning "POZOR: Toto může trvat hodiny pro velké množství produktů!"
        
        # Sestavení příkazu
        CMD="python compute_complementary.py --tenant $TENANT --ultra --batch-size $BATCH_SIZE --ultra-concurrency $CONCURRENCY"
        
        if [ -n "$LIMIT" ]; then
            CMD="$CMD --limit $LIMIT"
        fi
        
        echo_info "Spouštím: $CMD"
        echo ""
        
        # Spuštění s možností přerušení
        echo_info "Pro přerušení použijte Ctrl+C"
        sleep 2
        
        eval $CMD
        ;;
        
    "compare")
        echo_info "POROVNÁNÍ: Ultra vs. Standard optimalizace"
        
        LIMIT_ARG=""
        if [ -n "$LIMIT" ]; then
            LIMIT_ARG="--limit $LIMIT"
        else
            LIMIT_ARG="--limit 50"  # Default pro porovnání
        fi
        
        echo_ultra "1/2 Spouštím ULTRA optimalizaci..."
        START_TIME=$(date +%s)
        python compute_complementary.py \
            --tenant "$TENANT" \
            --ultra \
            --batch-size "$BATCH_SIZE" \
            --ultra-concurrency "$CONCURRENCY" \
            $LIMIT_ARG
        ULTRA_TIME=$(( $(date +%s) - START_TIME ))
        
        echo ""
        echo_info "2/2 Spouštím STANDARD optimalizaci..."
        START_TIME=$(date +%s)
        python compute_complementary.py \
            --tenant "$TENANT" \
            --optimized \
            --batch-size 7 \
            --concurrency 15 \
            $LIMIT_ARG
        STANDARD_TIME=$(( $(date +%s) - START_TIME ))
        
        echo ""
        echo_success "VÝSLEDKY POROVNÁNÍ:"
        echo "  Ultra čas: ${ULTRA_TIME}s"
        echo "  Standard čas: ${STANDARD_TIME}s"
        
        if [ $ULTRA_TIME -gt 0 ]; then
            SPEEDUP=$(echo "scale=2; $STANDARD_TIME / $ULTRA_TIME" | bc -l 2>/dev/null || echo "N/A")
            echo "  Zrychlení: ${SPEEDUP}x"
        fi
        ;;
        
    "full")
        echo_warning "POZOR: SPOUŠTÍM ZPRACOVÁNÍ VŠECH PRODUKTŮ!"
        echo_warning "Toto může trvat několik hodin!"
        echo ""
        echo_info "Pro pokračování stiskněte ENTER, pro zrušení Ctrl+C"
        read -r
        
        echo_ultra "Spouštím úplné zpracování s ultra optimalizacemi..."
        
        python compute_complementary.py \
            --tenant "$TENANT" \
            --ultra \
            --batch-size "$BATCH_SIZE" \
            --ultra-concurrency "$CONCURRENCY" \
            --streaming
        ;;
        
    *)
        echo_error "Neznámý režim: $MODE"
        echo "Dostupné režimy: test, ultra, compare, full"
        echo "Použijte --help pro nápovědu"
        exit 1
        ;;
esac

# Závěrečné informace
echo ""
echo_success "Script dokončen!"
echo_info "Pro sledování logů použijte:"
echo "  tail -f logs/compute_complementary.log"
echo ""
echo_info "Pro monitoring výkonu:"
echo "  htop"
echo "  curl http://localhost:6333/metrics"
echo "" 