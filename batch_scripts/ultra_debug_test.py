#!/usr/bin/env python3
"""
Ultra detailní debug test pro compute_complementary ultra verzi
"""
import asyncio
import logging
import os
import sys
import traceback

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("ultra_debug")

async def debug_ultra_process_single():
    """Debug ultra zpracování jednoho produktu krok za krokem."""
    
    logger.info("🔬 ULTRA DETAILNÍ DEBUG - JEDEN PRODUKT")
    
    try:
        # Import funkcí
        from compute_complementary import (
            get_custom_qdrant_async_client,
            get_gemini_client,
            get_neo4j_driver,
            compute_and_save_complementary_ultra_optimized,
            compute_complementary_ultra_fast_gemini,
            save_complementary_results_batch,
            preload_neo4j_cache
        )
        
        # 1. Inicializace klientů
        logger.info("🔧 Krok 1: Inicializace klientů...")
        
        try:
            qdrant_client = get_custom_qdrant_async_client()
            logger.info("✅ Qdrant klient OK")
        except Exception as e:
            logger.error(f"❌ Qdrant klient FAIL: {e}")
            return False
        
        try:
            gemini_client = get_gemini_client()
            logger.info("✅ Gemini klient OK")
        except Exception as e:
            logger.error(f"❌ Gemini klient FAIL: {e}")
            return False
        
        try:
            neo4j_driver = await get_neo4j_driver()
            logger.info("✅ Neo4j driver OK")
        except Exception as e:
            logger.error(f"❌ Neo4j driver FAIL: {e}")
            return False
        
        # 2. Pre-load Neo4j cache
        logger.info("🔧 Krok 2: Pre-load Neo4j cache...")
        try:
            await preload_neo4j_cache(neo4j_driver, "filsonstore")
            logger.info("✅ Neo4j cache OK")
        except Exception as e:
            logger.error(f"❌ Neo4j cache FAIL: {e}")
            return False
        
        # 3. Získání testovacího produktu
        logger.info("🔧 Krok 3: Získání testovacího produktu...")
        try:
            products_collection = "real_products_filsonstore"
            scroll_result, _ = await qdrant_client.scroll(
                collection_name=products_collection,
                limit=1,
                with_payload=["product_id", "category", "name"],
                with_vectors=False
            )
            
            if not scroll_result:
                logger.error("❌ Žádné produkty v kolekci")
                return False
                
            test_product_id = str(scroll_result[0].payload.get("product_id"))
            logger.info(f"✅ Test produkt: {test_product_id}")
            
        except Exception as e:
            logger.error(f"❌ Získání produktu FAIL: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
        
        # 4. Zpracování produktu - ultra optimalized
        logger.info("🔧 Krok 4: Ultra optimalizované zpracování...")
        try:
            batch_request = await compute_and_save_complementary_ultra_optimized(
                tenant_id="filsonstore",
                product_id=test_product_id,
                qdrant_client=qdrant_client,
                gemini_client=gemini_client,
                neo4j_driver=neo4j_driver
            )
            
            if batch_request:
                logger.info(f"✅ BatchProcessingRequest vytvořen: {len(batch_request.candidates)} kandidátů")
            else:
                logger.warning("⚠️ BatchProcessingRequest je None")
                return False
                
        except Exception as e:
            logger.error(f"❌ Ultra zpracování FAIL: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
        
        # 5. Gemini zpracování
        logger.info("🔧 Krok 5: Ultra-fast Gemini zpracování...")
        try:
            gemini_results = await compute_complementary_ultra_fast_gemini(
                gemini_client=gemini_client,
                batch_requests=[batch_request],
                rerank_limit=5
            )
            
            if gemini_results:
                logger.info(f"✅ Gemini výsledky: {len(gemini_results)} produktů")
                for prod_id, candidates in gemini_results.items():
                    logger.info(f"  {prod_id}: {len(candidates)} komplementárních")
            else:
                logger.warning("⚠️ Gemini nevrátil žádné výsledky")
                return False
                
        except Exception as e:
            logger.error(f"❌ Gemini zpracování FAIL: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
        
        # 6. Uložení výsledků
        logger.info("🔧 Krok 6: Uložení výsledků...")
        try:
            saved_count = await save_complementary_results_batch(
                qdrant_client=qdrant_client,
                tenant_id="filsonstore",
                results=gemini_results,
                gemini_model_name="gemini-2.0-flash-debug"
            )
            
            if saved_count > 0:
                logger.info(f"✅ Uloženo {saved_count} výsledků")
            else:
                logger.warning("⚠️ Žádné výsledky nebyly uloženy")
                return False
                
        except Exception as e:
            logger.error(f"❌ Uložení FAIL: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            return False
        
        # Cleanup
        if neo4j_driver:
            await neo4j_driver.close()
        
        logger.info("🎉 ÚPLNÝ ULTRA DEBUG ÚSPĚŠNÝ!")
        return True
        
    except Exception as e:
        logger.error(f"❌ KRITICKÁ CHYBA v ultra debug: {e}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

async def debug_ultra_timeout_issues():
    """Debug timeout problémů v ultra zpracování."""
    
    logger.info("⏱️ DEBUG timeout problémů...")
    
    try:
        from compute_complementary import get_custom_qdrant_async_client
        
        qdrant_client = get_custom_qdrant_async_client()
        
        # Test různých timeoutů
        timeouts_to_test = [5, 10, 20, 30]
        
        for timeout in timeouts_to_test:
            logger.info(f"🧪 Testuji timeout {timeout}s...")
            
            try:
                # Test jednoduchého scroll s timeoutem
                start_time = asyncio.get_event_loop().time()
                
                task = qdrant_client.scroll(
                    collection_name="real_products_filsonstore",
                    limit=5,
                    with_payload=["product_id"],
                    with_vectors=False
                )
                
                result = await asyncio.wait_for(task, timeout=timeout)
                
                end_time = asyncio.get_event_loop().time()
                duration = end_time - start_time
                
                logger.info(f"✅ Timeout {timeout}s OK - trvalo {duration:.2f}s")
                
            except asyncio.TimeoutError:
                logger.warning(f"⚠️ Timeout {timeout}s - operace byla příliš pomalá")
            except Exception as e:
                logger.error(f"❌ Chyba při timeout testu {timeout}s: {e}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba v timeout debug: {e}")
        return False

async def main():
    """Spustí všechny ultra debug testy."""
    logger.info("🚀 SPOUŠTÍM ULTRA DETAILNÍ DEBUG 🚀")
    
    # Test 1: Kompletní ultra zpracování
    logger.info("=" * 60)
    success1 = await debug_ultra_process_single()
    
    # Test 2: Timeout issues
    logger.info("=" * 60)
    success2 = await debug_ultra_timeout_issues()
    
    # Shrnutí
    logger.info("=" * 60)
    logger.info("📊 SHRNUTÍ ULTRA DEBUG:")
    logger.info(f"  Kompletní zpracování: {'✅ PASS' if success1 else '❌ FAIL'}")
    logger.info(f"  Timeout testy: {'✅ PASS' if success2 else '❌ FAIL'}")
    
    overall_success = success1 and success2
    
    if overall_success:
        logger.info("🎉 ULTRA DEBUG ÚSPĚŠNÝ - problém je identifikován!")
    else:
        logger.warning("⚠️ ULTRA DEBUG odhalil problémy")
    
    return overall_success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n🎉 ULTRA DEBUG ÚSPĚŠNÝ!")
        else:
            print("\n❌ ULTRA DEBUG odhalil problémy")
    except KeyboardInterrupt:
        print("\nDebug přerušen uživatelem")
    except Exception as e:
        print(f"\n❌ Kritická chyba: {e}")
        print(f"Traceback: {traceback.format_exc()}") 