#!/usr/bin/env python3
"""
Kontrola všech Neo4j vztahů pro filsonstore
"""

from neo4j import GraphDatabase
import time

def check_all_relationships():
    """Zkontroluje všechny vztahy v Neo4j."""
    
    try:
        # Zkus<PERSON><PERSON> různé formáty hesla
        passwords = ['Graph2025Secure!', 'Graph2025Secure']
        
        for password in passwords:
            try:
                print(f"🔄 Zkouším heslo: {password}")
                driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', password))
                
                with driver.session() as session:
                    # Test připojení
                    result = session.run("RETURN 1 as test")
                    if result.single()["test"] == 1:
                        print(f"✅ Připojeno s heslem: {password}")
                        break
                        
            except Exception as e:
                print(f"❌ Heslo {password} nefunguje: {e}")
                continue
        else:
            print("❌ Žádn<PERSON> heslo nefunguje")
            return
        
        with driver.session() as session:
            print('\n🔍 ANALÝZA VŠECH NEO4J VZTAHŮ PRO FILSONSTORE')
            print('=' * 60)
            
            # Celkové statistiky
            result = session.run('''
                MATCH (s:Category_filsonstore)-[r:RELATED_TO]->(t:Category_filsonstore)
                RETURN count(r) as total_relationships, 
                       count(DISTINCT s.name) as source_categories,
                       count(DISTINCT t.name) as target_categories
            ''')
            stats = result.single()
            
            print(f'📊 CELKOVÉ STATISTIKY:')
            print(f'   • Celkem vztahů: {stats["total_relationships"]}')
            print(f'   • Zdrojových kategorií: {stats["source_categories"]}')  
            print(f'   • Cílových kategorií: {stats["target_categories"]}')
            
            # Seznam všech kategorií v databázi
            result = session.run('''
                MATCH (c:Category_filsonstore)
                RETURN DISTINCT c.name as category_name
                ORDER BY c.name
            ''')
            all_categories = [record['category_name'] for record in result]
            
            print(f'\n📂 VŠECHNY KATEGORIE V DATABÁZI ({len(all_categories)}):')
            for i, cat in enumerate(all_categories, 1):
                print(f'   {i:2d}. {cat}')
            
            # Očekávané kategorie z filsonstore
            expected_categories = [
                "Autodoplňky > Autobaterie",
                "Autodoplňky > Autokoberce", 
                "Autodoplňky > Autokosmetika",
                "Autodoplňky > Autopotahy",
                "Autodoplňky > Autorádia",
                "Autodoplňky > Autožárovky",
                "Autodoplňky > Dílna a garáž",
                "Autodoplňky > Elektrické doplňky",
                "Autodoplňky > Exteriér vozidla",
                "Autodoplňky > Interiér vozidla",
                "Autodoplňky > Oleje a maziva",
                "Autodoplňky > Povinná a doporučená výbava",
                "Autodoplňky > Tuning",
                "Autodoplňky > Vany do zavazadlového prostoru"
            ]
            
            # Porovnání
            missing_categories = [cat for cat in expected_categories if cat not in all_categories]
            extra_categories = [cat for cat in all_categories if cat not in expected_categories]
            
            if missing_categories:
                print(f'\n❌ CHYBĚJÍCÍ KATEGORIE ({len(missing_categories)}):')
                for cat in missing_categories:
                    print(f'   • {cat}')
            
            if extra_categories:
                print(f'\n➕ EXTRA KATEGORIE ({len(extra_categories)}):')
                for cat in extra_categories:
                    print(f'   • {cat}')
            
            # Analýza vztahů pro každou kategorii
            print(f'\n🔗 VZTAHY PO KATEGORIÍCH:')
            
            categories_with_relationships = 0
            categories_without_relationships = []
            
            for category in all_categories:
                result = session.run('''
                    MATCH (s:Category_filsonstore {name: $category})-[r:RELATED_TO]->(t:Category_filsonstore)
                    RETURN t.name as target, r.weight as weight, r.reason as reason
                    ORDER BY r.weight DESC
                ''', category=category)
                
                relationships = result.data()
                
                if relationships:
                    categories_with_relationships += 1
                    total_weight = sum(rel['weight'] for rel in relationships)
                    
                    print(f'\n📂 {category}:')
                    print(f'   Celková váha: {total_weight}')
                    
                    for rel in relationships:
                        target = rel['target']
                        weight = rel['weight']
                        reason = rel['reason']
                        percentage = (weight / total_weight) * 100 if total_weight > 0 else 0
                        candidates = max(1, int(round(80 * weight / total_weight)))
                        
                        print(f'   → {target}')
                        print(f'     Váha: {weight} ({percentage:.1f}% = {candidates} kandidátů)')
                        print(f'     Důvod: {reason}')
                else:
                    categories_without_relationships.append(category)
            
            # Finální shrnutí
            print(f'\n📋 FINÁLNÍ SHRNUTÍ:')
            print(f'   ✅ Kategorie s vztahy: {categories_with_relationships}/{len(all_categories)}')
            print(f'   ❌ Kategorie bez vztahů: {len(categories_without_relationships)}')
            
            if categories_without_relationships:
                print(f'\n❌ KATEGORIE BEZ VZTAHŮ:')
                for cat in categories_without_relationships:
                    print(f'      • {cat}')
                    
                print(f'\n💡 POTŘEBA DOPLNIT VZTAHY PRO {len(categories_without_relationships)} KATEGORIÍ!')
            else:
                print(f'\n🎉 VŠECHNY KATEGORIE MAJÍ VZTAHY!')
        
        driver.close()
        
    except Exception as e:
        print(f'❌ Celková chyba: {e}')

if __name__ == "__main__":
    check_all_relationships()