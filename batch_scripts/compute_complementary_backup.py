import os
import sys
import asyncio
import logging
import json
import yaml
import time
import random
import re
from typing import List, Dict, Optional, Any, Tuple
from dotenv import load_dotenv
import google.generativeai as genai
from qdrant_client import QdrantClient, AsyncQdrantClient, models
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, PointStruct, HasIdCondition
import argparse
from pathlib import Path
from neo4j import AsyncGraphDatabase, basic_auth
from dataclasses import dataclass
from collections import defaultdict
import pickle

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

from core.clients import get_qdrant_async_client, get_gemini_client
from core.utils import create_qdrant_id

# --- Konfigurace logování ---
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("compute_complementary")
logger.setLevel(logging.INFO)  # Změněno z DEBUG na INFO pro lepší výkon

# Handler pro výpis do konzole
console_handler = logging.StreamHandler()
console_handler.setFormatter(log_formatter)
logger.addHandler(console_handler)

# Handler pro soubor
log_file_path = Path(__file__).parent / 'compute_complementary.log'
try:
    file_handler = logging.FileHandler(log_file_path, mode='a')
    file_handler.setFormatter(log_formatter)
    logger.addHandler(file_handler)
    logger.info(f"Logging also to file: {log_file_path}")
except Exception as e:
    logger.error(f"Could not set up file logging to {log_file_path}: {e}")

# Načtení .env
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# --- Metriky pro sledování výkonu ---
@dataclass
class ProcessingMetrics:
    """Třída pro sledování metrik zpracování."""
    start_time: float = 0.0
    neo4j_query_time: float = 0.0
    total_gemini_time: float = 0.0
    qdrant_search_time: float = 0.0
    gemini_api_calls: int = 0
    gemini_batch_calls: int = 0
    avg_gemini_time: float = 0.0
    errors: int = 0
    processed_products: int = 0
    saved_results: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    products_processed: int = 0
    products_skipped: int = 0
    
    def log_summary(self):
        """Vypíše shrnutí metrik."""
        total_time = time.time() - self.start_time if self.start_time > 0 else 0
        cache_total = self.cache_hits + self.cache_misses
        cache_hit_rate = (self.cache_hits / cache_total * 100) if cache_total > 0 else 0
        
        logger.info("=== SHRNUTÍ VÝKONU ===")
        logger.info(f"Celkový čas: {total_time:.2f}s")
        logger.info(f"Produktů zpracováno: {self.products_processed}")
        logger.info(f"Produktů přeskočeno (cache): {self.products_skipped}")
        logger.info(f"Cache hit rate: {cache_hit_rate:.1f}%")
        logger.info(f"Neo4j dotazy: {self.neo4j_query_time:.2f}s")
        logger.info(f"Gemini API volání: {self.gemini_api_calls}")
        logger.info(f"Gemini batch volání: {self.gemini_batch_calls}")
        logger.info(f"Celkový čas Gemini: {self.total_gemini_time:.2f}s")
        logger.info(f"Průměrný čas Gemini: {self.avg_gemini_time:.2f}s")
        logger.info(f"Chyby: {self.errors}")
        logger.info(f"Uložené výsledky: {self.saved_results}")

# Globální instance metrik
metrics = ProcessingMetrics()

# --- Dataclass definice pro batch processing (musí být před použitím v type hintech) ---
@dataclass
class ProductCandidate:
    """Struktura pro kandidáta na komplementární produkt."""
    product_id: str
    payload: Dict[str, Any]
    qdrant_score: float
    category: str

@dataclass 
class BatchProcessingRequest:
    """Struktura pro batch požadavek na Gemini."""
    source_product_id: str
    source_info: Dict[str, Any]
    candidates: List[ProductCandidate]

# --- NOVÉ: Globální cache pro Neo4j výsledky ---
NEO4J_CACHE = {}
NEO4J_CACHE_TTL = 3600  # 1 hodina cache pro Neo4j

# --- OPTIMALIZOVANÁ konfigurace pro maximální rychlost ---
ULTRA_BATCH_SIZE = 15  # ZVÝŠENO z 7 na 15 produktů v jednom Gemini volání
ULTRA_CONCURRENCY = 25  # ZVÝŠENO z 15 na 25 pro agresivnější paralelizaci
ULTRA_CANDIDATE_LIMIT = 60  # SNÍŽENO z 100 na 60 pro rychlejší zpracování
ULTRA_RERANK_LIMIT = 10  # SNÍŽENO z 15 na 10 pro rychlejší Gemini
CACHE_BULK_CHECK_SIZE = 100  # Kontrola cache po 100 produktech najednou

# --- Cache pre-load funkce ---
async def preload_neo4j_cache(neo4j_driver, tenant_id: str):
    """Předběžně načte všechny komplementární kategorie do cache."""
    if NEO4J_CACHE:  # Už je načtená
        return
        
    logger.info("🔄 Načítám všechny komplementární kategorie do cache...")
    start_time = time.time()
    
    category_label = f"Category_{tenant_id}" if tenant_id else "Category"
    query = (
        f"MATCH (source:{category_label})-[r:RELATED_TO]->(target:{category_label}) "
        "RETURN source.name AS source_name, target.name AS target_name, r.weight AS weight"
    )
    
    try:
        async with neo4j_driver.session() as session:
            result = await session.run(query)
            records = await result.data()
            
            cache_data = defaultdict(list)
            for record in records:
                source_name = record.get("source_name")
                target_name = record.get("target_name")
                weight = record.get("weight", 0.0)
                
                if source_name and target_name and weight > 0:
                    cache_data[source_name].append((target_name, float(weight)))
            
            # Seřazení podle vah
            for source_cat in cache_data:
                cache_data[source_cat].sort(key=lambda x: x[1], reverse=True)
            
            NEO4J_CACHE.update(cache_data)
            NEO4J_CACHE['_loaded_at'] = time.time()
            
            load_time = time.time() - start_time
            logger.info(f"✅ Neo4j cache načtena za {load_time:.2f}s, {len(cache_data)} kategorií")
            
    except Exception as e:
        logger.error(f"Chyba při načítání Neo4j cache: {e}")

async def get_complementary_categories_cached(source_category_name: str) -> List[Tuple[str, float]]:
    """Rychlé získání komplementárních kategorií z cache."""
    cache_age = time.time() - NEO4J_CACHE.get('_loaded_at', 0)
    if cache_age > NEO4J_CACHE_TTL:
        NEO4J_CACHE.clear()  # Cache expirovala
        return []
    
    return NEO4J_CACHE.get(source_category_name, [])

# --- BULK cache kontrola ---
async def bulk_cache_check(
    qdrant_client: AsyncQdrantClient,
    cache_collection: str,
    product_ids: List[str],
    ttl_seconds: int
) -> Tuple[List[str], List[str]]:
    """Bulk kontrola cache pro více produktů najednou."""
    valid_cache_ids = []
    needs_processing_ids = []
    
    # Rozdělení na chunky pro bulk operace
    chunk_size = CACHE_BULK_CHECK_SIZE
    for i in range(0, len(product_ids), chunk_size):
        chunk = product_ids[i:i + chunk_size]
        qdrant_ids = [create_qdrant_id(pid) for pid in chunk]
        
        try:
            cache_points = await qdrant_client.retrieve(
                collection_name=cache_collection,
                ids=qdrant_ids,
                with_payload=["computed_at", "product_id"]
            )
            
            found_ids = set()
            for point in cache_points:
                if point and point.payload:
                    computed_at = point.payload.get("computed_at")
                    product_id = point.payload.get("product_id")
                    
                    if computed_at and product_id:
                        cache_age = time.time() - computed_at
                        if cache_age < ttl_seconds:
                            valid_cache_ids.append(str(product_id))
                            found_ids.add(str(product_id))
            
            # Produkty, které nebyly nalezeny v cache
            for pid in chunk:
                if str(pid) not in found_ids:
                    needs_processing_ids.append(str(pid))
                    
        except Exception as e:
            logger.warning(f"Chyba při bulk cache check: {e}")
            # V případě chyby považujeme chunk za vyžadující zpracování
            needs_processing_ids.extend([str(pid) for pid in chunk])
    
    return valid_cache_ids, needs_processing_ids

# --- ULTRA rychlý Gemini prompt ---
async def compute_complementary_ultra_fast_gemini(
    gemini_client: genai.GenerativeModel,
    batch_requests: List[BatchProcessingRequest],
    rerank_limit: int = ULTRA_RERANK_LIMIT
) -> Dict[str, List[Tuple[str, float]]]:
    """ULTRA rychlá verze Gemini batch processing s minimálním promptem."""
    if not batch_requests:
        return {}
    
    start_time = time.time()
    
    # ULTRA KOMPAKTNÍ data preparation
    batch_data = []
    for req in batch_requests:
        # Pouze nejnutnější data
        source = {
            "id": req.source_product_id,
            "name": req.source_info.get("name", "")[:60],  # Ještě kratší názvy
            "cat": req.source_info.get("category", "")[:30]  # Zkrácené kategorie
        }
        
        # Pouze top kandidáty
        candidates = []
        for cand in req.candidates[:20]:  # Ještě méně kandidátů
            candidates.append({
                "id": cand.product_id,
                "name": cand.payload.get("name", "")[:50],  # Velmi krátké názvy
                "cat": cand.payload.get("category", "")[:20],
                "score": round(cand.qdrant_score, 2)
            })
        
        batch_data.append({"src": source, "cands": candidates})
    
    # ULTRA KOMPAKTNÍ prompt pro maximální rychlost
    ultra_prompt = f"""Find top {rerank_limit} cross-sell products for each:

{json.dumps(batch_data, ensure_ascii=False)}

Return JSON: {{"prod_id_1": [{{"id": "cand_id", "score": 0.9}}], "prod_id_2": [...]}}

Fast ranking only."""

    try:
        response = await gemini_client.generate_content_async(ultra_prompt)
        response_text = response.text.strip()
        
        # Ultra rychlé cleaning
        if response_text.startswith("```"):
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            if start_idx != -1 and end_idx > start_idx:
                response_text = response_text[start_idx:end_idx]
        
        batch_results = json.loads(response_text)
        
        # Rychlá validace
        validated_results = {}
        for source_id, candidates in batch_results.items():
            if isinstance(candidates, list):
                validated_candidates = []
                for item in candidates[:rerank_limit]:
                    if isinstance(item, dict) and "id" in item:
                        score = float(item.get("score", 0.5))
                        validated_candidates.append((str(item["id"]), score))
                validated_results[source_id] = validated_candidates
        
        processing_time = time.time() - start_time
        metrics.total_gemini_time += processing_time
        metrics.gemini_batch_calls += 1
        metrics.gemini_api_calls += len(batch_requests)
        
        if metrics.gemini_api_calls > 0:
            metrics.avg_gemini_time = metrics.total_gemini_time / metrics.gemini_api_calls
        
        logger.debug(f"⚡ Ultra-fast Gemini: {len(batch_requests)} produktů za {processing_time:.2f}s")
        return validated_results
        
    except Exception as e:
        logger.error(f"Ultra-fast Gemini error: {e}")
        metrics.errors += len(batch_requests)
        return {}

# --- ULTRA optimalizovaná main funkce ---
async def main_ultra_optimized(
    tenant_id: str, 
    product_ids: Optional[List[str]] = None, 
    concurrency_limit: int = ULTRA_CONCURRENCY,
    limit: int = None,
    batch_size: int = ULTRA_BATCH_SIZE,
    stream_processing: bool = True
):
    """ULTRA optimalizovaná hlavní funkce pro maximální rychlost."""
    logger.info(f"🚀 SPOUŠTÍM ULTRA OPTIMALIZOVANÝ REŽIM 🚀")
    logger.info(f"⚡ Batch: {batch_size}, Concurrency: {concurrency_limit}, Candidates: {ULTRA_CANDIDATE_LIMIT}")
    
    metrics.start_time = time.time()
    
    # Inicializace klientů
    neo4j_driver = None
    try:
        qdrant_client = get_custom_qdrant_async_client()
        gemini_client = get_gemini_client()
        neo4j_driver = await get_neo4j_driver()
    except Exception as e:
        logger.error(f"Chyba při inicializaci: {e}")
        return

    # Předběžné načtení Neo4j cache
    await preload_neo4j_cache(neo4j_driver, tenant_id)
    
    # Příprava kolekcí
    products_collection = f"real_products_{tenant_id}"
    complementary_cache_collection = f"complementary_products_{tenant_id}"
    
    try:
        await ensure_collection_exists(qdrant_client, complementary_cache_collection)
    except:
        if neo4j_driver:
            await neo4j_driver.close()
        return

    # Získání produktů
    if product_ids:
        products_to_process = list(product_ids)
    else:
        if stream_processing:
            products_to_process = await get_products_streaming(qdrant_client, products_collection, limit)
        else:
            products_to_process = await get_all_product_ids(qdrant_client, products_collection, limit)

    if not products_to_process:
        logger.warning("Žádné produkty k zpracování")
        if neo4j_driver:
            await neo4j_driver.close()
        return

    logger.info(f"📊 Celkem produktů: {len(products_to_process)}")

    # BULK cache kontrola
    cache_ttl = 30 * 24 * 60 * 60  # 30 dní
    logger.info("🔍 Provádím BULK cache kontrolu...")
    cache_start = time.time()
    
    valid_cache_ids, needs_processing_ids = await bulk_cache_check(
        qdrant_client, complementary_cache_collection, products_to_process, cache_ttl
    )
    
    cache_time = time.time() - cache_start
    cache_hit_rate = (len(valid_cache_ids) / len(products_to_process)) * 100 if products_to_process else 0
    
    metrics.cache_hits = len(valid_cache_ids)
    metrics.cache_misses = len(needs_processing_ids)
    
    logger.info(f"✅ Bulk cache kontrola za {cache_time:.2f}s")
    logger.info(f"📈 Cache hit rate: {cache_hit_rate:.1f}% ({len(valid_cache_ids)}/{len(products_to_process)})")
    logger.info(f"🎯 K zpracování: {len(needs_processing_ids)} produktů")

    if not needs_processing_ids:
        logger.info("🎉 Všechny produkty mají platnou cache!")
        metrics.log_summary()
        if neo4j_driver:
            await neo4j_driver.close()
        return

    # ULTRA rychlé batch zpracování
    semaphore = asyncio.Semaphore(concurrency_limit)
    total_processed = 0

    async def ultra_process_batch(product_batch: List[str]):
        """Ultra rychlé zpracování dávky."""
        nonlocal total_processed
        
        async with semaphore:
            try:
                # Paralelní příprava kandidátů s timeoutem
                prep_tasks = []
                for prod_id in product_batch:
                    prep_tasks.append(
                        compute_and_save_complementary_ultra_optimized(
                            tenant_id, prod_id, qdrant_client, gemini_client, neo4j_driver
                        )
                    )
                
                batch_requests = await asyncio.wait_for(
                    asyncio.gather(*prep_tasks, return_exceptions=True),
                    timeout=20.0  # Kratší timeout
                )
                
                # Filtrování platných požadavků
                valid_requests = [req for req in batch_requests 
                                if not isinstance(req, Exception) and req is not None]
                
                if not valid_requests:
                    return 0

                # Ultra rychlé Gemini zpracování
                gemini_results = await asyncio.wait_for(
                    compute_complementary_ultra_fast_gemini(gemini_client, valid_requests, ULTRA_RERANK_LIMIT),
                    timeout=30.0  # Kratší timeout pro Gemini
                )
                
                # Rychlé uložení
                if gemini_results:
                    saved_count = await save_complementary_results_batch(
                        qdrant_client, tenant_id, gemini_results, gemini_client.model_name
                    )
                    total_processed += saved_count
                    metrics.processed_products += saved_count
                    return len(valid_requests)
                
                return 0
                
            except Exception as e:
                logger.warning(f"Batch error: {e}")
                metrics.errors += len(product_batch)
                return 0

    # Zpracování v ultra rychlých vlnách
    batches = [needs_processing_ids[i:i + batch_size] 
               for i in range(0, len(needs_processing_ids), batch_size)]
    
    logger.info(f"🌊 ULTRA zpracování: {len(batches)} dávek po {batch_size} produktech")
    
    # Větší vlny pro vyšší throughput
    ULTRA_WAVE_SIZE = 80  # Ještě větší vlny
    
    for wave_start in range(0, len(batches), ULTRA_WAVE_SIZE):
        wave_end = min(wave_start + ULTRA_WAVE_SIZE, len(batches))
        wave_batches = batches[wave_start:wave_end]
        
        logger.info(f"🚀 Ultra vlna {wave_start//ULTRA_WAVE_SIZE + 1}: dávky {wave_start+1}-{wave_end}")
        
        batch_tasks = [ultra_process_batch(batch) for batch in wave_batches]
        wave_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        
        wave_processed = sum(r for r in wave_results if isinstance(r, int))
        progress = (wave_end / len(batches)) * 100
        
        logger.info(f"📊 Progress: {progress:.1f}% - vlna zpracovala {wave_processed} produktů")
        
        # Ultra krátká pauza mezi vlnami
        await asyncio.sleep(0.05)
    
    # Finální statistiky
    total_time = time.time() - metrics.start_time
    products_per_second = total_processed / total_time if total_time > 0 else 0
    
    logger.info(f"🎉 ULTRA OPTIMALIZACE DOKONČENA!")
    logger.info(f"⚡ Rychlost: {products_per_second:.2f} produktů/sekunda")
    logger.info(f"📈 Celkem zpracováno: {total_processed} produktů za {total_time:.2f}s")
    
    metrics.log_summary()

    if neo4j_driver:
        await neo4j_driver.close()

# Upravená ultra optimalizovaná verze compute_and_save_complementary
async def compute_and_save_complementary_ultra_optimized(
    tenant_id: str,
    product_id: str,
    qdrant_client: AsyncQdrantClient,
    gemini_client: genai.GenerativeModel,
    neo4j_driver,
    candidate_limit: int = ULTRA_CANDIDATE_LIMIT,
    rerank_limit: int = ULTRA_RERANK_LIMIT,
    vector_name: str = "combined"
) -> Optional[BatchProcessingRequest]:
    """Ultra optimalizovaná verze s cache Neo4j."""
    products_collection = f"real_products_{tenant_id}"
    source_qdrant_id = create_qdrant_id(product_id)

    # Rychlé získání zdrojových dat
    source_payload = await _get_product_payload_async(qdrant_client, products_collection, product_id)
    if not source_payload:
        return None

    source_vector = await _get_product_vector_async(qdrant_client, products_collection, product_id)
    if not source_vector:
        return None

    source_category = source_payload.get("category", "")
    if not source_category:
        return None

    # Použití cache pro Neo4j
    complementary_categories_with_weights = await get_complementary_categories_cached(source_category)
    if not complementary_categories_with_weights:
        return None

    # Ultra rychlé vektorové vyhledávání
    candidates = []
    processed_ids = {source_qdrant_id}

    # Rozdělení limitů podle vah (optimalizováno)
    total_weight = sum(w for _, w in complementary_categories_with_weights if w > 0)
    if total_weight <= 0:
        return None

    # Paralelní vyhledávání s menším počtem kategorií pro rychlost
    search_tasks = []
    for category_name, weight in complementary_categories_with_weights[:5]:  # Max 5 kategorií
        if weight <= 0:
            continue
            
        cat_limit = max(1, int((candidate_limit * weight / total_weight) / 2))  # Méně kandidátů
        
        category_filter = models.Filter(
            must=[models.FieldCondition(key="category", match=models.MatchValue(value=category_name))],
            must_not=[models.HasIdCondition(has_id=list(processed_ids))]
        )

        search_tasks.append(qdrant_client.search(
            collection_name=products_collection,
            query_vector=(vector_name, source_vector),
            query_filter=category_filter,
            limit=cat_limit,
            with_payload=True
        ))

    # Rychlé zpracování výsledků
    if search_tasks:
        search_results = await asyncio.gather(*search_tasks, return_exceptions=True)
        
        for result in search_results:
            if isinstance(result, Exception):
                continue
                
            for scored_point in result:
                cand_id = str(scored_point.payload.get("product_id") or scored_point.id)
                if cand_id not in processed_ids:
                    candidates.append(ProductCandidate(
                        product_id=cand_id,
                        payload=scored_point.payload,
                        qdrant_score=scored_point.score,
                        category=scored_point.payload.get("category", "")
                    ))
                    processed_ids.add(cand_id)

    if not candidates:
        return None

    # Rychlé seřazení a omezení
    candidates.sort(key=lambda x: x.qdrant_score, reverse=True)
    candidates = candidates[:candidate_limit]

    return BatchProcessingRequest(
        source_product_id=product_id,
        source_info=source_payload,
        candidates=candidates
    )

# --- Neo4j Klient ---
async def get_neo4j_driver():
    """Inicializuje a vrací asynchronní Neo4j driver."""
    uri = os.getenv("NEO4J_URI")
    user = os.getenv("NEO4J_USER")
    password = os.getenv("NEO4J_PASSWORD")
    if not uri or not user or not password:
        logger.error("Chybí konfigurační proměnné pro Neo4j (NEO4J_URI, NEO4J_USER, NEO4J_PASSWORD).")
        raise ValueError("Chybí konfigurační proměnné pro Neo4j.")
    try:
        driver = AsyncGraphDatabase.driver(uri, auth=basic_auth(user, password))
        await driver.verify_connectivity()
        logger.info("Neo4j driver úspěšně inicializován a připojen.")
        return driver
    except Exception as e:
        logger.error(f"Nepodařilo se připojit k Neo4j: {e}", exc_info=True)
        raise

# Upravená funkce pro inicializaci Qdrant klienta s vyšším timeoutem
def get_custom_qdrant_async_client() -> AsyncQdrantClient:
    """
    Vytvoří optimalizovaného Qdrant klienta s agresivnějšími timeouty pro rychlejší zpracování.
    """
    import httpx
    
    qdrant_url = os.getenv('QDRANT_URL', 'http://localhost:6333')
    
    # OPTIMALIZOVANÉ connection limits pro vyšší throughput
    limits = httpx.Limits(
        max_keepalive_connections=10,  # ZVÝŠENO pro více současných spojení
        max_connections=20,            # ZVÝŠENO pro rychlejší zpracování
        keepalive_expiry=30.0          # Kratší keepalive pro rychlejší refresh
    )
    
    return AsyncQdrantClient(
        url=qdrant_url,
        timeout=60.0,  # Číselný timeout místo Timeout objektu
        limits=limits,
        # OPTIMALIZACE: Přidání dalších parametrů pro výkon
        prefer_grpc=False,  # HTTP je často rychlejší pro batch operace
    )

async def _get_product_payload_async(client: AsyncQdrantClient, collection_name: str, product_id: str) -> Optional[Dict[str, Any]]:
    """Asynchronně získá payload produktu podle ID s mechanismem znovupokusů při selhání."""
    qdrant_id = create_qdrant_id(product_id)
    max_retries = 3
    retry_delay = 0.5  # Sníženo z 1.0s pro rychlejší retry
    
    for attempt in range(max_retries):
        try:
            points = await client.retrieve(collection_name=collection_name, ids=[qdrant_id], with_payload=True)
            if points: 
                return points[0].payload

            # Fallback na scroll (pouze na poslední pokus)
            if attempt == max_retries - 1:
                logger.warning(f"Payload for {product_id} not found via retrieve, trying scroll...")
                scroll_result = await client.scroll(
                    collection_name=collection_name,
                    scroll_filter=Filter(must=[FieldCondition(key="product_id", match=MatchValue(value=product_id))]),
                    limit=1, with_payload=True
                )
                if scroll_result and scroll_result[0]: 
                    return scroll_result[0][0].payload

                scroll_result = await client.scroll(
                    collection_name=collection_name,
                    scroll_filter=Filter(must=[FieldCondition(key="original_id", match=MatchValue(value=product_id))]),
                    limit=1, with_payload=True
                )
                if scroll_result and scroll_result[0]: 
                    return scroll_result[0][0].payload

                logger.error(f"Payload for {product_id} not found after fallback scroll.")
            return None
            
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = retry_delay * (2 ** attempt)
                logger.warning(f"Pokus {attempt+1}/{max_retries} selhal při získávání payloadu pro {product_id}: {e}. Retry za {wait_time:.1f}s.")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"Error retrieving payload for {product_id} in {collection_name} after {max_retries} attempts: {e}", exc_info=True)
                return None
    
    return None

async def _get_product_vector_async(client: AsyncQdrantClient, collection_name: str, product_id: str) -> Optional[List[float]]:
    """Asynchronně získá vektor pro daný produkt z Qdrantu s mechanismem znovupokusů."""
    qdrant_id = create_qdrant_id(product_id)
    max_retries = 3
    retry_delay = 0.5
    
    for attempt in range(max_retries):
        try:
            points = await client.retrieve(
                collection_name=collection_name,
                ids=[qdrant_id],
                with_payload=False,
                with_vectors=True
            )
            
            if not points:
                if attempt == max_retries - 1:
                    logger.error(f"Produkt {product_id} nenalezen v kolekci {collection_name} po {max_retries} pokusech.")
                continue
            
            point = points[0]
            if isinstance(point.vector, list):
                return point.vector
            elif isinstance(point.vector, dict) and 'combined' in point.vector:
                 return point.vector['combined']
            else:
                 logger.error(f"Pro produkt {product_id} nebyl nalezen vhodný vektor. Struktura: {type(point.vector)}")
                 return None
                
        except Exception as e:
            if attempt < max_retries - 1:
                wait_time = retry_delay * (2 ** attempt)
                logger.warning(f"Pokus {attempt+1}/{max_retries} selhal při získávání vektoru pro {product_id}: {e}. Retry za {wait_time:.1f}s.")
                await asyncio.sleep(wait_time)
            else:
                logger.error(f"Chyba při načítání vektoru pro {product_id} po {max_retries} pokusech: {e}")
                metrics.errors += 1
                return None
    
    return None

def get_tenant_yaml_config(tenant_id: str) -> Optional[Dict[str, Any]]:
    """Načte konfiguraci tenanta z YAML souboru."""
    config_path = os.path.join(project_root, "config", "tenants", f"{tenant_id}.yaml")
    if not os.path.exists(config_path):
        logger.error(f"Konfigurační soubor pro tenanta '{tenant_id}' nebyl nalezen na cestě: {config_path}")
        return None
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.debug(f"Konfigurace pro tenanta '{tenant_id}' úspěšně načtena z {config_path}.")
        return config
    except Exception as e:
        logger.error(f"Chyba při načítání YAML konfigurace z {config_path}: {e}", exc_info=True)
        return None

async def get_complementary_categories_from_neo4j_async(
    driver,
    source_category_name: str,
    tenant_id: str = None
) -> List[Tuple[str, float]]:
    """Získá seznam komplementárních kategorií a jejich vah z Neo4j s měřením času."""
    start_time = time.time()
    
    category_label = f"Category_{tenant_id}" if tenant_id else "Category"
    query = (
        f"MATCH (source:{category_label} {{name: $category_name}}) "
        f"MATCH (source)-[r:RELATED_TO]->(target:{category_label}) "
        "RETURN target.name AS category_name, r.weight AS weight "
        "ORDER BY r.weight DESC"
    )
    
    try:
        async with driver.session() as session:
            result = await session.run(query, category_name=source_category_name)
            records = await result.data()
            
            complementary_categories = []
            for record in records:
                category_name = record.get("category_name")
                weight = record.get("weight", 0.0)
                if category_name and weight > 0:
                    complementary_categories.append((category_name, float(weight)))
            
            query_time = time.time() - start_time
            metrics.neo4j_query_time += query_time
            
            logger.debug(f"Neo4j query took {query_time:.3f}s, found {len(complementary_categories)} categories")
            return complementary_categories
            
    except Exception as e:
        logger.error(f"Chyba při dotazování Neo4j pro komplementární kategorie k '{source_category_name}': {e}", exc_info=True)
        return []

def _extract_keywords(text: Optional[str], num_keywords: int = 4) -> List[str]:
    """Extrahuje klíčová slova z textu pomocí jednoduchých heuristik."""
    if not text:
        return []
    
    # Normalizace textu
    text = text.lower()
    
    # Odstranění speciálních znaků a rozdělení na slova
    words = re.findall(r'\b[a-záčďéěíňóřšťúůýž]{3,}\b', text)
    
    # Filtrování stop slov (rozšířený seznam)
    stop_words = {
        'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'a', 'an',
        'je', 'jsou', 'byl', 'byla', 'bylo', 'být', 'mít', 'má', 'mají', 'měl', 'měla', 'mělo',
        'se', 'si', 'na', 'do', 'za', 'po', 'před', 'při', 'od', 'bez', 'pro', 'proti', 'mezi',
        'nad', 'pod', 'přes', 'kolem', 'podle', 'během', 'místo', 'kromě', 'díky', 'kvůli',
        'tento', 'tato', 'toto', 'tyto', 'ten', 'ta', 'to', 'ti', 'ty', 'jeho', 'její', 'jejich'
    }
    
    # Filtrování a počítání četnosti
    word_freq = {}
    for word in words:
        if word not in stop_words and len(word) >= 3:
            word_freq[word] = word_freq.get(word, 0) + 1
    
    # Seřazení podle četnosti a vrácení top N
    sorted_words = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)
    return [word for word, _ in sorted_words[:num_keywords]]

# --- Nová: Batch processing pro Gemini ---
async def compute_complementary_batch_with_gemini(
    gemini_client: genai.GenerativeModel,
    batch_requests: List[BatchProcessingRequest],
    rerank_limit: int = 15  # Sníženo z 20 na 15 pro rychlejší zpracování
) -> Dict[str, List[Tuple[str, float]]]:
    """
    OPTIMALIZOVANÝ Batch processing pro Gemini - zpracuje více produktů najednou.
    Vrací slovník {source_product_id: [(complementary_id, score), ...]}
    """
    if not batch_requests:
        return {}
    
    start_time = time.time()
    
    # OPTIMALIZACE: Kratší a efektivnější příprava dat
    batch_data = []
    
    for req in batch_requests:
        # OPTIMALIZACE: Menší množství dat pro rychlejší zpracování
        source_data = {
            "id": req.source_product_id,
            "name": req.source_info.get("name", "")[:100],  # Zkráceno na 100 znaků
            "category": req.source_info.get("category", ""),
            "keywords": _extract_keywords(req.source_info.get("description", ""))[:5]  # Max 5 klíčových slov
        }
        
        # OPTIMALIZACE: Méně kandidátů pro rychlejší zpracování
        candidates_data = []
        for cand in req.candidates[:30]:  # Sníženo z 50 na 30 kandidátů
            candidates_data.append({
                "id": cand.product_id,
                "name": cand.payload.get("name", "")[:80],  # Zkráceno na 80 znaků
                "category": cand.payload.get("category", ""),
                "score": round(cand.qdrant_score, 3)  # Méně desetinných míst
            })
        
        batch_data.append({
            "source": source_data,
            "candidates": candidates_data
        })
    
    # OPTIMALIZOVANÝ a kratší prompt pro rychlejší zpracování
    batch_prompt = f"""
Analyze {len(batch_data)} products for cross-selling. Return top {rerank_limit} complementary products for each.

Data:
{json.dumps(batch_data, ensure_ascii=False)}

Return JSON format:
{{
  "product_id_1": [{{"id": "candidate_id", "score": 0.9}}],
  "product_id_2": [...]
}}

Focus on practical complementarity and cross-selling potential. Be fast and precise.
"""

    try:
        # OPTIMALIZACE: Rychlejší Gemini volání
        response = await gemini_client.generate_content_async(batch_prompt)
        response_text = response.text.strip()
        
        # OPTIMALIZACE: Rychlejší cleaning odpovědi
        if response_text.startswith("```json"):
            response_text = response_text[7:-3]
        elif response_text.startswith("```"):
            response_text = response_text[3:-3]
        response_text = response_text.strip()
        
        # OPTIMALIZACE: Rychlejší parsování s fallback
        try:
            batch_results = json.loads(response_text)
        except json.JSONDecodeError:
            # Pokus o opravu běžných chyb v JSON
            response_text = response_text.replace("'", '"').replace('\n', '')
            batch_results = json.loads(response_text)
        
        # OPTIMALIZACE: Rychlejší validace výsledků
        validated_results = {}
        for source_id in batch_results:
            if isinstance(batch_results[source_id], list):
                validated_candidates = []
                for item in batch_results[source_id][:rerank_limit]:  # Okamžité omezení
                    if isinstance(item, dict) and "id" in item and "score" in item:
                        try:
                            score = float(item["score"])
                            validated_candidates.append((str(item["id"]), score))
                        except (ValueError, TypeError):
                            continue
                validated_results[source_id] = validated_candidates
        
        # OPTIMALIZACE: Rychlejší metriky
        processing_time = time.time() - start_time
        metrics.total_gemini_time += processing_time
        metrics.gemini_batch_calls += 1
        metrics.gemini_api_calls += len(batch_requests)
        
        if metrics.gemini_api_calls > 0:
            metrics.avg_gemini_time = metrics.total_gemini_time / metrics.gemini_api_calls
        
        # Rychlejší logování jen pro významné události
        if len(batch_requests) > 5 or processing_time > 10:
            logger.info(f"⚡ Rychlý Gemini batch: {len(batch_requests)} produktů za {processing_time:.2f}s")
        
        return validated_results
        
    except Exception as e:
        logger.error(f"Chyba při rychlém Gemini processing: {e}")
        metrics.errors += len(batch_requests)
        return {}

async def compute_and_save_complementary_optimized(
    tenant_id: str,
    product_id: str,
    qdrant_client: AsyncQdrantClient,
    gemini_client: genai.GenerativeModel,
    neo4j_driver,
    candidate_limit: int = 100,
    rerank_limit: int = 20,
    vector_name: str = "combined"
) -> Optional[BatchProcessingRequest]:
    """
    Optimalizovaná verze - vrací BatchProcessingRequest místo přímého uložení.
    Umožňuje batch processing více produktů najednou.
    """
    products_collection = f"real_products_{tenant_id}"
    source_qdrant_id = create_qdrant_id(product_id)

    # Získání zdrojového produktu
    source_payload = await _get_product_payload_async(qdrant_client, products_collection, product_id)
    if source_payload is None:
        logger.error(f"Zdrojový produkt {product_id} nenalezen.")
        return None

    source_vector = await _get_product_vector_async(qdrant_client, products_collection, product_id)
    if source_vector is None:
        logger.error(f"Vektor pro zdrojový produkt {product_id} nenalezen.")
        return None

    source_category = source_payload.get("category", "")
    if not source_category:
        logger.warning(f"Zdrojový produkt {product_id} nemá kategorii.")
        return None

    # Získání komplementárních kategorií z Neo4j
    complementary_categories_with_weights = await get_complementary_categories_from_neo4j_async(
        neo4j_driver, source_category, tenant_id
    )
    
    if not complementary_categories_with_weights:
        logger.warning(f"Nenalezeny komplementární kategorie pro '{source_category}'.")
        return None

    # Optimalizované vektorové vyhledávání s měřením času
    search_start_time = time.time()
    candidates_for_rerank = []
    processed_ids = set([source_qdrant_id])

    # Rozdělení candidate_limit podle vah kategorií
    total_weight = sum(weight for _, weight in complementary_categories_with_weights if weight > 0)
    if total_weight <= 0:
        return None

    limits_per_category = {}
    for category_name, weight in complementary_categories_with_weights:
        if weight <= 0:
            continue
        relative_weight = weight / total_weight
        cat_limit = max(1, int(round(candidate_limit * relative_weight)))
        limits_per_category[category_name] = cat_limit

    # Paralelní vyhledávání v kategoriích
    search_tasks = []
    for category_name, limit in limits_per_category.items():
        if limit <= 0 or not category_name or not category_name.strip():
            logger.warning(f"Přeskakuji nevalidní kategorii: '{category_name}' s limitem {limit}")
            continue

        # Lepší kategoriální filtrování
        exact_category = category_name.strip()
        with_prefix = f"Autodoplňky > {exact_category}"
        
        category_filter = models.Filter(
            must=[
                models.FieldCondition(
                    key="category",
                    match=models.MatchAny(any=[exact_category, with_prefix])
                )
            ],
            must_not=[
                models.HasIdCondition(has_id=list(processed_ids))
            ]
        )

        search_tasks.append({
            'task': qdrant_client.search(
                collection_name=products_collection,
                query_vector=(vector_name, source_vector),
                query_filter=category_filter,
                limit=limit,
                with_payload=True
            ),
            'category_name': exact_category
        })

    # Spuštění paralelních vyhledávání
    if search_tasks:
        search_futures = [task_info['task'] for task_info in search_tasks]
        search_results_list = await asyncio.gather(*search_futures, return_exceptions=True)
    else:
        search_results_list = []

    # Zpracování výsledků vyhledávání s lepším error handling
    candidates = []
    for i, result in enumerate(search_results_list):
        category_name = search_tasks[i]['category_name'] if i < len(search_tasks) else "neznámá"
        
        if isinstance(result, Exception):
            # Specifické handling různých typů chyb
            if "timed out" in str(result).lower():
                logger.warning(f"Timeout při vyhledávání v kategorii '{category_name}': {result}")
            elif "500" in str(result) or "internal server error" in str(result).lower():
                logger.warning(f"Server error při vyhledávání v kategorii '{category_name}': Qdrant přetížen")
            else:
                logger.warning(f"Chyba při vyhledávání v kategorii '{category_name}': {result}")
            continue
        
        for scored_point in result:
            cand_id = str(scored_point.payload.get("product_id") or 
                         scored_point.payload.get("original_id") or 
                         scored_point.id)
            
            if cand_id not in processed_ids:
                candidates.append(ProductCandidate(
                    product_id=cand_id,
                    payload=scored_point.payload,
                    qdrant_score=scored_point.score,
                    category=scored_point.payload.get("category", "")
                ))
                processed_ids.add(cand_id)

    search_time = time.time() - search_start_time
    metrics.qdrant_search_time += search_time

    if not candidates:
        logger.info(f"Žádní kandidáti pro {product_id}.")
        return None

    # Seřazení podle Qdrant skóre a omezení
    candidates.sort(key=lambda x: x.qdrant_score, reverse=True)
    candidates = candidates[:candidate_limit]

    logger.debug(f"Nalezeno {len(candidates)} kandidátů pro {product_id} za {search_time:.3f}s")

    return BatchProcessingRequest(
        source_product_id=product_id,
        source_info=source_payload,
        candidates=candidates
    )

async def save_complementary_results_batch(
    qdrant_client: AsyncQdrantClient,
    tenant_id: str,
    results: Dict[str, List[Tuple[str, float]]],
    gemini_model_name: str
) -> int:
    """Uloží výsledky batch processing do cache kolekce."""
    complementary_cache_collection = f"complementary_products_{tenant_id}"
    saved_count = 0
    
    points_to_save = []
    for product_id, complementary_results in results.items():
        try:
            source_qdrant_id = create_qdrant_id(product_id)
            
            complementary_ids = [item[0] for item in complementary_results]
            scores = [item[1] for item in complementary_results]
            
            payload_to_save = {
                "product_id": product_id,
                "complementary_ids": complementary_ids,
                "scores": scores,
                "computed_at": time.time(),
                "model_version": gemini_model_name,
                "batch_processed": True
            }
            
            points_to_save.append(models.PointStruct(
                id=source_qdrant_id,
                vector={},
                payload=payload_to_save
            ))
            
        except Exception as e:
            logger.error(f"Chyba při přípravě dat pro uložení produktu {product_id}: {e}")
            metrics.errors += 1

    # Batch uložení do Qdrantu
    if points_to_save:
        try:
            await qdrant_client.upsert(
                collection_name=complementary_cache_collection,
                points=points_to_save,
                wait=True
            )
            saved_count = len(points_to_save)
            logger.info(f"Uloženo {saved_count} výsledků batch processing do cache.")
            
        except Exception as e:
            logger.error(f"Chyba při batch uložení do cache: {e}", exc_info=True)
            metrics.errors += len(points_to_save)

    return saved_count

async def ensure_collection_exists(client: AsyncQdrantClient, collection_name: str):
    """Funkce pro zajištění existence kolekce."""
    try:
        collections = await client.get_collections()
        collection_names = [c.name for c in collections.collections]
        
        if collection_name in collection_names:
            logger.debug(f"Cache kolekce '{collection_name}' již existuje.")
        else:
            logger.warning(f"Cache kolekce '{collection_name}' nenalezena, pokouším se vytvořit...")
            try:
                await client.create_collection(
                    collection_name=collection_name,
                    vectors_config={} # Bez vektorů
                )
                logger.info(f"Cache kolekce '{collection_name}' úspěšně vytvořena.")
            except Exception as create_err:
                logger.error(f"Nepodařilo se vytvořit cache kolekci '{collection_name}': {create_err}", exc_info=True)
                raise # Chybu propagujeme dál, bez cache kolekce nemá smysl pokračovat
    except Exception as e:
        logger.error(f"Chyba při kontrole/vytváření kolekce '{collection_name}': {e}", exc_info=True)
        raise # Propagujeme chybu nahoru

# --- Masivní optimalizace parametrů pro rychlost ---
async def main_optimized(
    tenant_id: str, 
    product_ids: Optional[List[str]] = None, 
    concurrency_limit: int = 15,  # ZVÝŠENO z 5 na 15 pro rychlejší zpracování
    limit: int = None,
    batch_size: int = 7,  # ZVÝŠENO z 3 na 7 produktů v jednom Gemini volání
    stream_processing: bool = True  # Nové: streaming vs. načtení všech ID
):
    """MASIVNĚ OPTIMALIZOVANÁ hlavní funkce s vyšším výkonem."""
    logger.info(f"Spouštím MASIVNĚ OPTIMALIZOVANÝ výpočet komplementárních produktů pro tenanta: {tenant_id}")
    logger.info(f"⚡ HIGH PERFORMANCE MODE ⚡")
    logger.info(f"Batch size: {batch_size}, Concurrency: {concurrency_limit}, Streaming: {stream_processing}")
    
    # Inicializace metrik
    metrics.start_time = time.time()
    
    # Inicializace klientů s optimalizovanými timeouty
    neo4j_driver = None
    try:
        qdrant_client = get_custom_qdrant_async_client()
        gemini_client = get_gemini_client()
        neo4j_driver = await get_neo4j_driver()
        logger.info("Klienti úspěšně inicializováni s optimalizovanými timeouty.")
    except Exception as client_err:
        logger.error(f"Chyba při inicializaci klientů: {client_err}", exc_info=True)
        if neo4j_driver:
            await neo4j_driver.close()
        return

    # Načtení konfigurace tenanta
    tenant_config = get_tenant_yaml_config(tenant_id)
    if not tenant_config:
        if neo4j_driver:
            await neo4j_driver.close()
        return

    # Zajištění existence cache kolekce
    products_collection = f"real_products_{tenant_id}"
    complementary_cache_collection = f"complementary_products_{tenant_id}"
    try:
        await ensure_collection_exists(qdrant_client, complementary_cache_collection)
    except:
        if neo4j_driver:
            await neo4j_driver.close()
        return

    # Získání seznamu produktů k zpracování
    if product_ids:
        logger.info(f"Zpracovávám zadaný seznam {len(product_ids)} produktů.")
        products_to_process = list(product_ids)
        metrics.total_products = len(products_to_process)
    else:
        # Streaming načítání produktů (nové)
        if stream_processing:
            logger.info("Používám streaming načítání produktů...")
            products_to_process = await get_products_streaming(
                qdrant_client, products_collection, limit
            )
        else:
            # Původní způsob - načtení všech ID najednou
            logger.info("Načítám všechna produktová ID...")
            products_to_process = await get_all_product_ids(
                qdrant_client, products_collection, limit
            )
        
        metrics.total_products = len(products_to_process)

    if not products_to_process:
        logger.warning("Nebyly nalezeny produkty ke zpracování.")
        if neo4j_driver:
            await neo4j_driver.close()
        return

    # Cache kontrola a příprava batch požadavků
    cache_ttl_seconds = 30 * 24 * 60 * 60  # 30 dní
    products_to_compute = []
    
    logger.info(f"Kontroluji cache pro {len(products_to_process)} produktů...")
    for prod_id in products_to_process:
        if await is_cache_valid(qdrant_client, complementary_cache_collection, prod_id, cache_ttl_seconds):
            metrics.cache_hits += 1
        else:
            metrics.cache_misses += 1
            products_to_compute.append(prod_id)

    logger.info(f"Produktů k zpracování po kontrole cache: {len(products_to_compute)}")
    logger.info(f"Cache hit rate: {(metrics.cache_hits / max(1, len(products_to_process))) * 100:.1f}%")

    if not products_to_compute:
        logger.info("Všechny produkty mají platnou cache. Zpracování dokončeno.")
        metrics.log_summary()
        if neo4j_driver:
            await neo4j_driver.close()
        return

    # OPTIMALIZOVANÉ batch processing s agresivnějším semaforem
    semaphore = asyncio.Semaphore(concurrency_limit)
    success_count = 0
    total_batches = 0

    async def process_product_batch(product_batch: List[str]):
        """Optimalizovaně zpracuje dávku produktů pomocí batch Gemini API."""
        nonlocal success_count, total_batches
        total_batches += 1
        
        async with semaphore:
            try:
                # Krok 1: Připravit batch požadavky paralelně (s timeoutem)
                batch_prep_tasks = []
                for prod_id in product_batch:
                    batch_prep_tasks.append(
                        compute_and_save_complementary_optimized(
                            tenant_id, prod_id, qdrant_client, gemini_client, neo4j_driver
                        )
                    )
                
                # OPTIMALIZACE: Timeout pro batch přípravu
                try:
                    batch_requests = await asyncio.wait_for(
                        asyncio.gather(*batch_prep_tasks, return_exceptions=True),
                        timeout=30.0  # 30s timeout pro batch přípravu
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout při přípravě dávky {total_batches} (30s)")
                    metrics.errors += len(product_batch)
                    return 0
                
                # Filtrování úspěšných požadavků
                valid_requests = []
                for i, req in enumerate(batch_requests):
                    if isinstance(req, Exception):
                        logger.warning(f"Chyba při přípravě produktu {product_batch[i]}: {req}")
                        metrics.errors += 1
                    elif req is not None:
                        valid_requests.append(req)

                if not valid_requests:
                    logger.warning(f"Žádné platné požadavky v dávce {total_batches}")
                    return 0

                # Krok 2: RYCHLÝ Batch Gemini processing
                try:
                    gemini_results = await asyncio.wait_for(
                        compute_complementary_batch_with_gemini(
                            gemini_client, valid_requests, rerank_limit=15  # Sníženo z 20 na 15
                        ),
                        timeout=45.0  # 45s timeout pro Gemini
                    )
                except asyncio.TimeoutError:
                    logger.warning(f"Timeout při Gemini zpracování dávky {total_batches} (45s)")
                    metrics.errors += len(valid_requests)
                    return 0

                # Krok 3: Rychlé uložení výsledků
                if gemini_results:
                    saved_count = await save_complementary_results_batch(
                        qdrant_client, tenant_id, gemini_results, gemini_client.model_name
                    )
                    success_count += saved_count
                    metrics.processed_products += saved_count
                    
                    # Rychlé logování jen pro velké dávky
                    if total_batches % 10 == 0 or saved_count > 5:
                        logger.info(f"Dávka {total_batches}: zpracováno {len(valid_requests)} produktů, "
                                   f"uloženo {saved_count} výsledků")
                else:
                    logger.warning(f"Dávka {total_batches}: Gemini nevrátil žádné výsledky")

                # KRATŠÍ pauza mezi dávkami (optimalizace)
                await asyncio.sleep(0.1)  # Sníženo z 0.2s na 0.1s
                return len(valid_requests)

            except Exception as e:
                logger.error(f"Chyba při zpracování dávky {total_batches}: {e}")
                metrics.errors += len(product_batch)
                return 0

    # RYCHLEJŠÍ zpracování v dávkách
    batches = [products_to_compute[i:i + batch_size] 
               for i in range(0, len(products_to_compute), batch_size)]
    
    logger.info(f"⚡ RYCHLÉ ZPRACOVÁNÍ: {len(batches)} dávek po {batch_size} produktech...")
    
    # OPTIMALIZACE: Zpracování ve vlnách pro lepší throughput
    WAVE_SIZE = 50  # Zpracování po 50 dávkách současně
    total_processed = 0
    
    for wave_start in range(0, len(batches), WAVE_SIZE):
        wave_end = min(wave_start + WAVE_SIZE, len(batches))
        wave_batches = batches[wave_start:wave_end]
        
        logger.info(f"🌊 Vlna {wave_start//WAVE_SIZE + 1}: zpracovávám dávky {wave_start+1}-{wave_end}")
        
        batch_tasks = []
        for batch in wave_batches:
            batch_tasks.append(process_product_batch(batch))
        
        # Spuštění vlny batch tasků
        wave_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
        wave_processed = sum(result for result in wave_results if isinstance(result, int))
        total_processed += wave_processed
        
        # Progress update
        processed_pct = (wave_end / len(batches)) * 100
        logger.info(f"📊 Progress: {processed_pct:.1f}% ({wave_end}/{len(batches)} dávek)")
    
    # Vyhodnocení výsledků
    successful_batches = sum(1 for batch in batches if batch)
    
    logger.info(f"⚡ RYCHLÉ ZPRACOVÁNÍ DOKONČENO!")
    logger.info(f"Úspěšně zpracováno: {total_processed} produktů")
    
    # Výpis finálních metrik
    metrics.log_summary()

    # Uzavření Neo4j driveru
    if neo4j_driver:
        try:
            await neo4j_driver.close()
            logger.info("Neo4j driver uzavřen.")
        except Exception as e:
            logger.error(f"Chyba při uzavírání Neo4j driveru: {e}")

# --- Pomocné funkce pro optimalizovanou main ---

async def get_products_streaming(
    qdrant_client: AsyncQdrantClient, 
    collection_name: str, 
    limit: Optional[int]
) -> List[str]:
    """Streaming načítání produktů - menší memory footprint."""
    products = []
    offset = None
    limit_per_scroll = 1000  # Větší dávky pro efektivnost
    
    try:
        while True:
            if limit and len(products) >= limit:
                break
                
            scroll_result, next_offset = await qdrant_client.scroll(
                collection_name=collection_name,
                limit=min(limit_per_scroll, limit - len(products) if limit else limit_per_scroll),
                offset=offset,
                with_payload=["product_id", "original_id"],
                with_vectors=False
            )
            
            if not scroll_result:
                break

            for point in scroll_result:
                p_id = point.payload.get("product_id") or point.payload.get("original_id")
                if p_id and str(p_id) not in products:
                    products.append(str(p_id))
                    
                if limit and len(products) >= limit:
                    break

            offset = next_offset
            if offset is None or (limit and len(products) >= limit):
                break
                
            # Progress logging pro dlouhé načítání
            if len(products) % 5000 == 0:
                logger.info(f"Načteno {len(products)} produktových ID...")

        logger.info(f"Streaming načítání dokončeno: {len(products)} produktů")
        return products

    except Exception as e:
        logger.error(f"Chyba při streaming načítání produktů: {e}", exc_info=True)
        return products

async def get_all_product_ids(
    qdrant_client: AsyncQdrantClient, 
    collection_name: str, 
    limit: Optional[int]
) -> List[str]:
    """Původní způsob načítání všech ID najednou."""
    all_ids = set()
    offset = None
    limit_per_scroll = 500

    try:
        while True:
            scroll_result, next_offset = await qdrant_client.scroll(
                collection_name=collection_name,
                limit=limit_per_scroll,
                offset=offset,
                with_payload=["product_id", "original_id"],
                with_vectors=False
            )
            
            if not scroll_result:
                break

            for point in scroll_result:
                p_id = point.payload.get("product_id") or point.payload.get("original_id")
                if p_id:
                    all_ids.add(str(p_id))

            offset = next_offset
            if offset is None:
                break

        products_list = list(all_ids)
        if limit:
            products_list = products_list[:limit]
            
        logger.info(f"Načteno {len(products_list)} produktových ID")
        return products_list

    except Exception as e:
        logger.error(f"Chyba při načítání produktů: {e}", exc_info=True)
        return []

async def is_cache_valid(
    qdrant_client: AsyncQdrantClient,
    cache_collection: str,
    product_id: str,
    ttl_seconds: int
) -> bool:
    """Rychlá kontrola validity cache."""
    try:
        qdrant_id = create_qdrant_id(product_id)
        cache_points = await qdrant_client.retrieve(
            collection_name=cache_collection,
            ids=[qdrant_id],
            with_payload=["computed_at"]
        )
        
        if cache_points and cache_points[0].payload and "computed_at" in cache_points[0].payload:
            computed_at = cache_points[0].payload["computed_at"]
            if isinstance(computed_at, (int, float)):
                cache_age = time.time() - computed_at
                return cache_age < ttl_seconds
                
        return False
        
    except Exception:
        return False

# --- Zachovaná původní funkce pro kompatibilitu ---
async def compute_and_save_complementary(
    tenant_id: str, 
    product_id: str,
    qdrant_client: AsyncQdrantClient,
    gemini_client: genai.GenerativeModel,
    neo4j_driver,
    candidate_limit: int = 100,
    rerank_limit: int = 20,
    vector_name: str = "combined"
):
    """Původní funkce pro kompatibilitu s existujícím kódem."""
    logger.warning("Používáte původní funkci compute_and_save_complementary. Pro lepší výkon použijte main_optimized.")
    
    # Implementace se přesune do batch request a pak single processing
    batch_request = await compute_and_save_complementary_optimized(
        tenant_id, product_id, qdrant_client, gemini_client, neo4j_driver,
        candidate_limit, rerank_limit, vector_name
    )
    
    if not batch_request:
        return False
    
    # Single processing pomocí batch funkce
    batch_results = await compute_complementary_batch_with_gemini(
        gemini_client, [batch_request], rerank_limit
    )
    
    if batch_results and product_id in batch_results:
        # Uložení výsledku
        saved_count = await save_complementary_results_batch(
            qdrant_client, tenant_id, {product_id: batch_results[product_id]}, 
            gemini_client.model_name
        )
        return saved_count > 0
    
    return False

# --- Hlavní spouštěcí funkce (původní styl) ---
async def main(tenant_id: str, product_ids: Optional[List[str]] = None, concurrency_limit: int = 20, limit: int = None):
    """Původní hlavní funkce pro zpětnou kompatibilitu."""
    logger.info("Spouštím PŮVODNÍ verzi výpočtu (nedoporučeno pro produkci)")
    logger.info("Pro lepší výkon použijte --optimized flag")
    
    # Redirect na optimalizovanou verzi s batch_size=1 pro podobné chování
    await main_optimized(
        tenant_id=tenant_id,
        product_ids=product_ids,
        concurrency_limit=concurrency_limit,
        limit=limit,
        batch_size=1,  # Simulace původního chování
        stream_processing=False  # Původní způsob načítání
    )

if __name__ == "__main__":
    # --- Start Argparse Implementation ---
    parser = argparse.ArgumentParser(
        description="Výpočet komplementárních produktů s Gemini AI a Neo4j"
    )
    parser.add_argument(
        "--tenant", 
        type=str, 
        default="filsonstore",
        help="Tenant ID (výchozí: filsonstore)"
    )
    parser.add_argument(
        "--product-ids", 
        type=str, 
        nargs="*",
        help="Seznam specifických produktových ID k zpracování"
    )
    parser.add_argument(
        "--concurrency", 
        type=int, 
        default=15,  # ZVÝŠENO z 5 na 15 pro rychlejší zpracování
        help="Maximální počet současně běžících úloh (výchozí: 15, doporučeno max 20)"
    )
    parser.add_argument(
        "--limit", 
        type=int, 
        help="Limit celkového počtu produktů k zpracování"
    )
    
    # Nové optimalizované argumenty
    parser.add_argument(
        "--optimized", 
        action="store_true",
        help="Použít optimalizovanou verzi s batch processing (doporučeno)"
    )
    parser.add_argument(
        "--ultra", 
        action="store_true",
        help="🚀 ULTRA optimalizovaná verze - maximální rychlost! (DOPORUČENO PRO PRODUKCI)"
    )
    parser.add_argument(
        "--batch-size", 
        type=int, 
        default=ULTRA_BATCH_SIZE,  # Použije ultra konstantu
        help=f"Počet produktů zpracovaných v jednom Gemini volání (výchozí: {ULTRA_BATCH_SIZE}, max doporučeno: 20)"
    )
    parser.add_argument(
        "--ultra-concurrency", 
        type=int, 
        default=ULTRA_CONCURRENCY,
        help=f"Ultra agresivní paralelizace (výchozí: {ULTRA_CONCURRENCY}, max doporučeno: 30)"
    )
    parser.add_argument(
        "--streaming", 
        action="store_true",
        default=True,
        help="Používat streaming načítání produktů (menší memory footprint)"
    )
    parser.add_argument(
        "--no-streaming", 
        action="store_true",
        help="Vypnout streaming načítání (načíst všechna ID najednou)"
    )
    
    args = parser.parse_args()

    # Určení režimu zpracování s prioritou ultra > optimized > původní
    use_ultra = args.ultra
    use_optimized = args.optimized or not use_ultra  # Pokud není ultra, použije optimized
    streaming = args.streaming and not args.no_streaming
    
    tenant_to_process = args.tenant
    specific_product_ids = args.product_ids if args.product_ids else None

    # Výběr správné funkce s prioritou na ultra verzi
    if use_ultra:
        logger.info("🚀🚀🚀 SPOUŠTÍM ULTRA OPTIMALIZOVANOU VERZI 🚀🚀🚀")
        logger.info("⚡ Maximální rychlost s agresivními optimalizacemi!")
        logger.info(f"⚡ Batch size: {args.batch_size}")
        logger.info(f"⚡ Ultra concurrency: {args.ultra_concurrency}")
        logger.info(f"⚡ Streaming: {streaming}")
        logger.info(f"⚡ Candidate limit: {ULTRA_CANDIDATE_LIMIT}")
        logger.info(f"⚡ Rerank limit: {ULTRA_RERANK_LIMIT}")
        
        try:
            asyncio.run(main_ultra_optimized(
                tenant_to_process, 
                specific_product_ids, 
                args.ultra_concurrency,  # Použije ultra concurrency
                args.limit,
                batch_size=args.batch_size,
                stream_processing=streaming
            ))
        except KeyboardInterrupt:
            logger.info("🚀 Ultra optimalizovaný skript přerušen uživatelem (KeyboardInterrupt).")
    elif use_optimized:
        logger.info("=== SPOUŠTÍM OPTIMALIZOVANOU VERZI ===")
        logger.info(f"Batch size: {args.batch_size}")
        logger.info(f"Streaming: {streaming}")
        try:
            asyncio.run(main_optimized(
                tenant_to_process, 
                specific_product_ids, 
                args.concurrency, 
                args.limit,
                batch_size=args.batch_size,
                stream_processing=streaming
            ))
        except KeyboardInterrupt:
            logger.info("Optimalizovaný skript přerušen uživatelem (KeyboardInterrupt).")
    else:
        logger.info("=== SPOUŠTÍM PŮVODNÍ VERZI ===")
        logger.warning("Původní verze je pomalejší. Použijte --ultra nebo --optimized pro lepší výkon.")
        try:
            asyncio.run(main(tenant_to_process, specific_product_ids, args.concurrency, args.limit))
        except KeyboardInterrupt:
            logger.info("Původní skript přerušen uživatelem (KeyboardInterrupt).")
    # --- End Argparse Implementation ---