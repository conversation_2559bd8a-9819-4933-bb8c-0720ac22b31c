# 🚀 Ultra optimalizace compute_complementary.py

## 📊 Analýza současného stavu
- **Původní rychlost**: ~3.31 sekund/produkt = ~20+ hodin pro 12K produktů  
- **Cíl**: Snížit na <1 sekunda/produkt = ~3-4 hodiny celkem

## 🎯 Implementované optimalizace

### 1. **ULTRA Batch Processing**
```python
ULTRA_BATCH_SIZE = 15       # Zvýšeno z 7 na 15 produktů/Gemini volání
ULTRA_CONCURRENCY = 25      # Zvýšeno z 15 na 25 paralelních tasků  
ULTRA_CANDIDATE_LIMIT = 60  # Sníženo z 100 na 60 kandidátů
ULTRA_RERANK_LIMIT = 10     # Sníženo z 15 na 10 pro rychlejší Gemini
```

### 2. **Neo4j Cache předběžné načtení**
- **Před**: Neo4j dotaz pro každý produkt
- **Po**: Jed<PERSON>ázo<PERSON><PERSON> načtení všech kategorií do memory cache
- **Zlepšení**: Eliminace 12K+ Neo4j dotazů → 1 dotaz

### 3. **Bulk Cache kontrola**
```python
CACHE_BULK_CHECK_SIZE = 100  # Kontrola 100 produktů najednou
```
- **Před**: Sekvenční kontrola cache pro každý produkt
- **Po**: Batch kontrola cache pro 100 produktů současně
- **Zlepšení**: ~50x rychlejší cache validace

### 4. **Ultra kompaktní Gemini prompt**
- **Před**: Dlouhé detailed prompty s plnými daty
- **Po**: Minimální kompaktní JSON formát
- **Zkrácené názvy**: 60 znaků místo 200+
- **Zkrácené kategorie**: 30 znaků místo 100+
- **Zlepšení**: ~40% rychlejší Gemini response

### 5. **Agresivní paralelizace**
- **ULTRA_WAVE_SIZE = 80**: Zpracování 80 dávek současně
- **Kratší timeouty**: 20s příprava, 30s Gemini (místo 60s+)
- **Rychlejší pauzy**: 0.05s mezi vlnami (místo 0.2s)

### 6. **Memory a I/O optimalizace**
- **Streaming načítání produktů**: Menší memory footprint
- **Rychlejší filtrování**: Max 5 kategorií místo všech
- **Optimalizované Qdrant timeouty**: Vyšší connection limits

## 🚀 Použití ultra optimalizované verze

### Základní spuštění (DOPORUČENO)
```bash
cd batch_scripts
python compute_complementary.py --tenant filsonstore --ultra
```

### Pokročilé nastavení
```bash
python compute_complementary.py \
  --tenant filsonstore \
  --ultra \
  --batch-size 20 \
  --ultra-concurrency 30 \
  --limit 1000
```

### Testování optimalizací
```bash
python test_ultra_optimizations.py
```

## ⚙️ Parametry ultra režimu

| Parametr | Výchozí | Popis |
|----------|---------|-------|
| `--ultra` | False | Zapne ultra optimalizovaný režim |
| `--batch-size` | 15 | Produktů v jednom Gemini volání |
| `--ultra-concurrency` | 25 | Paralelních tasků současně |
| `--limit` | None | Omezení počtu produktů k zpracování |
| `--streaming` | True | Streaming načítání (menší memory) |

## 📈 Očekávané zlepšení výkonu

### Teoretické zlepšení
- **Neo4j cache**: ~90% úspora času pro Neo4j dotazy
- **Bulk cache**: ~50x rychlejší cache kontroly  
- **Kompaktní Gemini**: ~40% rychlejší AI processing
- **Větší batch size**: ~2x méně API volání
- **Vyšší concurrency**: ~1.5x lepší využití zdrojů

### Celkové očekávané zlepšení: **3-5x rychlejší**

### Odhad nové rychlosti
- **Ultra optimalizace**: ~0.7-1.0 sekund/produkt
- **Celkový čas pro 12K produktů**: ~2-3 hodiny
- **Zlepšení z 20+ hodin**: **85% úspora času**

## ⚠️ Důležité poznámky

### Monitorování zdrojů
```bash
# Sledování CPU/Memory během běhu
htop

# Sledování Qdrant výkonu
curl http://localhost:6333/metrics

# Sledování Neo4j výkonu  
cat logs/compute_complementary.log | grep "Neo4j"
```

### Ladění výkonu
1. **Nízký cache hit rate** → Zvyšte TTL cache
2. **Gemini timeouts** → Snižte batch-size
3. **Qdrant timeouts** → Snižte ultra-concurrency
4. **Memory issues** → Zapněte streaming (`--streaming`)

### Bezpečnostní limity
- **Max batch-size**: 20 (vyšší může způsobit Gemini timeouts)
- **Max concurrency**: 30 (vyšší může přetížit Qdrant)
- **Min timeout**: 15s (kratší může způsobit falešné chyby)

## 🧪 Testing & Validace

### Rychlý test výkonu
```bash
python test_ultra_optimizations.py
```
Porovná ultra vs. standardní optimalizace na vzorku 20 produktů.

### Validace výsledků
```bash
python compute_complementary.py --tenant filsonstore --product-ids "12345" "67890" --ultra
```
Test na konkrétních produktech pro ověření správnosti.

### Monitoring postupu
```bash
tail -f logs/compute_complementary.log | grep "Progress"
```
Sledování real-time postupu zpracování.

## 🎯 Nejlepší praktiky

### Pro maximální rychlost
```bash
python compute_complementary.py \
  --tenant filsonstore \
  --ultra \
  --batch-size 15 \
  --ultra-concurrency 25 \
  --streaming
```

### Pro stabilitu (pomalejší, ale spolehlivější)
```bash
python compute_complementary.py \
  --tenant filsonstore \
  --ultra \
  --batch-size 10 \
  --ultra-concurrency 15 \
  --streaming
```

### Pro testování (malý vzorek)
```bash
python compute_complementary.py \
  --tenant filsonstore \
  --ultra \
  --limit 100 \
  --batch-size 20
```

## 📝 Changelog optimalizací

### v2.0 - Ultra optimalizace
- ✅ Neo4j cache pre-loading
- ✅ Bulk cache kontroly  
- ✅ Ultra kompaktní Gemini prompts
- ✅ Agresivní batch processing (15 produktů)
- ✅ Vyšší concurrency (25 tasků)
- ✅ Wave processing (80 dávek současně)

### v1.0 - Základní optimalizace  
- ✅ Batch Gemini processing (7 produktů)
- ✅ Paralelní Qdrant search
- ✅ Basic cache mechanismus
- ✅ Optimalizované timeouty

## 🔧 Troubleshooting

### Častý problémy a řešení

**Problem**: Gemini timeouts
```
Solution: Snižte batch-size na 10-12
python compute_complementary.py --ultra --batch-size 10
```

**Problem**: Qdrant connection errors
```
Solution: Snižte concurrency na 15-20
python compute_complementary.py --ultra --ultra-concurrency 15
```

**Problem**: Memory issues
```
Solution: Zapněte streaming
python compute_complementary.py --ultra --streaming
```

**Problem**: Cache miss rate vysoký
```
Solution: Zkontrolujte TTL cache (30 dní default)
# V kódu: cache_ttl = 30 * 24 * 60 * 60
``` 