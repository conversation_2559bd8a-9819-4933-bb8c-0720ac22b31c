#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON>ý script pro opravu duplikátních definic dataclassů v compute_complementary.py
"""

def fix_dataclasses():
    # Čten<PERSON> souboru
    with open('compute_complementary.py', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # Nalezení řádků s duplikátními definicemi dataclassů
    # Hledáme "# --- Nová: Batch processing pro Gemini ---" až po "candidates: List[ProductCandidate]"
    
    new_lines = []
    skip_lines = False
    dataclasses_added = False
    
    for i, line in enumerate(lines):
        # Přidání dataclassů hned po konstantách ULTRA_*
        if "CACHE_BULK_CHECK_SIZE = 100" in line and not dataclasses_added:
            new_lines.append(line)
            new_lines.append("\n")
            new_lines.append("# --- Dataclassy pro batch processing (MUSÍ být před použitím) ---\n")
            new_lines.append("@dataclass\n")
            new_lines.append("class ProductCandidate:\n")
            new_lines.append("    \"\"\"Struktura pro kandidáta na komplementární produkt.\"\"\"\n")
            new_lines.append("    product_id: str\n")
            new_lines.append("    payload: Dict[str, Any]\n")
            new_lines.append("    qdrant_score: float\n")
            new_lines.append("    category: str\n")
            new_lines.append("\n")
            new_lines.append("@dataclass \n")
            new_lines.append("class BatchProcessingRequest:\n")
            new_lines.append("    \"\"\"Struktura pro batch požadavek na Gemini.\"\"\"\n")
            new_lines.append("    source_product_id: str\n")
            new_lines.append("    source_info: Dict[str, Any]\n")
            new_lines.append("    candidates: List[ProductCandidate]\n")
            new_lines.append("\n")
            dataclasses_added = True
            continue
            
        # Přeskočení duplikátních definic
        if "# --- Nová: Batch processing pro Gemini ---" in line:
            skip_lines = True
            continue
            
        if skip_lines and "async def compute_complementary_batch_with_gemini(" in line:
            skip_lines = False
            new_lines.append(line)
            continue
            
        if not skip_lines:
            new_lines.append(line)
    
    # Zápis opraveného souboru
    with open('compute_complementary_fixed.py', 'w', encoding='utf-8') as f:
        f.writelines(new_lines)
    
    print("✅ Opraveno! Nový soubor: compute_complementary_fixed.py")

if __name__ == "__main__":
    fix_dataclasses() 