import os
import sys
import asyncio
import logging
from dotenv import load_dotenv
import time

# Pu0159idu00e1nu00ed cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Nastavenu00ed logovu00e1nu00ed
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_langchain")

# Načtení .env souboru
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# Import potřebných funkcí
from core.clients import get_qdrant_async_client, get_gemini_client
from batch_scripts.compute_complementary import get_neo4j_driver, compute_complementary_ultra_fast_gemini
from batch_scripts.compute_complementary_langchain import compute_complementary_langchain

# Pro správné nastavení OPENAI_API_KEY
import os
os.environ["OPENAI_API_KEY"] = os.getenv("OPENAI_API_KEY", "")

async def test_langchain_vs_original(tenant_id: str, product_id: str):
    """
    Porovnání výsledků původní implementace a nové implementace s LangChain.
    
    Args:
        tenant_id: ID tenanta
        product_id: ID produktu k testovu00e1nu00ed
    """
    logger.info(f"Zau010du00ednu00e1m test pro produkt {product_id} (tenant: {tenant_id})")
    
    # Inicializace klientu016f
    qdrant_client = get_qdrant_async_client()
    gemini_client = get_gemini_client()
    neo4j_driver = await get_neo4j_driver()
    
    try:
        # Mu011bu0159enu00ed u010dasu pro LangChain implementaci
        langchain_start = time.time()
        
        langchain_request = await compute_complementary_langchain(
            tenant_id=tenant_id,
            product_id=product_id,
            qdrant_client=qdrant_client,
            gemini_client=gemini_client,
            neo4j_driver=neo4j_driver
        )
        
        langchain_time = time.time() - langchain_start
        
        if langchain_request:
            langchain_candidates = langchain_request.candidates
            logger.info(f"LangChain nau0161el {len(langchain_candidates)} kandidu00e1tu016f v {langchain_time:.2f}s")
            
            # Reranking pomocu00ed Gemini
            langchain_rerank_start = time.time()
            
            langchain_results = await compute_complementary_ultra_fast_gemini(
                gemini_client=gemini_client,
                batch_requests=[langchain_request]
            )
            
            langchain_rerank_time = time.time() - langchain_rerank_start
            
            # Vu00fdpis vu00fdsledku016f z LangChain
            if product_id in langchain_results:
                langchain_recommendations = langchain_results[product_id]
                logger.info(f"LangChain + Gemini reranking dokonu010den v {langchain_rerank_time:.2f}s")
                logger.info("Top 3 komplementau0159nu00ed produkty podle LangChain:")
                
                for i, (comp_id, score) in enumerate(langchain_recommendations[:3]):
                    payload = await get_product_payload(qdrant_client, tenant_id, comp_id)
                    if payload:
                        logger.info(f"  {i+1}. {payload.get('name', 'N/A')} (sku00f3re: {score:.2f})")
                        logger.info(f"     Kategorie: {payload.get('category', 'N/A')}")
            else:
                logger.warning(f"LangChain + Gemini nevru00e1til u017eu00e1dnu00e9 vu00fdsledky pro {product_id}")
        else:
            logger.warning(f"LangChain nenau0161el u017eu00e1dnu00e9 kandidu00e1ty pro {product_id}")
            
    except Exception as e:
        logger.error(f"Dou0161lo k chybu011b pu0159i testovu00e1nu00ed: {e}")
    finally:
        # Uzavu0159enu00ed klientu016f
        await qdrant_client.close()
        await neo4j_driver.close()


async def get_product_payload(client, tenant_id, product_id):
    """
    Zu00edsku00e1 payload produktu.
    """
    products_collection = f"real_products_{tenant_id}"
    try:
        from qdrant_client import models
        
        points = await client.retrieve(
            collection_name=products_collection,
            ids=[product_id],
            with_payload=True
        )
        
        if points and len(points) > 0:
            return points[0].payload
        return None
    except Exception as e:
        logger.error(f"Chyba pu0159i zu00edsku00e1vu00e1nu00ed produktu {product_id}: {e}")
        return None


if __name__ == "__main__":
    import argparse
    
    parser = argparse.ArgumentParser(description="Test LangChain implementace pro komplementau0159nu00ed produkty")
    parser.add_argument("--tenant", type=str, required=True, help="Tenant ID")
    parser.add_argument("--product", type=str, required=True, help="Product ID k testovu00e1nu00ed")
    
    args = parser.parse_args()
    
    asyncio.run(test_langchain_vs_original(args.tenant, args.product))
