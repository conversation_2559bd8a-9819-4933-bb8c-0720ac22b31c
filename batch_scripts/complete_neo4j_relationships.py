#!/usr/bin/env python3
"""
Kompletní Neo4j vztahy pro VŠECHNY kategorie filsonstore
"""

from neo4j import GraphDatabase
import time

def create_complete_relationships():
    """Vytvoří vztahy pro všechny kategorie."""
    
    relationships = {
        # POVINNÁ VÝBAVA - už máme
        "Autodoplňky > Povinná a doporučená výbava": [
            ("Autodoplňky > Dílna a garáž", 0.6, "Nářadí pro nouzové opravy a údržbu"),
            ("Autodoplňky > Elektrické doplňky", 0.2, "<PERSON><PERSON><PERSON>, na<PERSON><PERSON><PERSON><PERSON><PERSON>, výstražn<PERSON> světla"),
            ("Autodoplňky > Interiér vozidla", 0.1, "Bezpečnostní doplňky v kabině"),
            ("Autodoplňky > Autokosmetika", 0.1, "Údržba a čištění výstražných prvků")
        ],
        
        # OLEJE A MAZIVA - už máme
        "Autodoplňky > Oleje a maziva": [
            ("Autodoplňky > Dílna a garáž", 0.8, "Nástroje pro výměnu olejů a filtrů"),
            ("Autodoplňky > Autokosmetika", 0.2, "Čisticí prostředky pro servis")
        ],
        
        # DÍLNA A GARÁŽ - už máme
        "Autodoplňky > Dílna a garáž": [
            ("Autodoplňky > Oleje a maziva", 0.4, "Provozní kapaliny pro servis"),
            ("Autodoplňky > Povinná a doporučená výbava", 0.3, "Bezpečnostní prvky při opravách"),
            ("Autodoplňky > Elektrické doplňky", 0.2, "Elektrické nástroje a přístroje"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čisticí prostředky pro dílnu")
        ],
        
        # AUTOPOTAHY - už máme
        "Autodoplňky > Autopotahy": [
            ("Autodoplňky > Autokoberce", 0.6, "Kompletní ochrana interiéru"),
            ("Autodoplňky > Interiér vozidla", 0.2, "Doplňky a vylepšení interiéru"),
            ("Autodoplňky > Vany do zavazadlového prostoru", 0.1, "Rozšíření ochrany na kufr"),
            ("Autodoplňky > Autokosmetika", 0.1, "Péče o textilie a čištění")
        ],
        
        # AUTOKOBERCE - už máme
        "Autodoplňky > Autokoberce": [
            ("Autodoplňky > Autopotahy", 0.6, "Kompletní ochrana interiéru"),
            ("Autodoplňky > Vany do zavazadlového prostoru", 0.2, "Ochrana celého vozu"),
            ("Autodoplňky > Interiér vozidla", 0.1, "Doplňky interiéru"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čisticí prostředky")
        ],
        
        # VANY DO KUFRU - NOVÉ
        "Autodoplňky > Vany do zavazadlového prostoru": [
            ("Autodoplňky > Autokoberce", 0.5, "Kompletní ochrana vozu"),
            ("Autodoplňky > Autopotahy", 0.3, "Ochrana celého interiéru"),
            ("Autodoplňky > Interiér vozidla", 0.1, "Organizace zavazadlového prostoru"),
            ("Autodoplňky > Autokosmetika", 0.1, "Údržba plastových dílů")
        ],
        
        # INTERIÉR - NOVÉ
        "Autodoplňky > Interiér vozidla": [
            ("Autodoplňky > Autopotahy", 0.4, "Ochrana a vylepšení sedadel"),
            ("Autodoplňky > Autokoberce", 0.3, "Ochrana podlahy"),
            ("Autodoplňky > Elektrické doplňky", 0.2, "Elektronické doplňky"),
            ("Autodoplňky > Autokosmetika", 0.1, "Péče o interiér")
        ],
        
        # EXTERIÉR - NOVÉ
        "Autodoplňky > Exteriér vozidla": [
            ("Autodoplňky > Autokosmetika", 0.4, "Péče o lak a vnější povrchy"),
            ("Autodoplňky > Elektrické doplňky", 0.3, "Vnější osvětlení a elektronika"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Nástroje pro montáž"),
            ("Autodoplňky > Stěrače", 0.1, "Vnější funkční prvky")
        ],
        
        # ELEKTRICKÉ DOPLŇKY - NOVÉ
        "Autodoplňky > Elektrické doplňky": [
            ("Autodoplňky > Autobaterie", 0.4, "Napájení elektrických systémů"),
            ("Autodoplňky > Dílna a garáž", 0.3, "Elektronické nástroje"),
            ("Autodoplňky > Interiér vozidla", 0.2, "Vnitřní elektronické doplňky"),
            ("Autodoplňky > Autožárovky", 0.1, "Osvětlovací systémy")
        ],
        
        # AUTOBATERIE - NOVÉ
        "Autodoplňky > Autobaterie": [
            ("Autodoplňky > Elektrické doplňky", 0.5, "Elektrické spotřebiče a systémy"),
            ("Autodoplňky > Dílna a garáž", 0.3, "Nástroje pro práci s baterií"),
            ("Autodoplňky > Autožárovky", 0.2, "Osvětlení vozu")
        ],
        
        # AUTOŽÁROVKY - NOVÉ
        "Autodoplňky > Autožárovky": [
            ("Autodoplňky > Elektrické doplňky", 0.4, "Elektronické osvětlovací systémy"),
            ("Autodoplňky > Autobaterie", 0.3, "Napájení osvětlení"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Nástroje pro výměnu žárovek"),
            ("Autodoplňky > Exteriér vozidla", 0.1, "Vnější osvětlení")
        ],
        
        # AUTOKOSMETIKA - NOVÉ
        "Autodoplňky > Autokosmetika": [
            ("Autodoplňky > Exteriér vozidla", 0.4, "Péče o vnější vzhled"),
            ("Autodoplňky > Interiér vozidla", 0.3, "Čištění kabiny"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Čisticí nástroje a prostředky"),
            ("Autodoplňky > Autopotahy", 0.1, "Péče o textilie")
        ],
        
        # STĚRAČE - NOVÉ
        "Autodoplňky > Stěrače": [
            ("Autodoplňky > Autokosmetika", 0.4, "Čisticí prostředky pro skla"),
            ("Autodoplňky > Exteriér vozidla", 0.3, "Vnější funkční prvky"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Nástroje pro výměnu"),
            ("Autodoplňky > Provozní kapaliny", 0.1, "Nemrznoucí směsi")
        ],
        
        # PROVOZNÍ KAPALINY - NOVÉ
        "Autodoplňky > Provozní kapaliny": [
            ("Autodoplňky > Oleje a maziva", 0.5, "Motorové a převodové oleje"),
            ("Autodoplňky > Stěrače", 0.2, "Nemrznoucí směsi"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Nástroje pro doplňování"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čisticí prostředky")
        ],
        
        # STŘEŠNÍ NOSIČE - NOVÉ
        "Autodoplňky > Střešní nosiče": [
            ("Autodoplňky > Střešní boxy", 0.4, "Doplňky pro přepravu"),
            ("Autodoplňky > Nosiče kol na střechu", 0.3, "Specializované nosiče"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Nástroje pro montáž"),
            ("Autodoplňky > Příslušenství k nosičům a boxům", 0.1, "Doplňky nosičů")
        ],
        
        # STŘEŠNÍ BOXY - NOVÉ
        "Autodoplňky > Střešní boxy": [
            ("Autodoplňky > Střešní nosiče", 0.5, "Nosný systém pro boxy"),
            ("Autodoplňky > Příslušenství k nosičům a boxům", 0.3, "Doplňky a příslušenství"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Nástroje pro montáž")
        ],
        
        # NOSIČE KOL NA STŘECHU - NOVÉ
        "Autodoplňky > Nosiče kol na střechu": [
            ("Autodoplňky > Střešní nosiče", 0.6, "Nosný systém"),
            ("Autodoplňky > Příslušenství k nosičům a boxům", 0.2, "Doplňky nosičů"),
            ("Autodoplňky > Dílna a garáž", 0.2, "Nástroje pro montáž")
        ],
        
        # ZIMNÍ VÝBAVA - NOVÉ
        "Autodoplňky > Zimní výbava": [
            ("Autodoplňky > Provozní kapaliny", 0.4, "Nemrznoucí směsi"),
            ("Autodoplňky > Dílna a garáž", 0.3, "Nástroje pro zimní údržbu"),
            ("Autodoplňky > Stěrače", 0.2, "Zimní stěrače"),
            ("Autodoplňky > Autokosmetika", 0.1, "Zimní péče o auto")
        ]
    }
    
    return relationships

def apply_complete_relationships():
    """Aplikuje kompletní sadu vztahů."""
    
    try:
        driver = GraphDatabase.driver('bolt://localhost:7687', auth=('neo4j', 'Graph2025Secure!'))
        
        with driver.session() as session:
            print('🔧 PŘIDÁVÁNÍ KOMPLETNÍCH NEO4J VZTAHŮ')
            print('=' * 50)
            
            relationships = create_complete_relationships()
            
            # Počítání nových vztahů
            existing_count = session.run("MATCH ()-[r:RELATED_TO]->() RETURN count(r) as count").single()["count"]
            new_relationships_count = sum(len(targets) for targets in relationships.values()) - existing_count
            
            print(f'📊 Aktuální vztahy: {existing_count}')
            print(f'📊 Přidám nové vztahy: {new_relationships_count}')
            
            # Přidání pouze nových vztahů (bez smazání existujících)
            added_count = 0
            
            for source_category, targets in relationships.items():
                # Zkontroluj jestli už kategorie má vztahy
                existing = session.run('''
                    MATCH (s:Category_filsonstore {name: $source})-[r:RELATED_TO]->()
                    RETURN count(r) as count
                ''', source=source_category).single()["count"]
                
                if existing > 0:
                    print(f'⏭️  {source_category}: už má {existing} vztahů')
                    continue
                
                print(f'➕ Přidávám vztahy pro: {source_category}')
                
                for target, weight, reason in targets:
                    try:
                        # Vytvoř kategorie
                        session.run('''
                            MERGE (s:Category_filsonstore {name: $source})
                            MERGE (t:Category_filsonstore {name: $target})
                        ''', source=source_category, target=target)
                        
                        # Vytvoř vztah
                        session.run('''
                            MATCH (s:Category_filsonstore {name: $source})
                            MATCH (t:Category_filsonstore {name: $target})
                            CREATE (s)-[:RELATED_TO {
                                weight: $weight, 
                                reason: $reason,
                                tenant: 'filsonstore',
                                updated_at: datetime()
                            }]->(t)
                        ''', source=source_category, target=target, weight=weight, reason=reason)
                        
                        added_count += 1
                        print(f'   ✅ {target} (váha: {weight})')
                        
                    except Exception as e:
                        print(f'   ❌ Chyba při přidání {target}: {e}')
                
                time.sleep(0.5)  # Pauza mezi kategoriemi
            
            # Finální kontrola
            final_count = session.run("MATCH ()-[r:RELATED_TO]->() RETURN count(r) as count").single()["count"]
            categories_with_relationships = session.run('''
                MATCH (s:Category_filsonstore)-[r:RELATED_TO]->()
                RETURN count(DISTINCT s.name) as count
            ''').single()["count"]
            
            print(f'\n📊 FINÁLNÍ STATISTIKY:')
            print(f'   ✅ Přidáno nových vztahů: {added_count}')
            print(f'   📊 Celkem vztahů: {final_count}')
            print(f'   📂 Kategorií s vztahy: {categories_with_relationships}')
            
        driver.close()
        print(f'\n🎉 KOMPLETACE DOKONČENA!')
        
    except Exception as e:
        print(f'❌ Chyba: {e}')

if __name__ == "__main__":
    apply_complete_relationships()