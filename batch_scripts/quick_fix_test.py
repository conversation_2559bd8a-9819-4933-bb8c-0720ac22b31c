#!/usr/bin/env python3
"""
Quick fix test pro compute_complementary problémy
"""
import asyncio
import logging
import os
import sys

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("quick_fix")

async def test_fixed_gemini():
    """Test opraveného Gemini klienta."""
    logger.info("🧠 Testuji OPRAVENOU Gemini funkcionalitu...")
    
    try:
        import google.generativeai as genai
        
        # Oprava: Použití synchronního generate_content
        api_key = os.getenv('GOOGLE_API_KEY')
        if not api_key:
            logger.error("❌ GOOGLE_API_KEY není nastaven!")
            return False
            
        genai.configure(api_key=api_key)
        
        model = genai.GenerativeModel(
            model_name="models/gemini-2.0-flash",
            generation_config={
                "temperature": 0.3,
                "top_p": 0.8,
                "max_output_tokens": 1024,
            }
        )
        
        # Test prompt
        test_prompt = 'Return JSON: {"test": "success", "fix": "working"}'
        
        # OPRAVA: Použití synchronního volání
        logger.info("Testuji SYNCHRONNÍ Gemini volání...")
        response = model.generate_content(test_prompt)
        
        logger.info(f"✅ FIXED Gemini odpověď: {response.text[:100]}...")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testování Gemini: {e}")
        return False

async def test_neo4j_categories():
    """Test real kategorií z Neo4j."""
    logger.info("🗄️ Testuji REAL kategorie z Neo4j...")
    
    try:
        from compute_complementary import get_neo4j_driver
        
        neo4j_driver = await get_neo4j_driver()
        tenant_id = "filsonstore"
        
        # Query pro získání real kategorií
        category_label = f"Category_{tenant_id}"
        query = f"""
        MATCH (c:{category_label})
        RETURN c.name AS category_name
        ORDER BY c.name
        LIMIT 10
        """
        
        async with neo4j_driver.session() as session:
            result = await session.run(query)
            records = await result.data()
            
            logger.info(f"📋 Nalezeno {len(records)} real kategorií:")
            for i, record in enumerate(records):
                cat_name = record.get("category_name", "N/A")
                logger.info(f"  {i+1}. {cat_name}")
            
            # Test komplementárních vztahů
            if records:
                test_category = records[0].get("category_name")
                comp_query = f"""
                MATCH (source:{category_label} {{name: $category_name}})
                MATCH (source)-[r:RELATED_TO]->(target:{category_label})
                RETURN target.name AS target_name, r.weight AS weight
                ORDER BY r.weight DESC
                LIMIT 5
                """
                
                comp_result = await session.run(comp_query, category_name=test_category)
                comp_records = await comp_result.data()
                
                logger.info(f"🔗 Komplementární kategorie pro '{test_category}':")
                for record in comp_records:
                    target = record.get("target_name", "N/A")
                    weight = record.get("weight", 0.0)
                    logger.info(f"  -> {target} (váha: {weight})")
        
        await neo4j_driver.close()
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testování Neo4j: {e}")
        return False

async def test_single_product_fix():
    """Test zpracování jednoho produktu s opravami."""
    logger.info("🎯 Testuji zpracování jednoho produktu s OPRAVAMI...")
    
    try:
        from compute_complementary import (
            get_custom_qdrant_async_client,
            create_qdrant_id
        )
        
        qdrant_client = get_custom_qdrant_async_client()
        tenant_id = "filsonstore"
        products_collection = f"real_products_{tenant_id}"
        
        # Získej testovací produkt
        scroll_result, _ = await qdrant_client.scroll(
            collection_name=products_collection,
            limit=1,
            with_payload=["product_id", "category", "name"],
            with_vectors=False
        )
        
        if not scroll_result:
            logger.error("❌ Žádné produkty v kolekci")
            return False
            
        test_product = scroll_result[0]
        product_id = str(test_product.payload.get("product_id"))
        category = test_product.payload.get("category", "")
        name = test_product.payload.get("name", "")
        
        logger.info(f"🎯 Test produkt: ID={product_id}, Kategorie='{category}', Název='{name[:50]}...'")
        
        # Test získání payloadu
        qdrant_id = create_qdrant_id(product_id)
        payload_points = await qdrant_client.retrieve(
            collection_name=products_collection,
            ids=[qdrant_id],
            with_payload=True
        )
        
        if payload_points:
            logger.info("✅ Payload úspěšně získán")
        else:
            logger.error("❌ Payload nenalezen")
            return False
        
        # Test získání vektoru
        vector_points = await qdrant_client.retrieve(
            collection_name=products_collection,
            ids=[qdrant_id],
            with_vectors=True
        )
        
        if vector_points and vector_points[0].vector:
            vector = vector_points[0].vector
            if isinstance(vector, dict) and 'combined' in vector:
                logger.info(f"✅ Combined vektor nalezen (dimenze: {len(vector['combined'])})")
            elif isinstance(vector, list):
                logger.info(f"✅ List vektor nalezen (dimenze: {len(vector)})")
            else:
                logger.warning(f"⚠️ Neočekávaný typ vektoru: {type(vector)}")
        else:
            logger.error("❌ Vektor nenalezen")
            return False
        
        logger.info("✅ Test základních funkcí úspěšný")
        return True
        
    except Exception as e:
        logger.error(f"❌ Chyba při testování produktu: {e}")
        return False

async def main():
    """Spustí všechny testy s opravami."""
    logger.info("🚀 SPOUŠTÍM QUICK FIX TESTY 🚀")
    
    results = []
    
    # Test 1: Gemini fix
    gemini_ok = await test_fixed_gemini()
    results.append(("Gemini fix", gemini_ok))
    
    # Test 2: Neo4j kategorie
    neo4j_ok = await test_neo4j_categories()
    results.append(("Neo4j kategorie", neo4j_ok))
    
    # Test 3: Produkt zpracování
    product_ok = await test_single_product_fix()
    results.append(("Produkt zpracování", product_ok))
    
    # Shrnutí
    logger.info("=" * 50)
    logger.info("📊 SHRNUTÍ TESTŮ:")
    
    success_count = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        logger.info(f"  {test_name}: {status}")
        if result:
            success_count += 1
    
    logger.info(f"📈 Úspěšnost: {success_count}/{len(results)} testů")
    
    if success_count == len(results):
        logger.info("🎉 Všechny testy prošły! Opravy fungují!")
        return True
    else:
        logger.warning(f"⚠️ {len(results) - success_count} testů selhalo")
        return False

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        if result:
            print("\n🎉 QUICK FIX TESTY ÚSPĚŠNÉ!")
        else:
            print("\n❌ Některé testy selhaly")
    except KeyboardInterrupt:
        print("\nTest přerušen uživatelem")
    except Exception as e:
        print(f"\n❌ Kritická chyba: {e}") 