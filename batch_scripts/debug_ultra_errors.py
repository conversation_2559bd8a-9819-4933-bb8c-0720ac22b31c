#!/usr/bin/env python3
"""
Debug script pro identifikaci problémů v ultra optimalizaci
"""
import asyncio
import logging
import os
import sys
import traceback

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Setup detailního loggingu
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("debug_ultra")

async def debug_single_product():
    """Debug zpracování jednoho produktu pro identifikaci chyb."""
    
    try:
        from compute_complementary import (
            compute_and_save_complementary_ultra_optimized,
            get_custom_qdrant_async_client,
            get_gemini_client,
            get_neo4j_driver
        )
        
        logger.info("🔍 DEBUGGING jednotlivého produktu...")
        
        # Inicializace klientů
        logger.info("Inicializuji klienty...")
        try:
            qdrant_client = get_custom_qdrant_async_client()
            logger.info("✅ Qdrant klient inicializován")
        except Exception as e:
            logger.error(f"❌ Chyba Qdrant klienta: {e}")
            return
            
        try:
            gemini_client = get_gemini_client()
            logger.info("✅ Gemini klient inicializován")
        except Exception as e:
            logger.error(f"❌ Chyba Gemini klienta: {e}")
            return
            
        try:
            neo4j_driver = await get_neo4j_driver()
            logger.info("✅ Neo4j driver inicializován")
        except Exception as e:
            logger.error(f"❌ Chyba Neo4j driveru: {e}")
            return
        
        # Získání testovacího produktu
        tenant_id = "filsonstore"
        products_collection = f"real_products_{tenant_id}"
        
        logger.info("Načítám testovací produkt...")
        try:
            scroll_result, _ = await qdrant_client.scroll(
                collection_name=products_collection,
                limit=1,
                with_payload=["product_id"],
                with_vectors=False
            )
            
            if not scroll_result:
                logger.error("❌ Žádné produkty v kolekci")
                return
                
            test_product_id = str(scroll_result[0].payload.get("product_id"))
            logger.info(f"✅ Testovací produkt: {test_product_id}")
            
        except Exception as e:
            logger.error(f"❌ Chyba při načítání produktu: {e}")
            return
        
        # Detailní debug zpracování
        logger.info(f"🔍 Debugging zpracování produktu {test_product_id}...")
        
        try:
            # Krok po kroku debugging
            result = await compute_and_save_complementary_ultra_optimized(
                tenant_id=tenant_id,
                product_id=test_product_id,
                qdrant_client=qdrant_client,
                gemini_client=gemini_client,
                neo4j_driver=neo4j_driver
            )
            
            if result:
                logger.info(f"✅ Úspěšně vytvořen BatchProcessingRequest s {len(result.candidates)} kandidáty")
                
                # Test Gemini
                logger.info("🧠 Testuji Gemini zpracování...")
                from compute_complementary import compute_complementary_ultra_fast_gemini
                
                gemini_results = await compute_complementary_ultra_fast_gemini(
                    gemini_client, [result], 5
                )
                
                if gemini_results:
                    logger.info(f"✅ Gemini zpracování úspěšné: {len(gemini_results)} výsledků")
                    for product_id, candidates in gemini_results.items():
                        logger.info(f"  {product_id}: {len(candidates)} komplementárních produktů")
                else:
                    logger.warning("⚠️ Gemini nevrátil žádné výsledky")
                    
            else:
                logger.warning("⚠️ compute_and_save_complementary_ultra_optimized vrátil None")
                
        except Exception as e:
            logger.error(f"❌ Kritická chyba při zpracování: {e}")
            logger.error(f"Stack trace: {traceback.format_exc()}")
        
        # Cleanup
        if neo4j_driver:
            await neo4j_driver.close()
            
    except Exception as e:
        logger.error(f"❌ Kritická chyba v debug: {e}")
        logger.error(f"Stack trace: {traceback.format_exc()}")

async def debug_data_availability():
    """Debug dostupnosti dat pro zpracování."""
    
    logger.info("🔍 DEBUGGING dostupnosti dat...")
    
    try:
        from compute_complementary import get_custom_qdrant_async_client
        
        qdrant_client = get_custom_qdrant_async_client()
        tenant_id = "filsonstore"
        
        # Kontrola kolekcí
        collections = await qdrant_client.get_collections()
        collection_names = [c.name for c in collections.collections]
        
        logger.info(f"📊 Dostupné kolekce: {collection_names}")
        
        products_collection = f"real_products_{tenant_id}"
        if products_collection not in collection_names:
            logger.error(f"❌ Kolekce {products_collection} neexistuje!")
            return
            
        # Kontrola počtu produktů
        try:
            count_result = await qdrant_client.count(products_collection)
            logger.info(f"📊 Počet produktů v {products_collection}: {count_result.count}")
        except Exception as e:
            logger.error(f"❌ Chyba při počítání produktů: {e}")
            
        # Kontrola vzorku produktů
        logger.info("🔍 Kontroluji vzorek produktů...")
        try:
            scroll_result, _ = await qdrant_client.scroll(
                collection_name=products_collection,
                limit=5,
                with_payload=True,
                with_vectors=True
            )
            
            for i, point in enumerate(scroll_result):
                product_id = point.payload.get("product_id", "N/A")
                category = point.payload.get("category", "N/A")
                has_vector = point.vector is not None
                vector_type = type(point.vector).__name__ if point.vector else "None"
                
                logger.info(f"  Produkt {i+1}: ID={product_id}, Kategorie='{category}', Vektor={has_vector} ({vector_type})")
                
                if isinstance(point.vector, dict):
                    logger.info(f"    Vektorové klíče: {list(point.vector.keys())}")
                    
        except Exception as e:
            logger.error(f"❌ Chyba při kontrole vzorku: {e}")
            
    except Exception as e:
        logger.error(f"❌ Kritická chyba v debug dat: {e}")
        logger.error(f"Stack trace: {traceback.format_exc()}")

async def debug_neo4j_cache():
    """Debug Neo4j cache a komplementárních kategorií."""
    
    logger.info("🔍 DEBUGGING Neo4j cache...")
    
    try:
        from compute_complementary import (
            get_neo4j_driver,
            preload_neo4j_cache,
            get_complementary_categories_cached
        )
        
        neo4j_driver = await get_neo4j_driver()
        tenant_id = "filsonstore"
        
        # Test preload cache
        logger.info("Testuji preload Neo4j cache...")
        await preload_neo4j_cache(neo4j_driver, tenant_id)
        
        # Test cache lookup
        test_categories = ["Hodinky", "Náramky", "Brýle"]
        
        for category in test_categories:
            complementary = await get_complementary_categories_cached(category)
            logger.info(f"Kategorie '{category}': {len(complementary)} komplementárních kategorií")
            for comp_cat, weight in complementary[:3]:  # Top 3
                logger.info(f"  -> {comp_cat} (váha: {weight})")
                
        await neo4j_driver.close()
        
    except Exception as e:
        logger.error(f"❌ Chyba v debug Neo4j: {e}")
        logger.error(f"Stack trace: {traceback.format_exc()}")

async def debug_gemini_connection():
    """Debug Gemini API připojení a základní funkcionalitu."""
    
    logger.info("🔍 DEBUGGING Gemini API...")
    
    try:
        from compute_complementary import get_gemini_client
        
        gemini_client = get_gemini_client()
        
        # Test základního volání
        test_prompt = "Return JSON: {\"test\": \"success\"}"
        
        logger.info("Testuji základní Gemini volání...")
        try:
            response = await gemini_client.generate_content_async(test_prompt)
            logger.info(f"✅ Gemini odpověď: {response.text[:100]}...")
        except Exception as e:
            logger.error(f"❌ Chyba Gemini API: {e}")
            
            # Kontrola API klíče
            import os
            gemini_key = os.getenv('GOOGLE_API_KEY')
            if gemini_key:
                logger.info(f"✅ GOOGLE_API_KEY je nastaven (délka: {len(gemini_key)})")
            else:
                logger.error("❌ GOOGLE_API_KEY není nastaven!")
                
    except Exception as e:
        logger.error(f"❌ Kritická chyba v debug Gemini: {e}")
        logger.error(f"Stack trace: {traceback.format_exc()}")

if __name__ == "__main__":
    logger.info("🚀 SPOUŠTÍM ULTRA DEBUG 🚀")
    
    try:
        # Spuštění všech debug testů
        asyncio.run(debug_data_availability())
        asyncio.run(debug_neo4j_cache()) 
        asyncio.run(debug_gemini_connection())
        asyncio.run(debug_single_product())
        
        logger.info("✅ DEBUG DOKONČEN")
        
    except KeyboardInterrupt:
        logger.info("Debug přerušen uživatelem")
    except Exception as e:
        logger.error(f"❌ Kritická chyba v debug: {e}")
        logger.error(f"Stack trace: {traceback.format_exc()}") 