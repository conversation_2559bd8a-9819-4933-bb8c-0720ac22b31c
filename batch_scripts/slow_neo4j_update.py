#!/usr/bin/env python3
"""
Pomalá a opatrná aplikace Neo4j změn s pausami
"""

from neo4j import GraphDatabase
import time
import json

def load_prepared_relationships():
    """Načte připravené vztahy z předchozího skriptu."""
    
    relationships = {
        "Autodoplňky > Povinná a doporučená výbava": [
            ("Autodoplňky > Dílna a garáž", 0.6, "Nářadí pro nouzové opravy a údržbu"),
            ("Autodoplňky > Elektrické doplňky", 0.2, "<PERSON><PERSON><PERSON>, na<PERSON><PERSON><PERSON><PERSON><PERSON>, výstražné světla"),
            ("Autodoplňky > Interiér vozidla", 0.1, "Bezpečnostní doplňky v kabině"),
            ("Autodoplňky > Autokosmetika", 0.1, "Údržba a čištění výstražných prvků")
        ],
        
        "Autodoplň<PERSON> > Oleje a maziva": [
            ("Autodoplňky > Dílna a garáž", 0.8, "Nástroje pro výměnu olejů a filtrů"),
            ("Autodoplňky > Autokosmetika", 0.2, "Čisticí prostředky pro servis")
        ],
        
        "Autodoplňky > Dílna a garáž": [
            ("Autodoplňky > Oleje a maziva", 0.4, "Provozní kapaliny pro servis"),
            ("Autodoplňky > Povinná a doporučená výbava", 0.3, "Bezpečnostní prvky při opravách"),
            ("Autodoplňky > Elektrické doplňky", 0.2, "Elektrické nástroje a přístroje"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čisticí prostředky pro dílnu")
        ],
        
        "Autodoplňky > Autopotahy": [
            ("Autodoplňky > Autokoberce", 0.6, "Kompletní ochrana interiéru"),
            ("Autodoplňky > Interiér vozidla", 0.2, "Doplňky a vylepšení interiéru"),
            ("Autodoplňky > Vany do zavazadlového prostoru", 0.1, "Rozšíření ochrany na kufr"),
            ("Autodoplňky > Autokosmetika", 0.1, "Péče o textilie a čištění")
        ],
        
        "Autodoplňky > Autokoberce": [
            ("Autodoplňky > Autopotahy", 0.6, "Kompletní ochrana interiéru"),
            ("Autodoplňky > Vany do zavazadlového prostoru", 0.2, "Ochrana celého vozu"),
            ("Autodoplňky > Interiér vozidla", 0.1, "Doplňky interiéru"),
            ("Autodoplňky > Autokosmetika", 0.1, "Čisticí prostředky")
        ]
    }
    
    return relationships

def try_connect_neo4j():
    """Zkusí různé přihlašovací údaje pro Neo4j."""
    
    credentials = [
        ('neo4j', 'Graph2025Secure!'),
        ('neo4j', 'Graph2025Secure'),
        ('neo4j', 'password'),
        ('neo4j', 'admin'),
        ('neo4j', 'gallitec')
    ]
    
    for username, password in credentials:
        try:
            print(f"🔄 Zkouším {username}:{'(prázdné)' if not password else password}")
            
            if not password:
                # Zkusím bez hesla
                driver = GraphDatabase.driver('bolt://localhost:7687')
            else:
                driver = GraphDatabase.driver('bolt://localhost:7687', auth=(username, password))
            
            # Test připojení
            with driver.session() as session:
                result = session.run("RETURN 1 as test")
                record = result.single()
                if record and record["test"] == 1:
                    print(f"✅ Úspěšné připojení s {username}:{password}")
                    return driver
                    
        except Exception as e:
            error_msg = str(e)
            if "authentication" in error_msg.lower():
                print(f"❌ Špatné přihlašovací údaje")
            elif "rate" in error_msg.lower():
                print(f"⏱️  Rate limit - čekám 30s...")
                time.sleep(30)
            else:
                print(f"❌ Chyba: {error_msg}")
            
            time.sleep(2)  # Pauza mezi pokusy
    
    print("❌ Všechny pokusy o připojení selhaly")
    return None

def clear_existing_relationships(session):
    """Pomalu vymaže existující vztahy."""
    print("🗑️  Mažu existující vztahy...")
    
    try:
        # Zjistím kolik vztahů existuje
        result = session.run("MATCH (s:Category_filsonstore)-[r:RELATED_TO]->(t:Category_filsonstore) RETURN count(r) as count")
        count = result.single()["count"]
        print(f"📊 Nalezeno {count} existujících vztahů")
        
        if count > 0:
            # Smažu po dávkách
            batch_size = 10
            deleted_total = 0
            
            while deleted_total < count:
                result = session.run(f"""
                    MATCH (s:Category_filsonstore)-[r:RELATED_TO]->(t:Category_filsonstore) 
                    WITH r LIMIT {batch_size}
                    DELETE r
                    RETURN count(r) as deleted
                """)
                
                deleted_batch = result.single()["deleted"]
                deleted_total += deleted_batch
                print(f"🗑️  Smazáno {deleted_batch} vztahů (celkem {deleted_total}/{count})")
                
                if deleted_batch == 0:
                    break
                    
                time.sleep(1)  # Pauza mezi dávkami
        
        print(f"✅ Všechny staré vztahy smazány")
        
    except Exception as e:
        print(f"❌ Chyba při mazání: {e}")
        
def add_relationships_slowly(session, relationships):
    """Pomalu přidá nové vztahy."""
    print("➕ Přidávám nové vztahy...")
    
    total_relationships = sum(len(targets) for targets in relationships.values())
    added_count = 0
    
    for source_category, targets in relationships.items():
        print(f"📂 Zpracovávám: {source_category}")
        
        for target, weight, reason in targets:
            try:
                # Vytvoř nebo aktualizuj kategorie
                session.run("""
                    MERGE (s:Category_filsonstore {name: $source})
                    MERGE (t:Category_filsonstore {name: $target})
                """, source=source_category, target=target)
                
                time.sleep(0.5)  # Pauza mezi operacemi
                
                # Vytvoř vztah
                session.run("""
                    MATCH (s:Category_filsonstore {name: $source})
                    MATCH (t:Category_filsonstore {name: $target})
                    CREATE (s)-[:RELATED_TO {
                        weight: $weight, 
                        reason: $reason,
                        tenant: 'filsonstore',
                        updated_at: datetime()
                    }]->(t)
                """, source=source_category, target=target, weight=weight, reason=reason)
                
                added_count += 1
                percentage = (added_count / total_relationships) * 100
                print(f"   ✅ {target} (váha: {weight}) - {added_count}/{total_relationships} ({percentage:.1f}%)")
                
                time.sleep(0.5)  # Pauza mezi vztahy
                
            except Exception as e:
                print(f"   ❌ Chyba při přidání {target}: {e}")
        
        print(f"   📊 Kategorie {source_category} dokončena")
        time.sleep(2)  # Delší pauza mezi kategoriemi
    
    print(f"✅ Přidáno celkem {added_count} vztahů")

def verify_changes(session):
    """Ověří že změny byly aplikovány správně."""
    print("🔍 Ověřuji aplikované změny...")
    
    # Zkontroluj kritické kategorie
    critical_checks = [
        "Autodoplňky > Povinná a doporučená výbava",
        "Autodoplňky > Oleje a maziva"
    ]
    
    for category in critical_checks:
        print(f"\n📂 {category}:")
        
        result = session.run("""
            MATCH (s:Category_filsonstore {name: $category})-[r:RELATED_TO]->(t:Category_filsonstore)
            RETURN t.name as target, r.weight as weight, r.reason as reason
            ORDER BY r.weight DESC
        """, category=category)
        
        records = result.data()
        
        if records:
            total_weight = sum(record['weight'] for record in records)
            print(f"   Celková váha: {total_weight}")
            
            for record in records:
                target = record['target']
                weight = record['weight']
                reason = record['reason']
                percentage = (weight / total_weight) * 100 if total_weight > 0 else 0
                candidates = max(1, int(round(80 * weight / total_weight)))
                
                print(f"   → {target}: {weight} ({percentage:.1f}% = {candidates} kandidátů)")
                print(f"     {reason}")
        else:
            print(f"   ❌ Žádné vztahy nenalezeny!")
    
    # Celkové statistiky
    result = session.run("MATCH (s:Category_filsonstore)-[r:RELATED_TO]->(t:Category_filsonstore) RETURN count(r) as total")
    total = result.single()["total"]
    print(f"\n📊 Celkem vztahů v databázi: {total}")

def main():
    """Hlavní funkce pro pomalou aplikaci změn."""
    
    print("🐌 POMALÁ APLIKACE NEO4J ZMĚN")
    print("=" * 50)
    
    # Načtení vztahů
    relationships = load_prepared_relationships()
    print(f"📋 Načteno {len(relationships)} kategorií s vztahy")
    
    # Připojení k Neo4j
    print(f"\n🔌 Připojování k Neo4j...")
    driver = try_connect_neo4j()
    
    if not driver:
        print("❌ Nepodařilo se připojit k Neo4j")
        return False
    
    try:
        with driver.session() as session:
            # Smazání starých vztahů
            clear_existing_relationships(session)
            time.sleep(3)
            
            # Přidání nových vztahů
            add_relationships_slowly(session, relationships)
            time.sleep(3)
            
            # Ověření změn
            verify_changes(session)
            
        print(f"\n🎉 AKTUALIZACE ÚSPĚŠNĚ DOKONČENA!")
        print(f"✅ K tažným lanům se nyní doporučuje nářadí místo autopotahů")
        print(f"✅ Všechny kategorie mají logické cross-sell vztahy")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba během aktualizace: {e}")
        return False
        
    finally:
        driver.close()
        print(f"🔌 Připojení k Neo4j uzavřeno")

if __name__ == "__main__":
    success = main()
    
    if success:
        print(f"\n🚀 Nyní můžeš testovat vylepšené doporučení!")
        print(f"💡 Vymaž cache a spusť test na produktech 131, 132")
    else:
        print(f"\n⚠️ Aktualizace se nezdařila, zkus to později")