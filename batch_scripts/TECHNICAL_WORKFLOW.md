# 🔧 Technické Workflow: Výpočet Komplementárních Produktů

## 📋 Přehled Systému

Systém vypočítává komplementární produkty pro celý katalog pomocí:
- **Qdrant** - vektorová databáze produktů
- **Neo4j** - graf kate<PERSON> a jejich vztahů  
- **Gemini AI** - reranking a filtrování kandidátů
- **<PERSON><PERSON> systém** - optimalizace opakovaných spuštění

---

## 🚀 Hlavní Workflow (main_optimized)

```mermaid
graph TD
    A[Start: python compute_complementary.py] --> B[Inicializace klientů]
    B --> C[Načtení konfigurace tenanta]
    C --> D[Zajištění cache kolekce]
    D --> E{Zadaná product_ids?}
    
    E -->|Ano| F[Použít zadaná ID]
    E -->|Ne| G{Streaming mode?}
    
    G -->|Ano| H[get_products_streaming]
    G -->|Ne| I[get_all_product_ids]
    
    H --> J[Seznam produktů k zpracování]
    I --> J
    F --> J
    
    J --> K[Cache kontrola - is_cache_valid]
    K --> L[Rozdělení: cache hit/miss]
    L --> M[Batch processing produktů]
    
    M --> N[process_product_batch]
    N --> O[Výsledky a metriky]
    O --> P[Uzavření Neo4j driveru]
    P --> Q[Konec]

    style A fill:#e1f5fe
    style Q fill:#c8e6c9
    style M fill:#fff3e0
    style K fill:#f3e5f5
```

---

## 🔄 Detailní Kroky

### **1. Inicializace (Start)**
```python
# Klienti
qdrant_client = get_custom_qdrant_async_client()
gemini_client = get_gemini_client()  
neo4j_driver = await get_neo4j_driver()

# Kolekce
products_collection = f"real_products_{tenant_id}"
cache_collection = f"complementary_products_{tenant_id}"
```

### **2. Načítání Produktů**

#### **2a. Streaming Mode (výchozí)**
```python
async def get_products_streaming():
    products = []
    offset = None
    limit_per_scroll = 1000
    
    while True:
        scroll_result, next_offset = await qdrant_client.scroll(
            collection_name=collection_name,
            limit=limit_per_scroll,
            offset=offset,
            with_payload=["product_id", "original_id"]
        )
        
        if not scroll_result: break
        
        for point in scroll_result:
            p_id = point.payload.get("product_id") or point.payload.get("original_id")
            if p_id: products.append(str(p_id))
            
        offset = next_offset
        if offset is None: break
```

#### **2b. Batch Mode**
```python
async def get_all_product_ids():
    # Načte všechna ID najednou do paměti
    # Rychlejší, ale více paměti
```

### **3. Cache Kontrola**
```python
async def is_cache_valid(product_id: str, ttl_seconds: int = 30*24*60*60):
    qdrant_id = create_qdrant_id(product_id)
    cache_points = await qdrant_client.retrieve(
        collection_name=cache_collection,
        ids=[qdrant_id],
        with_payload=["computed_at"]
    )
    
    if cache_points and "computed_at" in cache_points[0].payload:
        cache_age = time.time() - cache_points[0].payload["computed_at"]
        return cache_age < ttl_seconds
    return False
```

---

## 🎯 Batch Processing Workflow

```mermaid
graph TD
    A[process_product_batch] --> B[Semaphore limit]
    B --> C[Příprava batch požadavků]
    C --> D[compute_and_save_complementary_optimized]
    
    D --> E[Načtení source produktu]
    E --> F[Neo4j - komplementární kategorie]
    F --> G[Qdrant search - kandidáti]
    G --> H[Vytvoření BatchProcessingRequest]
    
    H --> I[compute_complementary_batch_with_gemini]
    I --> J[Gemini AI reranking]
    J --> K[save_complementary_results_batch]
    K --> L[Uložení do cache]

    style A fill:#e3f2fd
    style I fill:#fff3e0
    style J fill:#f1f8e9
    style K fill:#fce4ec
```

### **Krok 4: Příprava Batch Požadavků**

```python
async def compute_and_save_complementary_optimized(product_id: str):
    # 4.1 Načtení zdrojového produktu
    source_payload = await _get_product_payload_async(
        qdrant_client, products_collection, product_id
    )
    
    # 4.2 Získání komplementárních kategorií z Neo4j
    complementary_categories = await get_complementary_categories_from_neo4j_async(
        neo4j_driver, source_payload.get("category"), tenant_id
    )
    
    # 4.3 Vektorové vyhledávání kandidátů v Qdrantu
    all_candidates = []
    for category, weight in complementary_categories:
        candidates = await qdrant_client.search(
            collection_name=products_collection,
            query_vector=source_vector,
            query_filter=Filter(must=[
                FieldCondition(key="category", match=MatchValue(value=category))
            ]),
            limit=candidate_limit
        )
        
        for candidate in candidates:
            all_candidates.append(ProductCandidate(
                product_id=candidate.payload.get("product_id"),
                payload=candidate.payload,
                qdrant_score=candidate.score,
                category=category
            ))
    
    # 4.4 Vytvoření batch požadavku
    return BatchProcessingRequest(
        source_product_id=product_id,
        source_info=source_payload,
        candidates=all_candidates
    )
```

### **Krok 5: Gemini AI Batch Processing**

```python
async def compute_complementary_batch_with_gemini(
    batch_requests: List[BatchProcessingRequest]
):
    # 5.1 Příprava promptu pro více produktů najednou
    combined_prompt = """
    Analyzuj tyto produkty a jejich kandidáty na komplementární produkty.
    Pro každý produkt vyber 5-10 nejvhodnějších komplementárních produktů.
    
    PRODUKTY K ANALÝZE:
    """
    
    for req in batch_requests:
        combined_prompt += f"""
        
        === PRODUKT ID: {req.source_product_id} ===
        Název: {req.source_info.get('name')}
        Kategorie: {req.source_info.get('category')}
        Popis: {req.source_info.get('description', '')[:200]}...
        
        KANDIDÁTI:
        """
        
        for i, candidate in enumerate(req.candidates[:20]):
            combined_prompt += f"""
            {i+1}. ID: {candidate.product_id}
               Název: {candidate.payload.get('name')}
               Kategorie: {candidate.category}
               Skóre: {candidate.qdrant_score:.3f}
            """
    
    # 5.2 Volání Gemini API
    response = await gemini_client.generate_content_async(combined_prompt)
    
    # 5.3 Parsování odpovědi
    return parse_gemini_batch_response(response.text, batch_requests)
```

### **Krok 6: Uložení Výsledků**

```python
async def save_complementary_results_batch(
    results: Dict[str, List[Tuple[str, float]]]
):
    points_to_upsert = []
    
    for source_product_id, complementary_list in results.items():
        qdrant_id = create_qdrant_id(source_product_id)
        
        complementary_ids = [comp[0] for comp in complementary_list]
        scores = [comp[1] for comp in complementary_list]
        
        payload = {
            "main_product_id": source_product_id,
            "complementary_ids": complementary_ids,
            "scores": scores,
            "computed_at": time.time(),
            "gemini_model": gemini_model_name,
            "total_candidates": len(complementary_list)
        }
        
        point = PointStruct(
            id=qdrant_id,
            vector={},  # Prázdný vektor pro cache
            payload=payload
        )
        points_to_upsert.append(point)
    
    # Batch upsert do cache kolekce
    await qdrant_client.upsert(
        collection_name=cache_collection,
        points=points_to_upsert,
        wait=True
    )
```

---

## 📊 Paralelizace a Optimalizace

### **Concurrency Model**
```python
semaphore = asyncio.Semaphore(concurrency_limit)  # Např. 20

async def process_product_batch(product_batch: List[str]):
    async with semaphore:
        # Max 20 batch požadavků současně
        # Každý batch = 5 produktů v jednom Gemini volání
        pass
```

### **Memory Management**
- **Streaming**: Načítá produkty po 1000 kusech
- **Batch size**: 5 produktů na jeden Gemini prompt
- **Cache TTL**: 30 dní pro vypočtené výsledky

### **API Rate Limiting**
- **Gemini**: Max 20 současných volání
- **Pauza**: 0.2s mezi batch požadavky
- **Retry**: Automatické opakování při chybách

---

## 🔍 Data Flow Diagram

```mermaid
sequenceDiagram
    participant Main as main_optimized
    participant Qdrant as Qdrant DB
    participant Neo4j as Neo4j Graph
    participant Gemini as Gemini AI
    participant Cache as Cache Collection

    Main->>Qdrant: get_products_streaming()
    Qdrant-->>Main: product_ids[]
    
    Main->>Cache: is_cache_valid() for each product
    Cache-->>Main: valid/invalid flags
    
    Main->>Main: filter products_to_compute
    
    loop Batch Processing (5 products)
        Main->>Qdrant: get source product details
        Qdrant-->>Main: product payload + vector
        
        Main->>Neo4j: get_complementary_categories()
        Neo4j-->>Main: category relationships
        
        Main->>Qdrant: search candidates by categories
        Qdrant-->>Main: candidate products
        
        Main->>Gemini: batch_request (5 products)
        Gemini-->>Main: reranked recommendations
        
        Main->>Cache: save_results_batch()
        Cache-->>Main: success confirmation
    end
    
    Main->>Main: log_summary() metrics
```

---

## ⚡ Performance Metriky

```python
@dataclass
class ProcessingMetrics:
    total_products: int = 0
    processed_products: int = 0
    cache_hits: int = 0
    cache_misses: int = 0
    gemini_api_calls: int = 0
    gemini_batch_calls: int = 0
    total_gemini_time: float = 0.0
    avg_gemini_time: float = 0.0
    qdrant_search_time: float = 0.0
    neo4j_query_time: float = 0.0
    errors: int = 0
    start_time: float = 0.0
```

### **Typické Výsledky**
- **Cache hit rate**: 90%+ při opakovaném spuštění
- **Products per second**: 5-15 (závisí na API limitu)
- **Gemini batch efficiency**: 5x rychlejší než jednotlivé volání
- **Memory footprint**: 90% redukce při streaming mode

---

## 🎯 Praktické Spuštění

### **Testovací Run**
```bash
python batch_scripts/compute_complementary.py \
  --tenant filsonstore \
  --optimized \
  --limit 100 \
  --batch-size 3 \
  --concurrency 10
```

### **Produkční Run**
```bash
python batch_scripts/compute_complementary.py \
  --tenant filsonstore \
  --optimized \
  --batch-size 8 \
  --concurrency 25 \
  --streaming
```

### **Monitoring**
```bash
# Sledování logů
tail -f batch_scripts/logs/compute_complementary.log

# Kontrola cache kolekce
curl "http://localhost:6333/collections/complementary_products_filsonstore"
```

Tento workflow efektivně zpracuje **celý katalog** s optimálním využitím všech zdrojů! 🚀 