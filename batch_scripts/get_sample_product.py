import asyncio
from qdrant_client import AsyncQdrantClient

async def get_sample_product():
    try:
        # Inicializace klienta
        client = AsyncQdrantClient()
        tenant_id = "filsonstore"
        
        # Získání jednoho produktu z kolekce
        scroll_result, _ = await client.scroll(
            collection_name=f'real_products_{tenant_id}',
            limit=5,  # Získáme 5 produktů pro výběr
            with_payload=True
        )
        
        if scroll_result:
            print("Nalezeno produktů:", len(scroll_result))
            for i, product in enumerate(scroll_result):
                product_id = product.payload.get("product_id")
                name = product.payload.get("name")
                category = product.payload.get("category")
                print(f"{i+1}. Product ID: {product_id}")
                print(f"   Name: {name}")
                print(f"   Category: {category}")
                print("---")
        else:
            print("Žádné produkty nenalezeny")
    except Exception as e:
        print(f"Chyba: {e}")
    finally:
        # <PERSON>zavř<PERSON><PERSON> klienta
        await client.close()

if __name__ == "__main__":
    asyncio.run(get_sample_product())
