#!/usr/bin/env python3
"""
Testovací script pro ověření ultra optimalizací compute_complementary.py
"""
import asyncio
import logging
import time
import os
import sys
from typing import List

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger("test_ultra")

async def test_ultra_performance():
    """Test výkonu ultra optimalizace na malém vzorku produktů."""
    
    # Import funkcí z compute_complementary
    from compute_complementary import (
        main_ultra_optimized, 
        main_optimized, 
        get_custom_qdrant_async_client
    )
    
    tenant_id = "filsonstore"
    
    # Získání několik testovacích produktů
    logger.info("🧪 Načítám testovací produkty...")
    try:
        qdrant_client = get_custom_qdrant_async_client()
        products_collection = f"real_products_{tenant_id}"
        
        # Získání 50 náhodných produktů pro test
        scroll_result, _ = await qdrant_client.scroll(
            collection_name=products_collection,
            limit=50,
            with_payload=["product_id"],
            with_vectors=False
        )
        
        if not scroll_result:
            logger.error("Nelze načíst testovací produkty")
            return
            
        test_product_ids = []
        for point in scroll_result:
            p_id = point.payload.get("product_id")
            if p_id:
                test_product_ids.append(str(p_id))
        
        logger.info(f"✅ Načteno {len(test_product_ids)} testovacích produktů")
        
        if len(test_product_ids) < 10:
            logger.warning("Málo testovacích produktů, test nemusí být reprezentativní")
        
        # Omezíme na prvních 20 produktů pro rychlý test
        test_sample = test_product_ids[:20]
        logger.info(f"🎯 Testujeme na vzorku {len(test_sample)} produktů")
        
        # Test 1: Ultra optimalizovaná verze
        logger.info("\n🚀 === TEST 1: ULTRA OPTIMALIZOVANÁ VERZE ===")
        start_time_ultra = time.time()
        
        try:
            await main_ultra_optimized(
                tenant_id=tenant_id,
                product_ids=test_sample,
                concurrency_limit=25,
                batch_size=15,
                stream_processing=True
            )
            ultra_time = time.time() - start_time_ultra
            ultra_speed = len(test_sample) / ultra_time if ultra_time > 0 else 0
            
            logger.info(f"⚡ ULTRA verze: {ultra_time:.2f}s celkem")
            logger.info(f"⚡ ULTRA rychlost: {ultra_speed:.2f} produktů/sekunda")
            
        except Exception as e:
            logger.error(f"Chyba v ultra verzi: {e}")
            ultra_time = float('inf')
            ultra_speed = 0
        
        # Krátká pauza mezi testy
        await asyncio.sleep(2)
        
        # Test 2: Standardní optimalizovaná verze
        logger.info("\n📊 === TEST 2: STANDARDNÍ OPTIMALIZOVANÁ VERZE ===")
        start_time_optimized = time.time()
        
        try:
            await main_optimized(
                tenant_id=tenant_id,
                product_ids=test_sample,
                concurrency_limit=15,
                batch_size=7,
                stream_processing=True
            )
            optimized_time = time.time() - start_time_optimized
            optimized_speed = len(test_sample) / optimized_time if optimized_time > 0 else 0
            
            logger.info(f"📊 Optimalizovaná verze: {optimized_time:.2f}s celkem")
            logger.info(f"📊 Optimalizovaná rychlost: {optimized_speed:.2f} produktů/sekunda")
            
        except Exception as e:
            logger.error(f"Chyba v optimalizované verzi: {e}")
            optimized_time = float('inf')
            optimized_speed = 0
        
        # Porovnání výsledků
        logger.info("\n🏆 === POROVNÁNÍ VÝSLEDKŮ ===")
        
        if ultra_time < float('inf') and optimized_time < float('inf'):
            speedup = optimized_time / ultra_time if ultra_time > 0 else 0
            speed_improvement = ((ultra_speed - optimized_speed) / optimized_speed * 100) if optimized_speed > 0 else 0
            
            logger.info(f"🚀 Ultra je {speedup:.1f}x rychlejší než standardní optimalizace")
            logger.info(f"⚡ Zlepšení rychlosti: {speed_improvement:.1f}%")
            
            if speedup > 1.5:
                logger.info("✅ ULTRA optimalizace vykazuje významné zlepšení!")
            elif speedup > 1.2:
                logger.info("✅ ULTRA optimalizace vykazuje mírné zlepšení")
            else:
                logger.warning("⚠️  ULTRA optimalizace nevykazuje očekávané zlepšení")
                
            # Odhad času pro všechny produkty
            if ultra_speed > 0:
                estimated_products_count = 12000  # Odhad celkového počtu produktů
                estimated_time_hours = estimated_products_count / ultra_speed / 3600
                logger.info(f"📈 Odhad času pro {estimated_products_count} produktů s ULTRA: {estimated_time_hours:.1f} hodin")
                
        else:
            logger.error("❌ Test selhal, nelze porovnat výsledky")
            
    except Exception as e:
        logger.error(f"Kritická chyba v testu: {e}", exc_info=True)
    
    finally:
        # Cleanup
        try:
            await qdrant_client.close()
        except:
            pass

async def test_cache_performance():
    """Test výkonu cache mechanismů."""
    logger.info("\n🗄️  === TEST CACHE VÝKONU ===")
    
    from compute_complementary import (
        bulk_cache_check,
        get_custom_qdrant_async_client,
        is_cache_valid
    )
    
    try:
        qdrant_client = get_custom_qdrant_async_client()
        tenant_id = "filsonstore"
        cache_collection = f"complementary_products_{tenant_id}"
        
        # Získání nějakých produktových ID pro test
        products_collection = f"real_products_{tenant_id}"
        scroll_result, _ = await qdrant_client.scroll(
            collection_name=products_collection,
            limit=200,
            with_payload=["product_id"],
            with_vectors=False
        )
        
        test_ids = [str(point.payload.get("product_id")) for point in scroll_result 
                   if point.payload.get("product_id")][:100]
        
        if not test_ids:
            logger.warning("Nelze získat ID pro cache test")
            return
            
        logger.info(f"Testujeme cache pro {len(test_ids)} produktů")
        
        # Test 1: Bulk cache check
        logger.info("🚀 Testujeme BULK cache kontrolu...")
        start_time = time.time()
        
        valid_ids, invalid_ids = await bulk_cache_check(
            qdrant_client, cache_collection, test_ids, 30 * 24 * 60 * 60
        )
        
        bulk_time = time.time() - start_time
        bulk_speed = len(test_ids) / bulk_time if bulk_time > 0 else 0
        
        logger.info(f"✅ Bulk cache check: {bulk_time:.3f}s pro {len(test_ids)} ID")
        logger.info(f"⚡ Bulk rychlost: {bulk_speed:.1f} kontrol/sekunda")
        logger.info(f"📊 Nalezeno v cache: {len(valid_ids)}, chybí: {len(invalid_ids)}")
        
        # Test 2: Jednotlivé cache kontroly (pro porovnání)
        logger.info("📊 Testujeme JEDNOTLIVÉ cache kontroly...")
        start_time = time.time()
        
        single_valid_count = 0
        for test_id in test_ids[:20]:  # Jen prvních 20 pro rychlost
            if await is_cache_valid(qdrant_client, cache_collection, test_id, 30 * 24 * 60 * 60):
                single_valid_count += 1
        
        single_time = time.time() - start_time
        single_speed = 20 / single_time if single_time > 0 else 0
        
        logger.info(f"📊 Jednotlivé kontroly: {single_time:.3f}s pro 20 ID")
        logger.info(f"📊 Jednotlivá rychlost: {single_speed:.1f} kontrol/sekunda")
        
        # Porovnání
        if bulk_speed > 0 and single_speed > 0:
            speedup = bulk_speed / single_speed
            logger.info(f"🚀 Bulk cache je {speedup:.1f}x rychlejší než jednotlivé kontroly")
            
    except Exception as e:
        logger.error(f"Chyba v cache testu: {e}", exc_info=True)

if __name__ == "__main__":
    logger.info("🧪 SPOUŠTÍM TESTY ULTRA OPTIMALIZACÍ 🧪")
    
    try:
        # Spuštění testů
        asyncio.run(test_ultra_performance())
        asyncio.run(test_cache_performance())
        
        logger.info("\n✅ VŠECHNY TESTY DOKONČENY ✅")
        
    except KeyboardInterrupt:
        logger.info("Testy přerušeny uživatelem")
    except Exception as e:
        logger.error(f"Kritická chyba v testech: {e}", exc_info=True) 