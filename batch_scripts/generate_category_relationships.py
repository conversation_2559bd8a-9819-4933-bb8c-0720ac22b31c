#!/usr/bin/env python3
"""
Skript pro generování vztahů mezi kategoriemi pomocí LLM.
LLM je použit k ohodnocení komplementarity kategorií na škále 0-1,
kde 0 znamená žádný vztah a 1 maximální komplementaritu.
"""

import os
import sys
import asyncio
import logging
import time
from typing import List, Dict, Tuple, Optional, Any
from collections import defaultdict
from dotenv import load_dotenv
import json
import argparse

# Přidání cesty k rootu projektu
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..'))
sys.path.insert(0, project_root)

# Nastavení logování
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("category_relationships")

# Načtení .env souboru
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# Import potřebných funkcí
from qdrant_client import QdrantClient
from neo4j import GraphDatabase
from core.clients import get_openai_client
from openai import AsyncOpenAI
from core.config import settings

# Globální proměnné
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "Graph2025Secure!")

# Hlavní kategorie pro Apple produkty (jabkolevne)
MAIN_APPLE_CATEGORIES = [
    "iPhone",
    "iPad",
    "Mac",
    "Apple Watch",
    "Apple TV",
    "AirPods",
    "Příslušenství"
]

async def get_categories_from_qdrant(tenant_id: str) -> List[str]:
    """Získá kategorie z Qdrantu pro daného tenanta."""
    collection_name = f"real_products_{tenant_id}"
    logger.info(f"Načítám kategorie z kolekce {collection_name}")
    
    try:
        client = QdrantClient(host='localhost', port=6333)
        
        # Kontrola existence kolekce
        collections = client.get_collections().collections
        collection_names = [col.name for col in collections]
        if collection_name not in collection_names:
            logger.error(f"Kolekce {collection_name} neexistuje v Qdrantu")
            return []
        
        # Načtení všech produktů postupně
        all_categories = set()
        offset = None
        limit = 100
        
        while True:
            results = client.scroll(
                collection_name=collection_name,
                limit=limit,
                offset=offset,
                with_payload=['category']
            )
            
            points, next_offset = results
            
            for point in points:
                category = point.payload.get('category')
                if category and isinstance(category, str):
                    all_categories.add(category)
                    
                    # Přidáme také všechny nadřazené kategorie
                    if ' > ' in category:
                        parts = category.split(' > ')
                        for i in range(1, len(parts)):
                            parent = ' > '.join(parts[:i])
                            all_categories.add(parent)
            
            if next_offset is None or len(points) == 0:
                break
                
            offset = next_offset
            logger.info(f"Načteno {len(all_categories)} kategorií zatím")
        
        logger.info(f"Celkem nalezeno {len(all_categories)} kategorií")
        return sorted(list(all_categories))
        
    except Exception as e:
        logger.error(f"Chyba při načítání kategorií z Qdrantu: {e}")
        return []

async def analyze_category_relationships(categories: List[str], tenant_id: str) -> Dict[str, List[Tuple[str, float, str]]]:
    """Použije LLM k analýze vztahů mezi kategoriemi a jejich ohodnocení."""
    
    # Inicializace klienta OpenAI
    client = await get_openai_client()
    if not client:
        logger.error("Nelze inicializovat OpenAI klienta")
        client = AsyncOpenAI(api_key=os.getenv("OPENAI_API_KEY"))
    
    all_relationships = {}
    batch_size = 5  # Počet kategorií v jednom dotazu na LLM
    
    # Pro každou kategorii analyzujeme vztahy k ostatním
    total_categories = len(categories)
    for i, source_category in enumerate(categories):
        logger.info(f"Analyzuji kategorii {i+1}/{total_categories}: {source_category}")
        
        # Rozdělíme ostatní kategorie do dávek
        target_categories = [c for c in categories if c != source_category]
        target_batches = [target_categories[j:j+batch_size] for j in range(0, len(target_categories), batch_size)]
        
        relationships_for_source = []
        
        for batch in target_batches:
            # Sestavíme prompt pro LLM
            prompt = f"""
Zanalyzuj vztahy mezi kategorií produktů "{source_category}" a následujícími kategoriemi z obchodu s produkty Apple ({tenant_id}):

{', '.join(batch)}

Pro každou kategorii urči:
1. Míru komplementarity na škále 0.0 až 1.0, kde 0.0 znamená žádný vztah a 1.0 znamená maximální komplementaritu.
2. Stručný důvod, proč produkty z těchto kategorií mohou být komplementární.

Odpověz ve formátu JSON:
{{
  "relationships": [
    {{
      "target_category": "Název kategorie",
      "complementarity_score": 0.X,
      "reason": "Stručný důvod"
    }}
  ]
}}

Příklady komplementarity:
- Produkty, které se často kupují společně (iPhone a pouzdro)
- Produkty, které se vzájemně doplňují funkčně (MacBook a externí disk)
- Příslušenství, které zlepšuje použití hlavního produktu (AirPods a nabíjecí pouzdro)

Pokud je komplementarita nízká (méně než 0.2), uveď to jako důvod.
"""
            
            try:
                # Dotaz na LLM
                response = await client.chat.completions.create(
                    model="gpt-4o",  # Použijeme nejlepší dostupný model
                    messages=[
                        {"role": "system", "content": "Jsi expert na analýzu vztahů mezi produktovými kategoriemi."},
                        {"role": "user", "content": prompt}
                    ],
                    response_format={"type": "json_object"},
                    temperature=0.2  # Nízká teplota pro konzistentní výsledky
                )
                
                # Zpracování odpovědi
                response_text = response.choices[0].message.content
                try:
                    response_data = json.loads(response_text)
                    batch_relationships = response_data.get('relationships', [])
                    
                    for rel in batch_relationships:
                        target = rel.get('target_category')
                        score = float(rel.get('complementarity_score', 0))
                        reason = rel.get('reason', 'Bez uvedeného důvodu')
                        
                        # Přidáme pouze vztahy s dostatečnou komplementaritou
                        if score >= 0.2:  # Minimální práh pro vytvoření vztahu
                            relationships_for_source.append((target, score, reason))
                            logger.info(f"  → {target}: {score:.2f} - {reason}")
                    
                except json.JSONDecodeError as e:
                    logger.error(f"Chyba při zpracování odpovědi LLM: {e}")
                    logger.error(f"Surová odpověď: {response_text}")
                
                # Krátká pauza mezi požadavky na API
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"Chyba při volání OpenAI API: {e}")
                await asyncio.sleep(5)  # Delší pauza v případě chyby
        
        # Seřadíme vztahy podle komplementarity
        relationships_for_source.sort(key=lambda x: x[1], reverse=True)
        
        # Uložíme vztahy pro aktuální kategorii
        all_relationships[source_category] = relationships_for_source
        
        # Průběžné ukládání výsledků pro případ pádu
        save_checkpoint(all_relationships, tenant_id)
        
        # Krátká pauza mezi kategoriemi
        await asyncio.sleep(2)
    
    return all_relationships

def save_checkpoint(relationships: Dict[str, List[Tuple[str, float, str]]], tenant_id: str):
    """Uloží průběžné výsledky do souboru."""
    try:
        checkpoint_file = f"category_relationships_{tenant_id}_checkpoint.json"
        
        # Převedeme data do formátu JSON
        json_data = {}
        for source, targets in relationships.items():
            json_data[source] = [(target, score, reason) for target, score, reason in targets]
        
        with open(checkpoint_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, ensure_ascii=False, indent=2)
            
        logger.info(f"Checkpoint uložen do {checkpoint_file}")
    except Exception as e:
        logger.error(f"Chyba při ukládání checkpointu: {e}")

def load_checkpoint(tenant_id: str) -> Dict[str, List[Tuple[str, float, str]]]:
    """Načte uložené výsledky ze souboru."""
    try:
        checkpoint_file = f"category_relationships_{tenant_id}_checkpoint.json"
        
        if not os.path.exists(checkpoint_file):
            logger.info(f"Checkpoint soubor {checkpoint_file} nenalezen")
            return {}
        
        with open(checkpoint_file, 'r', encoding='utf-8') as f:
            json_data = json.load(f)
        
        # Převedeme zpět na původní formát
        relationships = {}
        for source, targets in json_data.items():
            relationships[source] = [(target, score, reason) for target, score, reason in targets]
        
        logger.info(f"Načten checkpoint s {len(relationships)} kategoriemi")
        return relationships
    except Exception as e:
        logger.error(f"Chyba při načítání checkpointu: {e}")
        return {}

async def update_neo4j_relationships(relationships: Dict[str, List[Tuple[str, float, str]]], tenant_id: str):
    """Aktualizuje vztahy v Neo4j na základě analýzy LLM."""
    try:
        logger.info(f"Připojuji se k Neo4j databázi na {NEO4J_URI}")
        driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        
        category_label = f"Category_{tenant_id}"
        
        with driver.session() as session:
            # 1. Nejprve vytvoříme všechny kategorie (pokud ještě neexistují)
            all_categories = set(relationships.keys())
            for targets in relationships.values():
                for target, _, _ in targets:
                    all_categories.add(target)
            
            logger.info(f"Vytvářím {len(all_categories)} kategorií v Neo4j")
            for category in all_categories:
                session.run(f"""
                    MERGE (c:{category_label} {{name: $name}})
                """, name=category)
            
            # 2. Smažeme existující vztahy RELATED_TO
            logger.info("Mažu existující vztahy RELATED_TO")
            session.run(f"""
                MATCH (a:{category_label})-[r:RELATED_TO]->(b:{category_label})
                DELETE r
            """)
            
            # 3. Vytvoříme nové vztahy
            created_relationships = 0
            logger.info("Vytvářím nové vztahy RELATED_TO")
            
            for source, targets in relationships.items():
                for target, weight, reason in targets:
                    session.run(f"""
                        MATCH (a:{category_label} {{name: $source}})
                        MATCH (b:{category_label} {{name: $target}})
                        CREATE (a)-[r:RELATED_TO {{weight: $weight, reason: $reason}}]->(b)
                    """, source=source, target=target, weight=weight, reason=reason)
                    
                    created_relationships += 1
            
            logger.info(f"Vytvořeno celkem {created_relationships} vztahů")
        
        driver.close()
        logger.info("Neo4j aktualizace dokončena")
        return True
    except Exception as e:
        logger.error(f"Chyba při aktualizaci Neo4j: {e}")
        return False

async def main():
    """Hlavní funkce pro generování a aktualizaci vztahů mezi kategoriemi."""
    parser = argparse.ArgumentParser(description="Generování vztahů mezi kategoriemi pomocí LLM")
    parser.add_argument("--tenant", type=str, default="jabkolevne", help="ID tenanta")
    parser.add_argument("--reload", action="store_true", help="Znovu načíst kategorie z Qdrantu")
    parser.add_argument("--analyze", action="store_true", help="Znovu analyzovat vztahy pomocí LLM")
    parser.add_argument("--update-neo4j", action="store_true", help="Aktualizovat Neo4j s novými vztahy")
    
    args = parser.parse_args()
    tenant_id = args.tenant
    
    # 1. Získání kategorií z Qdrantu nebo z checkpointu
    categories = []
    relationships = {}
    
    if args.reload:
        # Načtení kategorií z Qdrantu
        categories = await get_categories_from_qdrant(tenant_id)
        if not categories:
            logger.error("Nebyly nalezeny žádné kategorie v Qdrantu")
            return
        
        # Uložení kategorií do souboru pro budoucí použití
        with open(f"categories_{tenant_id}.json", 'w', encoding='utf-8') as f:
            json.dump(categories, f, ensure_ascii=False, indent=2)
    else:
        # Načtení kategorií z dříve uloženého souboru
        try:
            with open(f"categories_{tenant_id}.json", 'r', encoding='utf-8') as f:
                categories = json.load(f)
                logger.info(f"Načteno {len(categories)} kategorií ze souboru")
        except FileNotFoundError:
            logger.warning(f"Soubor s kategoriemi nenalezen, načítám z Qdrantu")
            categories = await get_categories_from_qdrant(tenant_id)
            if not categories:
                logger.error("Nebyly nalezeny žádné kategorie v Qdrantu")
                return
                
            # Uložení kategorií do souboru
            with open(f"categories_{tenant_id}.json", 'w', encoding='utf-8') as f:
                json.dump(categories, f, ensure_ascii=False, indent=2)
    
    # 2. Analýza vztahů pomocí LLM nebo načtení z checkpointu
    if args.analyze:
        # Analýza vztahů pomocí LLM
        logger.info("Začínám analýzu vztahů mezi kategoriemi pomocí LLM")
        relationships = await analyze_category_relationships(categories, tenant_id)
        
        # Uložení finálních výsledků
        save_checkpoint(relationships, tenant_id)
    else:
        # Načtení vztahů z checkpointu
        relationships = load_checkpoint(tenant_id)
        if not relationships:
            logger.warning("Checkpoint s vztahy nenalezen, je potřeba provést analýzu pomocí LLM")
            logger.info("Použijte přepínač --analyze pro provedení analýzy")
            return
    
    # 3. Aktualizace Neo4j
    if args.update_neo4j:
        logger.info("Aktualizuji Neo4j s novými vztahy")
        success = await update_neo4j_relationships(relationships, tenant_id)
        if success:
            logger.info(f"Neo4j úspěšně aktualizováno pro tenanta {tenant_id}")
        else:
            logger.error(f"Chyba při aktualizaci Neo4j pro tenanta {tenant_id}")
    else:
        # Výpis nalezených vztahů
        logger.info("Souhrn nalezených vztahů (použijte --update-neo4j pro aktualizaci Neo4j):")
        for source, targets in relationships.items():
            if targets:  # Pouze kategorie s nějakými vztahy
                print(f"\n{source}:")
                for target, weight, reason in targets[:5]:  # Zobrazíme prvních 5 vztahů
                    print(f"  → {target}: {weight:.2f} - {reason}")
                if len(targets) > 5:
                    print(f"  ... a dalších {len(targets) - 5} vztahů")

if __name__ == "__main__":
    asyncio.run(main())
