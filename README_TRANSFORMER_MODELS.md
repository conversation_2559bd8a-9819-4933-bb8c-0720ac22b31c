# Pokročilé Transformer modely pro doporučování komplementárních produktů

Tento dokument popisuje implementaci pokročilých Transformer modelů pro doporučování komplementárních produktů.

## Přehled

Implementace zahrnuje následující komponenty:

1. **Pokročilý Transformer model** - Model využívající transformer architekturu pro zachycení komplexních vztahů mezi produkty
2. **Ensemble model** - Model kombinující výsledky různých modelů pro dosažení lepších výsledků
3. **Workflow pro trénování a testování** - Kompletní workflow pro přípravu dat, trénování modelů a testování doporučení

## Architektura

### Pokročilý Transformer model

Pokročilý Transformer model využív<PERSON> následující architekturu:

```
Input Embeddings
    |
Input Projection (Dense)
    |
Transformer Block 1
    |
Transformer Block 2
    |
Output Projection (Dense)
    |
L2 Normalization
```

Každý Transformer blok obsahuje:
- Multi-head attention
- Feed-forward síť
- Layer normalization
- Dropout

### Ensemble model

Ensemble model kombinuje výsledky různých modelů:

```
Input Embeddings
    |
  /   |   \
 /    |    \
Model 1  Model 2  Model 3
 \     |    /
  \    |   /
Weighted Average
    |
L2 Normalization
```

Ensemble model může kombinovat různé typy modelů:
- Transformer modely
- Enhanced modely
- Contrastive Teacher-Student modely

## Použití

### Trénování pokročilého Transformer modelu

```bash
python train_advanced_transformer_model.py --tenant avenberg --batch-size 32 --epochs 25 --patience 8 --learning-rate 0.0004 --dropout 0.1 --initial-temp 0.5 --min-temp 0.05 --num-transformer-layers 2 --num-heads 8 --d-model 256
```

### Indexace produktů pomocí pokročilého Transformer modelu

```bash
python generate_advanced_transformer_recommendations.py --tenant avenberg --index --force
```

### Generování doporučení pomocí pokročilého Transformer modelu

```bash
python generate_advanced_transformer_recommendations.py --tenant avenberg --product-id 278 --top-k 5
```

### Porovnání s LLM doporučeními

```bash
python generate_advanced_transformer_recommendations.py --tenant avenberg --product-id 278 --compare
```

### Indexace produktů pomocí Ensemble modelu

```bash
python generate_ensemble_transformer_recommendations.py --tenant avenberg --index --force
```

### Generování doporučení pomocí Ensemble modelu

```bash
python generate_ensemble_transformer_recommendations.py --tenant avenberg --product-id 278 --top-k 5
```

### Spuštění celého workflow

```bash
python run_transformer_models_workflow.py --tenant avenberg
```

## Výhody oproti předchozím modelům

1. **Lepší zachycení kontextu** - Transformer architektura lépe zachycuje kontext a vztahy mezi produkty
2. **Attention mechanismus** - Multi-head attention umožňuje modelu zaměřit se na různé aspekty produktů
3. **Ensemble přístup** - Kombinace různých modelů poskytuje robustnější a přesnější doporučení
4. **Dynamická teplota** - Dynamický teplotní parametr pro contrastive loss zlepšuje konvergenci modelu

## Implementované soubory

- `advanced_transformer_model.py` - Implementace pokročilého Transformer modelu
- `train_advanced_transformer_model.py` - Skript pro trénování pokročilého Transformer modelu
- `generate_advanced_transformer_recommendations.py` - Skript pro generování doporučení pomocí pokročilého Transformer modelu
- `ensemble_transformer_model.py` - Implementace Ensemble modelu
- `generate_ensemble_transformer_recommendations.py` - Skript pro generování doporučení pomocí Ensemble modelu
- `run_transformer_models_workflow.py` - Skript pro spuštění celého workflow

## Další možná vylepšení

1. **Multimodální Transformer** - Rozšíření modelu o zpracování obrazových dat
2. **Cross-attention** - Implementace cross-attention mezi produkty a uživatelskými preferencemi
3. **Personalizace** - Přidání personalizace na základě historie uživatele
4. **Dynamické váhy** - Implementace dynamických vah pro Ensemble model na základě kategorie produktu
5. **Online learning** - Průběžné učení modelu na základě zpětné vazby uživatelů
