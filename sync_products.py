#!/usr/bin/env python3
# sync_products.py
import asyncio
import logging
import sys
import argparse
import time
import os
import yaml
from typing import Dict, List, Set, Any, Optional, Union, Tuple
from datetime import datetime

# Přidán import models
from qdrant_client import models
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, PointStruct, PointIdsList
from core.clients import get_qdrant_async_client, get_openai_client, close_clients
from core.config import get_tenant_config
from core.utils import create_qdrant_id, process_batches_concurrently
from embedding_strategies import MultiEmbeddingStrategy
from feed_processor import load_and_transform_feed
from data_models import Product

# Nastavení loggingu
LOG_DIR = "logs"
os.makedirs(LOG_DIR, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(LOG_DIR, "sync_products.log"), encoding='utf-8')
    ]
)
logger = logging.getLogger("sync_products")

class ProductSynchronizer:
    """
    Třída pro synchronizaci produktů mezi feedem a databází.
    Implementuje následující funkce:
    1. Zpracovávat všechny produkty z feedu
    2. Synchronizovat produkty mezi feedem a databází
    3. Počítat embeddingy pro nové produkty
    4. Mazat produkty, které již nejsou ve feedu
    5. Kontrolovat dostupnost produktů a nedoporučovat produkty, které nejsou skladem
    6. Implementovat event-based sledování změn
    """

    def __init__(self, tenant_id: str, batch_size: int = 50, workers: int = 10):
        """
        Inicializuje synchronizátor produktů.

        Args:
            tenant_id: ID tenanta
            batch_size: Velikost dávky pro zpracování produktů
            workers: Počet paralelních workerů pro generování embeddingů
        """
        self.tenant_id = tenant_id
        self.batch_size = batch_size
        self.workers = workers
        self.collection_name = f"real_products_{tenant_id}"
        self.complementary_collection_name = f"complementary_products_{tenant_id}"
        self.changes_log_file = os.path.join(LOG_DIR, f"product_changes_{tenant_id}.log")

        # Klienti budou inicializovány v metodě sync
        self.qdrant_client = None
        self.openai_client = None
        self.embedding_strategy = None

        # Statistiky
        self.stats = {
            "feed_products": 0,
            "db_products": 0,
            "new_products": 0,
            "deleted_products": 0,
            "updated_products": 0,
            "unavailable_products": 0,
            "processing_time": 0
        }

    async def sync(self, force_update: bool = False, skip_embedding_updates: bool = False):
        """
        Synchronizuje produkty mezi feedem a databází.

        Args:
            force_update: Zda mají být aktualizovány všechny produkty (včetně embeddingů)
            skip_embedding_updates: Zda přeskočit aktualizaci embeddingů pro existující produkty
        """
        start_time = time.time()

        # Inicializace klientů
        self.qdrant_client = get_qdrant_async_client()
        self.openai_client = get_openai_client()
        self.embedding_strategy = MultiEmbeddingStrategy(self.openai_client)

        try:
            # --- Začátek přidané logiky pro kontrolu/vytvoření kolekce ---
            logger.info(f"Kontroluji existenci kolekce '{self.collection_name}'...")
            try:
                await self.qdrant_client.get_collection(collection_name=self.collection_name)
                logger.info(f"Kolekce '{self.collection_name}' již existuje.")
            except Exception as e:
                 # Zkontrolujeme, zda je chyba způsobená neexistující kolekcí (očekávaný případ)
                 # Poznámka: Qdrant klient může vracet specifickou výjimku, ale pro obecnost chytáme Exception
                 # a díváme se na text chyby nebo status kód (pokud je dostupný v e.args)
                 # Zde předpokládáme, že chyba znamená neexistující kolekci.
                 # V reálné aplikaci by bylo robustnější kontrolovat typ výjimky nebo status kód.
                 logger.warning(f"Kolekce '{self.collection_name}' nenalezena, pokouším se ji vytvořit.")

                 # Definice parametrů pro POUZE "combined" vektor
                 vector_size = self.embedding_strategy.embedding_dimensions
                 vector_configs = {
                     "combined": models.VectorParams(size=vector_size, distance=models.Distance.COSINE),
                     # Odstraněny ostatní vektory
                     # "name_brand": models.VectorParams(size=vector_size, distance=models.Distance.COSINE),
                     # "pure_description": models.VectorParams(size=vector_size, distance=models.Distance.COSINE),
                     # "category_hierarchy": models.VectorParams(size=vector_size, distance=models.Distance.COSINE),
                     # "brand_category": models.VectorParams(size=vector_size, distance=models.Distance.COSINE),
                 }

                 try:
                    await self.qdrant_client.create_collection(
                        collection_name=self.collection_name,
                        vectors_config=vector_configs
                    )
                    logger.info(f"Kolekce '{self.collection_name}' úspěšně vytvořena.")
                 except Exception as create_e:
                    logger.error(f"Nepodařilo se vytvořit kolekci '{self.collection_name}': {create_e}")
                    # Pokud selže vytvoření kolekce, nemá smysl pokračovat
                    return False
            # --- Konec přidané logiky ---

            # Načtení tenant settings
            logger.info(f"Načítám nastavení pro tenanta {self.tenant_id}...")
            try:
                tenant_settings = get_tenant_config(self.tenant_id)
                if not tenant_settings:
                    raise ValueError(f"Nastavení pro tenanta {self.tenant_id} nebyla nalezena nebo jsou neplatná.")
                logger.info(f"Nastavení pro tenanta {self.tenant_id} úspěšně načtena.")
            except Exception as e:
                logger.error(f"Chyba při načítání nastavení pro tenanta {self.tenant_id}: {e}")
                return False

            # 1. Načtení produktů z feedu
            logger.info(f"Načítám produkty z feedu pro tenanta {self.tenant_id}...")
            feed_products = await load_and_transform_feed(tenant_settings)
            if not feed_products:
                logger.warning(f"Nebyly nalezeny žádné produkty ve feedu pro tenanta {self.tenant_id}.")
                return False


            self.stats["feed_products"] = len(feed_products)
            logger.info(f"Načteno {len(feed_products)} produktů z feedu")

            # 2. Načtení produktů z databáze
            logger.info(f"Načítám produkty z databáze pro tenanta {self.tenant_id}...")
            db_products = await self._get_all_products_from_db()
            self.stats["db_products"] = len(db_products)
            logger.info(f"Načteno {len(db_products)} produktů z databáze")

            # 3. Identifikace nových, aktualizovaných a smazaných produktů
            feed_product_ids = {p.id for p in feed_products}
            db_product_ids = {p["product_id"] for p in db_products.values()}

            new_product_ids = feed_product_ids - db_product_ids
            deleted_product_ids = db_product_ids - feed_product_ids
            existing_product_ids = feed_product_ids.intersection(db_product_ids)

            self.stats["new_products"] = len(new_product_ids)
            self.stats["deleted_products"] = len(deleted_product_ids)

            logger.info(f"Identifikováno {len(new_product_ids)} nových produktů")
            logger.info(f"Identifikováno {len(deleted_product_ids)} smazaných produktů")
            logger.info(f"Identifikováno {len(existing_product_ids)} existujících produktů")

            # 4. Zpracování nových produktů
            if new_product_ids:
                logger.info(f"Zpracovávám {len(new_product_ids)} nových produktů...")
                new_products = [p for p in feed_products if p.id in new_product_ids]
                await self._process_new_products(new_products)

            # 5. Aktualizace existujících produktů
            if existing_product_ids:
                logger.info(f"Zpracovávám {len(existing_product_ids)} existujících produktů...")
                existing_products = [p for p in feed_products if p.id in existing_product_ids]
                # Předáme `force_update` přímo do metody, která si s tím poradí
                await self._update_existing_products(existing_products, db_products, force_update, skip_embedding_updates)

            # 6. Smazání produktů, které již nejsou ve feedu
            if deleted_product_ids:
                logger.info(f"Mažu {len(deleted_product_ids)} produktů, které již nejsou ve feedu...")
                await self._delete_products(deleted_product_ids, db_products)

            # 7. Kontrola dostupnosti produktů
            logger.info("Kontroluji dostupnost produktů...")
            await self._check_product_availability(feed_products)

            # 8. Kontrola a aktualizace cen produktů
            logger.info("Kontroluji změny cen produktů...")
            await self._check_product_prices(feed_products)

            # 9. Zápis změn do logu
            self._log_changes(new_product_ids, deleted_product_ids, self.stats["updated_products"], 
                              self.stats["unavailable_products"], self.stats.get("price_updated_products", 0))

            # Výpočet statistik
            self.stats["processing_time"] = time.time() - start_time

            # Výpis statistik
            logger.info("=== Statistiky synchronizace ===")
            logger.info(f"Počet produktů ve feedu: {self.stats['feed_products']}")
            logger.info(f"Počet produktů v databázi: {self.stats['db_products']}")
            logger.info(f"Počet nových produktů: {self.stats['new_products']}")
            logger.info(f"Počet smazaných produktů: {self.stats['deleted_products']}")
            logger.info(f"Počet aktualizovaných produktů: {self.stats['updated_products']}")
            logger.info(f"Počet nedostupných produktů: {self.stats['unavailable_products']}")
            logger.info(f"Počet produktů se změněnou cenou: {self.stats.get('price_updated_products', 0)}")
            logger.info(f"Doba zpracování: {self.stats['processing_time']:.2f}s")

            return True

        except Exception as e:
            logger.error(f"Chyba při synchronizaci produktů: {e}")
            import traceback
            traceback.print_exc()
            return False

        finally:
            # Uzavření klientů
            await close_clients()
            logger.info("Klienti byli ukončeni")

    async def _get_all_products_from_db(self) -> Dict[str, Dict[str, Any]]:
        """
        Načte všechny produkty z databáze.

        Returns:
            Slovník mapující Qdrant ID na payload produktu
        """
        products = {}
        offset = None

        try:
            # Kontrola existence kolekce
            collections = await self.qdrant_client.get_collections()
            if self.collection_name not in [c.name for c in collections.collections]:
                logger.warning(f"Kolekce {self.collection_name} neexistuje")
                return {}

            # Načtení produktů po dávkách
            while True:
                results, next_offset = await self.qdrant_client.scroll(
                    collection_name=self.collection_name,
                    limit=self.batch_size,
                    offset=offset,
                    with_payload=True,
                    with_vectors=False
                )

                if not results:
                    break

                for point in results:
                    products[point.id] = point.payload

                offset = next_offset
                if offset is None:
                    break

            logger.info(f"Načteno {len(products)} produktů z databáze")
            return products

        except Exception as e:
            logger.error(f"Chyba při načítání produktů z databáze: {e}")
            return {}

    async def _process_new_products(self, new_products: List[Product]):
        """
        Zpracuje nové produkty - vygeneruje embeddingy a uloží je do databáze.

        Args:
            new_products: Seznam nových produktů
        """
        # Funkce pro zpracování dávky produktů
        async def process_batch(batch):
            batch_products = []
            points_to_upload = []

            for product in batch:
                # Vytvoření Qdrant ID
                qdrant_id = create_qdrant_id(product.id)

                # Generování embeddingů
                try:
                    embeddings = await self.embedding_strategy.create_embeddings(product.model_dump())

                    # 1. Vytvoření původního payloadu jako slovníku
                    initial_payload = {
                        "product_id": product.id,
                        "name": product.name,
                        "description": product.description,
                        "category": product.category,
                        "brand": product.brand,
                        "price": product.price,
                        "image_url": product.image_url,
                        "product_url": product.product_url,
                        "availability": product.availability,
                        "tenant_id": self.tenant_id,
                        "created_at": time.time(),
                        "updated_at": time.time(),
                        # Uložíme všechny embeddingy do payloadu
                        "embeddings": {
                            "combined": embeddings.get("combined"),
                            "category": embeddings.get("category_hierarchy"),
                            "description": embeddings.get("pure_description"),
                            "name_brand": embeddings.get("name_brand"),
                            "brand_category": embeddings.get("brand_category")
                        }
                    }

                    # 2. Odstranění None hodnot z payloadu
                    filtered_payload = {k: v for k, v in initial_payload.items() if v is not None}

                    # 3. Vytvoření finálního bodu pro Qdrant s POUZE "combined" vektorem a filtrovaným payloadem
                    if "combined" in embeddings and embeddings["combined"] is not None:
                        point = PointStruct(
                            id=qdrant_id,
                            vector={'combined': embeddings['combined']}, # Použijeme POUZE combined vektor
                            payload=filtered_payload # Použijeme filtrovaný payload
                        )
                        points_to_upload.append(point)
                    else:
                        logger.warning(f"Chybí 'combined' embedding pro produkt {product.id}, produkt nebude přidán.")

                except Exception as e:
                    logger.error(f"Chyba při generování embeddingu nebo tvorbě bodu pro produkt {product.id}: {e}")

            # Nahrání dávky do Qdrant
            if points_to_upload:
                try:
                    await self.qdrant_client.upsert(
                        collection_name=self.collection_name,
                        points=points_to_upload,
                        wait=True # Změněno na True pro lepší sledování chyb při upsertu
                    )
                    logger.info(f"Nahráno/aktualizováno {len(points_to_upload)} nových produktů do Qdrant.")
                    return len(points_to_upload)
                except Exception as e:
                    logger.error(f"Chyba při nahrávání produktů do Qdrantu: {e}")
                    # Logujeme stack trace
                    import traceback
                    logger.error(traceback.format_exc())
                    return 0

            return 0

        # Zpracování všech produktů paralelně
        results = await process_batches_concurrently(
            items=new_products,
            process_func=process_batch,
            batch_size=self.batch_size,
            concurrency_limit=self.workers
        )

        # Výpočet statistik
        total_processed = sum(results)
        logger.info(f"Zpracováno {total_processed} nových produktů")

    async def _update_existing_products(self, existing_products: List[Product], 
                                        db_products: Dict[str, Dict[str, Any]], 
                                        force_update: bool = False, 
                                        skip_embedding_updates: bool = False):
        """
        Aktualizuje existující produkty - kontroluje změny nebo vynucuje aktualizaci.

        Args:
            existing_products: Seznam existujících produktů
            db_products: Slovník mapující Qdrant ID na payload produktu
            force_update: Zda vynutit aktualizaci bez ohledu na změny
            skip_embedding_updates: Zda přeskočit generování a aktualizaci embeddingů
        """
        # Vytvoření mapy produkt_id -> qdrant_id
        product_id_to_qdrant_id = {}
        for qdrant_id, payload in db_products.items():
            product_id = payload.get("product_id")
            if product_id:
                product_id_to_qdrant_id[product_id] = qdrant_id

        # Funkce pro zpracování dávky produktů
        async def process_batch(batch):
            updated_count = 0
            points_to_upsert = []   # Pro body s vektory
            payloads_to_set = []    # Pro body jen s payloadem (id, payload)

            for product in batch:
                qdrant_id = product_id_to_qdrant_id.get(product.id)
                if not qdrant_id:
                    logger.warning(f"Nenalezeno Qdrant ID pro produkt {product.id}")
                    continue
                
                current_payload = db_products.get(qdrant_id)
                if not current_payload:
                    logger.warning(f"Nenalezen payload pro produkt {product.id}")
                    continue

                # Rozhodneme, zda aktualizovat
                needs_update = force_update or self._check_product_changes(product, current_payload)

                if needs_update:
                    # Připravíme nový payload (vždy se aktualizuje, pokud needs_update)
                    new_payload = product.model_dump(exclude_unset=True)
                    if 'id' in new_payload:
                        new_payload['product_id'] = new_payload.pop('id')
                    if 'tenant_id' not in new_payload:
                        new_payload['tenant_id'] = self.tenant_id
                    new_payload['last_updated'] = datetime.now().isoformat()

                    # Pokusíme se získat vektory, pokud to není explicitně zakázáno
                    vectors = None
                    if not skip_embedding_updates:
                        try:
                            logger.debug(f"Generuji embeddingy pro aktualizovaný produkt {product.id}...")
                            embeddings = await self.embedding_strategy.create_embeddings(product.model_dump())
                            # Získáme pouze 'combined' vektor
                            vectors = {'combined': embeddings.get('combined')} if embeddings else None
                            if vectors and vectors.get('combined') is None:
                                logger.warning(f"Chybí 'combined' embedding i po přepočtu pro {product.id}.")
                                vectors = None # Nepodařilo se získat combined

                        except Exception as emb_e:
                            logger.error(f"Chyba při generování embeddingů pro produkt {product.id}: {emb_e}")
                            vectors = None # Vektory zůstanou None

                    # Rozhodnutí: upsert (s vektory) nebo set_payload (bez vektorů)
                    if vectors is not None: # Kontrolujeme, zda máme platný slovník s combined vektorem
                        points_to_upsert.append(PointStruct(
                            id=qdrant_id,
                            vector=vectors, # Předáme slovník {'combined': vektor}
                            payload=new_payload
                        ))
                    else:
                        # Pokud nemáme vektory (přeskočeno, chyba, nebo chybí combined),
                        # přidáme ID a payload do seznamu pro set_payload
                        payloads_to_set.append((qdrant_id, new_payload))
                    
                    updated_count += 1

            # Zpracování dávkového upsertu (s vektory)
            if points_to_upsert:
                try:
                    await self.qdrant_client.upsert(
                        collection_name=self.collection_name,
                        points=points_to_upsert,
                        wait=True
                    )
                    logger.info(f"Dávkově UPSERTováno {len(points_to_upsert)} produktů s vektory.")
                except Exception as e:
                    logger.error(f"Chyba při dávkovém UPSERTU aktualizovaných produktů: {e}")
                    failed_ids = [p.id for p in points_to_upsert]
                    logger.error(f"Selhal upsert pro Qdrant IDs: {failed_ids}")
                    # I když upsert selže, set_payload může projít, takže nevracíme 0 hned

            # Zpracování dávkového set_payload (bez vektorů)
            if payloads_to_set:
                try:
                    # Připravíme data pro set_payload - seznam ID a seznam payloadů
                    set_ids = [item[0] for item in payloads_to_set]
                    set_payloads = [item[1] for item in payloads_to_set]
                    
                    # Poznámka: Qdrant Python klient vrací metodu set_payload, která 
                    # podporuje pouze jeden payload pro více bodů, nebo jednotlivé volání. 
                    # Pro dávkovou aktualizaci různých payloadů musíme iterovat nebo použít batch update.
                    # Zde pro jednoduchost iterujeme, ale pro výkon by bylo lepší použít:
                    # await self.qdrant_client.update_batch(collection_name=..., points=[...])
                    # Níže je iterativní přístup pro kompatibilitu a přehlednost:
                    # TODO: Refaktorovat na update_batch pro lepší výkon
                    
                    for q_id, pload in payloads_to_set:
                         await self.qdrant_client.set_payload(
                             collection_name=self.collection_name,
                             payload=pload,
                             points=[q_id],
                             wait=True
                         )
                         
                    logger.info(f"Dávkově nastaven PAYLOAD pro {len(payloads_to_set)} produktů (bez vektorů).")
                except Exception as e:
                    logger.error(f"Chyba při dávkovém SET PAYLOAD aktualizovaných produktů: {e}")
                    failed_ids = [item[0] for item in payloads_to_set]
                    logger.error(f"Selhal set_payload pro Qdrant IDs: {failed_ids}")
                    # Pokud selže i set_payload, můžeme vrátit 0, nebo nechat updated_count
                    # Prozatím necháme updated_count, jak byl (počet detekovaných změn)

            return updated_count # Vrátíme počet produktů, které byly označeny k aktualizaci

        # Zpracování všech produktů paralelně
        results = await process_batches_concurrently(
            items=existing_products,
            process_func=process_batch,
            batch_size=self.batch_size,
            concurrency_limit=self.workers
        )

        # Výpočet statistik
        # Ošetření None hodnot z results, pokud by process_batches_concurrently vrátilo None pro neúspěšné dávky
        valid_results = [r for r in results if isinstance(r, int)]
        total_updated = sum(valid_results)
        self.stats["updated_products"] = total_updated
        logger.info(f"Aktualizováno {total_updated} existujících produktů")

    async def _delete_products(self, deleted_product_ids: Set[str], db_products: Dict[str, Dict[str, Any]]):
        """
        Smaže produkty, které již nejsou ve feedu.

        Args:
            deleted_product_ids: Set ID produktů, které mají být smazány
            db_products: Slovník mapující Qdrant ID na payload produktu
        """
        # Vytvoření mapy produkt_id -> qdrant_id
        product_id_to_qdrant_id = {}
        for qdrant_id, payload in db_products.items():
            product_id = payload.get("product_id")
            if product_id:
                product_id_to_qdrant_id[product_id] = qdrant_id

        # Získání Qdrant ID produktů, které mají být smazány
        qdrant_ids_to_delete = []
        for product_id in deleted_product_ids:
            qdrant_id = product_id_to_qdrant_id.get(product_id)
            if qdrant_id:
                qdrant_ids_to_delete.append(qdrant_id)

        # Smazání produktů po dávkách
        for i in range(0, len(qdrant_ids_to_delete), self.batch_size):
            batch = qdrant_ids_to_delete[i:i+self.batch_size]
            try:
                await self.qdrant_client.delete(
                    collection_name=self.collection_name,
                    points_selector=PointIdsList(points=batch),
                    wait=True
                )
                logger.info(f"Smazáno {len(batch)} produktů z Qdrantu (dávka {i//self.batch_size + 1}/{(len(qdrant_ids_to_delete)-1)//self.batch_size + 1})")
            except Exception as e:
                logger.error(f"Chyba při mazání produktů z Qdrantu: {e}")

        # Smazání komplementárních záznamů pro smazané produkty
        try:
            # Kontrola existence kolekce
            collections = await self.qdrant_client.get_collections()
            if self.complementary_collection_name in [c.name for c in collections.collections]:
                # Smazání komplementárních záznamů po dávkách
                for i in range(0, len(qdrant_ids_to_delete), self.batch_size):
                    batch = qdrant_ids_to_delete[i:i+self.batch_size]
                    try:
                        await self.qdrant_client.delete(
                            collection_name=self.complementary_collection_name,
                            points_selector=PointIdsList(points=batch),
                            wait=True
                        )
                        logger.info(f"Smazáno {len(batch)} komplementárních záznamů z Qdrantu (dávka {i//self.batch_size + 1}/{(len(qdrant_ids_to_delete)-1)//self.batch_size + 1})")
                    except Exception as e:
                        logger.error(f"Chyba při mazání komplementárních záznamů z Qdrantu: {e}")
        except Exception as e:
            logger.error(f"Chyba při kontrole existence kolekce komplementárních záznamů: {e}")

    async def _check_product_availability(self, feed_products: List[Product]):
        """
        Kontroluje dostupnost produktů a aktualizuje jejich stav v databázi.

        Args:
            feed_products: Seznam produktů z feedu
        """
        # Vytvoření mapy produkt_id -> dostupnost
        product_availability = {}
        for product in feed_products:
            product_availability[product.id] = product.availability

        # Načtení všech produktů z databáze
        db_products = await self._get_all_products_from_db()

        # Vytvoření mapy produkt_id -> qdrant_id
        product_id_to_qdrant_id = {}
        for qdrant_id, payload in db_products.items():
            product_id = payload.get("product_id")
            if product_id:
                product_id_to_qdrant_id[product_id] = qdrant_id

        # Kontrola dostupnosti produktů
        unavailable_products = []
        for product_id, availability in product_availability.items():
            if availability and availability.lower() in ["out of stock", "vyprodáno", "není skladem"]:
                unavailable_products.append(product_id)

        # Aktualizace stavu nedostupných produktů v databázi
        for product_id in unavailable_products:
            qdrant_id = product_id_to_qdrant_id.get(product_id)
            if qdrant_id:
                try:
                    # Aktualizace payloadu
                    await self.qdrant_client.set_payload(
                        collection_name=self.collection_name,
                        payload={"availability": "out of stock", "updated_at": time.time()},
                        points=[qdrant_id],
                        wait=True
                    )
                except Exception as e:
                    logger.error(f"Chyba při aktualizaci dostupnosti produktu {product_id}: {e}")

        self.stats["unavailable_products"] = len(unavailable_products)
        logger.info(f"Identifikováno {len(unavailable_products)} nedostupných produktů")

    async def _check_product_prices(self, feed_products: List[Product]):
        """
        Kontroluje změny cen produktů a aktualizuje je v databázi.

        Args:
            feed_products: Seznam produktů z feedu
        """
        # Vytvoření mapy produkt_id -> cena
        product_prices = {}
        for product in feed_products:
            product_prices[product.id] = product.price

        # Načtení všech produktů z databáze
        db_products = await self._get_all_products_from_db()

        # Vytvoření mapy produkt_id -> qdrant_id a produkt_id -> stará cena
        product_id_to_qdrant_id = {}
        old_prices = {}
        for qdrant_id, payload in db_products.items():
            product_id = payload.get("product_id")
            if product_id:
                product_id_to_qdrant_id[product_id] = qdrant_id
                old_prices[product_id] = payload.get("price")

        # Kontrola změn cen
        price_changed_products = []
        for product_id, new_price in product_prices.items():
            if product_id in old_prices:
                old_price = old_prices[product_id]
                # Porovnání cen (s konverzí na float)
                try:
                    if old_price is not None and new_price is not None:
                        old_price_float = float(old_price)
                        new_price_float = float(new_price)
                        if old_price_float != new_price_float:
                            price_changed_products.append((product_id, old_price, new_price))
                            logger.info(f"Změna ceny produktu {product_id}: {old_price} -> {new_price}")
                except (ValueError, TypeError):
                    # Pokud nelze převést na float, předpokládáme změnu
                    logger.warning(f"Nelze porovnat ceny pro produkt {product_id} ({old_price} vs {new_price}), považováno za změnu.")
                    price_changed_products.append((product_id, old_price, new_price))

        # Aktualizace produktů se změněnou cenou v databázi
        updated_prices_count = 0
        for product_id, old_price, new_price in price_changed_products:
            qdrant_id = product_id_to_qdrant_id.get(product_id)
            if qdrant_id:
                try:
                    # Aktualizace payloadu - pouze cena
                    await self.qdrant_client.set_payload(
                        collection_name=self.collection_name,
                        payload={"price": new_price, "updated_at": time.time()},
                        points=[qdrant_id],
                        wait=True
                    )
                    updated_prices_count += 1
                except Exception as e:
                    logger.error(f"Chyba při aktualizaci ceny produktu {product_id}: {e}")

        # Aktualizace statistik
        self.stats["price_updated_products"] = updated_prices_count
        logger.info(f"Aktualizováno {updated_prices_count} produktů se změněnou cenou")

    def _check_product_changes(self, product: Product, current_payload: Dict[str, Any]) -> bool:
        """
        Kontroluje, zda se produkt změnil.

        Args:
            product: Aktuální produkt z feedu
            current_payload: Aktuální payload produktu v databázi

        Returns:
            True, pokud se produkt změnil, jinak False
        """
        changed = False # Příznak, zda došlo ke změně

        # Funkce pro bezpečné porovnání s logováním
        def compare_attr(attr_name: str, product_value: Any, payload_value: Any, is_price: bool = False):
            nonlocal changed
            if changed: return # Pokud už byla změna detekována, dál nekontrolujeme

            payload_val = current_payload.get(attr_name)

            if payload_val is None:
                logger.info(f"Atribut '{attr_name}' chybí v payloadu produktu {product.id}, považováno za změnu.")
                changed = True
                return

            # Speciální ošetření pro cenu (porovnáváme jako float)
            if is_price:
                try:
                    product_float = float(product_value)
                    payload_float = float(payload_val)
                    if product_float != payload_float:
                        logger.info(f"Změna ceny produktu {product.id}: {payload_float} -> {product_float}")
                        changed = True
                except (ValueError, TypeError):
                    logger.warning(f"Nelze porovnat ceny pro produkt {product.id} ({product_value} vs {payload_val}), považováno za změnu.")
                    changed = True # Pokud nelze porovnat, raději aktualizujeme
            # Běžné porovnání
            elif product_value != payload_val:
                logger.info(f"Změna atributu '{attr_name}' produktu {product.id}: '{payload_val}' -> '{product_value}'")
                changed = True

        # Porovnání relevantních atributů
        compare_attr("name", product.name, current_payload.get("name"))
        compare_attr("description", product.description, current_payload.get("description"))
        compare_attr("category", product.category, current_payload.get("category"))
        compare_attr("brand", product.brand, current_payload.get("brand"))
        compare_attr("price", product.price, current_payload.get("price"), is_price=True)
        compare_attr("availability", product.availability, current_payload.get("availability"))
        compare_attr("image_url", product.image_url, current_payload.get("image_url"))
        compare_attr("product_url", product.product_url, current_payload.get("product_url"))

        if changed:
            return True

        # Pokud nedošlo ke změně atributů, kontrolujeme stáří embeddingů
        updated_at = current_payload.get("updated_at", 0)
        if time.time() - updated_at > 30 * 24 * 60 * 60:  # 30 dní
            logger.info(f"Embeddingy produktu {product.id} jsou starší než 30 dní, budou aktualizovány")
            return True

        return False # Žádná změna nedetekována

    def _log_changes(self, new_product_ids: Set[str] = None, deleted_product_ids: Set[str] = None, 
                     updated_count: int = 0, unavailable_count: int = 0, price_updated_count: int = 0):
        """Zaznamená změny produktů do log souboru."""
        log_entry = f"{datetime.now().isoformat()} - Tenant: {self.tenant_id}\n"
        log_entry += f"Nové produkty ({len(new_product_ids) if new_product_ids else 'Není nových produktů'} - {', '.join(list(new_product_ids)[:10]) if new_product_ids else 'Není nových produktů'}\n"
        log_entry += f"Smazané produkty ({len(deleted_product_ids) if deleted_product_ids else 'Není smazaných produktů'} - {', '.join(list(deleted_product_ids)[:10]) if deleted_product_ids else 'Není smazaných produktů'}\n"
        log_entry += f"Aktualizované produkty: {updated_count}\n"
        log_entry += f"Nedostupné produkty: {unavailable_count}\n"
        log_entry += f"Produkty se změněnou cenou: {price_updated_count}\n"
        log_entry += "\n"

        with open(self.changes_log_file, "a", encoding="utf-8") as f:
            f.write(log_entry)

async def main():
    """Hlavní funkce pro synchronizaci produktů."""

    # Zpracování argumentů
    parser = argparse.ArgumentParser(description="Synchronizace produktů mezi feedem a databází")
    parser.add_argument("--tenant", type=str, default="filsonstore",
                        help="ID tenanta (výchozí: filsonstore)")
    parser.add_argument("--batch-size", type=int, default=50,
                        help="Velikost dávky pro zpracování produktů (výchozí: 50)")
    parser.add_argument("--workers", type=int, default=10,
                        help="Počet paralelních workerů pro generování embeddingů (výchozí: 10)")
    parser.add_argument("--force", action="store_true",
                        help="Vynutit aktualizaci všech produktů (včetně payloadu a embeddingů)")
    parser.add_argument("--skip-embedding-updates", action="store_true",
                        help="Aktualizovat pouze payload existujících produktů, přeskočit aktualizaci embeddingů.")
    args = parser.parse_args()

    # Kontrola konfliktních argumentů
    if args.force and args.skip_embedding_updates:
         logger.error("Argumenty --force a --skip-embedding-updates nelze použít současně.")
         sys.exit(1)

    # Vytvoření synchronizátoru produktů
    synchronizer = ProductSynchronizer(
        tenant_id=args.tenant,
        batch_size=args.batch_size,
        workers=args.workers
    )
    
    # Předání nové volby do metody sync
    await synchronizer.sync(force_update=args.force, skip_embedding_updates=args.skip_embedding_updates)

if __name__ == "__main__":
    asyncio.run(main())
