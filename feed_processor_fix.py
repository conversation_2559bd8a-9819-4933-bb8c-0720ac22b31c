#!/usr/bin/env python3
# Oprava pro feed_processor.py
# Problém: Atribut 'product_url' chybí v payloadu mnoha produktů
# Řešení: Zajistit, že hodnota ProductUrl z feedu je namapována na oba atributy: url i product_url

import logging
import asyncio
from typing import List, Optional, Dict, Any, Tuple
import pandas as pd
from core.connectors.connector_factory import ConnectorFactory
from core.connectors.base_connector import BaseConnector
from core.config import TenantSettings, FeedSettings
from core.utils import normalize_category
from core.models import ProductInternal
import re

logger = logging.getLogger(__name__)

def _extract_price(price_str: Optional[str]) -> float:
    """Extrahuje cenu z řetězce."""
    if not price_str:
        return 0.0
    
    # Odstranění nečíselných znaků kromě tečky a čárky
    price_clean = re.sub(r'[^\d.,]', '', str(price_str))
    price_clean = price_clean.replace(',', '.')
    
    try:
        return float(price_clean)
    except ValueError:
        logger.warning(f"Nepodařilo se převést cenu '{price_str}' na float")
        return 0.0

async def load_and_transform_feed(tenant_settings: TenantSettings) -> List[ProductInternal]:
    """
    Načte data z feedu pomocí správného konektoru a transformuje je na ProductInternal.
    Používá synchronní konektor, běží v executoru.
    """
    logger.info(f"Loading and transforming feed for tenant {tenant_settings.tenant_id} using connector type {tenant_settings.feed.type}")

    try:
        # Vytvoření konektoru - předpokládá synchronní implementaci
        # Pokud by byl konektor asynchronní, nebylo by potřeba run_in_executor
        connector: BaseConnector = ConnectorFactory.create_connector(
            tenant_settings.feed.type,
            tenant_settings.feed.config
        )

        # Spuštění synchronního kódu v executoru, aby neblokoval asyncio loop
        loop = asyncio.get_running_loop()
        raw_products = await loop.run_in_executor(None, connector.get_feed) # None použije defaultní ThreadPoolExecutor

        if not raw_products:
            logger.warning(f"Feed for tenant {tenant_settings.tenant_id} is empty or failed to load.")
            return []

        logger.info(f"Loaded {len(raw_products)} products from feed for tenant {tenant_settings.tenant_id}")

        # Transformace na ProductInternal
        products = []
        for raw_product in raw_products:
            try:
                # Získání ID produktu
                product_id = str(raw_product.get('ID', '')).strip()
                if not product_id:
                    logger.warning(f"Product without ID will be skipped: {raw_product}")
                    continue

                # DŮLEŽITÁ OPRAVA: Explicitní přiřazení hodnoty ProductUrl jak do url, tak do product_url
                product_url = str(raw_product.get('ProductUrl', '')).strip() or None
                
                # Vytvoření produktu
                product = ProductInternal(
                    id=product_id,
                    name=str(raw_product.get('Name', '')).strip(),
                    description=str(raw_product.get('Description', '')).strip() or None,
                    category=str(raw_product.get('Category', '')).strip() or None,
                    brand=str(raw_product.get('Brand', '')).strip() or None,
                    price=_extract_price(raw_product.get('Price')),
                    image_url=str(raw_product.get('ImageUrl', '')).strip() or None,
                    url=product_url,
                    product_url=product_url,  # Přidáno pro řešení problému chybějícího product_url
                    availability=str(raw_product.get('Availability', '')).strip() or None,
                    tenant_id=tenant_settings.tenant_id,
                    metadata=raw_product
                )
                products.append(product)
            except Exception as e:
                logger.error(f"Error processing product {raw_product.get('ID', 'unknown')}: {e}")
                continue

        return products

    except Exception as e:
        logger.exception(f"Error loading feed for tenant {tenant_settings.tenant_id}: {e}")
        return [] 