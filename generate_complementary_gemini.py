import google.generativeai as genai
from qdrant_client import QdrantClient
import os
import time
import csv
from tqdm import tqdm
import pandas as pd
from dotenv import load_dotenv
import json
import re # Pro základní čištění odpovědi

# Načtení API klíče z .env souboru (volitelné, pokud ho nemáš v systémových proměnných)
load_dotenv()

# --- Konfigurace ---
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY")
if not GOOGLE_API_KEY:
    raise ValueError("Není nastaven GOOGLE_API_KEY v proměnných prostředí nebo .env souboru.")

genai.configure(api_key=GOOGLE_API_KEY)

# Nastavení modelu Gemini
generation_config = {
    "temperature": 0.6, # Trochu kreativity, ale stále relevantní
    "top_p": 1,
    "top_k": 1,
    "max_output_tokens": 2048,
}

safety_settings = [
    {"category": "HARM_CATEGORY_HARASSMENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_HATE_SPEECH", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_SEXUALLY_EXPLICIT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
    {"category": "HARM_CATEGORY_DANGEROUS_CONTENT", "threshold": "BLOCK_MEDIUM_AND_ABOVE"},
]

model = genai.GenerativeModel(model_name="gemini-2.0-flash", # nebo gemini-1.5-flash pro rychlejší a levnější variantu
                              generation_config=generation_config,
                              safety_settings=safety_settings)

tenant_id = "filsonstore"
collection_name = f"real_products_{tenant_id}"
qdrant_url = os.getenv("QDRANT_URL", "http://localhost:6333")
test_size = 100  # Počet produktů k načtení a zpracování pro testování
output_file = f"complementary_products_{tenant_id}_{test_size}.csv"
api_call_delay = 1 # Sekundy pauzy mezi API voláními pro zamezení rate limiting

# --- Připojení k Qdrantu ---
try:
    client = QdrantClient(url=qdrant_url)
    # Ověření existence kolekce (volitelné)
    client.get_collection(collection_name=collection_name)
    print(f"Úspěšně připojeno ke Qdrantu na {qdrant_url} a kolekce '{collection_name}' nalezena.")
except Exception as e:
    print(f"Chyba při připojení ke Qdrantu nebo hledání kolekce: {e}")
    print("Ujistěte se, že Qdrant běží a kolekce existuje.")
    exit()

# --- Načtení produktů z Qdrantu ---
print(f"Načítání {test_size} produktů z Qdrantu...")
start_time_qdrant = time.time()
try:
    scroll_results = client.scroll(
        collection_name=collection_name,
        with_payload=True,
        limit=test_size
    )
    products_data = scroll_results[0] # První prvek tuple obsahuje body
    products = []
    if not products_data:
         print(f"Nebyly nalezeny žádné produkty v kolekci '{collection_name}'.")
         exit()

    for point in products_data:
        if point.payload:
            products.append({
                'id': point.id, # Přidáme ID pro identifikaci
                'name': point.payload.get('name', 'N/A'),
                'category': point.payload.get('category', 'N/A')
            })
        else:
             print(f"Produkt s ID {point.id} nemá payload.")

except Exception as e:
    print(f"Chyba při načítání dat z Qdrantu: {e}")
    exit()

end_time_qdrant = time.time()
duration_qdrant = end_time_qdrant - start_time_qdrant

if not products:
    print("Seznam produktů je prázdný. Konec skriptu.")
    exit()

print(f"Načteno {len(products)} produktů z Qdrantu za {duration_qdrant:.2f} sekund.")
print(f"Průměrný čas na produkt (načítání): {(duration_qdrant/len(products)):.4f} sekund.")
total_products_estimate = 29500 # Odhadovaný celkový počet produktů
qdrant_load_estimate_total = (duration_qdrant / len(products)) * total_products_estimate if products else 0
print(f"Odhadovaná doba načítání pro {total_products_estimate} produktů: {qdrant_load_estimate_total:.1f} sekund ({(qdrant_load_estimate_total/60):.1f} minut)")

# --- Funkce pro získání komplementárních produktů pomocí Gemini ---
def get_complementary_products(product_name, product_category):
    """
    Zavolá Gemini API pro získání komplementárních produktů.
    """
    if not product_name or product_name == 'N/A':
        return "Chybí název produktu"
    if not product_category or product_category == 'N/A':
         product_category = "Nespecifikováno" # Výchozí hodnota, pokud kategorie chybí

    prompt = f"""
    Jsi expert na automobilové doplňky a potřeby řidičů v České republice. Tvým úkolem je navrhnout komplementární produkty pro daný produkt.
    Představ si českého zákazníka, který si kupuje následující produkt, a přemýšlej, co dalšího by *prakticky* mohl potřebovat k jeho použití, instalaci, údržbě nebo co by logicky souviselo s jeho nákupem v kontextu českého motoristy. Zvaž také kategorii produktu.

    Produkt:
    Název: "{product_name}"
    Kategorie: "{product_category}"

    Požadavky na výstup:
    1. Vypiš 3 až 5 nejdůležitějších komplementárních produktů nebo typů produktů.
    2. Pro každý navržený produkt uveď *velmi krátké* (max. 5-7 slov) vysvětlení, proč je komplementární.
    3. Pokud pro daný produkt neexistují žádné zjevné a smysluplné komplementy (např. u velmi specifických dílů nebo dárkových poukazů), uveď pouze text: "Žádné zjevné komplementy."
    4. Neuváděj přesné značky, ale spíše obecné typy produktů (např. "Motorový olej 5W-30" místo "Castrol Edge 5W-30", "Klíč na kola" místo "Hazet klíč").
    5. Formátuj výstup jako seznam s odrážkami (pomlčkami). Každá položka na novém řádku.

    Příklad výstupu pro "Nádoba na vypouštění oleje":
    - Motorový olej: Nutný pro doplnění po vypuštění starého.
    - Olejový filtr: Obvykle se mění společně s olejem.
    - Klíč na olejový filtr: Nástroj potřebný pro výměnu filtru.
    - Ochranné rukavice: Ochrana rukou při práci s olejem.
    - Trychtýř: Pro snadnější nalévání nového oleje.

    Příklad výstupu pro "Dárkový poukaz":
    Žádné zjevné komplementy.

    Nyní navrhni komplementární produkty pro výše uvedený produkt:
    """

    retries = 3
    for i in range(retries):
        try:
            response = model.generate_content(prompt)
            # Základní čištění odpovědi - odstranění případných úvodních/závěrečných frází modelu
            text_response = response.text.strip()
            # Odstranění případné úvodní fráze jako "Zde jsou komplementární produkty:"
            text_response = re.sub(r"^(.*?):\s*\n", "", text_response, flags=re.IGNORECASE)
             # Nahrazení hvězdiček za pomlčky pro konzistenci
            text_response = text_response.replace('*', '-')
            # Zajistíme, že každá položka začíná pomlčkou a mezerou
            lines = [line.strip() for line in text_response.split('\n') if line.strip()]
            formatted_lines = []
            for line in lines:
                if not line.startswith('-'):
                     formatted_lines.append(f"- {line}")
                else:
                     formatted_lines.append(line)

            # Pokud je odpověď jen "Žádné zjevné komplementy.", vrátíme ji tak.
            if len(formatted_lines) == 1 and "žádné zjevné komplementy" in formatted_lines[0].lower():
                 return "Žádné zjevné komplementy."

            # Jinak spojíme řádky s odrážkami
            return "\n".join(formatted_lines)

        except Exception as exc: # Přejmenování proměnné pro přehlednost
            print(f"Chyba při volání Gemini API pro '{product_name}' (pokus {i+1}/{retries}): {exc}")
            error_message = str(exc) # Uložení chybové zprávy
            if "rate limit" in error_message.lower():
                 print("Zasažen rate limit, zvyšuji pauzu...")
                 time.sleep(api_call_delay * (i + 2) * 5) # Exponenciální backoff
            else:
                 time.sleep(api_call_delay * (i + 1)) # Normální backoff
        # Pokud selžou všechny pokusy
        if i == retries - 1:
            print(f"Nepodařilo se získat odpověď pro '{product_name}' po {retries} pokusech.")
            return f"Chyba API: {error_message}" # Použití uložené chybové zprávy
    return "Neznámá chyba"


# --- Zpracování produktů a volání API ---
results = []
print(f"\nZpracování {len(products)} produktů pomocí Google Gemini API (pauza {api_call_delay}s mezi voláními)...")
start_time_api = time.time()

for product in tqdm(products, desc="Získávání komplementů"):
    complementary = get_complementary_products(product['name'], product['category'])
    results.append({
        'input_id': product['id'],
        'input_name': product['name'],
        'input_category': product['category'],
        'complementary_items': complementary
    })
    time.sleep(api_call_delay) # Pauza pro zamezení rate limiting

end_time_api = time.time()
duration_api = end_time_api - start_time_api

print(f"\nZpracováno {len(products)} produktů pomocí API za {duration_api:.2f} sekund.")
avg_time_per_product_api = duration_api / len(products) if products else 0
print(f"Průměrný čas na produkt (včetně API volání a pauzy): {avg_time_per_product_api:.3f} sekund.")

api_processing_estimate_total = avg_time_per_product_api * total_products_estimate if products else 0
print(f"\n*** DŮLEŽITÝ ODHAD DOBY PRO {total_products_estimate} PRODUKTŮ (API + pauzy) ***")
print(f"Odhadovaná doba: {api_processing_estimate_total:.1f} sekund")
print(f"Odhadovaná doba: {(api_processing_estimate_total/60):.1f} minut")
print(f"Odhadovaná doba: {(api_processing_estimate_total/3600):.1f} hodin")
print("POZNÁMKA: Tento odhad je velmi hrubý a nezahrnuje případné chyby API, delší pauzy kvůli rate limiting nebo složitější zpracování. Reálný čas může být výrazně delší.")

# --- Uložení výsledků do CSV ---
try:
    df = pd.DataFrame(results)
    df.to_csv(output_file, index=False, quoting=csv.QUOTE_ALL, encoding='utf-8-sig')
    print(f"\nVýsledky byly úspěšně uloženy do souboru: {output_file}")
except Exception as e:
    print(f"\nChyba při ukládání výsledků do CSV: {e}")

print("\nSkript dokončen.")
