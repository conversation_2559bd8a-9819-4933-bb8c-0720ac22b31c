from typing import Dict, List, Any, Optional
import logging
import pandas as pd
import sys
import os
import requests
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup

# Přidáme cestu k root adresáři
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .base_connector import BaseConnector


class FilsonstoreConnector(BaseConnector):
    """
    Konektor pro zpracování XML feedu z filsonstore.cz.
    
    Tento konektor zpracovává produktový feed ve formátu RSS XML s Google prvky,
    dostupný přes Mergado feeds.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Inicializuje konektor s danou konfigurací.
        
        Args:
            config: Konfigurace konektoru, která musí obsahovat:
                - feed_url: URL k XML feedu
        """
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")
        
        # Namespace pro Google feed
        self.ns = {'g': 'http://base.google.com/ns/1.0'}

    def get_feed(self) -> List[Dict[str, Any]]:
        """
        Získá a zpracuje XML feed z Filsonstore.
        
        Returns:
            List slovníků s daty o produktech ve standardizovaném formátu.
        """
        self.logger.info(f"Začínám získávat data z feedu")
        
        try:
            # Stažení XML feedu
            feed_content = self._fetch_feed()
            
            # Parsování XML
            products = self._parse_rss_feed(feed_content)
            
            self.logger.info(f"Úspěšně načteno a transformováno {len(products)} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při zpracování feedu: {e}")
            raise

    def _fetch_feed(self) -> str:
        """
        Stáhne XML feed z URL.
        
        Returns:
            Obsah XML feedu jako string.
        """
        self.logger.info(f"Fetching feed from {self.feed_url}")
        
        # Explicitně nastavíme kódování a hlavičky
        headers = {
            'Accept-Charset': 'utf-8',
            'Accept': 'application/xml, text/xml, */*'
        }
        
        # Stažení obsahu s explicitním nastavením kódování
        response = requests.get(self.feed_url, headers=headers)
        response.raise_for_status()  # Vyvolá výjimku, pokud není odpověď 2xx
        
        # Pokus o detekci kódování z hlaviček HTTP
        encoding = response.encoding
        self.logger.info(f"Detekované kódování z HTTP hlaviček: {encoding}")
        
        # Pokud je kódování nespecifikované nebo zjevně nesprávné, zkusíme UTF-8
        if encoding.lower() in ['iso-8859-1', 'latin1', 'ascii', None, '']:
            try:
                # Zkusíme explicitně nastavit UTF-8
                response.encoding = 'utf-8'
                content = response.text
                self.logger.info(f"Kódování změněno na utf-8")
            except Exception as e:
                self.logger.warning(f"Nepodařilo se změnit kódování na utf-8: {e}")
                content = response.text
        else:
            content = response.text
        
        self.logger.info("Feed fetched successfully")
        return content

    def _parse_rss_feed(self, xml_content: str) -> List[Dict[str, Any]]:
        """
        Parsuje RSS XML feed s Google atributy.
        
        Args:
            xml_content: Obsah XML feedu jako string.
            
        Returns:
            List slovníků s daty o produktech.
        """
        self.logger.info("Parsování RSS XML feedu")
        
        try:
            # Parsování XML s explicitním nastavením kódování
            soup = BeautifulSoup(xml_content, 'lxml-xml', from_encoding='utf-8')
            
            # Pokus o detekci kódování z XML deklarace
            xml_declaration = None
            if xml_content.startswith('<?xml'):
                xml_end = xml_content.find('?>')
                if xml_end > 0:
                    xml_declaration = xml_content[:xml_end+2]
                    self.logger.info(f"XML deklarace: {xml_declaration}")
                    
                    # Extrakce kódování z deklarace
                    encoding_start = xml_declaration.find('encoding=')
                    if encoding_start > 0:
                        encoding_str = xml_declaration[encoding_start:].split('"')[1]
                        self.logger.info(f"Kódování specifikované v XML: {encoding_str}")
            
            # Najdeme všechny položky v feedu
            items = soup.find_all('item')
            self.logger.info(f"Nalezeno {len(items)} položek v RSS XML")
            
            products = []
            for item in items:
                try:
                    # Extrakce základních údajů
                    product_id = item.find('g:id').text.strip() if item.find('g:id') else None
                    if not product_id:
                        self.logger.warning("Produkt přeskočen - chybí ID")
                        continue
                    
                    # Extrakce a mapování údajů na náš interní formát s ošetřením kódování
                    product = {
                        'ID': product_id,  # Požadované velké písmeno
                        'Name': self._normalize_text(item.find('title').text.strip() if item.find('title') else ''),
                        'Category': self._normalize_text(item.find('g:product_type').text.strip() if item.find('g:product_type') else ''),
                        'Description': self._normalize_text(item.find('g:description').text.strip() if item.find('g:description') else ''),
                        'Price': self._parse_price(item.find('g:price').text.strip()) if item.find('g:price') else None,
                        'ImageUrl': item.find('g:image_link').text.strip() if item.find('g:image_link') else '',
                        'ProductUrl': item.find('link').text.strip() if item.find('link') else '',
                        'Brand': self._normalize_text(item.find('g:brand').text.strip() if item.find('g:brand') else ''),
                        'Availability': item.find('g:availability').text.strip() if item.find('g:availability') else '',
                        'condition': item.find('g:condition').text.strip() if item.find('g:condition') else '',
                        'mpn': item.find('g:mpn').text.strip() if item.find('g:mpn') else '',
                        'gtin': item.find('g:gtin').text.strip() if item.find('g:gtin') else '',
                    }
                    
                    products.append(product)
                    
                except Exception as e:
                    self.logger.warning(f"Chyba při zpracování položky {product_id if 'product_id' in locals() else 'unknown'}: {e}")
                    continue
            
            # Kontrola formátu dat
            if products:
                # Vytvoření DataFrame pro kontrolu
                df = pd.DataFrame(products)
                
                # Kontrola povinných sloupců
                required_columns = ['ID', 'Name', 'Category']
                missing_columns = [col for col in required_columns if col not in df.columns]
                
                if missing_columns:
                    self.logger.warning(f"Ve feedu chybí tyto sloupce: {missing_columns}")
                
                # Logování ukázky dat pro kontrolu kódování
                if len(products) > 0:
                    sample = products[0]
                    self.logger.info(f"Ukázka prvního produktu po zpracování - Název: {sample.get('Name')}, Kategorie: {sample.get('Category')}")
            
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při parsování RSS feedu: {e}")
            raise
            
    def _normalize_text(self, text: str) -> str:
        """
        Normalizuje text pro správné zobrazení českých znaků.
        
        Args:
            text: Vstupní text k normalizaci
            
        Returns:
            Normalizovaný text s opravenými českými znaky
        """
        if not text:
            return ""
            
        try:
            # Slovník známých problematických znaků a jejich správných ekvivalentů
            replacements = {
                'Å¡': 'š',
                'Å™': 'ř',
                'Å½': 'ž',
                'Ä›': 'ě',
                'Ä\x8d': 'č',
                'Ã¡': 'á',
                'Ã­': 'í',
                'Ãº': 'ú',
                'Å¯': 'ů',
                'Ã½': 'ý',
                'Å\x99': 'ř',
                'Ã©': 'é',
                'Ã\x9a': 'Ú',
                'Ã': 'í',
                'Å¥': 'ť',
                'Å¾': 'ž',
                'Ä\x8f': 'ď',
                'Å„': 'ň',
                'Ä': 'ě',
                '\xa0': ' ',  # Nahrazení non-breaking space normálním
                '\xad': '',   # Soft hyphen - odstranit
                '\xc2': '',   # Nežádoucí bajt - odstranit
            }
            
            # Pokud text obsahuje znaky, které naznačují problém s kódováním
            if ('Å' in text or 'Ã' in text or 'Ä' in text or '\xad' in text or '\xbd' in text
                or 'Ã©' in text or 'Ã\x9a' in text):
                
                # Nejprve zkusíme všechny tři metody opravy a vybereme tu nejlepší
                
                # Metoda 1: latin1 -> utf-8
                try:
                    corrected1 = text.encode('latin1').decode('utf-8', errors='replace')
                except Exception:
                    corrected1 = text
                
                # Metoda 2: cp1250 -> utf-8
                try:
                    corrected2 = text.encode('cp1250').decode('utf-8', errors='replace')
                except Exception:
                    corrected2 = text
                
                # Metoda 3: manuální nahrazení
                corrected3 = text
                for bad, good in replacements.items():
                    corrected3 = corrected3.replace(bad, good)
                
                # Heuristika pro výběr nejlepšího výsledku: počet českých znaků
                czech_chars = 'ěščřžýáíéúůťďňóĚŠČŘŽÝÁÍÉÚŮŤĎŇÓ'
                
                count1 = sum(1 for char in corrected1 if char in czech_chars)
                count2 = sum(1 for char in corrected2 if char in czech_chars)
                count3 = sum(1 for char in corrected3 if char in czech_chars)
                
                # Výběr nejlepšího výsledku podle nejvyššího počtu českých znaků
                if count1 >= count2 and count1 >= count3:
                    result = corrected1
                elif count2 >= count1 and count2 >= count3:
                    result = corrected2
                else:
                    result = corrected3
                
                # Pokud nejlepší výsledek stále obsahuje problematické znaky, aplikujeme manuální nahrazení
                for bad, good in replacements.items():
                    if bad in result:
                        result = result.replace(bad, good)
                
                # Další čištění textu - odstranění nevhodných znaků
                result = ''.join(char for char in result if ord(char) >= 32 or char in ' \t\n')
                
                return result
            
            # Pokud text neobsahuje problematické znaky, vrátíme ho jak je
            return text
        
        except Exception as e:
            self.logger.warning(f"Chyba při normalizaci textu: {e}")
            return text

    def _parse_price(self, price_text: str) -> Optional[float]:
        """
        Převede textovou cenu (např. '279 CZK') na float.
        
        Args:
            price_text: Textový řetězec ceny.
            
        Returns:
            Cena jako float, nebo None pokud převod selže.
        """
        if not price_text:
            return None
        try:
            # Odstraníme měnu a mezery, nahradíme desetinnou čárku tečkou
            price_str = price_text.split(' ')[0].replace(',', '.') 
            return float(price_str)
        except (ValueError, IndexError) as e:
            self.logger.warning(f"Nepodařilo se převést cenu '{price_text}' na float: {e}")
            return None 