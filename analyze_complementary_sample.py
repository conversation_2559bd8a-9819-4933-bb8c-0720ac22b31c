#!/usr/bin/env python3
"""
Skript pro analýzu vzorku komplementárních doporučení
"""
import json
import random
from collections import Counter, defaultdict

def analyze_complementary_recommendations(file_path: str, sample_size: int = 5):
    """Analyzuje vzorek komplementárních doporučení"""
    
    print("📊 Načítám data...")
    with open(file_path, 'r', encoding='utf-8') as f:
        full_data = json.load(f)
    
    # Extrahujeme metadata a produkty
    metadata = full_data.get('metadata', {})
    comp_products = full_data.get('complementary_products', {})
    
    print(f"\n📋 METADATA:")
    print(f"   Tenant: {metadata.get('tenant', 'N/A')}")
    print(f"   Vygenerováno: {metadata.get('generated_at', 'N/A')}")
    print(f"   Celkem produktů s doporučeními: {metadata.get('total_products_with_recommendations', 0)}")
    print(f"   Celkem doporučení: {metadata.get('total_recommendations', 0):,}")
    print(f"   Průměr doporučení na produkt: {metadata.get('average_recommendations_per_product', 0):.1f}")
    
    # Náhodný vzorek produktů
    print(f"\n🎲 Náhodný vzorek {sample_size} produktů:")
    print("="*80)
    
    # Vybereme náhodné klíče
    product_ids = list(comp_products.keys())
    sample_ids = random.sample(product_ids, min(sample_size, len(product_ids)))
    
    for i, product_id in enumerate(sample_ids, 1):
        item = comp_products[product_id]
        source_product = item['source_product']
        recommendations = item['complementary_products']
        
        print(f"\n{i}. PRODUKT: {source_product['name']}")
        print(f"   ID: {source_product['id']}")
        print(f"   Kategorie: {source_product['category']}")
        print(f"   Cena: {source_product['price']} Kč")
        print(f"   Počet doporučení: {len(recommendations)}")
        
        if recommendations:
            print(f"\n   Top 10 doporučených produktů:")
            # Zobrazíme prvních 10 doporučení
            for j, rec in enumerate(recommendations[:10], 1):
                print(f"   {j:2d}. {rec['name'][:60]}")
                print(f"       Kategorie: {rec['category']}")
                print(f"       Cena: {rec['price']} Kč")
                if 'match_score' in rec:
                    print(f"       Match skóre: {rec['match_score']}")
                if 'complementary_weight' in rec:
                    print(f"       Váha vztahu: {rec['complementary_weight']}")
                if 'complementary_category' in rec:
                    print(f"       Komplementární kategorie: {rec['complementary_category']}")
        
        print("-"*80)
    
    # Analýza nejčastějších doporučovaných kategorií
    print("\n📊 ANALÝZA DOPORUČOVANÝCH KATEGORIÍ")
    print("="*80)
    
    category_counts = Counter()
    category_by_source = defaultdict(Counter)
    weight_distribution = Counter()
    
    for product_id, item in comp_products.items():
        source_category = item['source_product']['category']
        # Extrahujeme hlavní kategorii (před první >)
        main_source_category = source_category.split(' > ')[0] if ' > ' in source_category else source_category
        
        for rec in item['complementary_products']:
            # Používáme complementary_category místo category pro doporučení
            rec_category = rec.get('complementary_category', rec.get('category', ''))
            
            category_counts[rec_category] += 1
            category_by_source[main_source_category][rec_category] += 1
            
            if 'complementary_weight' in rec:
                weight_distribution[rec['complementary_weight']] += 1
    
    print("\n🏆 Top 15 nejčastěji doporučovaných kategorií celkově:")
    for cat, count in category_counts.most_common(15):
        print(f"   - {cat}: {count:,} doporučení")
    
    # Analýza podle zdrojových kategorií
    print("\n📱 Doporučení podle zdrojových kategorií:")
    # Seřadíme kategorie podle počtu produktů
    source_categories = sorted(category_by_source.keys(), 
                              key=lambda x: sum(category_by_source[x].values()), 
                              reverse=True)[:5]
    
    for source_cat in source_categories:
        total_recs = sum(category_by_source[source_cat].values())
        print(f"\n   Pro kategorii '{source_cat}' (celkem {total_recs} doporučení):")
        top_recommendations = category_by_source[source_cat].most_common(5)
        for rec_cat, count in top_recommendations:
            percentage = (count / total_recs) * 100
            print(f"      → {rec_cat}: {count} ({percentage:.1f}%)")
    
    # Distribuce vah
    if weight_distribution:
        print("\n⚖️ Distribuce vah vztahů:")
        for weight, count in sorted(weight_distribution.items(), reverse=True):
            print(f"   - Váha {weight}: {count:,} doporučení")

if __name__ == "__main__":
    analyze_complementary_recommendations("all_complementary_jabkolevne.json", sample_size=5) 