import argparse
import json
import re
from typing import Set
import xml.etree.ElementTree as ET
import requests
import yaml

def extract_categories_from_feed(feed_url: str) -> Set[str]:
    """Extract unique categories from <g:product_type> and <g:google_product_category> from the XML feed."""
    resp = requests.get(feed_url, timeout=30)
    resp.raise_for_status()
    root = ET.fromstring(resp.content)
    ns = {'g': 'http://base.google.com/ns/1.0'}
    categories = set()
    for item in root.findall('.//item'):
        for tag in ['g:product_type', 'g:google_product_category']:
            for node in item.findall(tag, ns):
                val = node.text.strip() if node.text else ''
                if val:
                    # Normalize: single >, remove extra spaces
                    categories.add(re.sub(r'\s*>\s*', ' > ', val))
    return categories

def extract_graph_categories(graph_path: str) -> Set[str]:
    """Extract all top-level keys from the category graph JSON."""
    with open(graph_path, encoding='utf-8') as f:
        graph = json.load(f)
    return set(graph.keys())

def main(feed_url: str, graph_path: str):
    print(f"Stahuji feed: {feed_url}")
    feed_cats = extract_categories_from_feed(feed_url)
    print(f"Načítám graf: {graph_path}")
    graph_cats = extract_graph_categories(graph_path)

    only_in_feed = sorted(feed_cats - graph_cats)
    only_in_graph = sorted(graph_cats - feed_cats)
    in_both = sorted(feed_cats & graph_cats)

    print("\n--- Kategorie pouze ve feedu (chybí v grafu) ---")
    for cat in only_in_feed:
        print(cat)
    print(f"Celkem: {len(only_in_feed)}\n")

    print("--- Kategorie pouze v grafu (nejsou ve feedu) ---")
    for cat in only_in_graph:
        print(cat)
    print(f"Celkem: {len(only_in_graph)}\n")

    print("--- Kategorie v obou ---")
    print(f"Celkem: {len(in_both)}\n")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Porovná kategorie z feedu a grafu.")
    parser.add_argument('--feed', type=str, default=None, help='URL XML feedu (defaultně z avenberg.yaml)')
    parser.add_argument('--graph', type=str, default=None, help='Cesta ke grafu (defaultně z avenberg.yaml)')
    parser.add_argument('--config', type=str, default='config/tenants/avenberg.yaml', help='Cesta ke konfiguračnímu YAML souboru')
    args = parser.parse_args()

    with open(args.config, encoding='utf-8') as f:
        config = yaml.safe_load(f)
    feed_url = args.feed or config['feed']['feed_url']
    graph_path = args.graph or config['category_graph_path']

    main(feed_url, graph_path)
