import os
import yaml
from typing import Dict, Any
import logging


class ConfigManager:
    def __init__(self, tenant_id: str = "default"):
        self.tenant_id = tenant_id
        self.logger = logging.getLogger("ConfigManager")
        self.config = {}
        
        # Načtení základní konfigurace
        self._load_base_config()
        
        # Načtení konfigurace tenanta
        self._load_tenant_config()
        
    def _load_base_config(self):
        """Načte základní konfiguraci ze souboru base.yaml"""
        base_config_path = os.path.join("config", "base.yaml")
        try:
            with open(base_config_path, 'r', encoding='utf-8') as f:
                self.config = yaml.safe_load(f)
            self.logger.info("Načtena základní konfigurace")
        except Exception as e:
            self.logger.error(f"Chyba při načítání základní konfigurace: {e}")
            raise
    
    def _load_tenant_config(self):
        """Načte a sloučí konfiguraci specifickou pro tenanta"""
        tenant_config_path = os.path.join("config", "tenants", f"{self.tenant_id}.yaml")
        try:
            with open(tenant_config_path, 'r', encoding='utf-8') as f:
                tenant_config = yaml.safe_load(f)
                
            # Rekurzivní sloučení konfigurací
            self._merge_configs(self.config, tenant_config)
            self.logger.info(f"Načtena konfigurace pro tenanta {self.tenant_id}")
        except FileNotFoundError:
            self.logger.warning(f"Konfigurace pro tenanta {self.tenant_id} nenalezena, používám základní konfiguraci")
        except Exception as e:
            self.logger.error(f"Chyba při načítání konfigurace tenanta: {e}")
            raise
    
    def _merge_configs(self, base: Dict, override: Dict):
        """Rekurzivně sloučí dvě konfigurace"""
        for key, value in override.items():
            if key in base and isinstance(base[key], dict) and isinstance(value, dict):
                self._merge_configs(base[key], value)
            else:
                base[key] = value
    
    def get(self, path: str, default: Any = None) -> Any:
        """
        Získá hodnotu z konfigurace podle cesty (např. 'recommender.similar_products_count')
        """
        try:
            value = self.config
            for key in path.split('.'):
                value = value[key]
            return value
        except (KeyError, TypeError):
            return default
