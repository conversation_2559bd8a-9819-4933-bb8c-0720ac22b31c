services:
  qdrant:
    image: qdrant/qdrant:latest
    container_name: qdrant_server # Změněno z gallitec_qdrant_semantic_local
    ports:
      - "6333:6333"  # HTTP API
      - "6334:6334"  # gRPC
    volumes:
      # Změněno volume a cesta
      - qdrant_server_data:/qdrant/storage
    restart: unless-stopped
    networks:
      - semantic_network # Připojení do společné sítě

  neo4j:
    image: neo4j:latest
    container_name: neo4j_gallitec
    ports:
      - "7474:7474"  # HTTP pro Neo4j Browser
      - "7687:7687"  # Bolt pro připojení aplikace
    volumes:
      - neo4j_data:/data
      - neo4j_plugins:/plugins
      - neo4j_logs:/logs
    environment:
      - NEO4J_AUTH=neo4j/Graph2025Secure!
      - NEO4J_ACCEPT_LICENSE_AGREEMENT=yes
    restart: unless-stopped
    networks:
      - semantic_network

  semantic_api:
    build: . # Sestaví image z Dockerfile v aktuálním adresáři
    container_name: semantic_api_server # Změněno z gallitec_semantic_api_local
    ports:
      - "8000:8000" # Mapuje port 8000 kontejneru na port 8000 hostitele
    volumes:
      # Mapuje aktuální adresář s kódem do /app v kontejneru
      # To umožňuje "hot-reloading" při lokálním vývoji (díky --reload v CMD)
      # Změny v lokálním kódu se hned projeví v běžícím kontejneru
      - .:/app
    env_file:
      - .env # Načte proměnné prostředí ze souboru .env
    restart: unless-stopped
    depends_on:
      - qdrant # Spustí se až po úspěšném startu qdrant služby
    networks:
      - semantic_network # Připojení do společné sítě

  sync_service:
    build:
      context: .
      dockerfile: Dockerfile.sync
    container_name: product_sync_service
    volumes:
      - .:/app
    env_file:
      - .env
    depends_on:
      - qdrant
    networks:
      - semantic_network
    # Není zde restart: always, protože tuto službu budeme spouštět manuálně nebo přes cron

  nightly_process:
    build:
      context: .
      dockerfile: Dockerfile.nightly
    container_name: nightly_process
    volumes:
      - ./logs:/app/logs
    env_file:
      - .env
    depends_on:
      - qdrant
    networks:
      - semantic_network
    # Není zde restart: always, protože tuto službu budeme spouštět manuálně nebo přes cron

networks:
  semantic_network: # Definice sítě
    driver: bridge # Standardní bridge driver
    name: semantic_network
volumes:
  # Definice volume pro Qdrant - název změněn
  qdrant_server_data: # Změněno z qdrant_data
  # Definice volumes pro Neo4j
  neo4j_data:
  neo4j_plugins:
  neo4j_logs:
