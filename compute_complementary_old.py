import os
import sys
import asyncio
import logging
import json
import yaml
import time
import random
import re
from typing import List, Dict, Optional, Any, Tuple
from dotenv import load_dotenv
import google.generativeai as genai
from qdrant_client import QdrantClient, AsyncQdrantClient, models
from qdrant_client.http.models import Filter, FieldCondition, MatchValue, PointStruct, HasIdCondition

# Přidání cesty k rootu projektu, aby fungovaly importy z 'core'
# Detekce, zda jsme v kořenovém adresáři nebo v podsložce
script_dir = os.path.dirname(os.path.abspath(__file__))
if os.path.basename(script_dir) == 'batch_scripts':
    project_root = os.path.abspath(os.path.join(script_dir, '..'))
else:
    project_root = os.path.abspath(script_dir)
sys.path.insert(0, project_root)

from core.clients import get_qdrant_async_client, get_gemini_client # Budeme potřebovat async klienty
from core.utils import create_qdrant_id # Pro práci s ID

# --- Konfigurace logování ---
log_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger("compute_complementary")
logger.setLevel(logging.INFO)

# Handler pro výpis do konzole
console_handler = logging.StreamHandler()
console_handler.setFormatter(log_formatter)
logger.addHandler(console_handler)

# Načtení .env
load_dotenv(dotenv_path=os.path.join(project_root, '.env'))

# --- Přesunuté pomocné funkce z api.py ---

async def _get_product_payload_async(client: AsyncQdrantClient, collection_name: str, product_id: str) -> Optional[Dict[str, Any]]:
    """Asynchronně získá payload produktu podle ID."""
    try:
        qdrant_id = create_qdrant_id(product_id)
        points = await client.retrieve(collection_name=collection_name, ids=[qdrant_id], with_payload=True)
        if points: return points[0].payload

        # Fallback na hledání v payloadu (pomalejší) - Můžeme zvážit, zda je zde potřeba
        logger.warning(f"Payload for {product_id} (ID: {qdrant_id}) not found via retrieve, trying scroll...")
        scroll_result = await client.scroll(
            collection_name=collection_name,
            scroll_filter=Filter(must=[FieldCondition(key="product_id", match=MatchValue(value=product_id))]),
            limit=1, with_payload=True
        )
        if scroll_result and scroll_result[0]: return scroll_result[0][0].payload

        scroll_result = await client.scroll(
            collection_name=collection_name,
            scroll_filter=Filter(must=[FieldCondition(key="original_id", match=MatchValue(value=product_id))]),
            limit=1, with_payload=True
        )
        if scroll_result and scroll_result[0]: return scroll_result[0][0].payload

        logger.error(f"Payload for {product_id} (ID: {qdrant_id}) not found after fallback scroll.")
        return None
    except Exception as e:
        logger.error(f"Error retrieving payload for {product_id} in {collection_name}: {e}", exc_info=True)
        return None

async def _get_product_vector_async(client: AsyncQdrantClient, collection_name: str, product_id: str, vector_name: str = "combined") -> Optional[List[float]]:
    """Asynchronně získá specifický vektor produktu."""
    try:
        qdrant_id = create_qdrant_id(product_id)
        points = await client.retrieve(collection_name=collection_name, ids=[qdrant_id], with_payload=False, with_vectors=[vector_name])
        if points and points[0].vector:
            if vector_name in points[0].vector:
                return points[0].vector[vector_name]
            elif vector_name == "combined" and "default" in points[0].vector:
                 logger.warning(f"Using 'default' vector for {product_id} as '{vector_name}' fallback.")
                 return points[0].vector["default"]
        logger.warning(f"Vector '{vector_name}' not found for {product_id} (ID: {qdrant_id}).")
        return None
    except Exception as e:
        logger.error(f"Error retrieving vector '{vector_name}' for {product_id} in {collection_name}: {e}", exc_info=True)
        return None

def get_tenant_yaml_config(tenant_id: str) -> Optional[Dict[str, Any]]:
    """Načte konfiguraci tenanta z YAML souboru."""
    config_path = os.path.join(project_root, "config", "tenants", f"{tenant_id}.yaml") # Upravená cesta
    if not os.path.exists(config_path):
        logger.error(f"Konfigurační soubor pro tenanta '{tenant_id}' nebyl nalezen na cestě: {config_path}")
        return None
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        logger.debug(f"Konfigurace pro tenanta '{tenant_id}' úspěšně načtena z {config_path}.")
        return config
    except Exception as e:
        logger.error(f"Chyba při načítání nebo parsování YAML konfigurace z {config_path}: {e}", exc_info=True)
        return None

def load_category_graph(graph_path: str) -> Optional[Dict[str, Dict[str, float]]]:
    """Načte graf kategorií z JSON souboru."""
    if not os.path.isabs(graph_path):
        graph_path = os.path.join(project_root, graph_path) # Předpokládáme relativní cestu k rootu

    if not os.path.exists(graph_path):
        logger.error(f"Soubor s grafem kategorií nebyl nalezen: {graph_path}")
        return None
    try:
        with open(graph_path, 'r', encoding='utf-8') as f:
            graph_data = json.load(f)
        if not isinstance(graph_data, dict):
             logger.error(f"Soubor s grafem {graph_path} nemá očekávaný formát kořenového slovníku.")
             return None
        return graph_data
    except Exception as e:
        logger.error(f"Chyba při načítání grafu kategorií z {graph_path}: {e}", exc_info=True)
        return None

def get_complementary_categories_from_graph(graph_data: Dict[str, Dict[str, float]], source_category: str) -> List[Tuple[str, float]]:
    """Získá seznam komplementárních kategorií a jejich vah."""
    if source_category in graph_data:
        complementary_dict = graph_data[source_category]
        if isinstance(complementary_dict, dict):
            sorted_complementary = sorted(complementary_dict.items(), key=lambda item: item[1], reverse=True)
            return sorted_complementary
        else:
            logger.warning(f"Data pro zdrojovou kategorii '{source_category}' v grafu nejsou ve formátu slovníku.")
            return []
    else:
        logger.debug(f"Zdrojová kategorie '{source_category}' nebyla nalezena v grafu kategorií.")
        return []

# --- Nová pomocná funkce pro extrakci klíčových slov ---
def _extract_keywords(text: Optional[str], num_keywords: int = 4) -> List[str]:
    """Jednoduchá extrakce klíčových slov z textu."""
    if not text:
        return []

    # Základní seznam českých stopwords (lze rozšířit)
    stopwords = set([
        "a", "aby", "ale", "anebo", "ani", "ano", "asi", "az", "bez", "bude", "budem", "budes",
        "by", "byl", "byla", "byli", "bylo", "byt", "ci", "clanek", "clanku", "clanky", "co",
        "coz", "cz", "dalsi", "datum", "do", "email", "ho", "i", "jak", "jako", "je", "jeho",
        "jej", "jeji", "jejich", "jemu", "jen", "jeste", "jenz", "ji", "jine", "jiz", "jsem",
        "jses", "jsme", "jsou", "jste", "k", "kam", "kde", "kdo", "kdyz", "ke", "ktera",
        "ktere", "kterou", "ktery", "ku", "ma", "mate", "me", "mezi", "mi", "mit", "mne",
        "mnou", "muj", "muze", "my", "na", "nad", "nam", "napiste", "nas", "nasi", "ne", "nebo",
        "necht", "nejsou", "neni", "net", "nez", "ni", "nic", "nove", "novy", "o", "od", "on",
        "ona", "oni", "ono", "ony", "po", "pod", "podle", "pokud", "polozka", "polozky",
        "pouze", "prave", "pred", "pres", "pri", "pro", "proc", "proto", "protoze", "prvni",
        "pta", "re", "s", "se", "si", "sice", "strana", "sve", "svuj", "svych", "svym",
        "svymi", "ta", "tak", "take", "takze", "tam", "tamhle", "tato", "tedy", "tema", "ten",
        "tento", "teto", "tim", "timto", "tipy", "to", "tohle", "toho", "tohoto", "tom",
        "tomto", "tomuto", "totiz", "tvuj", "ty", "tyto", "u", "uz", "v", "vam", "vas", "vase",
        "ve", "vice", "vsak", "z", "za", "zda", "zde", "ze", "zpet", "zpravy", "ktere", "pro",
        "na", "z", "s", "do", "v", "je", "jsou"
    ])

    # Odstranění interpunkce a převod na malá písmena
    text = re.sub(r'[^\w\s]', '', text.lower())
    words = text.split()

    # Filtrace stopwords a krátkých slov
    keywords = [word for word in words if word not in stopwords and len(word) > 2]

    # Vrácení prvních N klíčových slov
    return keywords[:num_keywords]

# --- Hlavní logika pro výpočet ---

async def compute_and_save_complementary(
    tenant_id: str,
    product_id: str,
    qdrant_client: AsyncQdrantClient,
    gemini_client: genai.GenerativeModel,
    category_graph_data: Optional[Dict], # Graf předáme jako argument
    candidate_limit: int = 100, # <-- Zvýšeno na 100
    rerank_limit: int = 10 # Limit pro finální počet výsledků z Gemini
):
    """Vypočítá komplementární produkty pro JEDEN produkt a uloží je do cache."""
    products_collection = f"real_products_{tenant_id}"
    complementary_cache_collection = f"complementary_products_{tenant_id}"
    source_qdrant_id = create_qdrant_id(product_id)
    
    logger.info(f"Zpracovávám produkt ID: {product_id} pro tenant: {tenant_id}")

    # --- Získání zdrojového produktu ---
    source_payload = await _get_product_payload_async(qdrant_client, products_collection, product_id)
    if source_payload is None:
        logger.error(f"Zdrojový produkt {product_id} nenalezen.")
        return False # Nebo jiná signalizace chyby

    source_category = source_payload.get("category", "")
    if not source_category:
        logger.warning(f"Zdrojový produkt {product_id} nemá kategorii.")
        return False

    logger.info(f"Zdrojová kategorie pro graf: '{source_category}'") # <-- Upraveno logování

    # --- Práce s grafem ---
    complementary_categories_with_weights: List[Tuple[str, float]] = []
    if category_graph_data:
        complementary_categories_with_weights = get_complementary_categories_from_graph(category_graph_data, source_category) # <-- Použita plná kategorie
        logger.info(f"Nalezeno {len(complementary_categories_with_weights)} kompl. kategorií pro '{source_category}'.") # <-- Použita plná kategorie
    else:
        logger.warning("Graf kategorií není k dispozici.")
        return False # Bez grafu nemůžeme pokračovat

    # --- Získání kandidátů (přesunutá logika z API) ---
    candidates_for_rerank = []
    if complementary_categories_with_weights:
        target_simple_categories = [cat for cat, weight in complementary_categories_with_weights]
        logger.info(f"Cílové jednoduché kategorie: {target_simple_categories}")

        try:
            logger.info(f"Spouštím scroll pro {candidate_limit} kandidátů (kromě {product_id}).")
            general_scroll_results, _ = await qdrant_client.scroll(
                collection_name=products_collection,
                scroll_filter=models.Filter(must_not=[models.HasIdCondition(has_id=[source_qdrant_id])]),
                limit=candidate_limit,
                with_payload=True
            )
            logger.info(f"Scroll získal {len(general_scroll_results)} potenciálních kandidátů.")

            processed_ids = set()
            temp_candidates = []
            for point in general_scroll_results:
                payload = point.payload
                qdrant_category = payload.get("category")
                point_id_str = str(payload.get('product_id') or payload.get('original_id') or point.id) # Převedeme na string

                if not qdrant_category or point_id_str == product_id or point_id_str in processed_ids: # Dodatečná kontrola ID
                    continue

                category_matches = False
                for simple_cat in target_simple_categories:
                    if qdrant_category == simple_cat or qdrant_category.endswith(f" > {simple_cat}"):
                        category_matches = True
                        break

                if category_matches:
                    temp_candidates.append({"payload": payload, "score": None})
                    processed_ids.add(point_id_str)

            random.shuffle(temp_candidates)
            candidates_for_rerank = temp_candidates
            logger.info(f"Po lokálním filtrování zbývá {len(candidates_for_rerank)} kandidátů.")

        except Exception as scroll_err:
             logger.error(f"Chyba při scrollu/filtrování pro {product_id}: {scroll_err}", exc_info=True)
             return False

    else:
         logger.warning(f"Nenalezeny komplementární kategorie pro '{source_category}'.")
         # Uložíme prázdný výsledek? Nebo neukládáme nic? Zatím neukládáme.
         return False

    # --- Reranking pomocí Gemini ---
    reranked_results_with_data = []
    gemini_scores = {}
    sorted_candidate_ids = []

    if not candidates_for_rerank:
        logger.info(f"Žádní kandidáti pro reranking pro {product_id}.")
        # Uložíme prázdný výsledek? Zatím ne.
        return False # Nebylo co rerankovat

    try:
        # Upraveno: Posíláme jen ID, název a klíčová slova zdrojového produktu
        source_description = source_payload.get('description', '')
        source_keywords = _extract_keywords(source_description)
        source_product_info = {
            "id": product_id,
            "name": source_payload.get('name', ''),
            "keywords": source_keywords
        }
        candidate_info_list = []
        candidate_payload_map = {}

        if len(candidates_for_rerank) > candidate_limit:
             logger.info(f"Omezuji počet kandidátů pro Gemini z {len(candidates_for_rerank)} na {candidate_limit}.")
             candidates_for_rerank = candidates_for_rerank[:candidate_limit]

        for cand in candidates_for_rerank:
             payload = cand['payload']
             cand_id = str(payload.get('product_id') or payload.get('original_id') or cand.get('id'))
             description = payload.get('description', '')
             keywords = _extract_keywords(description)
             # Upraveno: Posíláme jen ID, název a klíčová slova kandidáta
             candidate_info_list.append({
                 "id": cand_id,
                 "name": payload.get('name', ''),
                 "keywords": keywords
             })
             candidate_payload_map[cand_id] = payload

        logger.info(f"Posílám {len(candidate_info_list)} kandidátů (název + klíčová slova) do Gemini reranker.")

        if candidate_info_list:
            # Upraven prompt, aby reflektoval změnu dat (keywords místo description/category)
            prompt = f"""
Source product:
{json.dumps(source_product_info, indent=2, ensure_ascii=False)}

Candidate products (with keywords):
{json.dumps(candidate_info_list, indent=2, ensure_ascii=False)}

Please rerank the candidate products based on how complementary they are to the source product.
Consider the product names and associated keywords. Think about typical usage scenarios and potential cross-selling opportunities.
Focus on relevance for a customer who bought the source product.

Return ONLY the reranked list as a valid JSON array of objects, ordered from most to least complementary.
Each object MUST contain only the 'id' (string) of the product and a 'relevance_score' (float between 0.0 and 1.0).
Include exactly the top {rerank_limit} results.

Example Output Format:
[{{"id": "prodC", "relevance_score": 0.95}}, {{"id": "prodA", "relevance_score": 0.88}}]
"""
            logger.debug(f"Gemini Prompt (prvních 500 znaků):\n{prompt[:500]}...")

            try:
                response = await gemini_client.generate_content_async(
                    prompt,
                    generation_config=genai.types.GenerationConfig(response_mime_type="application/json", temperature=0.1)
                )
                logger.debug(f"Raw Gemini response: {response.text}")
                reranked_list = json.loads(response.text)

                if not isinstance(reranked_list, list): raise ValueError("Gemini response is not a list")
                if not all(isinstance(item, dict) and 'id' in item and 'relevance_score' in item for item in reranked_list):
                     raise ValueError("Gemini response items lack 'id' or 'relevance_score'")

                gemini_scores = {item['id']: item['relevance_score'] for item in reranked_list}
                sorted_candidate_ids = [item['id'] for item in reranked_list]
                logger.info(f"Úspěšně získáno {len(sorted_candidate_ids)} výsledků z Gemini pro {product_id}.")

            except json.JSONDecodeError as json_err:
                logger.error(f"Chyba parsování JSON odpovědi z Gemini pro {product_id}: {json_err}\nRaw response: {response.text if 'response' in locals() else 'N/A'}", exc_info=True)
            except Exception as gemini_err:
                logger.error(f"Chyba při volání/zpracování Gemini API pro {product_id}: {gemini_err}", exc_info=True)

        # Zpracování výsledků
        if sorted_candidate_ids:
            for candidate_id in sorted_candidate_ids:
                 if candidate_id in candidate_payload_map:
                     payload = candidate_payload_map[candidate_id]
                     payload['gemini_score'] = gemini_scores.get(candidate_id) # Přidáme skóre
                     reranked_results_with_data.append(payload)
                 else:
                      logger.warning(f"Kandidát s ID {candidate_id} vrácený Gemini nebyl nalezen v původních kandidátech.")
            logger.info(f"Proveden reranking, finální počet: {len(reranked_results_with_data)}")
        else:
             logger.warning(f"Gemini nevrátil výsledky nebo nastala chyba pro {product_id}. Používám fallback - prvních {rerank_limit} neřazených kandidátů.")
             # Fallback: vezmeme prvních N z kandidátů (už jsou zamíchaní)
             reranked_results_with_data = [c['payload'] for c in candidates_for_rerank[:rerank_limit]]
             # Nastavíme skóre na None nebo 0? Nastavíme None
             for item in reranked_results_with_data: item['gemini_score'] = None

    except Exception as rerank_exc:
        logger.error(f"Neočekávaná chyba v sekci Gemini rerankingu pro {product_id}: {rerank_exc}", exc_info=True)
        # Fallback: vezmeme prvních N z kandidátů
        reranked_results_with_data = [c['payload'] for c in candidates_for_rerank[:rerank_limit]]
        for item in reranked_results_with_data: item['gemini_score'] = None
        logger.warning("Použit fallback kvůli chybě v reranking sekci.")

    # --- Uložení do Cache ---
    if reranked_results_with_data:
        try:
            # Ověření/vytvoření cache kolekce (mělo by se dělat jednou na začátku skriptu)
            # await ensure_collection_exists(qdrant_client, complementary_cache_collection)

            cache_payload = {
                "main_product_id": product_id,
                "complementary_product_ids": [str(p.get("product_id") or p.get("original_id")) for p in reranked_results_with_data],
                "gemini_scores": { str(p.get("product_id") or p.get("original_id")): p.get("gemini_score") for p in reranked_results_with_data if p.get("gemini_score") is not None },
                "computed_at": time.time()
            }
            await qdrant_client.upsert(
                collection_name=complementary_cache_collection,
                points=[PointStruct(id=source_qdrant_id, payload=cache_payload, vector={})],
                wait=True # V batchi chceme počkat na uložení
            )
            logger.info(f"Uloženy komplementární produkty do cache pro {product_id}.")
            return True
        except Exception as cache_write_err:
            logger.error(f"Chyba při ukládání do cache {complementary_cache_collection} pro {product_id}: {cache_write_err}", exc_info=True)
            return False
    else:
        logger.info(f"Nebyly nalezeny žádné finální komplementární produkty pro {product_id} k uložení.")
        # Můžeme zvážit uložení prázdného záznamu do cache, aby se příště nehledal znovu
        # await save_empty_cache_entry(qdrant_client, complementary_cache_collection, source_qdrant_id, product_id)
        return False # Nebylo co uložit

# --- Funkce pro zajištění existence kolekce ---
async def ensure_collection_exists(client: AsyncQdrantClient, collection_name: str):
    try:
        await client.get_collection(collection_name=collection_name)
        logger.debug(f"Cache kolekce '{collection_name}' již existuje.")
    except Exception as e:
        # Předpokládáme, že chyba znamená, že kolekce neexistuje
        logger.warning(f"Cache kolekce '{collection_name}' nenalezena, pokouším se vytvořit: {e}")
        try:
            await client.create_collection(
                collection_name=collection_name,
                vectors_config={} # Bez vektorů
            )
            logger.info(f"Cache kolekce '{collection_name}' úspěšně vytvořena.")
        except Exception as create_err:
            logger.error(f"Nepodařilo se vytvořit cache kolekci '{collection_name}': {create_err}", exc_info=True)
            raise # Chybu propagujeme dál, bez cache kolekce nemá smysl pokračovat

# --- Hlavní spouštěcí funkce ---
async def main(tenant_id: str, product_ids: Optional[List[str]] = None, concurrency_limit: int = 20):
    """Hlavní funkce pro spuštění batch výpočtu."""
    logger.info(f"Spouštím výpočet komplementárních produktů pro tenanta: {tenant_id}")
    logger.info(f"Limit souběžných volání Gemini nastaven na: {concurrency_limit}")

    # Inicializace klientů
    try:
        qdrant_client = get_qdrant_async_client()
        gemini_client = get_gemini_client()
        logger.info("Klienti Qdrant a Gemini úspěšně inicializováni.")
    except Exception as client_err:
        logger.error(f"Chyba při inicializaci klientů: {client_err}", exc_info=True)
        return

    # Načtení konfigurace a grafu tenanta
    tenant_config = get_tenant_yaml_config(tenant_id)
    if not tenant_config: return

    category_graph_path = tenant_config.get("recommendations", {}).get("category_graph_path") or tenant_config.get("category_graph_path")
    category_graph_data = None
    if category_graph_path:
        category_graph_data = load_category_graph(category_graph_path)
        if category_graph_data: logger.info("Graf kategorií úspěšně načten.")
        else: logger.warning("Graf kategorií nebyl načten, výpočet nemusí fungovat správně."); return
    else:
        logger.error("Cesta ke grafu kategorií není v konfiguraci tenanta.")
        return

    # Zajištění existence cache kolekce
    products_collection = f"real_products_{tenant_id}"
    complementary_cache_collection = f"complementary_products_{tenant_id}"
    try:
        await ensure_collection_exists(qdrant_client, complementary_cache_collection)
    except:
        return # Pokud se nepodaří vytvořit/ověřit cache, končíme

    # Získání seznamu produktů k zpracování
    products_to_process = []
    if product_ids:
        logger.info(f"Zpracovávám zadaný seznam {len(product_ids)} produktů.")
        products_to_process = [str(pid) for pid in product_ids] # Zajistíme stringy
    else:
        logger.info(f"Získávám VŠECHNA produktová ID z kolekce '{products_collection}'...")
        try:
            all_ids = set()
            offset = None
            processed_count = 0 # Jen pro logování
            limit_per_scroll = 500 # Kolik ID načíst v jednom scroll volání

            while True: # Odebrán limit target_total
                scroll_result, next_offset = await qdrant_client.scroll(
                    collection_name=products_collection,
                    limit=limit_per_scroll, # Načítáme po dávkách
                    offset=offset,
                    with_payload=["product_id", "original_id"], # Jen potřebná pole
                    with_vectors=False
                )
                
                if not scroll_result:
                    logger.info("Scroll nevrátil žádné další výsledky, končím načítání ID.")
                    break # Pokud scroll nic nevrátí, končíme

                found_new_in_batch = 0
                for point in scroll_result:
                    # Prioritně bereme product_id, fallback na original_id
                    p_id = point.payload.get("product_id") or point.payload.get("original_id")
                    if p_id:
                        if str(p_id) not in all_ids: # Přidáváme jen pokud ještě není
                             all_ids.add(str(p_id)) # Ukládáme jako string
                             found_new_in_batch += 1
                
                processed_count += len(scroll_result)
                offset = next_offset
                logger.debug(f"Načteno {len(scroll_result)} bodů v dávce, nalezeno {found_new_in_batch} nových unikátních ID. Celkem unikátních ID: {len(all_ids)}. Další offset: {offset}")

                # Pokud už není další offset, končíme
                if offset is None:
                    logger.info("Dosáhli jsme konce scrollování (offset is None).")
                    break
            
            products_to_process = list(all_ids)
            logger.info(f"Získáno {len(products_to_process)} unikátních produktových ID ke zpracování.")

        except Exception as e:
            logger.error(f"Chyba při získávání ID produktů z Qdrant: {e}", exc_info=True)
            return

    if not products_to_process:
        logger.warning("Nebyly nalezeny/zadány žádné produkty ke zpracování.")
        return

    # Zpracování produktů s omezením souběžnosti
    start_time_total = time.time()
    success_count = 0
    fail_count = 0
    skipped_count = 0 # Počítadlo přeskočených
    cache_ttl_seconds = 30 * 24 * 60 * 60 # 30 dní v sekundách
    semaphore = asyncio.Semaphore(concurrency_limit)

    async def process_single_product(prod_id):
        async with semaphore: # Získáme zámek před spuštěním
            logger.debug(f"Získán semaphore pro produkt {prod_id}")
            result = await compute_and_save_complementary(
                tenant_id=tenant_id,
                product_id=prod_id,
                qdrant_client=qdrant_client,
                gemini_client=gemini_client,
                category_graph_data=category_graph_data
            )
            # Přidáme krátkou pauzu i zde, abychom byli ještě šetrnější k limitům
            await asyncio.sleep(0.1) # Malá pauza po každém zpracování
            logger.debug(f"Uvolněn semaphore pro produkt {prod_id}")
            return result # Vrátíme výsledek (True/False)

    tasks = []
    logger.info(f"Kontroluji cache a připravuji úlohy pro {len(products_to_process)} produktů...")
    for prod_id in products_to_process:
        qdrant_id = create_qdrant_id(prod_id)
        try:
            # Zkusíme načíst jen payload s časovým razítkem z cache
            cache_points = await qdrant_client.retrieve(
                collection_name=complementary_cache_collection,
                ids=[qdrant_id],
                with_payload=["computed_at"]
            )
            
            if cache_points and cache_points[0].payload and "computed_at" in cache_points[0].payload:
                computed_at = cache_points[0].payload["computed_at"]
                if isinstance(computed_at, (int, float)):
                    cache_age = time.time() - computed_at
                    if cache_age < cache_ttl_seconds:
                        logger.debug(f"Produkt {prod_id} přeskočen, cache je čerstvá (stáří: {cache_age/3600:.1f}h).")
                        skipped_count += 1
                        continue # Přeskočíme vytvoření tasku pro tento produkt
                    else:
                        logger.debug(f"Cache pro {prod_id} je starší než 30 dní, bude přepočítána.")
                else:
                    logger.warning(f"Neplatný formát 'computed_at' v cache pro {prod_id}, bude přepočítán.")
            else:
                 logger.debug(f"Produkt {prod_id} nenalezen v cache nebo nemá 'computed_at', bude zpracován.")

        except Exception as cache_check_err:
            logger.warning(f"Chyba při kontrole cache pro {prod_id}: {cache_check_err}. Produkt bude zpracován.")

        # Pokud jsme se dostali sem, produkt se má zpracovat
        tasks.append(process_single_product(prod_id))
    
    logger.info(f"Celkem produktů ke zpracování po kontrole cache: {len(tasks)} (Přeskočeno: {skipped_count})")
    
    # Spustíme tasky (díky semaforu poběží max `concurrency_limit` najednou)
    if tasks:
        results = await asyncio.gather(*tasks, return_exceptions=True)
    else:
        results = [] # Pokud nejsou žádné tasky

    # Vyhodnocení výsledků (zůstává stejné, ale upravíme logování)
    actual_processed_count = len(tasks) # Počet skutečně zpracovaných
    for i, result in enumerate(results):
        # Získání správného ID ze seznamu tasks (ne products_to_process)
        # Potřebujeme extrahovat ID z tasku nebo si ho pamatovat jinak
        # Jednodušší je upravit logování na konci
        if isinstance(result, Exception):
            # Logování chyby už probíhá v `process_single_product` nebo `compute_and_save`
            # Zde jen počítáme
            fail_count += 1
        elif result is True:
            success_count += 1
        else: # result is False
            # Logování warningu už probíhá v `compute_and_save`
            fail_count += 1 

    end_time_total = time.time()
    total_duration = end_time_total - start_time_total
    logger.info(f"Dokončeno zpracování pro tenant '{tenant_id}'.")
    logger.info(f"Počet produktů ke kontrole: {len(products_to_process)}")
    logger.info(f"Přeskočeno (čerstvá cache): {skipped_count}")
    logger.info(f"Skutečně zpracováno: {actual_processed_count}")
    logger.info(f"Úspěšně uloženo do cache: {success_count}")
    logger.info(f"Neúspěšně/Nepodařilo se uložit: {fail_count}")
    logger.info(f"Celkový čas: {total_duration:.2f} s")

if __name__ == "__main__":
    # Ponecháme možnost zadat tenanta a ID z argumentů
    # Pokud ID nejsou zadána, `main` se pokusí načíst prvních 1000
    tenant_to_process = "filsonstore"
    specific_product_ids = None 
    # Nastavení výchozí concurrency na 20 pro Tier 1
    concurrency = 20 
    logger.info(f"Výchozí limit souběžnosti nastaven na: {concurrency} (pro Tier 1)")

    if len(sys.argv) > 1:
        tenant_to_process = sys.argv[1]
    if len(sys.argv) > 2:
        # Pokud druhý argument není číslo (pro concurrency), bereme ho jako ID
        try:
            concurrency = int(sys.argv[2])
            logger.info(f"Používám limit souběžnosti z argumentu: {concurrency}")
            if len(sys.argv) > 3:
                 specific_product_ids = [p_id.strip() for p_id in sys.argv[3].split(',')]
        except ValueError:
             # Druhý argument nebyl číslo, předpokládáme, že to jsou ID
             specific_product_ids = [p_id.strip() for p_id in sys.argv[2].split(',')]
             # Zkusíme získat concurrency ze třetího argumentu
             if len(sys.argv) > 3:
                 try:
                     concurrency = int(sys.argv[3])
                     logger.info(f"Používám limit souběžnosti z argumentu: {concurrency}")
                 except ValueError:
                     logger.warning("Třetí argument není platné číslo pro limit souběžnosti, používám default: 5")
    
    # Spuštění hlavní asynchronní funkce
    asyncio.run(main(tenant_id=tenant_to_process, product_ids=specific_product_ids, concurrency_limit=concurrency)) 