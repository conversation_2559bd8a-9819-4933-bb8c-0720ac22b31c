#!/usr/bin/env python3
"""
Monitoring skript pro sledování pokroku výpočtu komplementárních produktů.
Tento skript pomáhá sledovat, jak probíhá výpočet na pozadí.

Analogie: Jako dashboard pro sledování výroby v továrně - 
vidíme, kolik je hotovo a jak rychle se pokračuje.

Autor: <PERSON>: 2025-01-17
"""

import os
import time
import json
from datetime import datetime

def check_output_files():
    """Kontroluje existenci a velikost výstupních souborů."""
    files_to_check = [
        "all_complementary_jabkolevne.json",
        "test_complementary_jabkolevne.json"
    ]
    
    print("📁 === KONTROLA VÝSTUPNÍCH SOUBORŮ ===")
    print(f"⏰ Čas: {datetime.now().strftime('%H:%M:%S')}")
    
    for filename in files_to_check:
        if os.path.exists(filename):
            size_mb = os.path.getsize(filename) / 1024 / 1024
            modified_time = os.path.getmtime(filename)
            modified_str = datetime.fromtimestamp(modified_time).strftime('%H:%M:%S')
            print(f"✅ {filename}: {size_mb:.2f} MB (změněn: {modified_str})")
            
            # Pokud je soubor větší než 1 MB, zkusíme načíst metadata
            if size_mb > 0.1:
                try:
                    with open(filename, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        metadata = data.get('metadata', {})
                        if metadata:
                            total_products = metadata.get('total_products_with_recommendations', 0)
                            total_recommendations = metadata.get('total_recommendations', 0)
                            print(f"   📊 Produkty s doporučeními: {total_products:,}")
                            print(f"   🔗 Celkem doporučení: {total_recommendations:,}")
                except Exception as e:
                    print(f"   ⚠️ Nepodařilo se načíst metadata: {e}")
        else:
            print(f"❌ {filename}: neexistuje")
    
    print()

def check_process_status():
    """Kontroluje, zda běží proces výpočtu."""
    import subprocess
    try:
        # Hledáme Python procesy s názvem našeho skriptu
        result = subprocess.run(['pgrep', '-f', 'compute_all_complementary_jabkolevne.py'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            pids = result.stdout.strip().split('\n')
            print(f"🔄 Výpočet běží (PID: {', '.join(pids)})")
            return True
        else:
            print("⏸️ Výpočet neběží")
            return False
            
    except Exception as e:
        print(f"⚠️ Nepodařilo se zkontrolovat proces: {e}")
        return False

def show_estimated_completion():
    """Odhadne dokončení na základě velikosti souboru."""
    filename = "all_complementary_jabkolevne.json"
    
    if not os.path.exists(filename):
        print("📊 Odhad dokončení: soubor ještě neexistuje")
        return
    
    try:
        size_mb = os.path.getsize(filename) / 1024 / 1024
        
        # Odhadujeme na základě velikosti (velmi přibližné)
        # Předpokládáme, že kompletní soubor bude mít cca 10-50 MB
        estimated_final_size = 25  # MB
        progress_percent = min(100, (size_mb / estimated_final_size) * 100)
        
        print(f"📊 Odhadovaný pokrok: {progress_percent:.1f}% ({size_mb:.2f}/{estimated_final_size} MB)")
        
        if progress_percent > 0 and progress_percent < 100:
            # Odhadovaný zbývající čas
            # Toto je velmi hrubý odhad
            estimated_remaining_percent = 100 - progress_percent
            print(f"⏳ Odhadovaný zbývající pokrok: {estimated_remaining_percent:.1f}%")
            
    except Exception as e:
        print(f"⚠️ Chyba při odhadu: {e}")

def main():
    """Hlavní monitoring funkce."""
    print("🔍 === MONITORING VÝPOČTU KOMPLEMENTÁRNÍCH PRODUKTŮ ===")
    print("Spustím monitoring každých 30 sekund. Pro ukončení stiskněte Ctrl+C")
    print()
    
    try:
        while True:
            check_output_files()
            check_process_status()
            show_estimated_completion()
            
            print("─" * 60)
            print("⏰ Další kontrola za 30 sekund...")
            print()
            
            time.sleep(30)
            
    except KeyboardInterrupt:
        print("\n🛑 Monitoring ukončen uživatelem")
    except Exception as e:
        print(f"\n❌ Chyba v monitoringu: {e}")

if __name__ == "__main__":
    main() 