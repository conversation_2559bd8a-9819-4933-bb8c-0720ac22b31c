# Použijeme oficiální Python image
FROM python:3.10-slim

# Nastavíme pracovní adres<PERSON>ř uvnitř kontejneru
WORKDIR /app

# Zkopírujeme soubor se závislostmi
COPY requirements.txt requirements.txt

# Nainstalujeme závislosti
# --no-cache-dir: Nezanechává cache, šetří místo v image
# --upgrade pip: <PERSON><PERSON><PERSON><PERSON> nejnovější pip
# -r requirements.txt: Instaluje z requirements souboru
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Zkopírujeme zbytek kódu aplikace do pracovního adresáře v kontejneru
COPY . .

# Vystavíme port, na kterém bude API naslouchat uvnitř kontejneru
# (Předpokládáme, že Uvicorn/FastAPI poběží na portu 8000)
EXPOSE 8000

# Spouště<PERSON><PERSON> příkaz pro API server (Předpokládáme FastAPI a soubor api.py)
# --host 0.0.0.0: Um<PERSON>žní přístup zvenčí kontejneru
# --port 8000: Port uvnitř kontejneru
# --reload: Automaticky restartuje server při změně kódu (pro lokální vývoj)
# TODO: Pro produkci na serveru odstraň '--reload' a případně nastav '--workers'
CMD ["uvicorn", "api:app", "--host", "0.0.0.0", "--port", "8000", "--reload"] 