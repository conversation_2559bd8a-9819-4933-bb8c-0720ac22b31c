# Ultimate Ensemble Model pro doporučování komplementárních produktů

Tento dokument popisuje implementaci Ultimate Ensemble Modelu, který kombinuje nejlepší vlastnosti různých modelů pro doporučování komplementárních produktů s cílem maximálně se přiblížit kvalitě LLM doporučení.

## Přehled

Ultimate Ensemble Model je pokročilý ensemble model, který kombinuje výsledky tří nejlepš<PERSON>ch modelů:

1. **Improved Teacher-Student Model** - Model přímo imitující LLM doporučení pomocí KL divergence
2. **Advanced Transformer Model** - Pok<PERSON>čilý model využívající transformer architekturu
3. **Contrastive Teacher-Student Model** - Model využívající contrastive learning

Hlavní výhody Ultimate Ensemble Modelu:

- **Automatická kalibrace vah** - <PERSON><PERSON><PERSON> jed<PERSON> modelů jsou automaticky kalibrovány na zák<PERSON><PERSON> jejich překryvu s LLM doporučeními
- **<PERSON>yšš<PERSON> přesnost** - Kombinace modelů poskytuje vyšší přesnost než jednotlivé modely samostatně
- **Robustnost** - Ensemble model je robustnější vůči chybám jednotlivých modelů
- **Komplexnější pohled** - Různé modely zachycují různé aspekty dat, což poskytuje komplexnější pohled

## Architektura

```
Input Embeddings
    |
  /   |   \
 /    |    \
Teacher-Student  Advanced Transformer  Contrastive Model
 \     |    /
  \    |   /
Weighted Average (automaticky kalibrované váhy)
    |
L2 Normalization
```

## Použití

### Kalibrace vah a indexace produktů

```bash
python generate_ultimate_ensemble_recommendations.py --tenant avenberg --calibrate --sample-size 20
python generate_ultimate_ensemble_recommendations.py --tenant avenberg --index --force
```

### Generování doporučení

```bash
python generate_ultimate_ensemble_recommendations.py --tenant avenberg --product-id 278 --top-k 5
```

### Porovnání s LLM doporučeními

```bash
python generate_ultimate_ensemble_recommendations.py --tenant avenberg --product-id 278 --compare
```

### Vyhodnocení výkonu modelu

```bash
python evaluate_ensemble_performance.py --tenant avenberg --sample-size 20 --model all
```

### Spuštění celého workflow

```bash
python run_ultimate_ensemble_workflow.py --tenant avenberg
```

## Implementace

Ultimate Ensemble Model je implementován v následujících souborech:

- `ultimate_ensemble_model.py` - Implementace UltimateEnsembleModel třídy
- `generate_ultimate_ensemble_recommendations.py` - Skript pro generování doporučení
- `evaluate_ensemble_performance.py` - Skript pro vyhodnocení výkonu modelu
- `run_ultimate_ensemble_workflow.py` - Skript pro spuštění celého workflow

## Výhody oproti předchozím modelům

1. **Automatická kalibrace vah** - Váhy modelů jsou automaticky kalibrovány na základě jejich překryvu s LLM doporučeními, což zajišťuje optimální kombinaci modelů
2. **Vyšší přesnost** - Kombinace nejlepších modelů poskytuje vyšší přesnost než jednotlivé modely nebo základní ensemble modely
3. **Detailní vyhodnocení** - Implementace zahrnuje detailní vyhodnocení výkonu modelu včetně přesných a částečných shod s LLM doporučeními
4. **Vizualizace výsledků** - Výsledky jsou vizualizovány pomocí grafů pro snadné porovnání s jinými modely

## Další možná vylepšení

1. **Dynamické váhy podle kategorie** - Implementace dynamických vah podle kategorie produktu
2. **Online learning** - Průběžné učení modelu na základě zpětné vazby uživatelů
3. **Personalizace** - Přidání personalizace na základě historie uživatele
4. **Multimodální integrace** - Integrace s obrazovými daty pro lepší doporučení
5. **Kontextové doporučování** - Zohlednění kontextu uživatele při generování doporučení
