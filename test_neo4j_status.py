#!/usr/bin/env python3
"""
Test skript pro kontrolu stavu Neo4j databáze.
"""

from neo4j import GraphDatabase
import os
from dotenv import load_dotenv

def check_neo4j_status():
    load_dotenv()
    
    neo4j_uri = os.getenv('NEO4J_URI', 'bolt://localhost:7687')
    neo4j_user = os.getenv('NEO4J_USER', 'neo4j')
    neo4j_password = os.getenv('NEO4J_PASSWORD', 'password')
    
    print(f"🔗 Připojuji se k Neo4j: {neo4j_uri}")
    
    try:
        driver = GraphDatabase.driver(neo4j_uri, auth=(neo4j_user, neo4j_password))
        
        with driver.session() as session:
            # Zkontrolovat kategorie filsonstore
            result = session.run('MATCH (c:Category_filsonstore) RETURN count(c) as count')
            cat_count = result.single()['count']
            print(f'📂 Kategorie filsonstore: {cat_count}')
            
            # Zkontrolovat vztahy COMPLEMENTARY_TO
            result = session.run('MATCH ()-[r:COMPLEMENTARY_TO]->() RETURN count(r) as count')
            rel_count = result.single()['count']
            print(f'🔗 COMPLEMENTARY_TO vztahy: {rel_count}')
            
            # Zkontrolovat všechny typy vztahů
            result = session.run('CALL db.relationshipTypes()')
            rel_types = [record['relationshipType'] for record in result]
            print(f'📋 Dostupné typy vztahů: {", ".join(rel_types)}')
            
            # Ukázka kategorií
            result = session.run('MATCH (c:Category_filsonstore) RETURN c.name LIMIT 5')
            print('📝 Ukázka kategorií:')
            for record in result:
                print(f'   - {record["c.name"]}')
                
            # Zkontrolovat jestli existují nějaké vztahy vůbec
            result = session.run('MATCH ()-[r]->() RETURN type(r) as rel_type, count(r) as count')
            print('🔍 Všechny vztahy v databázi:')
            for record in result:
                print(f'   - {record["rel_type"]}: {record["count"]}')
        
        driver.close()
        return True
        
    except Exception as e:
        print(f"❌ Chyba při připojení k Neo4j: {e}")
        return False

if __name__ == "__main__":
    check_neo4j_status() 