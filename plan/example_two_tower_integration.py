# Příklad integrace Two-Tower modelu do současného systému

import numpy as np
import tensorflow as tf
from typing import List, Dict, Tuple

class TwoTowerRecommender:
    """Two-Tower model pro nahrazení scroll-based kandid<PERSON>tů"""
    
    def __init__(self, model_path: str):
        self.query_tower = tf.keras.models.load_model(f"{model_path}/query_tower.h5")
        self.item_tower = tf.keras.models.load_model(f"{model_path}/item_tower.h5")
        
        # Pre-computed item embeddings for all products
        self.item_embeddings = np.load(f"{model_path}/item_embeddings.npy")
        self.product_id_to_idx = np.load(f"{model_path}/product_id_to_idx.npy", allow_pickle=True).item()
    
    def get_candidates(self, source_product: Dict, candidate_limit: int = 100) -> List[Tuple[str, float]]:
        """Nahrazuje scroll-based v<PERSON><PERSON><PERSON><PERSON>ndi<PERSON>"""
        
        # 1. Encode query product features
        query_features = self._prepare_query_features(source_product)
        query_embedding = self.query_tower.predict(query_features)[0]
        
        # 2. Compute similarities with all items
        similarities = np.dot(self.item_embeddings, query_embedding)
        
        # 3. Get top candidates with scores
        top_indices = np.argsort(similarities)[-candidate_limit:][::-1]
        
        candidates = []
        for idx in top_indices:
            product_id = self.idx_to_product_id[idx]
            score = float(similarities[idx])
            candidates.append((product_id, score))
            
        return candidates
    
    def _prepare_query_features(self, product: Dict) -> np.ndarray:
        """Convert product to model features"""
        # Features: category_encoded, price_normalized, brand_encoded, etc.
        features = [
            self._encode_category(product.get('category', '')),
            self._normalize_price(product.get('price', 0)),
            self._encode_brand(product.get('brand', '')),
            # ... další features
        ]
        return np.array([features])

# Integrace do hlavního systému:
async def compute_and_save_complementary_with_two_tower(
    tenant_id: str,
    product_id: str,
    qdrant_client: AsyncQdrantClient,
    gemini_client: genai.GenerativeModel,
    two_tower_model: TwoTowerRecommender  # NOVÝ parametr
):
    """Upravená verze s Two-Tower modelem místo scroll"""
    
    # 1. Získej source product
    source_payload = await _get_product_payload_async(qdrant_client, products_collection, product_id)
    
    # 2. TWO-TOWER kandidáti místo scroll
    candidates_with_scores = two_tower_model.get_candidates(source_payload, candidate_limit=100)
    
    # 3. Convert to ProductCandidate format
    candidates = []
    for prod_id, score in candidates_with_scores:
        candidate_payload = await _get_product_payload_async(qdrant_client, products_collection, prod_id)
        candidates.append(ProductCandidate(
            product_id=prod_id,
            payload=candidate_payload,
            qdrant_score=score,  # Two-tower score místo random
            category=candidate_payload.get("category", "")
        ))
    
    # 4. Zbytek zůstává stejný - Gemini reranking
    batch_request = BatchProcessingRequest(
        source_product_id=product_id,
        source_info=source_payload,
        candidates=candidates
    )
    
    return batch_request