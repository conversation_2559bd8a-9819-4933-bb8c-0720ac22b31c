# Příklad integrace Transformer modelu 

import torch
import torch.nn as nn
from transformers import AutoModel, AutoTokenizer
from typing import List, Dict, Tuple

class TransformerRecommender:
    """Transformer model pro end-to-end recommendations"""
    
    def __init__(self, model_path: str):
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
        self.tokenizer = AutoTokenizer.from_pretrained("sentence-transformers/all-MiniLM-L6-v2")
        self.model = self._load_recommendation_transformer(model_path)
        
    def _load_recommendation_transformer(self, model_path: str):
        """Load custom transformer for recommendations"""
        class RecommendationTransformer(nn.Module):
            def __init__(self):
                super().__init__()
                self.encoder = AutoModel.from_pretrained("sentence-transformers/all-MiniLM-L6-v2")
                self.recommendation_head = nn.Sequential(
                    nn.Linear(384, 256),
                    nn.ReLU(),
                    nn.Dropout(0.1),
                    nn.Linear(256, 1)  # Compatibility score
                )
                
            def forward(self, source_tokens, candidate_tokens):
                # Encode source and candidates
                source_emb = self.encoder(**source_tokens).last_hidden_state.mean(dim=1)
                candidate_emb = self.encoder(**candidate_tokens).last_hidden_state.mean(dim=1)
                
                # Cross-attention or concatenation
                combined = torch.cat([source_emb, candidate_emb, source_emb * candidate_emb], dim=-1)
                score = self.recommendation_head(combined)
                return score
        
        model = RecommendationTransformer()
        model.load_state_dict(torch.load(f"{model_path}/transformer_model.pt"))
        model.to(self.device)
        model.eval()
        return model
    
    def get_recommendations(self, source_product: Dict, candidates: List[Dict], 
                          rerank_limit: int = 10) -> List[Tuple[str, float]]:
        """Nahrazuje Gemini reranking"""
        
        # 1. Prepare source text
        source_text = self._product_to_text(source_product)
        source_tokens = self.tokenizer(source_text, padding=True, truncation=True, 
                                     return_tensors="pt").to(self.device)
        
        # 2. Score all candidates
        scores = []
        for candidate in candidates:
            candidate_text = self._product_to_text(candidate['payload'])
            candidate_tokens = self.tokenizer(candidate_text, padding=True, truncation=True,
                                            return_tensors="pt").to(self.device)
            
            with torch.no_grad():
                score = self.model(source_tokens, candidate_tokens).item()
            
            scores.append((candidate['product_id'], score))
        
        # 3. Sort and return top recommendations
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores[:rerank_limit]
    
    def _product_to_text(self, product: Dict) -> str:
        """Convert product to text for transformer"""
        name = product.get('name', '')
        category = product.get('category', '')
        brand = product.get('brand', '')
        description = product.get('description', '')[:200]  # Truncate
        
        return f"Product: {name}. Category: {category}. Brand: {brand}. {description}"

# Integrace do hlavního systému:
async def compute_complementary_with_transformer(
    gemini_client: genai.GenerativeModel,
    batch_requests: List[BatchProcessingRequest],
    transformer_model: TransformerRecommender,  # NOVÝ parametr
    rerank_limit: int = 10
) -> Dict[str, List[Tuple[str, float]]]:
    """Nahrazuje Gemini batch processing"""
    
    results = {}
    for request in batch_requests:
        # TRANSFORMER reranking místo Gemini
        recommendations = transformer_model.get_recommendations(
            request.source_info,
            [{'product_id': c.product_id, 'payload': c.payload} for c in request.candidates],
            rerank_limit
        )
        results[request.source_product_id] = recommendations
    
    return results