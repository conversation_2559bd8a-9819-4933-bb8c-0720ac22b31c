# State-of-the-Art Real-time Recommendation System

import numpy as np
import redis
import time
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from enum import Enum

class UserAction(Enum):
    VIEW = "view"
    CLICK = "click"
    ADD_TO_CART = "add_to_cart"
    PURCHASE = "purchase"
    REJECT = "reject"  # Explicitly passed by

@dataclass
class UserEvent:
    user_id: str
    product_id: str
    action: UserAction
    timestamp: float
    session_id: str
    dwell_time: Optional[float] = None  # Time spent viewing
    context: Optional[Dict] = None  # Search query, category browsed, etc.

class RealTimeUserVector:
    """
    State-of-the-art real-time user intent vector
    Combines: recency, frequency, diversity, intent signals
    """
    
    def __init__(self, redis_client: redis.Redis, vector_dim: int = 512):
        self.redis = redis_client
        self.vector_dim = vector_dim
        
        # Decay parameters (tunable)
        self.time_decay_half_life = 3600  # 1 hour
        self.action_weights = {
            UserAction.VIEW: 1.0,
            UserAction.CLICK: 2.0,
            UserAction.ADD_TO_CART: 5.0,
            UserAction.PURCHASE: 10.0,
            UserAction.REJECT: -1.0  # Negative signal
        }
        
    async def update_user_vector(self, event: UserEvent, product_embedding: np.ndarray):
        """Update user vector in real-time"""
        
        # 1. Get current user vector
        current_vector = await self._get_user_vector(event.user_id)
        
        # 2. Apply time decay to existing vector
        current_vector = self._apply_time_decay(current_vector, event.user_id)
        
        # 3. Compute event contribution
        event_contribution = self._compute_event_contribution(event, product_embedding)
        
        # 4. Update with momentum (like Adam optimizer)
        updated_vector = self._momentum_update(current_vector, event_contribution, event.user_id)
        
        # 5. Store updated vector with metadata
        await self._store_user_vector(event.user_id, updated_vector, event.timestamp)
        
        return updated_vector
    
    def _compute_event_contribution(self, event: UserEvent, product_embedding: np.ndarray) -> np.ndarray:
        """Compute how much this event contributes to user vector"""
        
        # Base weight from action type
        base_weight = self.action_weights[event.action]
        
        # Dwell time bonus (longer = more interested)
        dwell_bonus = 1.0
        if event.dwell_time:
            # Sigmoid function: 0-5s = 1x, 30s+ = 2x
            dwell_bonus = 1 + 1 / (1 + np.exp(-(event.dwell_time - 15) / 10))
        
        # Recency bonus (fresher events matter more)
        recency_bonus = 1.0  # Most recent = full weight
        
        # Context bonus (search vs random browse)
        context_bonus = 1.0
        if event.context and event.context.get('from_search'):
            context_bonus = 1.5  # Searched items are more intentional
            
        total_weight = base_weight * dwell_bonus * recency_bonus * context_bonus
        
        return product_embedding * total_weight
    
    def _apply_time_decay(self, vector: np.ndarray, user_id: str) -> np.ndarray:
        """Apply exponential time decay to existing preferences"""
        
        last_update = self.redis.hget(f"user_vector:{user_id}", "last_update")
        if not last_update:
            return vector
            
        time_since_update = time.time() - float(last_update)
        decay_factor = np.exp(-time_since_update / self.time_decay_half_life)
        
        return vector * decay_factor
    
    def _momentum_update(self, current_vector: np.ndarray, 
                        event_contribution: np.ndarray, user_id: str) -> np.ndarray:
        """Momentum-based update (like deep learning optimizers)"""
        
        # Get momentum vector
        momentum_key = f"user_momentum:{user_id}"
        momentum = self.redis.get(momentum_key)
        
        if momentum:
            momentum = np.frombuffer(momentum, dtype=np.float32)
        else:
            momentum = np.zeros(self.vector_dim, dtype=np.float32)
        
        # Update momentum (β = 0.9 like Adam)
        beta = 0.9
        momentum = beta * momentum + (1 - beta) * event_contribution
        
        # Update vector
        learning_rate = 0.1
        updated_vector = current_vector + learning_rate * momentum
        
        # Store momentum for next update
        self.redis.setex(momentum_key, 3600, momentum.tobytes())
        
        return updated_vector
    
    async def get_real_time_recommendations(self, user_id: str, 
                                          candidate_products: List[Dict],
                                          num_recommendations: int = 10) -> List[Tuple[str, float]]:
        """Get recommendations based on current user vector"""
        
        # 1. Get current user vector
        user_vector = await self._get_user_vector(user_id)
        
        if np.linalg.norm(user_vector) == 0:
            # Cold start - return diverse recommendations
            return self._cold_start_recommendations(candidate_products, num_recommendations)
        
        # 2. Score all candidates
        scores = []
        for product in candidate_products:
            product_embedding = np.array(product['embedding'])
            
            # Cosine similarity
            similarity = np.dot(user_vector, product_embedding) / (
                np.linalg.norm(user_vector) * np.linalg.norm(product_embedding)
            )
            
            # Apply business rules (diversity, novelty, etc.)
            final_score = self._apply_business_rules(similarity, product, user_id)
            
            scores.append((product['id'], final_score))
        
        # 3. Sort and return top recommendations
        scores.sort(key=lambda x: x[1], reverse=True)
        return scores[:num_recommendations]
    
    def _apply_business_rules(self, base_score: float, product: Dict, user_id: str) -> float:
        """Apply business logic: diversity, novelty, inventory, etc."""
        
        score = base_score
        
        # Diversity penalty (don't recommend too similar products)
        recent_views = self._get_recent_views(user_id, hours=24)
        for viewed_product in recent_views:
            if self._are_products_similar(product, viewed_product):
                score *= 0.8  # Diversity penalty
        
        # Novelty bonus (promote new/trending products)
        if product.get('is_new', False):
            score *= 1.1
            
        # Inventory bonus (promote in-stock items)
        if product.get('stock_level', 0) > 10:
            score *= 1.05
        elif product.get('stock_level', 0) == 0:
            score *= 0.1  # Heavy penalty for out-of-stock
            
        # Price sensitivity (learned from user behavior)
        user_price_preference = self._get_user_price_preference(user_id)
        price_match = self._compute_price_match(product['price'], user_price_preference)
        score *= price_match
        
        return score
    
    async def _get_user_vector(self, user_id: str) -> np.ndarray:
        """Get user vector from Redis"""
        vector_data = self.redis.hget(f"user_vector:{user_id}", "vector")
        
        if vector_data:
            return np.frombuffer(vector_data, dtype=np.float32)
        else:
            return np.zeros(self.vector_dim, dtype=np.float32)
    
    async def _store_user_vector(self, user_id: str, vector: np.ndarray, timestamp: float):
        """Store user vector in Redis with metadata"""
        pipe = self.redis.pipeline()
        pipe.hset(f"user_vector:{user_id}", "vector", vector.tobytes())
        pipe.hset(f"user_vector:{user_id}", "last_update", timestamp)
        pipe.hset(f"user_vector:{user_id}", "update_count", 
                 self.redis.hget(f"user_vector:{user_id}", "update_count") or 0 + 1)
        pipe.expire(f"user_vector:{user_id}", 7 * 24 * 3600)  # 7 days TTL
        await pipe.execute()

# Integration with existing system
class SOTARecommendationEngine:
    """State-of-the-art recommendation engine"""
    
    def __init__(self):
        self.redis_client = redis.Redis(host='localhost', port=6379, db=0)
        self.user_vector_system = RealTimeUserVector(self.redis_client)
        
    async def process_user_event(self, event: UserEvent):
        """Process real-time user event"""
        
        # 1. Get product embedding
        product_embedding = await self._get_product_embedding(event.product_id)
        
        # 2. Update user vector
        await self.user_vector_system.update_user_vector(event, product_embedding)
        
        # 3. Trigger real-time recommendations update (optional)
        if event.action in [UserAction.VIEW, UserAction.CLICK]:
            await self._update_user_recommendations(event.user_id)
    
    async def get_personalized_recommendations(self, user_id: str, 
                                             context: Optional[Dict] = None) -> List[Dict]:
        """Get personalized recommendations"""
        
        # 1. Get base candidates (from our existing system)
        base_candidates = await self._get_base_candidates(user_id, context)
        
        # 2. Apply real-time personalization
        personalized_recs = await self.user_vector_system.get_real_time_recommendations(
            user_id, base_candidates, num_recommendations=20
        )
        
        # 3. Final business logic and formatting
        final_recommendations = await self._format_recommendations(personalized_recs)
        
        return final_recommendations