# Integrace SOTA systému s naším existujícím batch systémem

from typing import Dict, List, Optional
import asyncio

class HybridRecommendationSystem:
    """
    Kombinuje náš batch systém s real-time personalizací
    """
    
    def __init__(self, existing_system, sota_engine):
        self.batch_system = existing_system  # Náš současný systém
        self.realtime_engine = sota_engine   # Nový SOTA systém
        
    async def get_hybrid_recommendations(self, 
                                       user_id: str,
                                       product_id: Optional[str] = None,
                                       context: Optional[Dict] = None) -> List[Dict]:
        """
        Hybridní doporučení: <PERSON>ch kandidáti + Real-time personalizace
        """
        
        # 1. Získej batch kandidáty (náš existující systém)
        if product_id:
            # Product-based recommendations (complementary)
            batch_candidates = await self._get_batch_complementary(product_id)
        else:
            # General recommendations for user
            batch_candidates = await self._get_batch_popular_products()
        
        # 2. Obohatit o product embeddings
        enriched_candidates = await self._enrich_with_embeddings(batch_candidates)
        
        # 3. Aplikovat real-time personalizaci
        personalized_scores = await self.realtime_engine.user_vector_system.get_real_time_recommendations(
            user_id, enriched_candidates, num_recommendations=50
        )
        
        # 4. Merge batch scores s personalized scores
        final_recommendations = self._merge_scores(batch_candidates, personalized_scores)
        
        return final_recommendations
    
    async def _get_batch_complementary(self, product_id: str) -> List[Dict]:
        """Použije náš existující batch systém"""
        
        # Simulace volání našeho existujícího systému
        cache_result = await self._check_complementary_cache(product_id)
        
        if cache_result:
            return [
                {
                    'id': comp_id,
                    'batch_score': score,
                    'source': 'batch_cache'
                }
                for comp_id, score in zip(cache_result['complementary_ids'], cache_result['scores'])
            ]
        else:
            # Fallback na batch processing
            return await self._run_batch_processing(product_id)
    
    def _merge_scores(self, batch_candidates: List[Dict], 
                     personalized_scores: List[Tuple[str, float]]) -> List[Dict]:
        """
        Merge batch scores s personalized scores
        Weighted combination: 70% personalization + 30% batch logic
        """
        
        # Convert to lookup
        personalized_lookup = dict(personalized_scores)
        
        merged_results = []
        for candidate in batch_candidates:
            product_id = candidate['id']
            
            batch_score = candidate.get('batch_score', 0.5)
            personal_score = personalized_lookup.get(product_id, 0.0)
            
            # Weighted combination
            final_score = 0.7 * personal_score + 0.3 * batch_score
            
            merged_results.append({
                'id': product_id,
                'final_score': final_score,
                'batch_score': batch_score,
                'personal_score': personal_score,
                'explanation': self._generate_explanation(batch_score, personal_score)
            })
        
        # Sort by final score
        merged_results.sort(key=lambda x: x['final_score'], reverse=True)
        return merged_results[:20]
    
    def _generate_explanation(self, batch_score: float, personal_score: float) -> str:
        """Generate explanation for recommendation"""
        
        if personal_score > 0.7:
            return "Based on your recent interests"
        elif batch_score > 0.7:
            return "Frequently bought together"
        elif personal_score > batch_score:
            return "Matches your preferences"
        else:
            return "Popular complementary product"

# API endpoint integrace
class RecommendationAPI:
    """API endpoints pro real-time recommendations"""
    
    def __init__(self, hybrid_system):
        self.hybrid_system = hybrid_system
    
    async def track_user_event(self, user_id: str, event_data: Dict):
        """Track user event (view, click, purchase)"""
        
        event = UserEvent(
            user_id=user_id,
            product_id=event_data['product_id'],
            action=UserAction(event_data['action']),
            timestamp=time.time(),
            session_id=event_data.get('session_id', ''),
            dwell_time=event_data.get('dwell_time'),
            context=event_data.get('context')
        )
        
        await self.hybrid_system.realtime_engine.process_user_event(event)
        
        return {"status": "success", "message": "Event tracked"}
    
    async def get_recommendations(self, user_id: str, 
                                context: Optional[Dict] = None) -> Dict:
        """Get personalized recommendations"""
        
        recommendations = await self.hybrid_system.get_hybrid_recommendations(
            user_id, context=context
        )
        
        return {
            "user_id": user_id,
            "recommendations": recommendations,
            "generated_at": time.time(),
            "model_version": "hybrid_v1.0"
        }
    
    async def get_product_recommendations(self, user_id: str, 
                                        product_id: str) -> Dict:
        """Get complementary product recommendations"""
        
        recommendations = await self.hybrid_system.get_hybrid_recommendations(
            user_id, product_id=product_id
        )
        
        return {
            "user_id": user_id,
            "source_product": product_id,
            "recommendations": recommendations,
            "generated_at": time.time(),
            "model_version": "hybrid_v1.0"
        }

# Usage example:
"""
# 1. Initialize systems
existing_system = YourExistingBatchSystem()
sota_engine = SOTARecommendationEngine()
hybrid_system = HybridRecommendationSystem(existing_system, sota_engine)
api = RecommendationAPI(hybrid_system)

# 2. Track user events
await api.track_user_event("user_123", {
    "product_id": "product_456",
    "action": "view",
    "dwell_time": 25.0,
    "session_id": "session_789",
    "context": {"from_search": True, "query": "střešní nosiče"}
})

# 3. Get personalized recommendations
recommendations = await api.get_recommendations("user_123")

# 4. Get complementary recommendations with personalization
comp_recs = await api.get_product_recommendations("user_123", "product_456")
"""