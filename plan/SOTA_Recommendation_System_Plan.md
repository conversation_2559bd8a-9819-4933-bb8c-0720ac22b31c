# State-of-the-Art Recommendation System - Implementation Plan

## 🎯 **Cíl: Přechod na State-of-the-Art Recommendation Engine**

Tento dokument popisuje kompletní plán pro upgrade našeho současného hybridního recommendation systému na true state-of-the-art úroveň s real-time personalizací a pokročilými ML technikami.

---

## 📊 **Current State vs. Target State**

### **Současný stav (Hybridní systém):**
- ✅ Batch processing s Neo4j category graphs
- ✅ Gemini LLM reranking
- ✅ Scroll-based candidate selection
- ✅ Intelligent caching (30-day TTL)
- ✅ Performance: ~110 produktů/min batch processing

### **Target State (SOTA systém):**
- 🎯 Real-time user intent vectors
- 🎯 Sub-10ms recommendation latency
- 🎯 Personalized recommendations based on user behavior
- 🎯 Multi-objective optimization (relevance + diversity + business rules)
- 🎯 A/B testing framework
- 🎯 Explainable recommendations

---

## 🏗️ **Architecture Overview**

### **High-Level Architecture:**

```
User Events → [Real-time Tracking] → [User Vector Updates] → [Redis Storage]
                                                                    ↓
Product Catalog → [Batch Processing] → [Candidate Generation] → [Personalized Ranking] → API Response
```

### **Komponenty systému:**

1. **Real-time Event Tracking**
   - User behavior capture (views, clicks, purchases, rejections)
   - Session management
   - Context awareness (search queries, categories)

2. **User Intent Vector System**
   - Exponential time decay
   - Momentum-based updates
   - Multi-signal fusion
   - Negative feedback incorporation

3. **Hybrid Recommendation Engine**
   - Batch candidates (existing system) + Real-time personalization
   - Weighted score combination
   - Business rules integration

4. **Production Infrastructure**
   - Redis for real-time storage
   - API endpoints for tracking and recommendations
   - Monitoring and analytics

---

## 📋 **Detailed Implementation Steps**

### **Phase 1: Infrastructure Setup (Week 1-2)**

#### **1.1 Redis Infrastructure**
```bash
# Redis setup for user vectors and session data
docker run -d --name redis-recommendations \
  -p 6379:6379 \
  -v redis-data:/data \
  redis:7-alpine redis-server --appendonly yes
```

**Potřebné Redis struktury:**
- `user_vector:{user_id}` - User intent vector + metadata
- `user_momentum:{user_id}` - Momentum vector pro updates
- `user_session:{session_id}` - Session tracking data
- `user_events:{user_id}` - Recent user events (sliding window)

#### **1.2 Event Tracking API**
```python
# Endpoint pro tracking user events
POST /api/v1/track-event
{
  "user_id": "user_123",
  "product_id": "product_456", 
  "action": "view|click|add_to_cart|purchase|reject",
  "session_id": "session_789",
  "timestamp": 1640995200,
  "dwell_time": 25.0,
  "context": {
    "from_search": true,
    "search_query": "střešní nosiče",
    "category": "Autodoplňky",
    "page_type": "product_detail|search_results|category"
  }
}
```

#### **1.3 Database Schema Updates**
```sql
-- Tracking table pro user events
CREATE TABLE user_events (
    id BIGSERIAL PRIMARY KEY,
    user_id VARCHAR(255) NOT NULL,
    session_id VARCHAR(255) NOT NULL,
    product_id VARCHAR(255) NOT NULL,
    action VARCHAR(50) NOT NULL,
    timestamp TIMESTAMP NOT NULL,
    dwell_time FLOAT,
    context JSONB,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE INDEX idx_user_events_user_timestamp ON user_events(user_id, timestamp DESC);
CREATE INDEX idx_user_events_product ON user_events(product_id);
```

### **Phase 2: Core Real-time System (Week 3-4)**

#### **2.1 User Vector Computation**

**Klíčové komponenty:**
```python
class UserVector:
    def __init__(self, vector_dim=512):
        self.vector_dim = vector_dim
        self.time_decay_half_life = 3600  # 1 hour
        self.action_weights = {
            'view': 1.0,
            'click': 2.0, 
            'add_to_cart': 5.0,
            'purchase': 10.0,
            'reject': -1.0
        }
    
    def update_vector(self, current_vector, event, product_embedding):
        # 1. Apply time decay
        decayed_vector = self._apply_time_decay(current_vector)
        
        # 2. Compute event contribution
        contribution = self._compute_contribution(event, product_embedding)
        
        # 3. Momentum update
        updated_vector = self._momentum_update(decayed_vector, contribution)
        
        return updated_vector
```

**Time decay formula:**
```
decay_factor = exp(-time_since_last_update / half_life)
decayed_vector = current_vector * decay_factor
```

**Momentum update (podobně jako Adam optimizer):**
```
momentum = β * momentum + (1-β) * event_contribution
updated_vector = current_vector + learning_rate * momentum
```

#### **2.2 Real-time Scoring Engine**

```python
class RealTimeScorer:
    def score_candidates(self, user_vector, candidates):
        scores = []
        for candidate in candidates:
            # Base similarity
            similarity = cosine_similarity(user_vector, candidate.embedding)
            
            # Business rule adjustments
            adjusted_score = self._apply_business_rules(similarity, candidate, user_id)
            
            scores.append((candidate.id, adjusted_score))
            
        return sorted(scores, key=lambda x: x[1], reverse=True)
    
    def _apply_business_rules(self, base_score, product, user_id):
        score = base_score
        
        # Diversity penalty
        if self._is_similar_to_recent_views(product, user_id):
            score *= 0.8
            
        # Novelty bonus
        if product.is_new:
            score *= 1.1
            
        # Inventory consideration
        if product.stock_level == 0:
            score *= 0.1
        elif product.stock_level > 10:
            score *= 1.05
            
        # Price match
        user_price_pref = self._get_user_price_preference(user_id)
        price_match = self._compute_price_match(product.price, user_price_pref)
        score *= price_match
        
        return score
```

### **Phase 3: Integration with Existing System (Week 5-6)**

#### **3.1 Hybrid Architecture**

```python
class HybridRecommendationEngine:
    def __init__(self, batch_system, realtime_system):
        self.batch_system = batch_system      # Náš současný systém
        self.realtime_system = realtime_system # Nový SOTA systém
        
    async def get_recommendations(self, user_id, product_id=None, context=None):
        # 1. Get batch candidates (existing system)
        if product_id:
            batch_candidates = await self.batch_system.get_complementary(product_id)
        else:
            batch_candidates = await self.batch_system.get_popular_products()
            
        # 2. Apply real-time personalization  
        user_vector = await self.realtime_system.get_user_vector(user_id)
        personalized_scores = await self.realtime_system.score_candidates(
            user_vector, batch_candidates
        )
        
        # 3. Merge scores (70% personalization + 30% batch logic)
        final_recommendations = self._merge_scores(
            batch_candidates, personalized_scores, weights=[0.7, 0.3]
        )
        
        return final_recommendations
```

#### **3.2 API Endpoints**

```python
# FastAPI endpoints
@app.post("/api/v1/events")
async def track_event(event: UserEvent):
    await recommendation_engine.track_event(event)
    return {"status": "success"}

@app.get("/api/v1/recommendations/{user_id}")
async def get_recommendations(user_id: str, limit: int = 20):
    recs = await recommendation_engine.get_recommendations(user_id, limit=limit)
    return {
        "user_id": user_id,
        "recommendations": recs,
        "generated_at": time.time(),
        "model_version": "hybrid_v1.0"
    }

@app.get("/api/v1/recommendations/{user_id}/product/{product_id}")
async def get_product_recommendations(user_id: str, product_id: str):
    recs = await recommendation_engine.get_complementary_recommendations(
        user_id, product_id
    )
    return {
        "user_id": user_id,
        "source_product": product_id,
        "recommendations": recs
    }
```

### **Phase 4: Advanced Features (Week 7-8)**

#### **4.1 A/B Testing Framework**

```python
class ABTestingFramework:
    def __init__(self):
        self.experiments = {}
        
    def create_experiment(self, experiment_id, variants, traffic_split):
        self.experiments[experiment_id] = {
            'variants': variants,
            'traffic_split': traffic_split,
            'start_time': time.time(),
            'metrics': defaultdict(list)
        }
    
    def get_variant(self, user_id, experiment_id):
        # Consistent hashing pro user assignment
        hash_value = hash(f"{user_id}:{experiment_id}") % 100
        
        experiment = self.experiments[experiment_id]
        cumulative = 0
        for variant, percentage in experiment['traffic_split'].items():
            cumulative += percentage
            if hash_value < cumulative:
                return variant
        
        return 'control'  # fallback
    
    def track_metric(self, user_id, experiment_id, variant, metric_name, value):
        self.experiments[experiment_id]['metrics'][variant].append({
            'user_id': user_id,
            'metric': metric_name,
            'value': value,
            'timestamp': time.time()
        })
```

#### **4.2 Explainable Recommendations**

```python
class RecommendationExplainer:
    def explain_recommendation(self, user_id, product_id, score_breakdown):
        explanations = []
        
        # Personalization explanation
        if score_breakdown['personal_score'] > 0.7:
            recent_categories = self._get_user_recent_categories(user_id)
            explanations.append(f"Based on your interest in {recent_categories[0]}")
            
        # Complementary explanation  
        if score_breakdown['batch_score'] > 0.6:
            explanations.append("Frequently bought together")
            
        # Popularity explanation
        if score_breakdown.get('popularity_score', 0) > 0.5:
            explanations.append("Popular choice among similar customers")
            
        return explanations
```

#### **4.3 Performance Monitoring**

```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {
            'recommendation_latency': [],
            'cache_hit_rate': [],
            'user_engagement': [],
            'conversion_rate': []
        }
    
    def track_recommendation_request(self, user_id, latency, cache_hit):
        self.metrics['recommendation_latency'].append(latency)
        self.metrics['cache_hit_rate'].append(1 if cache_hit else 0)
        
    def track_user_engagement(self, user_id, recommended_products, clicked_products):
        engagement_rate = len(clicked_products) / len(recommended_products)
        self.metrics['user_engagement'].append(engagement_rate)
    
    def get_performance_report(self):
        return {
            'avg_latency': np.mean(self.metrics['recommendation_latency']),
            'cache_hit_rate': np.mean(self.metrics['cache_hit_rate']),
            'engagement_rate': np.mean(self.metrics['user_engagement'])
        }
```

---

## 🎯 **Key Performance Indicators (KPIs)**

### **Technical Metrics:**
- **Latency:** <10ms pro recommendation scoring
- **Throughput:** 10,000+ events/second
- **Cache hit rate:** >80%
- **System uptime:** 99.9%

### **Business Metrics:**
- **Click-through rate (CTR):** +25% improvement
- **Conversion rate:** +15% improvement  
- **Average order value:** +10% improvement
- **User engagement:** +30% improvement

### **User Experience Metrics:**
- **Recommendation relevance:** >4.0/5.0 rating
- **Diversity score:** >0.7 (intra-list diversity)
- **Novelty score:** >0.6 (new product discovery)

---

## 🛠️ **Technology Stack**

### **Core Technologies:**
- **Python 3.11+** - Main development language
- **FastAPI** - API framework
- **Redis** - Real-time user vector storage
- **PostgreSQL** - Event tracking and analytics
- **NumPy/SciPy** - Vector computations
- **Docker** - Containerization

### **ML/AI Technologies:**
- **Sentence Transformers** - Product embeddings
- **Scikit-learn** - Basic ML utilities
- **Qdrant** - Vector database (existing)
- **Neo4j** - Graph relationships (existing)
- **Gemini API** - LLM reranking (existing)

### **Infrastructure:**
- **Kubernetes** - Container orchestration
- **Prometheus** - Metrics collection
- **Grafana** - Monitoring dashboards
- **ELK Stack** - Logging and analytics

---

## 📈 **Expected Outcomes**

### **Short-term (1-2 měsíce):**
- ✅ Real-time user behavior tracking
- ✅ Basic personalized recommendations
- ✅ Integration s existing batch system
- ✅ Performance monitoring

### **Medium-term (3-6 měsíců):**
- ✅ Advanced business rules engine
- ✅ A/B testing framework
- ✅ Explainable recommendations
- ✅ Multi-objective optimization

### **Long-term (6-12 měsíců):**
- ✅ Deep learning model integration
- ✅ Collaborative filtering
- ✅ Real-time model updates
- ✅ Cross-domain recommendations

---

## 🚀 **Getting Started**

1. **Review this plan** s týmem a stakeholders
2. **Setup development environment** podle Phase 1
3. **Implement MVP** s basic user tracking
4. **Integrate with existing system** postupně
5. **Measure and iterate** based on KPIs

Tento plán nás dovede k true state-of-the-art recommendation systému s real-time personalizací a pokročilými ML technikami, který bude konkurenceschopný s řešeními velkých tech společností.