#!/usr/bin/env python3
"""
Skript pro výpočet všech komplementárních produktů pro celý katalog jabkolevne.cz
Tento skript používá Neo4j graf kategorií a Qdrant databázi produktů k vytvoření 
kompletních komplementárních doporučení.

Analogie: Jako zkušený prodavač v obchodě, který ví, které produkty se kupují společně.
Když zákazník přijde s iPhone, automaticky navrhne pouzdro, ochranné sklo a nabíječku.

Autor: <PERSON>: 2025-01-17
"""

import json
import logging
import os
import time
from typing import Dict, List, Set, Tuple, Optional
from collections import defaultdict

from dotenv import load_dotenv
from neo4j import GraphDatabase
from qdrant_client import QdrantClient
from qdrant_client.http.models import Filter, FieldCondition, MatchValue

# Nastavení loggingu
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Načtení proměnných prostředí
load_dotenv()

# Konfigurace
TENANT_ID = "jabkolevne"
QDRANT_COLLECTION = f"real_products_{TENANT_ID}"
CATEGORY_LABEL = f"Category_{TENANT_ID}"

# Neo4j konfigurace
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "Graph2025Secure!")

# Qdrant konfigurace
QDRANT_HOST = "localhost"
QDRANT_PORT = 6333

# Mapování zjednodušených kategorií na původní kategorie pro vyhledávání v Qdrant
# Klíče jsou zjednodušené kategorie, hodnoty jsou vzorce pro vyhledávání v původních kategoriích
QDRANT_CATEGORY_PATTERNS = {
    "iPhone": ["iPhone"],
    "iPad": ["iPad"],
    "MacBook": ["MacBook", "Mac"],
    "Apple Watch": ["Watch"],
    "AirPods": ["AirPods"],
    "Nabíječky (Apple)": ["Nabíječky", "nabíječky"],
    "Kabely (Apple)": ["kabely", "Kabely"],
    "Pouzdra pro iPhone": ["Kryty na mobilní telefony Apple"],
    "Ochranná skla pro iPhone": ["Tvrzená skla pro iPhone"],
    "Pouzdra pro iPad": ["iPad", "Pouzdra"],  # Musíme být opatrní s filtry
    "Ochranná skla pro iPad": ["iPad", "Tvrzená skla"],
    "Řemínky pro Apple Watch": ["Watch", "Řemínky"],
    "Bezdrátové nabíječky (Apple)": ["Bezdrátové nabíječky"],
    "Powerbanky (pro Apple)": ["Powerbanky"],
    "Apple Pencil": ["Apple Pencil"],
    "AirTag": ["AirTag"],
    "Pouzdra pro AirPods": ["Pouzdra na sluchátka"],
    "Sluchátka (Apple ostatní)": ["Sluchátka", "Audio"],
    "Reproduktory (Apple kompatibilní)": ["Reproduktory"],
    "Apple TV": ["Apple TV"],
    "Příslušenství do auta (Apple)": ["Příslušenství do auta"]
}

class ComplementaryProductCalculator:
    """
    Kalkulátor komplementárních produktů - jako virtuální asistent prodavače,
    který zná všechny produkty a jejich vztahy.
    """
    
    def __init__(self):
        self.neo4j_driver = None
        self.qdrant_client = None
        self.complementary_cache = {}
        
    def connect_to_databases(self) -> bool:
        """Připojí se k Neo4j a Qdrant databázím - jako otevření obchodních knih."""
        try:
            # Neo4j připojení
            logger.info(f"🔗 Připojuji se k Neo4j na {NEO4J_URI}")
            self.neo4j_driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
            
            # Test Neo4j připojení
            with self.neo4j_driver.session() as session:
                result = session.run("RETURN 1 AS test")
                assert result.single()["test"] == 1
            
            # Qdrant připojení  
            logger.info(f"🔗 Připojuji se k Qdrant na {QDRANT_HOST}:{QDRANT_PORT}")
            self.qdrant_client = QdrantClient(host=QDRANT_HOST, port=QDRANT_PORT)
            
            # Test Qdrant připojení
            collections = self.qdrant_client.get_collections().collections
            collection_names = [col.name for col in collections]
            if QDRANT_COLLECTION not in collection_names:
                logger.error(f"❌ Kolekce {QDRANT_COLLECTION} neexistuje v Qdrant")
                return False
                
            logger.info("✅ Úspěšně připojeno k oběma databázím")
            return True
            
        except Exception as e:
            logger.error(f"❌ Chyba při připojování k databázím: {e}")
            return False
    
    def get_complementary_relationships(self) -> Dict[str, List[Tuple[str, float]]]:
        """
        Načte komplementární vztahy z Neo4j - jako načtení pravidel o tom,
        které produkty se dobře prodávají společně.
        """
        try:
            with self.neo4j_driver.session() as session:
                query = f"""
                MATCH (source:{CATEGORY_LABEL})-[r:COMPLEMENTARY_TO]->(target:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN source.name as source_category, target.name as target_category, r.weight as weight
                ORDER BY source.name, r.weight DESC
                """
                
                result = session.run(query)
                relationships = defaultdict(list)
                
                for record in result:
                    source = record["source_category"]
                    target = record["target_category"]
                    weight = record["weight"]
                    relationships[source].append((target, weight))
                
                logger.info(f"📊 Načteno {len(relationships)} zdrojových kategorií s komplementárními vztahy")
                
                # Ukázka načtených vztahů
                for category, relations in list(relationships.items())[:3]:
                    logger.info(f"   {category}: {len(relations)} komplementárních kategorií")
                
                return dict(relationships)
                
        except Exception as e:
            logger.error(f"❌ Chyba při načítání komplementárních vztahů: {e}")
            return {}
    
    def find_products_by_category_pattern(self, simplified_category: str, limit: int = 50) -> List[Dict]:
        """
        Najde produkty podle zjednodušené kategorie - jako hledání konkrétních produktů
        v regálech obchodu podle typu zboží.
        """
        patterns = QDRANT_CATEGORY_PATTERNS.get(simplified_category, [simplified_category])
        all_products = []
        
        try:
            for pattern in patterns:
                # Vyhledáme produkty, jejichž kategorie obsahuje daný vzorec
                results = self.qdrant_client.scroll(
                    collection_name=QDRANT_COLLECTION,
                    limit=limit,
                    with_payload=True,
                    with_vectors=False,
                    scroll_filter=Filter(
                        must=[
                            FieldCondition(
                                key="category",
                                match=MatchValue(value=pattern)
                            )
                        ]
                    )
                )
                
                points, _ = results
                for point in points:
                    product_data = {
                        'id': point.id,
                        'name': point.payload.get('name', 'Neznámý produkt'),
                        'category': point.payload.get('category', ''),
                        'price': point.payload.get('price', 0),
                        'url': point.payload.get('url', ''),
                        'availability': point.payload.get('availability', 'Neznámá'),
                        'brand': point.payload.get('brand', ''),
                        'description': point.payload.get('description', '')[:200] + '...' if point.payload.get('description', '') else ''
                    }
                    all_products.append(product_data)
            
            # Odstranění duplicit podle ID
            unique_products = {}
            for product in all_products:
                unique_products[product['id']] = product
            
            result_products = list(unique_products.values())
            logger.info(f"   🔍 Kategorie '{simplified_category}': nalezeno {len(result_products)} produktů")
            
            return result_products[:limit]  # Omezíme počet
            
        except Exception as e:
            logger.error(f"❌ Chyba při hledání produktů pro kategorii '{simplified_category}': {e}")
            return []
    
    def calculate_complementary_for_product(self, product: Dict, relationships: Dict) -> List[Dict]:
        """
        Vypočítá komplementární produkty pro jeden konkrétní produkt - 
        jako když prodavač vidí, co zákazník kupuje, a navrhne doplňky.
        """
        product_category = product.get('category', '')
        
        # Najdeme, do které zjednodušené kategorie produkt patří
        matching_simplified_category = None
        for simplified_cat, patterns in QDRANT_CATEGORY_PATTERNS.items():
            for pattern in patterns:
                if pattern.lower() in product_category.lower():
                    matching_simplified_category = simplified_cat
                    break
            if matching_simplified_category:
                break
        
        if not matching_simplified_category:
            logger.debug(f"   ⚠️  Produkt '{product['name']}' nemá odpovídající zjednodušenou kategorii")
            return []
        
        # Získáme komplementární kategorie
        complementary_categories = relationships.get(matching_simplified_category, [])
        
        if not complementary_categories:
            logger.debug(f"   ⚠️  Kategorie '{matching_simplified_category}' nemá komplementární vztahy")
            return []
        
        # Najdeme produkty pro každou komplementární kategorii
        all_complementary_products = []
        for target_category, weight in complementary_categories:
            complementary_products = self.find_products_by_category_pattern(target_category, limit=10)
            
            # Přidáme váhu ke každému produktu
            for comp_product in complementary_products:
                comp_product['complementary_weight'] = weight
                comp_product['complementary_category'] = target_category
                comp_product['source_product_id'] = product['id']
                comp_product['source_product_name'] = product['name']
                all_complementary_products.append(comp_product)
        
        # Seřadíme podle váhy (nejsilnější doporučení první)
        all_complementary_products.sort(key=lambda x: x['complementary_weight'], reverse=True)
        
        logger.info(f"   ✅ Produkt '{product['name']}' -> {len(all_complementary_products)} komplementárních produktů")
        
        return all_complementary_products
    
    def calculate_all_complementary_products(self) -> Dict:
        """
        Vypočítá komplementární produkty pro celý katalog - jako vytvoření 
        kompletního průvodce doporučeními pro celý obchod.
        """
        logger.info("🚀 === VÝPOČET KOMPLEMENTÁRNÍCH PRODUKTŮ PRO CELÝ KATALOG ===")
        
        # 1. Načtení komplementárních vztahů z Neo4j
        logger.info("📊 Načítám komplementární vztahy z Neo4j...")
        relationships = self.get_complementary_relationships()
        if not relationships:
            logger.error("❌ Žádné komplementární vztahy nenalezeny")
            return {}
        
        # 2. Načtení všech produktů z Qdrant
        logger.info(f"📦 Načítám všechny produkty z kolekce {QDRANT_COLLECTION}...")
        all_products = []
        offset = None
        batch_size = 100
        
        try:
            while True:
                results = self.qdrant_client.scroll(
                    collection_name=QDRANT_COLLECTION,
                    limit=batch_size,
                    offset=offset,
                    with_payload=True,
                    with_vectors=False
                )
                
                points, next_offset = results
                
                for point in points:
                    product_data = {
                        'id': point.id,
                        'name': point.payload.get('name', 'Neznámý produkt'),
                        'category': point.payload.get('category', ''),
                        'price': point.payload.get('price', 0),
                        'url': point.payload.get('url', ''),
                        'availability': point.payload.get('availability', 'Neznámá'),
                        'brand': point.payload.get('brand', '')
                    }
                    all_products.append(product_data)
                
                if next_offset is None or len(points) == 0:
                    break
                    
                offset = next_offset
                logger.info(f"   📦 Načteno {len(all_products)} produktů...")
        
        except Exception as e:
            logger.error(f"❌ Chyba při načítání produktů z Qdrant: {e}")
            return {}
        
        logger.info(f"✅ Celkem načteno {len(all_products)} produktů")
        
        # 3. Výpočet komplementárních produktů pro každý produkt
        logger.info("🔄 Počítám komplementární produkty pro každý produkt...")
        
        all_complementary_results = {}
        processed_count = 0
        
        for product in all_products:
            try:
                complementary_products = self.calculate_complementary_for_product(product, relationships)
                
                if complementary_products:
                    all_complementary_results[product['id']] = {
                        'source_product': product,
                        'complementary_products': complementary_products,
                        'total_recommendations': len(complementary_products)
                    }
                
                processed_count += 1
                
                # Progress report každých 50 produktů
                if processed_count % 50 == 0:
                    logger.info(f"   📊 Zpracováno {processed_count}/{len(all_products)} produktů...")
                    
            except Exception as e:
                logger.error(f"   ❌ Chyba při zpracování produktu {product['name']}: {e}")
                continue
        
        logger.info(f"🎉 Výpočet dokončen! {len(all_complementary_results)} produktů má komplementární doporučení")
        
        return all_complementary_results
    
    def save_results_to_file(self, results: Dict, filename: str = "complementary_products_jabkolevne.json"):
        """Uloží výsledky do JSON souboru - jako vytvoření katalogu doporučení."""
        try:
            # Příprava dat pro JSON (konverze na serializovatelný formát)
            json_data = {
                'metadata': {
                    'tenant': TENANT_ID,
                    'total_products_with_recommendations': len(results),
                    'generated_at': time.strftime('%Y-%m-%d %H:%M:%S'),
                    'total_recommendations': sum(item['total_recommendations'] for item in results.values())
                },
                'recommendations': {}
            }
            
            for product_id, data in results.items():
                json_data['recommendations'][str(product_id)] = {
                    'source_product': data['source_product'],
                    'complementary_products': data['complementary_products'],
                    'total_recommendations': data['total_recommendations']
                }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"💾 Výsledky uloženy do souboru: {filename}")
            logger.info(f"📊 Velikost souboru: {os.path.getsize(filename) / 1024 / 1024:.2f} MB")
            
        except Exception as e:
            logger.error(f"❌ Chyba při ukládání výsledků: {e}")
    
    def generate_statistics(self, results: Dict):
        """Vygeneruje statistiky o komplementárních produktech - jako report pro management."""
        try:
            total_products = len(results)
            if total_products == 0:
                logger.info("📊 Žádné výsledky pro statistiky")
                return
            
            # Základní statistiky
            total_recommendations = sum(item['total_recommendations'] for item in results.values())
            avg_recommendations = total_recommendations / total_products
            
            # Nejlépe doporučované kategorie (cílové)
            category_targets = defaultdict(int)
            for data in results.values():
                for comp_product in data['complementary_products']:
                    category_targets[comp_product['complementary_category']] += 1
            
            # Produkty s nejvíce doporučeními
            top_recommended = sorted(
                results.items(), 
                key=lambda x: x[1]['total_recommendations'], 
                reverse=True
            )[:5]
            
            logger.info("📊 === STATISTIKY KOMPLEMENTÁRNÍCH PRODUKTŮ ===")
            logger.info(f"📦 Celkem produktů s doporučeními: {total_products}")
            logger.info(f"🔗 Celkem doporučení: {total_recommendations}")
            logger.info(f"📈 Průměr doporučení na produkt: {avg_recommendations:.1f}")
            
            logger.info("🏆 Top 5 nejdoporučovanějších kategorií:")
            for category, count in sorted(category_targets.items(), key=lambda x: x[1], reverse=True)[:5]:
                logger.info(f"   - {category}: {count} doporučení")
            
            logger.info("🌟 Top 5 produktů s nejvíce doporučeními:")
            for product_id, data in top_recommended:
                product_name = data['source_product']['name']
                recommendations_count = data['total_recommendations']
                logger.info(f"   - {product_name}: {recommendations_count} doporučení")
                
        except Exception as e:
            logger.error(f"❌ Chyba při generování statistik: {e}")
    
    def close_connections(self):
        """Uzavře připojení k databázím."""
        if self.neo4j_driver:
            self.neo4j_driver.close()
        if self.qdrant_client:
            self.qdrant_client.close()
        logger.info("🔒 Připojení k databázím uzavřena")

def main():
    """
    Hlavní funkce - jako spuštění celého procesu vytváření doporučení 
    pro všechny produkty v obchodě.
    """
    calculator = ComplementaryProductCalculator()
    
    try:
        # Připojení k databázím
        if not calculator.connect_to_databases():
            logger.error("❌ Nepodařilo se připojit k databázím. Ukončuji.")
            return False
        
        # Výpočet komplementárních produktů
        results = calculator.calculate_all_complementary_products()
        
        if not results:
            logger.error("❌ Žádné komplementární produkty nebyly vypočítány")
            return False
        
        # Uložení výsledků
        calculator.save_results_to_file(results)
        
        # Generování statistik
        calculator.generate_statistics(results)
        
        logger.info("🎉 === ÚSPĚŠNĚ DOKONČENO ===")
        return True
        
    except Exception as e:
        logger.error(f"❌ Neočekávaná chyba: {e}")
        return False
        
    finally:
        calculator.close_connections()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 