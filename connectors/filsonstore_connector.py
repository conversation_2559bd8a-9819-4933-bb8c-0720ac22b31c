from typing import Dict, List, Any, Optional
import logging
import pandas as pd
import sys
import os
import requests
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup

# Přidáme cestu k root adresáři
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .base_connector import BaseConnector


class FilsonstoreConnector(BaseConnector):
    """
    Konektor pro zpracování XML feedu z filsonstore.cz.
    
    Tento konektor zpracovává produktový feed ve formátu RSS XML s Google prvky,
    dostupný přes Mergado feeds.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Inicializuje konektor s danou konfigurací.
        
        Args:
            config: Konfigurace konektoru, která musí obsahovat:
                - feed_url: URL k XML feedu
        """
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")
        
        # Namespace pro Google feed
        self.ns = {'g': 'http://base.google.com/ns/1.0'}

    def get_feed(self) -> List[Dict[str, Any]]:
        """
        Získá a zpracuje XML feed z Filsonstore.
        
        Returns:
            List slovníků s daty o produktech ve standardizovaném formátu.
        """
        self.logger.info(f"Začínám získávat data z feedu")
        
        try:
            # Stažení XML feedu
            feed_content = self._fetch_feed()
            
            # Parsování XML
            products = self._parse_rss_feed(feed_content)
            
            self.logger.info(f"Úspěšně načteno a transformováno {len(products)} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při zpracování feedu: {e}")
            raise

    def _fetch_feed(self) -> str:
        """
        Stáhne XML feed z URL a dekóduje ho jako UTF-8.

        Returns:
            Obsah XML feedu jako UTF-8 string.
        """
        self.logger.info(f"Fetching feed from {self.feed_url}")
        response = requests.get(self.feed_url)
        response.raise_for_status()  # Vyvolá výjimku, pokud není odpověď 2xx
        self.logger.info("Feed fetched successfully")
        # Explicitní dekódování pomocí UTF-8
        try:
            content = response.content.decode('utf-8')
            return content
        except UnicodeDecodeError as e:
            self.logger.error(f"Failed to decode feed content as UTF-8: {e}")
            # Fallback na detekci z hlaviček nebo původní response.text?
            # Prozatím vyvoláme chybu, protože předpokládáme UTF-8.
            self.logger.warning(f"Trying fallback with apparent_encoding: {response.apparent_encoding}")
            try:
                 # Zkusíme detekované kódování knihovnou requests
                 content = response.content.decode(response.apparent_encoding, errors='replace')
                 self.logger.warning(f"Fallback decoding using {response.apparent_encoding} successful.")
                 return content
            except Exception as fallback_e:
                 self.logger.error(f"Fallback decoding failed: {fallback_e}")
                 raise ValueError("Could not decode feed content correctly.") from e

    def _parse_rss_feed(self, xml_content: str) -> List[Dict[str, Any]]:
        """
        Parsuje RSS XML feed s Google atributy.
        
        Args:
            xml_content: Obsah XML feedu jako string.
            
        Returns:
            List slovníků s daty o produktech.
        """
        self.logger.info("Parsování RSS XML feedu")
        
        try:
            # Parsování XML
            products = []
            soup = BeautifulSoup(xml_content, 'lxml-xml')
            
            # Najdeme všechny položky v feedu
            items = soup.find_all('item')
            self.logger.info(f"Nalezeno {len(items)} položek v RSS XML")
            
            for item in items:
                try:
                    # Extrakce základních údajů
                    product_id = item.find('g:id').text.strip() if item.find('g:id') else None
                    if not product_id:
                        self.logger.warning("Produkt přeskočen - chybí ID")
                        continue
                    
                    # Extrakce a mapování údajů na náš interní formát s ošetřením kódování
                    product = {
                        'ID': product_id,  # Požadované velké písmeno
                        'Name': self._normalize_text(item.find('title').text.strip() if item.find('title') else ''),
                        'Category': self._normalize_text(item.find('g:product_type').text.strip() if item.find('g:product_type') else ''),
                        'Description': self._normalize_text(item.find('g:description').text.strip() if item.find('g:description') else ''),
                        'Price': item.find('g:price').text.strip() if item.find('g:price') else '',
                        'ImageUrl': item.find('g:image_link').text.strip() if item.find('g:image_link') else '',
                        'ProductUrl': item.find('link').text.strip() if item.find('link') else '',
                        'Brand': self._normalize_text(item.find('g:brand').text.strip() if item.find('g:brand') else ''),
                        'Availability': item.find('g:availability').text.strip() if item.find('g:availability') else '',
                        'condition': item.find('g:condition').text.strip() if item.find('g:condition') else '',
                        'mpn': item.find('g:mpn').text.strip() if item.find('g:mpn') else '',
                        'gtin': item.find('g:gtin').text.strip() if item.find('g:gtin') else '',
                    }
                    
                    products.append(product)
                    
                except Exception as e:
                    self.logger.warning(f"Chyba při zpracování položky {product_id if 'product_id' in locals() else 'unknown'}: {e}")
                    continue
            
            # Kontrola formátu dat
            if products:
                # Vytvoření DataFrame pro kontrolu
                df = pd.DataFrame(products)
                
                # Kontrola povinných sloupců
                required_columns = ['ID', 'Name', 'Category']
                missing_columns = [col for col in required_columns if col not in df.columns]
                
                if missing_columns:
                    self.logger.warning(f"Ve feedu chybí tyto sloupce: {missing_columns}")
            
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při parsování RSS feedu: {e}")
            raise
            
    def _normalize_text(self, text: str) -> str:
        """
        Základní čištění textu.
        """
        if not text:
            return ""
        try:
            # Odstranění problematických znaků, pokud se objeví (např. non-breaking space)
            text = text.replace('\xa0', ' ')
            # Případně další specifické čištění dle potřeby, ale BEZ oprav kódování
            return text.strip() # Vrátíme oříznutý text
        except Exception as e:
            self.logger.warning(f"Chyba při normalizaci textu: {e}")
            return text # V případě chyby vrátíme původní text