from typing import List, Dict, Any
import xml.etree.ElementTree as ET
from .base_connector import BaseConnector


class BrawsonConnector(BaseConnector):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")
        
        # Odstranit 'file://' z cesty pokud existuje
        self.feed_url = self.feed_url.replace('file://', '')
        
        # Namespace pro Google feed
        self.ns = {'g': 'http://base.google.com/ns/1.0'}

    def get_feed(self) -> List[Dict[str, Any]]:
        """Získá a zpracuje XML feed od BRAWSON"""
        self.logger.info(f"Čtení XML souboru z {self.feed_url}")
        try:
            # Čtení lokálního souboru
            tree = ET.parse(self.feed_url)
            root = tree.getroot()
            
            self.logger.info("Feed načten ú<PERSON>")
            
            # Zpracování produktů
            products = []
            for item in root.findall('.//item'):
                try:
                    # Pomocná funkce pro bezpečné získání textu
                    def safe_get_text(element_name, default="", use_namespace=True):
                        try:
                            if use_namespace and element_name.startswith('g:'):
                                elem = item.find(element_name, namespaces=self.ns)
                            else:
                                elem = item.find(element_name)
                                
                            if elem is not None:
                                if elem.text and '![CDATA[' in elem.text:
                                    # Odstranění CDATA wrapperu
                                    text = elem.text.replace('<![CDATA[', '').replace(']]>', '').strip()
                                else:
                                    text = elem.text.strip() if elem.text else ""
                                return text
                            return default
                        except Exception as e:
                            self.logger.warning(f"Chyba při získávání elementu {element_name}: {str(e)}")
                            return default

                    # Pomocná funkce pro zpracování ceny
                    def parse_price(price_str):
                        try:
                            # Odstraníme měnu a převedeme na float
                            return float(price_str.replace(' CZK', '').strip())
                        except:
                            return 0.0
                    
                    # Extrahujeme data z XML elementu
                    product_id = safe_get_text('g:item_group_id')
                    name = safe_get_text('title', "Neznámý produkt", use_namespace=False)
                    description = safe_get_text('description', "", use_namespace=False)
                    category = safe_get_text('g:product_type', "Nezařazeno")
                    image_link = safe_get_text('g:image_link', "")
                    price_str = safe_get_text('g:price', "0 CZK")
                    price = parse_price(price_str)
                    brand = safe_get_text('g:brand', "")
                    availability = safe_get_text('g:availability', "out of stock")
                    link = safe_get_text('link', "", use_namespace=False)
                    gtin = safe_get_text('g:gtin', "")
                    
                    # Kontrola povinných polí
                    if not product_id:
                        self.logger.warning(f"Produkt přeskočen - chybí ID: {name}")
                        continue
                        
                    # Logování pro debugging
                    self.logger.debug(f"Zpracovávám produkt: ID={product_id}, Name={name}, Category={category}, Brand={brand}")
                    
                    # Vytvoření produktu s velkými písmeny v klíčích pro kompatibilitu s ProductProcessorem
                    product = {
                        'ID': product_id,
                        'Name': name,
                        'Description': description,
                        'Category': category,
                        'ImageLink': image_link,
                        'Price': price,
                        'Brand': brand,
                        'Availability': availability,
                        'Link': link,
                        'GTIN': gtin
                    }
                    
                    # Kontrola integrity dat
                    if not name or not category:
                        self.logger.warning(f"Produkt {product_id} má chybějící povinná pole: Name={bool(name)}, Category={bool(category)}")
                    
                    products.append(product)
                    
                except Exception as e:
                    self.logger.error(f"Chyba při zpracování produktu: {str(e)}")
                    continue
            
            self.logger.info(f"Zpracováno {len(products)} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při zpracování feedu: {str(e)}")
            raise
