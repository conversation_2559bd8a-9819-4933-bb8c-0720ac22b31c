from typing import List, Dict, Any
import requests
import xml.etree.ElementTree as ET
from .base_connector import BaseConnector


class AvenbergConnector(BaseConnector):
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")
        
        # Namespace pro Google feed
        self.ns = {'g': 'http://base.google.com/ns/1.0'}

    def get_feed(self) -> List[Dict[str, Any]]:
        """Získá a zpracuje XML feed z Avenbergu"""
        self.logger.info(f"Getting XML content from {self.feed_url}")
        try:
            response = requests.get(self.feed_url)
            response.raise_for_status()
            
            self.logger.info("Feed fetched successfully")
            
            # Parsování XML
            self.logger.info("Starting XML parsing")
            root = ET.fromstring(response.content)
            
            # Zpracování produktů
            products = []
            for item in root.findall('./channel/item'):
                try:
                    # Pomocná funkce pro bezpečné získání textu
                    def safe_get_text(element_name, default=""):
                        # Pro elementy s Google namespace
                        if element_name.startswith('g:'):
                            elem = item.find(element_name, namespaces=self.ns)
                        else:
                            elem = item.find(element_name)
                            
                        if elem is not None and elem.text:
                            return elem.text.strip()
                        return default
                    
                    # Získání ID
                    product_id = safe_get_text('g:id')
                    if not product_id:
                        self.logger.warning("Produkt přeskočen - chybí ID")
                        continue
                        
                    # Získání názvu
                    name = safe_get_text('title')
                    if not name:
                        self.logger.warning(f"Produkt {product_id} přeskočen - chybí název")
                        continue
                    
                    product = {
                        'ID': product_id,
                        'Name': name,
                        'Category': safe_get_text('g:product_type'),
                        'Description': safe_get_text('description'),
                        'Price': safe_get_text('g:price'),
                        'Brand': safe_get_text('g:brand'),
                        'Availability': safe_get_text('g:availability'),
                        'Condition': safe_get_text('g:condition'),
                        'ImageUrl': safe_get_text('g:image_link'),
                        'ProductUrl': safe_get_text('link')  # URL produktu je v elementu link
                    }
                    products.append(product)
                except (AttributeError, ValueError) as e:
                    self.logger.warning(f"Chyba při zpracování produktu: {e}")
                    continue
            
            self.logger.info(f"XML parsing completed successfully - zpracováno {len(products)} produktů")
            return products
            
        except requests.RequestException as e:
            self.logger.error(f"Chyba při stahování feedu: {e}")
            raise
        except ET.ParseError as e:
            self.logger.error(f"Chyba při parsování XML: {e}")
            raise
        except Exception as e:
            self.logger.error(f"Neočekávaná chyba: {e}")
            raise
