from typing import Dict, Any
import logging
from .base_connector import BaseConnector
from .avenberg_connector import AvenbergConnector
from .brawson_connector import <PERSON>rawsonConnector
from .brawson_2_connector import <PERSON><PERSON>son2Connector
from .coolpohyb_connector import CoolPohybConnector
from .filsonstore_connector import <PERSON>lsonstoreConnector
from .testshop_connector import TestshopConnector

from .demo_test_connector import Demo_testConnector
from .jabkolevne_connector import JabkolevneConnector

class ConnectorFactory:
    _connectors = {
        'avenberg': AvenbergConnector,
        'brawson': BrawsonConnector,
        'brawson_2': Brawson2Connector,
        'coolpohyb': CoolPohybConnector,
        'filsonstore': FilsonstoreConnector,
        'testshop': TestshopConnector,
        # Zde budou dalš<PERSON> konektory
        'demo_test': Demo_testConnector,
        'jabkolevne': JabkolevneConnector,
}
    
    @classmethod
    def create_connector(cls, connector_type: str, config: Dict[str, Any]) -> BaseConnector:
        """
        Vyt<PERSON><PERSON><PERSON> a vrátí instanci konektoru podle typu
        
        Args:
            connector_type: Typ konektoru (např. 'avenberg', 'brawson', 'brawson_2', 'coolpohyb')
            config: Konfigurace pro konektor
            
        Returns:
            Instance konektoru
        """
        logger = logging.getLogger("ConnectorFactory")
        
        if connector_type not in cls._connectors:
            logger.error(f"Neznámý typ konektoru: {connector_type}")
            raise ValueError(f"Neznámý typ konektoru: {connector_type}")
        
        try:
            return cls._connectors[connector_type](config)
        except Exception as e:
            logger.error(f"Chyba při vytváření konektoru {connector_type}: {e}")
            raise
