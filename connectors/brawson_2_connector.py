from connectors.brawson_connector import BrawsonConnector
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)

class Brawson2Connector(BrawsonConnector):
    """
    Konektor pro zpracování XML feedu od Brawson 2.
    Mapuje specifický formát Brawson 2 na standardní interní formát.
    
    Analogie:
    - Jako adaptér pro různé typy zásuvek
    - Převádí specifický formát na standardní
    """
    
    def get_feed(self) -> List[Dict[str, Any]]:
        """
        Získá data z Brawson 2 feedu a převede je na standardní formát.
        
        Returns:
            List[Dict[str, Any]]: Seznam produktů ve standardním formátu
        """
        # Získáme data pomocí původního konektoru
        products = super().get_feed()
        
        # Převedeme na standardní formát
        standardized_products = []
        for product in products:
            standardized_product = {
                'ID': product['ID'],  # ID zůstává stejné
                'Name': product['Name'],  # Zachováme velká písmena pro kompatibilitu
                'Description': product['Description'],  # Zachováme velká písmena
                'Category': product['Category'],  # Zachováme velká písmena
                'Price': product.get('Price', ''),  # Zachováme velká písmena
                'product_url': product.get('Link', ''),  # Mapujeme Link na interní product_url
                'image_url': product.get('ImageLink', ''),  # Mapujeme ImageLink na interní image_url
                'Availability': product.get('Availability', '')  # Zachováme velká písmena
            }
            standardized_products.append(standardized_product)
            
        return standardized_products 