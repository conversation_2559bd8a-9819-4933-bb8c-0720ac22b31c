from typing import Dict, List, Any, Optional
import logging
import pandas as pd
import sys
import os
import requests
import xml.etree.ElementTree as ET
from bs4 import BeautifulSoup

# Přidáme cestu k root adresáři pro import XMLConnector
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from .base_connector import BaseConnector


class CoolPohybConnector(BaseConnector):
    """
    Konektor pro zpracování XML feedu z coolpohyb.cz.
    
    Tento konektor zpracovává produktový feed ve formátu Atom XML s Google prvky,
    dostupný na URL https://www.coolpohyb.cz/google.xml.
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        Inicializuje konektor s danou konfigurací.
        
        Args:
            config: Konfigurace konektoru, která musí obsahovat:
                - feed_url: URL k XML feedu
        """
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("Konfigurace musí obsahovat 'feed_url'")
        
        self.logger.info(f"CoolPohybConnector inicializován s URL: {self.feed_url}")
    
    def get_feed(self):
        """
        Získá a zpracuje feed z URL.
        
        Vrací seznam slovníků s produkty v standardizovaném formátu.
        Sloupce: id, name, description, category, price, brand, availability,
        condition, image_link, additional_image_link, product_url
        """
        self.logger.info("Začínám získávat data z feedu")
        
        if not self.feed_url:
            self.logger.error("Chyba: Feed URL není specifikováno")
            return []
        
        try:
            feed_content = self._fetch_feed(self.feed_url)
            if feed_content:
                # Parsování Atom XML
                self.logger.info("Parsování Atom XML feedu")
                data = self._parse_atom_feed(feed_content)
                
                # Převedení na seznam slovníků a standardizace názvů sloupců
                if isinstance(data, pd.DataFrame):
                    # Přejmenovat sloupec 'link' na 'product_url' pokud existuje
                    if 'link' in data.columns:
                        data = data.rename(columns={'link': 'product_url'})
                    
                    # Kontrola, zda má data potřebné sloupce
                    required_columns = ['ID', 'Name', 'product_url']
                    missing_columns = [col for col in required_columns if col not in data.columns]
                    if missing_columns:
                        self.logger.warning(f"Ve feedu chybí tyto sloupce: {missing_columns}")
                    
                    self.logger.info(f"Úspěšně načteno a transformováno {len(data)} produktů")
                    return data.to_dict('records')
                else:
                    self.logger.error("Neočekávaný formát dat z parsování")
                    return []
            else:
                self.logger.error("Nepodařilo se získat feed - prázdný obsah")
                return []
        except Exception as e:
            self.logger.error(f"Chyba při zpracování feedu: {e}")
            return []
            
    def _fetch_feed(self, feed_url: str) -> bytes:
        """Stáhne feed z URL."""
        try:
            self.logger.info(f"Fetching feed from {feed_url}")
            response = requests.get(feed_url, timeout=30)
            response.raise_for_status()  # Vyvolá výjimku při HTTP chybách
            self.logger.info("Feed fetched successfully")
            return response.content
        except requests.exceptions.RequestException as e:
            self.logger.error(f"Chyba při získávání feedu: {e}")
            return None
            
    def _parse_atom_feed(self, content):
        """
        Parsuje Atom XML feed a vrací DataFrame s produkty.
        """
        try:
            soup = BeautifulSoup(content, 'xml')
            entries = soup.find_all('entry')
            self.logger.info(f"Nalezeno {len(entries)} položek v Atom XML")
            
            data = []
            
            for entry in entries:
                try:
                    # Extrakce základních informací z XML
                    product_id = self._get_element_text_with_ns(entry, 'g:id')
                    product_title = self._get_element_text_with_ns(entry, 'title')
                    product_description = self._get_element_text_with_ns(entry, 'g:description')
                    product_category = self._get_element_text_with_ns(entry, 'g:product_type')
                    product_price = self._get_element_text_with_ns(entry, 'g:price')
                    product_brand = self._get_element_text_with_ns(entry, 'g:brand')
                    product_availability = self._get_element_text_with_ns(entry, 'g:availability')
                    product_condition = self._get_element_text_with_ns(entry, 'g:condition')
                    product_image_link = self._get_element_text_with_ns(entry, 'g:image_link')
                    product_additional_image_link = self._get_element_text_with_ns(entry, 'g:additional_image_link')
                    
                    # Získání URL produktu
                    product_url = ""
                    link_element = entry.find('link')
                    if link_element and link_element.text:
                        product_url = link_element.text.strip()
                        self.logger.debug(f"Nalezena URL: {product_url}")
                    else:
                        self.logger.debug(f"URL nenalezena pro produkt ID: {product_id}")
                    
                    # Vytvoření slovníku produktu
                    product = {
                        'ID': product_id,
                        'Name': product_title,
                        'Description': product_description,
                        'Category': product_category,
                        'Price': product_price,
                        'Brand': product_brand,
                        'Availability': product_availability,
                        'Condition': product_condition,
                        'image_link': product_image_link,
                        'additional_image_link': product_additional_image_link,
                        'product_url': product_url
                    }
                    
                    # Přidat produkt do dat, pouze pokud má ID a název
                    if product_id and product_title:
                        data.append(product)
                    else:
                        self.logger.warning(f"Přeskakuji neúplný produkt bez ID nebo názvu: {product_id or 'N/A'}")
                except Exception as e:
                    self.logger.error(f"Chyba při zpracování položky: {e}")
            
            # Vytvoření DataFrame z dat
            df = pd.DataFrame(data)
            
            # Debug informace o sloupcích
            self.logger.debug(f"Dostupné sloupce: {df.columns.tolist()}")
            
            # Kontrola počtu produktů s URL
            products_with_url = df[df['product_url'] != ""]
            self.logger.info(f"Počet produktů s URL: {len(products_with_url)}")
            
            self.logger.info(f"Parsování dokončeno, zpracováno {len(df)} validních produktů")
            return df
            
        except Exception as e:
            self.logger.error(f"Chyba při parsování Atom feedu: {e}")
            return pd.DataFrame()
    
    def _get_element_text_with_ns(self, element, tag, namespaces=None) -> Optional[str]:
        """
        Získá text z elementu s podporou jmenných prostorů.
        
        Podporuje různé formáty tagů (g:tag, tag, {ns}tag).
        """
        # Nejprve zkusíme přímý tag
        found = element.find(tag)
        if found:
            return found.text.strip() if found.text else ""
            
        # Zkusíme vyhledat bez prefixu g:
        if ":" in tag:
            simple_tag = tag.split(":")[-1]
            found = element.find(simple_tag)
            if found:
                return found.text.strip() if found.text else ""
                
        # Poslední pokus - jakýkoliv tag končící na požadovaný název
        if ":" in tag:
            tag_suffix = tag.split(":")[-1]
            for child in element.find_all():
                if child.name and child.name.endswith(tag_suffix):
                    return child.text.strip() if child.text else ""
                    
        return "" 