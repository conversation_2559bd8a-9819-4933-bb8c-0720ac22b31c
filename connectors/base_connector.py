from abc import ABC, abstractmethod
from typing import List, Dict, Any
import logging


class BaseConnector(ABC):
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
    
    @abstractmethod
    def get_feed(self) -> List[Dict[str, Any]]:
        """
        Získá data z feedu a vrátí je jako list slovníků
        Každý slovník musí obsahovat minimálně:
        - id: str
        - name: str
        - category: str
        - description: str (volitelné)
        """
        pass
