from typing import List, Dict, Any
import requests
import xml.etree.ElementTree as ET
from .base_connector import BaseConnector


class JabkolevneConnector(BaseConnector):
    """Automaticky generovaný XML konektor pro jabkolevne"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")
        
        # Namespaces pro Atom feed
        self.ns = {
            'atom': 'http://www.w3.org/2005/Atom',
            'g': 'http://base.google.com/ns/1.0'
        }

    def get_feed(self) -> List[Dict[str, Any]]:
        """Získá a zpracuje XML feed"""
        self.logger.info(f"Načítám XML feed z {self.feed_url}")
        try:
            response = requests.get(self.feed_url, timeout=60)
            response.raise_for_status()
            
            # Parsování XML
            self.logger.info("Parsování XML...")
            root = ET.fromstring(response.content)
            
            # Hledáme produktové polo<PERSON> (entry) v Atom feedu
            items = []
            
            # Zkusíme najít různé možné formáty produktových položek s respektováním namespace
            for path in ['.//atom:entry', './/entry', './/item', './/SHOPITEM']:
                if ':' in path and path.split(':')[0].strip('.//') in self.ns:
                    prefix = path.split(':')[0].strip('.//') 
                    tag = path.split(':')[1]
                    path_with_ns = './/{{{0}}}{1}'.format(self.ns[prefix], tag)
                    found_items = root.findall(path_with_ns)
                else:
                    found_items = root.findall(path)
                
                if found_items:
                    items = found_items
                    self.logger.info(f"Nalezeno {len(items)} produktů pomocí xpath: {path}")
                    break
            
            if not items:
                self.logger.warning("Nenalezeny žádné produktové položky v XML feedu!")
                return []
            
            products = []
            
            for item in items:
                try:
                    def safe_get_text(element_name, default=""):
                        # Zkusíme najít element s použitím namespace
                        elem = None
                        
                        # Pokud je formát s namespace (např. g:id)
                        if ':' in element_name:
                            prefix = element_name.split(':')[0]
                            tag = element_name.split(':')[1]
                            
                            if prefix in self.ns:
                                # Použijeme namespace pro hledání
                                ns_tag = '{{{0}}}{1}'.format(self.ns[prefix], tag)
                                elem = item.find(ns_tag)
                            
                            # Zkusíme také hledat bez namespace
                            if elem is None:
                                elem = item.find(tag)
                        else:
                            # Zkusíme najít element přímo
                            elem = item.find(element_name)
                            
                            # Pro Atom feed zkusíme najít také s atom namespace
                            if elem is None:
                                ns_tag = '{{{0}}}{1}'.format(self.ns['atom'], element_name)
                                elem = item.find(ns_tag)
                        
                        # Ještě zkusíme case-insensitive a bez namespace
                        if elem is None:
                            tag_to_find = element_name.split(':')[1].lower() if ':' in element_name else element_name.lower()
                            for child in item:
                                # Získáme jméno tagu bez namespace
                                child_tag = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                                if child_tag.lower() == tag_to_find:
                                    elem = child
                                    break
                        
                        return elem.text.strip() if elem is not None and elem.text else default
                    
                    # Zkusíme běžné názvy polí pro různé typy XML feedů
                    product = {
                        'ID': (
                            safe_get_text('g:id') or safe_get_text('id') or safe_get_text('ID') or
                            safe_get_text('ITEM_ID') or safe_get_text('CODE')
                        ),
                        'Name': (
                            safe_get_text('title') or safe_get_text('g:title') or safe_get_text('name') or
                            safe_get_text('PRODUCTNAME') or safe_get_text('NAME')
                        ),
                        'Description': (
                            safe_get_text('description') or safe_get_text('g:description') or 
                            safe_get_text('DESCRIPTION') or safe_get_text('desc')
                        ),
                        'Category': (
                            safe_get_text('g:product_type') or safe_get_text('category') or 
                            safe_get_text('CATEGORY') or safe_get_text('CATEGORYTEXT')
                        ),
                        'Price': (
                            safe_get_text('g:price') or safe_get_text('price') or 
                            safe_get_text('PRICE') or safe_get_text('PRICE_VAT')
                        ),
                        'Brand': (
                            safe_get_text('g:brand') or safe_get_text('brand') or 
                            safe_get_text('MANUFACTURER') or safe_get_text('BRAND')
                        ),
                        'Availability': (
                            safe_get_text('g:availability') or safe_get_text('availability') or 
                            safe_get_text('AVAILABILITY') or safe_get_text('DELIVERY_DATE')
                        ),
                        'ImageUrl': (
                            safe_get_text('g:image_link') or safe_get_text('image') or 
                            safe_get_text('IMGURL') or safe_get_text('IMAGE')
                        ),
                        'ProductUrl': (
                            safe_get_text('link') or safe_get_text('g:link') or 
                            safe_get_text('URL') or safe_get_text('url')
                        )
                    }
                    
                    # Kontrola povinných polí
                    if product['ID'] and product['Name']:
                        products.append(product)
                    else:
                        self.logger.warning(f"Přeskakuji produkt bez ID nebo názvu")
                        
                except Exception as e:
                    self.logger.warning(f"Chyba při zpracování produktu: {e}")
                    continue
            
            self.logger.info(f"Načteno {len(products)} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání feedu: {e}")
            raise
