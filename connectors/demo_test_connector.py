from typing import List, Dict, Any
import requests
import pandas as pd
import os
from io import StringIO
from .base_connector import BaseConnector
import urllib.parse


class Demo_testConnector(BaseConnector):
    """Automaticky generovaný CSV konektor pro demo_test"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")

    def get_feed(self) -> List[Dict[str, Any]]:
        """Získá a zpracuje CSV feed"""
        self.logger.info(f"Načítám CSV feed z {self.feed_url}")
        try:
            # Kontrola, zda se jedná o lokální soubor nebo URL
            if self.feed_url.startswith('file://'):
                # Lokální soubor - zpracujeme přímo
                file_path = self.feed_url[7:]  # Odstraníme 'file://'
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                else:
                    raise FileNotFoundError(f"Soubor {file_path} neexistuje")
            else:
                # Běžné URL - použijeme requests
                response = requests.get(self.feed_url, timeout=30)
                response.raise_for_status()
                content = response.content.decode('utf-8')
            
            # Načtení CSV
            df = pd.read_csv(StringIO(content), sep=',')
            
            # Standardizace názvů sloupců
            column_mapping = {}
            for col in df.columns:
                col_lower = col.lower()
                if 'id' in col_lower:
                    column_mapping[col] = 'ID'
                elif 'name' in col_lower or 'title' in col_lower:
                    column_mapping[col] = 'Name'
                elif 'desc' in col_lower:
                    column_mapping[col] = 'Description'
                elif 'categ' in col_lower:
                    column_mapping[col] = 'Category'
                elif 'price' in col_lower or 'cena' in col_lower:
                    column_mapping[col] = 'Price'
                elif 'brand' in col_lower or 'znacka' in col_lower:
                    column_mapping[col] = 'Brand'
                elif 'avail' in col_lower or 'dostup' in col_lower:
                    column_mapping[col] = 'Availability'
                elif 'image' in col_lower or 'foto' in col_lower:
                    column_mapping[col] = 'ImageUrl'
                elif 'url' in col_lower or 'link' in col_lower:
                    column_mapping[col] = 'ProductUrl'
            
            # Přejmenování sloupců
            df = df.rename(columns=column_mapping)
            
            # Konverze na seznam slovníků
            products = df.to_dict('records')
            
            # Vyčištění dat
            for product in products:
                for key, value in product.items():
                    if pd.isna(value):
                        product[key] = ""
                    else:
                        product[key] = str(value)
            
            self.logger.info(f"Načteno {len(products)} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání feedu: {e}")
            raise
