import pandas as pd
from typing import Dict, Any, Optional
from .base_connector import BaseConnector

class ShopKingConnector(BaseConnector):
    """
    Konektor pro ShopKing feed
    Předpokládejme že mají jin<PERSON> form<PERSON>, např. CSV s jin<PERSON><PERSON>
    """
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.required_columns = ['product_id', 'title', 'description', 'category']
        self.column_mapping = {
            'product_id': 'ID',           # ShopKing má ID jako product_id
            'title': 'Name',              # title -> Name
            'description': 'Description',  # description -> Description
            'category': 'Category',        # category -> Category
            'price': 'Price',             # volitelné
            'brand': 'Brand',             # volitelné
            'url': 'Link',                # volitelné
            'image_url': 'ImageLink'      # volitelné
        }
    
    def _load_feed(self) -> pd.DataFrame:
        """
        Načte feed a převede ho do standardního formátu
        """
        # Načtení CSV feedu (příklad)
        df = pd.read_csv(self.config['feed_url'])
        
        # Kontrola povinných sloupců
        missing_columns = [col for col in self.required_columns if col not in df.columns]
        if missing_columns:
            raise ValueError(f"Chybí povinné sloupce: {', '.join(missing_columns)}")
        
        # Přejmenování sloupců podle mappingu
        df_mapped = pd.DataFrame()
        for target_col, source_col in self.column_mapping.items():
            if target_col in df.columns:
                df_mapped[source_col] = df[target_col]
            else:
                # Pro chybějící nepovinné sloupce použijeme výchozí hodnoty
                if target_col == 'price':
                    df_mapped[source_col] = '0'
                elif target_col in ['brand', 'url', 'image_url']:
                    df_mapped[source_col] = ''
        
        return df_mapped
    
    def _validate_feed(self, df: pd.DataFrame) -> None:
        """
        Validace dat ve feedu
        """
        # Kontrola datových typů
        if not df['ID'].astype(str).str.match(r'^[A-Za-z0-9_-]+$').all():
            raise ValueError("ID produktů obsahují neplatné znaky")
        
        # Kontrola prázdných hodnot v povinných sloupcích
        required_columns = ['ID', 'Name', 'Description', 'Category']
        for col in required_columns:
            if df[col].isnull().any():
                raise ValueError(f"Sloupec {col} obsahuje prázdné hodnoty")
        
        # Další specifické validace pro ShopKing
        if 'Price' in df.columns and not df['Price'].astype(str).str.match(r'^\d+(\.\d{1,2})?$').all():
            self.logger.warning("Některé ceny nejsou ve správném formátu")
