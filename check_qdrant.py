#!/usr/bin/env python3

from qdrant_client import QdrantClient

# Inicializace klienta
client = QdrantClient(host='localhost', port=6333)

# Získání seznamu kolekcí
collections = client.get_collections().collections
collection_names = [col.name for col in collections]
print(f'Dostupné kolekce: {collection_names}')

# Kontrola počtu produktů v kolekci jabkolevne
if 'real_products_jabkolevne' in collection_names:
    results = client.count(collection_name='real_products_jabkolevne')
    print(f'Počet produktů v kolekci real_products_jabkolevne: {results.count}')
else:
    print('Kolekce real_products_jabkolevne neexistuje!')

# Výpis všech kolekcí a počtu produktů
print('\nPočty produktů ve všech kolekcích:')
for col_name in collection_names:
    if col_name.startswith('real_products_'):
        count = client.count(collection_name=col_name).count
        print(f'  - {col_name}: {count} produktů')
