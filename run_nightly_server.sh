#!/bin/bash
# run_nightly_server.sh - Spustí noční proces v Docker kontejneru

cd /opt/gallitec/recommendatio_and_semantic/
echo "=== SPOUŠTÍM NOČNÍ PROCES V DOCKER KONTEJNERU ==="
echo "Datum a čas: $(date)"
echo "Parametry: $@"

# Spuštění Docker kontejneru s předanými parametry
docker compose run --rm nightly_process "$@"

exit_code=$?
echo "=== NOČNÍ PROCES V DOCKER KONTEJNERU BYL DOKONČEN ==="
echo "Datum a čas dokončení: $(date)"
echo "Návratový kód: $exit_code"

exit $exit_code 