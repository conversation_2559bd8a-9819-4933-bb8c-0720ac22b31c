# feed_processor.py
import logging
import asyncio
from typing import List, Optional, Dict, Any
from data_models import Product
from connectors.connector_factory import ConnectorFactory # Předpokládá existenci
from connectors.base_connector import BaseConnector # Předpokládá existenci
from config import TenantSettings, FeedSettings
from utils import normalize_category
import re

logger = logging.getLogger(__name__)

def _extract_price(price_str: Optional[str]) -> float:
    """Extrahuje cenu z řetězce."""
    if not price_str:
        return 0.0
    try:
        # Odstranění měny a mezer, nahrazení desetinné čárky tečkou
        price_str = re.sub(r"[^\d,.]", "", str(price_str)).replace(',', '.')
        # Odstranění teček kromě poslední (pro případy jako "1.234.56")
        parts = price_str.split('.')
        if len(parts) > 1:
            price_str = "".join(parts[:-1]) + "." + parts[-1]
        return float(price_str) if price_str else 0.0
    except Exception:
        logger.warning(f"Could not parse price string: {price_str}", exc_info=True)
        return 0.0

def _validate_raw_product(raw_product: Dict[str, Any]) -> bool:
    """Validuje základní povinná pole v surových datech z feedu."""
    product_id = raw_product.get('ID')
    name = raw_product.get('Name')
    category = raw_product.get('Category')

    if not product_id:
        logger.warning("Skipping product: Missing 'ID'")
        return False
    if not name:
        logger.warning(f"Skipping product {product_id}: Missing 'Name'")
        return False
    if not category:
        logger.warning(f"Skipping product {product_id}: Missing 'Category'")
        return False
    return True

def _transform_raw_product(raw_product: Dict[str, Any]) -> Optional[Product]:
    """Transformuje surová data z feedu na Pydantic model Product."""
    if not _validate_raw_product(raw_product):
        return None

    try:
        # Získáme ID a kategorii, které jsou povinné
        product_id = str(raw_product['ID']).strip()
        # Použijeme get pro kategorii pro případ, že by validace selhala (i když by neměla)
        category_raw = str(raw_product.get('Category', '')).strip()

        # Standardizace kategorie
        category_normalized = normalize_category(category_raw)
        if not category_normalized:
             logger.warning(f"Product {product_id} has empty normalized category from '{category_raw}', skipping.")
             return None

        # Získání ostatních polí s fallbacky pro Google Shopping feed (g: prefix a link tag)
        name = str(raw_product.get('Name', '')).strip()
        description = str(raw_product.get('Description', '')).strip()
        # Cena: Zkusí 'Price', pak 'g:price'
        price_raw = raw_product.get('Price') or raw_product.get('g:price') # Získáme hodnotu
        logger.debug(f"Raw price value for ID {product_id}: {price_raw!r}") # LOG
        price = _extract_price(price_raw)
        logger.debug(f"Extracted price for ID {product_id}: {price}") # LOG
        # URL obrázku: Zkusí 'ImageUrl', pak 'g:image_link'
        image_url_raw = str(raw_product.get('ImageUrl', '') or raw_product.get('g:image_link', '')).strip()
        logger.debug(f"Raw image_url value for ID {product_id}: {image_url_raw!r}") # LOG
        image_url = image_url_raw or None
        # URL produktu: Zkusí 'ProductUrl', pak 'link'
        product_url_raw = str(raw_product.get('ProductUrl', '') or raw_product.get('link', '')).strip()
        logger.debug(f"Raw product_url value for ID {product_id}: {product_url_raw!r}") # LOG
        product_url = product_url_raw or None
        # Brand: Zkusí 'Brand', pak 'g:brand'
        brand_raw = str(raw_product.get('Brand', '') or raw_product.get('g:brand', '')).strip()
        logger.debug(f"Raw brand value for ID {product_id}: {brand_raw!r}") # LOG
        brand = brand_raw or None
        # Dostupnost: Zkusí 'Availability', pak 'g:availability'
        availability_raw = str(raw_product.get('Availability', '') or raw_product.get('g:availability', '')).strip()
        availability = availability_raw or None

        # DEBUG LOG: Vypíšeme hodnoty těsně před vytvořením Product
        logger.debug(f"Creating Product for ID {product_id} with: price={price}, image_url={image_url!r}, product_url={product_url!r}, brand={brand!r}")

        product = Product(
            id=product_id,
            name=name,
            category=category_normalized,
            description=description,
            price=price,
            image_url=image_url,
            product_url=product_url,
            brand=brand,
            availability=availability,
            raw_data=raw_product # Uložíme i původní data pro případnou potřebu
        )
        return product
    except Exception as e:
        logger.error(f"Failed to transform product with raw ID {raw_product.get('ID')}: {e}", exc_info=True)
        return None

async def load_and_transform_feed(tenant_settings: TenantSettings) -> List[Product]:
    """
    Načte data z feedu pomocí správného konektoru a transformuje je.
    Používá synchronní konektor, běží v executoru.
    """
    logger.info(f"Loading and transforming feed for tenant {tenant_settings.tenant_id} using connector type {tenant_settings.feed.type}")

    try:
        # Vytvoření konektoru - předpokládá synchronní implementaci
        # Pokud by byl konektor asynchronní, nebylo by potřeba run_in_executor
        connector: BaseConnector = ConnectorFactory.create_connector(
            tenant_settings.feed.type,
            tenant_settings.feed.config
        )

        # Spuštění synchronního kódu v executoru, aby neblokoval asyncio loop
        loop = asyncio.get_running_loop()
        raw_products = await loop.run_in_executor(None, connector.get_feed) # None použije defaultní ThreadPoolExecutor

        if not raw_products:
            logger.warning(f"Feed for tenant {tenant_settings.tenant_id} is empty or failed to load.")
            return []

        logger.info(f"Loaded {len(raw_products)} raw products from feed.")

        transformed_products: List[Product] = []
        for raw_product in raw_products:
            product = _transform_raw_product(raw_product)
            if product:
                transformed_products.append(product)

        logger.info(f"Successfully transformed {len(transformed_products)} products.")
        if len(transformed_products) < len(raw_products):
             logger.warning(f"Skipped {len(raw_products) - len(transformed_products)} products during transformation due to validation errors.")

        return transformed_products

    except Exception as e:
        logger.error(f"Failed to load or transform feed for tenant {tenant_settings.tenant_id}: {e}", exc_info=True)
        return []

def get_feed_parser(feed_type: str, feed_config: Dict[str, Any]):
    """
    Vytvoří a vrátí parser pro konkrétní typ feedu.
    
    Args:
        feed_type: Typ feedu (např. 'avenberg', 'coolpohyb', apod.)
        feed_config: Konfigurace pro feed
        
    Returns:
        Funkce pro načtení a transformaci dat z feedu
    """
    logger.info(f"Creating feed parser for type: {feed_type}")
    
    # Funkce, která bude použita pro načtení a transformaci dat z feedu
    async def feed_parser(tenant_settings):
        # Vytvoříme dočasné nastavení tenanta s konkrétním typem a konfigurací feedu
        temp_settings = TenantSettings(
            tenant_id=tenant_settings.tenant_id,
            feed=FeedSettings(type=feed_type, config=feed_config)
        )
        return await load_and_transform_feed(temp_settings)
        
    return feed_parser