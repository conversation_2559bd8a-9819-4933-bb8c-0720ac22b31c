# hybrid_search.py
import logging
from typing import Dict, List, Any, Optional, Union, Tuple
from openai import AsyncOpenAI
import cohere
from qdrant_client import AsyncQdrantClient

from semantic import search_semantic_products, generate_semantic_embedding
from fulltext import CzechFulltextRetriever, load_products_from_qdrant

logger = logging.getLogger(__name__)

async def ensemble_retrievers(
    fulltext_results: List[Dict[str, Any]], 
    semantic_results: List[Dict[str, Any]], 
    alpha: float = 0.5
) -> List[Dict[str, Any]]:
    """
    Kombinuje výsledky z fulltextového a sémantického vyhledávání pomocí váženého skóre.
    
    Args:
        fulltext_results: Výsledky z fulltextového vyhledávání
        semantic_results: Výsledky ze sémantického vyhledávání
        alpha: Váha pro sémantické výsledky (1-alpha pro fulltext)
        
    Returns:
        <PERSON>znam kombinovaných výsledků seřazený podle kombinovaného skóre
    """
    combined_dict = {}
    
    # Normalizace fulltext skóre
    max_ft_score = max([r["score"] for r in fulltext_results]) if fulltext_results else 1.0
    
    # Přidání fulltext výsledků s normalizací skóre
    for result in fulltext_results:
        product_id = result.get("product_id") or result.get("original_id")
        if not product_id:
            continue
            
        normalized_score = result["score"] / max_ft_score if max_ft_score > 0 else 0
        
        combined_dict[product_id] = {
            **result,  # Kopírujeme všechny původní hodnoty
            "fulltext_score": normalized_score,
            "semantic_score": 0.0,
            "combined_score": (1 - alpha) * normalized_score  # Aplikujeme váhu pro fulltext
        }
    
    # Normalizace sémantických skóre
    max_sem_score = max([r["score"] for r in semantic_results]) if semantic_results else 1.0
    
    # Přidání sémantických výsledků s normalizací skóre
    for result in semantic_results:
        product_id = result.get("product_id") or result.get("original_id")
        if not product_id:
            continue
            
        normalized_score = result["score"] / max_sem_score if max_sem_score > 0 else 0
        
        if product_id in combined_dict:
            # Produkt už existuje ve výsledcích z fulltext, aktualizujeme
            combined_dict[product_id]["semantic_score"] = normalized_score
            combined_dict[product_id]["combined_score"] += alpha * normalized_score  # Přidáme váženou sémantickou část
        else:
            # Nový produkt pouze ze sémantického vyhledávání
            combined_dict[product_id] = {
                **result,  # Kopírujeme všechny původní hodnoty
                "fulltext_score": 0.0,
                "semantic_score": normalized_score,
                "combined_score": alpha * normalized_score  # Aplikujeme váhu pouze pro sémantickou část
            }
    
    # Seřazení podle kombinovaného skóre
    combined_results = sorted(
        list(combined_dict.values()), 
        key=lambda x: x["combined_score"], 
        reverse=True
    )
    
    logger.info(f"Ensemble: Kombinováno {len(fulltext_results)} fulltext a {len(semantic_results)} semantic výsledků do {len(combined_results)} výsledků.")
    return combined_results


async def search_hybrid_products(
    client: AsyncQdrantClient,
    collection_name: str,
    query_text: str,
    openai_client: AsyncOpenAI,
    cohere_client: Optional[cohere.Client] = None,
    limit: int = 10,
    enable_reranking: bool = True,
    fulltext_ratio: float = 0.25,  # Poměr kandidátů z fulltext vyhledávání (25%)
    semantic_ratio: float = 0.75,  # Poměr kandidátů ze sémantického vyhledávání (75%)
    ensemble_alpha: float = 0.5,   # Váha pro sémantické výsledky v ensemble metodě
    rerank_limit_multiplier: int = 4,  # Násobitel pro počet kandidátů před rerankingem
    fulltext_retriever: Optional[CzechFulltextRetriever] = None
) -> List[Dict[str, Any]]:
    """
    Hybridní vyhledávání kombinující fulltextové a sémantické vyhledávání s rerankingem.
    
    Args:
        client: AsyncQdrantClient instance
        collection_name: Název kolekce v Qdrantu
        query_text: Vyhledávací dotaz
        openai_client: AsyncOpenAI instance pro generování embeddings
        cohere_client: Cohere client pro reranking
        limit: Počet výsledků k vrácení
        enable_reranking: Zda použít reranking pomocí Cohere
        fulltext_ratio: Poměr kandidátů z fulltextu (0.0-1.0)
        semantic_ratio: Poměr kandidátů ze sémantiky (0.0-1.0)
        ensemble_alpha: Váha pro sémantické výsledky (0.0-1.0)
        rerank_limit_multiplier: Násobitel počtu kandidátů pro reranking
        fulltext_retriever: Předem inicializovaný fulltext retriever
        
    Returns:
        Seznam výsledků po hybridním vyhledávání a rerankingu
    """
    logger.info(f"Začínám hybridní vyhledávání pro dotaz: '{query_text}'")
    
    # Zajistíme, že poměry dávají dohromady 1.0
    total_ratio = fulltext_ratio + semantic_ratio
    if total_ratio != 1.0:
        fulltext_ratio = fulltext_ratio / total_ratio
        semantic_ratio = semantic_ratio / total_ratio
        logger.warning(f"Upravuji poměry na fulltext={fulltext_ratio:.2f}, semantic={semantic_ratio:.2f}")
    
    # Výpočet počtu kandidátů pro jednotlivé retrievery
    total_candidates = limit * rerank_limit_multiplier
    fulltext_candidates = int(total_candidates * fulltext_ratio)
    semantic_candidates = total_candidates - fulltext_candidates
    
    logger.info(f"Hybrid search: celkem {total_candidates} kandidátů, z toho {fulltext_candidates} fulltext a {semantic_candidates} semantic")
    
    # 1. Fulltextové vyhledávání
    fulltext_results = []
    if fulltext_candidates > 0:
        # Použijeme existující retriever nebo vytvoříme nový
        if fulltext_retriever is None:
            logger.info("Inicializuji nový fulltext retriever a načítám produkty...")
            fulltext_retriever = CzechFulltextRetriever()
            products = await load_products_from_qdrant(client, collection_name)
            fulltext_retriever.add_documents(products)
        
        fulltext_results = fulltext_retriever.search(query_text, top_k=fulltext_candidates)
        logger.info(f"Fulltext search: nalezeno {len(fulltext_results)} výsledků")
    
    # 2. Sémantické vyhledávání
    semantic_results = []
    if semantic_candidates > 0:
        semantic_results = await search_semantic_products(
            client=client,
            collection_name=collection_name,
            query_text=query_text,
            openai_client=openai_client,
            limit=semantic_candidates,
            enable_reranking=False,  # Vypneme reranking zde, provedeme ho až na konci
            rerank_limit_multiplier=1  # Nepotřebujeme násobit, už máme přesný počet
        )
        logger.info(f"Semantic search: nalezeno {len(semantic_results)} výsledků")
    
    # 3. Kombinace výsledků pomocí ensemble metody
    combined_results = await ensemble_retrievers(
        fulltext_results=fulltext_results,
        semantic_results=semantic_results,
        alpha=ensemble_alpha
    )
    
    # 4. Reranking (volitelný)
    if enable_reranking and cohere_client and combined_results:
        logger.info(f"Provádím reranking {len(combined_results)} kandidátů pomocí Cohere")
        try:
            # Příprava dokumentů pro reranking
            documents = [result.get("name", "") for result in combined_results]
            
            # Reranking pomocí Cohere
            rerank_results = cohere_client.rerank(
                query=query_text,
                documents=documents,
                model="rerank-v3.5",
                top_n=limit
            )
            
            # Aktualizace skóre ve výsledcích
            for i, result in enumerate(rerank_results.results):
                combined_results[i]["rerank_score"] = result.relevance_score
            
            # Seřazení podle rerank skóre
            combined_results = sorted(
                combined_results[:len(rerank_results.results)], 
                key=lambda x: x.get("rerank_score", 0), 
                reverse=True
            )
            
            logger.info(f"Reranking dokončen, vráceno {len(combined_results)} výsledků")
            
        except Exception as e:
            logger.error(f"Chyba při rerankingu: {e}", exc_info=True)
    
    # Omezení počtu výsledků
    final_results = combined_results[:limit]
    logger.info(f"Hybridní vyhledávání dokončeno, vráceno {len(final_results)} výsledků")
    
    return final_results
