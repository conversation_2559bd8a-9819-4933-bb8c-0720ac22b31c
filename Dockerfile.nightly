FROM python:3.10-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# <PERSON>p<PERSON>rov<PERSON><PERSON> skriptů pro noční proces
COPY run_nightly_process.py /app/
COPY run_nightly_process.sh /app/
COPY clean_complementary_references.py /app/
COPY batch_scripts/compute_complementary.py /app/batch_scripts/
COPY core /app/core/
COPY embedding_strategies.py /app/
COPY avenberg_graph_data.json /app/
COPY config /app/config/

# Vytvoření adresáře pro logy
RUN mkdir -p /app/logs

# Nastavení oprávnění
RUN chmod +x /app/run_nightly_process.sh
RUN chmod +x /app/run_nightly_process.py
RUN chmod +x /app/clean_complementary_references.py
RUN chmod +x /app/batch_scripts/compute_complementary.py

ENTRYPOINT ["/app/run_nightly_process.sh"] 