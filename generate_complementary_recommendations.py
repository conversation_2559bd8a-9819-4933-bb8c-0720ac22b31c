# generate_complementary_recommendations.py
import asyncio
import logging
import json
import time
import re
import os
import argparse
from typing import Dict, List, Any, Optional

from clients import get_qdrant_async_client, get_openai_client, get_anthropic_client, close_clients
from embedding_strategies import MultiEmbeddingStrategy
from context_builder import DynamicContextBuilder
from cluster_representation import AdaptiveClusterRepresentation
from qdrant_client.http import models

# Nastavení loggingu
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler(f"logs/complementary_recommendations_{time.strftime('%Y%m%d_%H%M%S')}.log")
    ]
)
logger = logging.getLogger("complementary_recommendations")

# Vytvoření adresáře pro logy, pokud neexistuje
os.makedirs("logs", exist_ok=True)

async def create_recommendation_collection(qdrant_client, collection_name: str, force: bool = False) -> bool:
    """
    Vytvoří kolekci pro ukládání doporučení v Qdrantu.

    Args:
        qdrant_client: Klient pro Qdrant
        collection_name: Název kolekce
        force: Vynutit přepsání existující kolekce

    Returns:
        True, pokud byla kolekce vytvořena nebo již existuje, jinak False
    """
    try:
        # Kontrola existence kolekce
        collections = await qdrant_client.get_collections()
        collection_names = [c.name for c in collections.collections]

        if collection_name in collection_names:
            if force:
                logger.info(f"Kolekce {collection_name} již existuje, bude přepsána (--force)")
                await qdrant_client.delete_collection(collection_name=collection_name)
                logger.info(f"Kolekce {collection_name} byla smazána")
            else:
                logger.info(f"Kolekce {collection_name} již existuje, bude použita existující kolekce")
                return True

        # Vytvoření kolekce pro doporučení
        logger.info(f"Vytváření kolekce pro doporučení: {collection_name}")
        await qdrant_client.create_collection(
            collection_name=collection_name,
            vectors_config={
                "embedding": models.VectorParams(
                    size=1536,  # Standardní velikost pro OpenAI embeddings
                    distance=models.Distance.COSINE
                )
            }
        )

        # Vytvoření indexů pro payload
        await qdrant_client.create_payload_index(
            collection_name=collection_name,
            field_name="product_id",
            field_schema="keyword"
        )
        await qdrant_client.create_payload_index(
            collection_name=collection_name,
            field_name="tenant_id",
            field_schema="keyword"
        )
        await qdrant_client.create_payload_index(
            collection_name=collection_name,
            field_name="recommendation_type",
            field_schema="keyword"
        )
        await qdrant_client.create_payload_index(
            collection_name=collection_name,
            field_name="category",
            field_schema="keyword"
        )

        logger.info(f"Kolekce {collection_name} byla úspěšně vytvořena")
        return True

    except Exception as e:
        logger.error(f"Chyba při vytváření kolekce {collection_name}: {e}")
        return False

async def get_products_by_category(qdrant_client, collection_name: str, batch_size: int = 100) -> Dict[str, List[Dict[str, Any]]]:
    """
    Získá produkty z Qdrantu a rozdělí je podle kategorií.

    Args:
        qdrant_client: Klient pro Qdrant
        collection_name: Název kolekce
        batch_size: Velikost dávky pro načítání

    Returns:
        Slovník mapující kategorie na seznamy produktů
    """
    logger.info(f"Získávání produktů z kolekce: {collection_name}")

    products_by_category = {}
    all_products = []
    offset = None

    while True:
        scroll_result = await qdrant_client.scroll(
            collection_name=collection_name,
            limit=batch_size,
            offset=offset,
            with_payload=True,
            with_vectors=False
        )

        batch_products = scroll_result[0]
        if not batch_products:
            break

        all_products.extend(batch_products)
        offset = scroll_result[1]

        if len(all_products) % 100 == 0:
            logger.info(f"Získáno {len(all_products)} produktů...")

    logger.info(f"Celkem získáno {len(all_products)} produktů")

    # Rozdělení produktů podle kategorií
    for product in all_products:
        category = product.payload.get("category", "Uncategorized")
        if category not in products_by_category:
            products_by_category[category] = []
        products_by_category[category].append(product.payload)

    logger.info(f"Produkty rozděleny do {len(products_by_category)} kategorií")

    return products_by_category

async def generate_llm_complementary_recommendations(
    product: Dict[str, Any],
    category_context: str,
    representatives: List[Dict[str, Any]],
    anthropic_client
) -> List[Dict[str, Any]]:
    """
    Generuje komplementární doporučení pomocí LLM (Claude 3.5 Haiku) s využitím chain of thought.

    Args:
        product: Produkt, ke kterému hledáme komplementární produkty
        category_context: Kontext kategorie
        representatives: Reprezentativní produkty v kategorii
        anthropic_client: Klient pro Anthropic API

    Returns:
        Seznam komplementárních doporučení
    """
    # Příprava kontextu pro LLM
    product_info = {k: v for k, v in product.items() if k in ["name", "category", "description", "price", "brand"]}
    representatives_info = [{k: v for k, v in rep.items() if k in ["name", "category", "price", "brand"]} for rep in representatives]

    # Vytvoření promptu s chain of thought přístupem
    prompt = f"""
    # Kontext kategorie
    {category_context}

    # Reprezentativní produkty v kategorii
    {json.dumps(representatives_info, indent=2, ensure_ascii=False)}

    # Produkt pro doporučení
    {json.dumps(product_info, indent=2, ensure_ascii=False)}

    Jsi expertní asistent pro doporučování komplementárních produktů v e-shopu.
    Na základě poskytnutého kontextu navrhni 5 typů komplementárních produktů k výše uvedenému produktu.

    Použij chain of thought přístup - nejprve analyzuj produkt a jeho vlastnosti, pak uvažuj o možných komplementárních produktech, a nakonec vyber 5 nejlepších typů.

    ## Krok 1: Analýza produktu
    Nejprve analyzuj hlavní produkt - jeho účel, funkce, vlastnosti, cenu a cílovou skupinu zákazníků. Jak se produkt používá? Jaké jsou jeho hlavní výhody? Jaké jsou jeho omezení nebo potřeby?

    ## Krok 2: Identifikace potřeb a příležitostí
    Na základě analýzy produktu identifikuj různé potřeby a příležitosti, kde by komplementární produkty mohly přidat hodnotu. Například: instalace, používání, údržba, rozšíření funkcí, ochrana, skladování, atd.

    ## Krok 3: Brainstorming možných komplementárních produktů
    Pro každou identifikovanou potřebu nebo příležitost navrhni několik možných komplementárních produktů. Uvažuj o různých kategoriích, cenových úrovních a značkách.

    ## Krok 4: Hodnocení a prioritizace
    Ohodnoť každý typ komplementárního produktu podle následujících kritérií:
    - Jak moc zvyšuje hodnotu nebo užitečnost hlavního produktu?
    - Jak často je kupován společně s hlavním produktem?
    - Jak dobře odpovídá cenové kategorii hlavního produktu?
    - Jak je relevantní pro cílovou skupinu zákazníků?

    ## Krok 5: Výsledná doporučení
    Na základě hodnocení vyber 5 nejlepších typů komplementárních produktů. Pro každý typ uveď:
    1. Název typu produktu (např. "Příslušenství ke grilu")
    2. Konkrétní příklady produktů tohoto typu (např. "Grilovací kleště", "Čistící kartáč")
    3. Vysvětlení, proč je tento typ produktu komplementární k hlavnímu produktu
    4. Prioritu doporučení na stupnici 1-5 (kde 5 je nejvyšší)

    Zaměř se na produkty, které:
    - Jsou skutečně komplementární (doplňují hlavní produkt, ne nahrazují ho)
    - Zvyšují hodnotu nebo užitečnost hlavního produktu
    - Jsou typicky kupovány společně s hlavním produktem
    - Odpovídají cenové kategorii hlavního produktu (ne příliš levné ani příliš drahé)

    Po dokončení chain of thought analýzy odpověz ve strukturovaném formátu JSON:
    {{
        "complementary_products": [
            {{
                "type": "Název typu produktu",
                "examples": ["Příklad 1", "Příklad 2", "Příklad 3"],
                "explanation": "Vysvětlení, proč je tento typ komplementární",
                "priority": 5
            }},
            ...
        ]
    }}
    """

    # Vypíšeme kontext, který posíláme do LLM
    logger.info("=== Kontext posílaný do LLM ===")
    logger.info(f"Produkt: {product_info['name']}")
    logger.info(f"Kategorie: {product_info['category']}")
    logger.info(f"Cena: {product_info['price']}")
    logger.info(f"Počet reprezentativních produktů: {len(representatives_info)}")
    logger.info(f"Délka kontextu kategorie: {len(category_context)} znaků")
    logger.info(f"Celková délka promptu: {len(prompt)} znaků")

    try:
        logger.info("Odesílání požadavku na Claude 3.5 Haiku...")
        # Použití Claude 3.5 Haiku pro generování doporučení
        response = await anthropic_client.messages.create(
            model="claude-3-5-haiku-20241022",
            max_tokens=8192,  # Zvýšení limitu tokenů pro chain of thought
            messages=[{"role": "user", "content": prompt}]
        )

        # Extrakce JSON z odpovědi
        content = response.content[0].text

        # Vypíšeme celou odpověď pro analýzu chain of thought
        logger.info("=== Odpověď Claude 3.5 Haiku ===")
        # Rozdělíme odpověď na části a vypíšeme ji celou
        content_parts = [content[i:i+1000] for i in range(0, len(content), 1000)]
        for i, part in enumerate(content_parts):
            logger.info(f"=== Část {i+1}/{len(content_parts)} ===")
            logger.info(part)

        # Pokus o nalezení JSON bloku
        json_match = re.search(r'```json\n(.*?)\n```', content, re.DOTALL)
        if json_match:
            json_str = json_match.group(1)
            logger.info("JSON nalezen v code bloku")
        else:
            # Pokus o nalezení JSON bez code bloku
            json_match = re.search(r'\{\s*"complementary_products"\s*:\s*\[', content)
            if json_match:
                # Najít začátek a konec JSON
                start_idx = json_match.start()
                # Hledáme konec JSON objektu
                brace_count = 0
                end_idx = start_idx
                for i in range(start_idx, len(content)):
                    if content[i] == '{':
                        brace_count += 1
                    elif content[i] == '}':
                        brace_count -= 1
                        if brace_count == 0:
                            end_idx = i + 1
                            break
                json_str = content[start_idx:end_idx]
                logger.info("JSON nalezen v textu")
            else:
                # Pokud není JSON blok, použijeme celou odpověď
                json_str = content
                logger.info("JSON nenalezen, použita celá odpověď")

        # Zajistíme správné kódování českých znaků
        # Neodstraňujeme všechny non-ASCII znaky, pouze ty, které by mohly způsobit problémy s JSON
        json_str = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', json_str)

        # Parsování JSON
        try:
            result = json.loads(json_str)
            complementary_products = result.get("complementary_products", [])
            logger.info(f"Nalezeno {len(complementary_products)} komplementárních produktů")
            return complementary_products
        except json.JSONDecodeError as e:
            logger.error(f"Chyba při parsování JSON: {e}")
            logger.error(f"JSON string: {json_str}")
            return []

    except Exception as e:
        logger.error(f"Chyba při generování komplementárních doporučení pomocí LLM: {e}")
        logger.error(f"Odpověď LLM: {content if 'content' in locals() else 'N/A'}")
        return []

async def process_category(
    category: str,
    category_products: List[Dict[str, Any]],
    tenant_id: str,
    qdrant_client,
    openai_client,
    anthropic_client,
    recommendation_collection: str,
    force_update: bool = False,
    max_products: Optional[int] = None
) -> int:
    """
    Zpracuje kategorii produktů a vygeneruje komplementární doporučení.

    Args:
        category: Název kategorie
        category_products: Seznam produktů v kategorii
        tenant_id: ID tenanta
        qdrant_client: Klient pro Qdrant
        openai_client: Klient pro OpenAI
        anthropic_client: Klient pro Anthropic
        recommendation_collection: Název kolekce pro doporučení
        force_update: Vynutit aktualizaci, i když doporučení již existují
        max_products: Maximální počet produktů ke zpracování

    Returns:
        Počet úspěšně zpracovaných produktů
    """
    logger.info(f"Zpracování kategorie: {category} ({len(category_products)} produktů)")

    # Omezení počtu produktů, pokud je zadáno
    if max_products and max_products < len(category_products):
        logger.info(f"Omezení na {max_products} produktů z {len(category_products)}")
        category_products = category_products[:max_products]

    # Inicializace komponent
    embedding_strategy = MultiEmbeddingStrategy(openai_client)

    # Vytvoření clusteru z produktů v kategorii
    cluster_rep = AdaptiveClusterRepresentation(category_products)

    # Získání reprezentativních produktů
    representatives = cluster_rep.get_top_representatives(n=5)

    # Vytvoření kontextu pro kategorii
    context_builder = DynamicContextBuilder(
        openai_client=openai_client,
        anthropic_client=anthropic_client,
        llm_provider="anthropic"
    )

    category_context = await context_builder.build_context_for_cluster(representatives)

    # Zpracování produktů v kategorii
    successful_count = 0

    for i, product in enumerate(category_products):
        product_id = product.get("product_id")

        try:
            # Kontrola, zda již existují doporučení pro tento produkt
            if not force_update:
                existing_recommendations = await qdrant_client.scroll(
                    collection_name=recommendation_collection,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="product_id",
                                match=models.MatchValue(value=product_id)
                            )
                        ]
                    ),
                    limit=1,
                    with_payload=True
                )

                if existing_recommendations[0]:
                    logger.debug(f"Přeskakuji produkt {product_id}, doporučení již existují")
                    continue

            # Generování komplementárních doporučení pomocí LLM
            complementary_recommendations = await generate_llm_complementary_recommendations(
                product=product,
                category_context=category_context,
                representatives=representatives,
                anthropic_client=anthropic_client
            )

            if not complementary_recommendations:
                logger.warning(f"Nepodařilo se vygenerovat doporučení pro produkt {product_id}")
                continue

            # Generování embedingu pro produkt
            product_embedding = await embedding_strategy.create_embeddings({
                "id": product_id,
                "name": product.get("name", ""),
                "category": product.get("category", ""),
                "description": product.get("description", "")
            })

            # Vytvoření ID pro doporučení
            # Použijeme UUID pro bezpečné ID
            import uuid
            recommendation_id = str(uuid.uuid4())

            # Uložení doporučení do Qdrantu
            await qdrant_client.upsert(
                collection_name=recommendation_collection,
                points=[
                    {
                        "id": recommendation_id,
                        "vector": {
                            "embedding": product_embedding["combined"]
                        },
                        "payload": {
                            "product_id": product_id,
                            "tenant_id": tenant_id,
                            "recommendation_type": "complementary",
                            "recommendations": complementary_recommendations,
                            "generated_at": time.time(),
                            "category": product.get("category", "")
                        }
                    }
                ]
            )

            successful_count += 1

            if (i + 1) % 10 == 0 or i == len(category_products) - 1:
                logger.info(f"Zpracováno {i + 1}/{len(category_products)} produktů v kategorii {category}")

        except Exception as e:
            logger.error(f"Chyba při zpracování produktu {product_id}: {e}")
            import traceback
            traceback.print_exc()

    logger.info(f"Dokončeno zpracování kategorie: {category}, úspěšně zpracováno {successful_count}/{len(category_products)} produktů")

    return successful_count

async def generate_and_store_complementary_recommendations(
    tenant_id: str,
    batch_size: int = 50,
    force_update: bool = False,
    max_products_per_category: Optional[int] = None,
    categories: Optional[List[str]] = None,
    product_ids: Optional[List[str]] = None
):
    """
    Generuje a ukládá komplementární doporučení pro všechny produkty.

    Args:
        tenant_id: ID tenanta
        batch_size: Velikost dávky pro zpracování
        force_update: Vynutit aktualizaci, i když doporučení již existují
        max_products_per_category: Maximální počet produktů ke zpracování v každé kategorii
        categories: Seznam kategorií ke zpracování (None = všechny kategorie)
        product_ids: Seznam ID produktů ke zpracování (None = všechny produkty)
    """
    logger.info(f"Generování komplementárních doporučení pro tenanta: {tenant_id}")
    logger.info(f"Parametry: batch_size={batch_size}, force_update={force_update}, max_products_per_category={max_products_per_category}")
    if categories:
        logger.info(f"Zpracování pouze kategorií: {categories}")
    if product_ids:
        logger.info(f"Zpracování pouze produktů: {product_ids}")

    # Inicializace klientů
    qdrant_client = get_qdrant_async_client()
    openai_client = get_openai_client()
    anthropic_client = get_anthropic_client()

    # Názvy kolekcí
    product_collection = f"real_products_{tenant_id}"
    recommendation_collection = f"complementary_recommendations_{tenant_id}"

    try:
        # Vytvoření kolekce pro doporučení
        collection_created = await create_recommendation_collection(
            qdrant_client=qdrant_client,
            collection_name=recommendation_collection,
            force=force_update
        )

        if not collection_created:
            logger.error(f"Nepodařilo se vytvořit kolekci {recommendation_collection}")
            return

        # Pokud jsou zadána konkrétní ID produktů, zpracujeme pouze tyto produkty
        if product_ids:
            logger.info(f"Zpracování konkrétních produktů: {product_ids}")

            # Získání produktů podle ID
            specific_products = []
            for product_id in product_ids:
                product_result = await qdrant_client.scroll(
                    collection_name=product_collection,
                    scroll_filter=models.Filter(
                        must=[
                            models.FieldCondition(
                                key="product_id",
                                match=models.MatchValue(value=product_id)
                            )
                        ]
                    ),
                    limit=1,
                    with_payload=True,
                    with_vectors=True
                )

                if product_result[0]:
                    product = product_result[0][0]
                    specific_products.append(product.payload)
                    logger.info(f"Nalezen produkt: {product.payload.get('name')} (ID: {product.payload.get('product_id')})")
                else:
                    logger.warning(f"Produkt s ID {product_id} nebyl nalezen")

            if not specific_products:
                logger.error(f"Nebyly nalezeny žádné produkty podle zadaných ID")
                return

            # Zpracování produktů podle kategorií
            products_by_category = {}
            for product in specific_products:
                category = product.get("category", "Uncategorized")
                if category not in products_by_category:
                    products_by_category[category] = []
                products_by_category[category].append(product)
        else:
            # Získání produktů podle kategorií
            products_by_category = await get_products_by_category(
                qdrant_client=qdrant_client,
                collection_name=product_collection,
                batch_size=batch_size
            )

            if not products_by_category:
                logger.error(f"Nepodařilo se získat produkty z kolekce {product_collection}")
                return

            # Filtrování kategorií, pokud jsou zadány
            if categories:
                filtered_categories = {}
                for category in categories:
                    if category in products_by_category:
                        filtered_categories[category] = products_by_category[category]
                    else:
                        logger.warning(f"Kategorie {category} nebyla nalezena")
                products_by_category = filtered_categories

        # Zpracování produktů po kategoriích
        total_processed = 0
        total_products = sum(len(products) for products in products_by_category.values())

        logger.info(f"Celkem ke zpracování: {total_products} produktů v {len(products_by_category)} kategoriích")

        for category, category_products in products_by_category.items():
            processed_count = await process_category(
                category=category,
                category_products=category_products,
                tenant_id=tenant_id,
                qdrant_client=qdrant_client,
                openai_client=openai_client,
                anthropic_client=anthropic_client,
                recommendation_collection=recommendation_collection,
                force_update=force_update,
                max_products=max_products_per_category
            )

            total_processed += processed_count

        logger.info(f"Generování komplementárních doporučení dokončeno pro tenanta: {tenant_id}")
        logger.info(f"Celkem zpracováno {total_processed}/{total_products} produktů")

    except Exception as e:
        logger.error(f"Chyba při generování komplementárních doporučení: {e}")
        import traceback
        traceback.print_exc()

    finally:
        # Ukončení klientů
        await close_clients()
        logger.info("Klienti byli ukončeni")

if __name__ == "__main__":
    parser = argparse.ArgumentParser(description='Generování komplementárních doporučení')
    parser.add_argument('--tenant', type=str, required=True, help='ID tenanta')
    parser.add_argument('--batch-size', type=int, default=50, help='Velikost dávky pro zpracování')
    parser.add_argument('--force', action='store_true', help='Vynutit aktualizaci, i když doporučení již existují')
    parser.add_argument('--max-products', type=int, help='Maximální počet produktů ke zpracování v každé kategorii')
    parser.add_argument('--categories', type=str, nargs='+', help='Seznam kategorií ke zpracování')

    args = parser.parse_args()

    asyncio.run(generate_and_store_complementary_recommendations(
        tenant_id=args.tenant,
        batch_size=args.batch_size,
        force_update=args.force,
        max_products_per_category=args.max_products,
        categories=args.categories
    ))
