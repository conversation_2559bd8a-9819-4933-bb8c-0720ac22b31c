# orchestrator.py
import logging
from typing import List, Dict, Optional, Union, Any, Tuple
import asyncio
import time
import random

# <PERSON><PERSON><PERSON><PERSON> z na<PERSON> modulů
from config import Settings, TenantSettings
from clients import AsyncQdrantClient, openai, anthropic
from state_store import StateStore
from data_models import (
    Product,
    ProductInternal,
    CategoryRelationship,
    # FunctionalContext, # Odstraněno
    ComplementaryProductRecord,
    LLMComplementaryRelationship
)
from feed_processor import load_and_transform_feed
import qdrant_ops
import embedding_service
import llm_service
from utils import create_qdrant_id
from qdrant_client.models import Filter, FieldCondition, MatchValue
from qdrant_client.http.models import PointIdsList, ScrollRequest, HasIdCondition

logger = logging.getLogger(__name__)

async def run_initialization_workflow(
    tenant_settings: TenantSettings,
    force_update: bool,
    skip_relationships: bool,
    # skip_contexts: bool, # Odstraněno
    use_llm_complementary: bool,
    sample_size: Optional[int],
    settings: Settings,
    qdrant_client: AsyncQdrantClient,
    openai_client: openai.AsyncOpenAI,
    anthropic_client: Optional[anthropic.AsyncAnthropic],
    state_store: StateStore,
    llm_provider: str = 'anthropic',  # Výběr LLM poskytovatele
    openai_model: str = 'gpt4o',      # Výběr OpenAI modelu (nahrazuje use_cheap_model)
    use_arango: bool = False,         # Zda používat ArangoDB místo in-memory grafu
    use_qdrant_categories: bool = False,
    force_recreate: bool = False,
    max_products: int = None
):
    """Hlavní funkce orchestrace inicializačního workflow."""
    tenant_id = tenant_settings.tenant_id
    logger.info(f"--- Starting Initialization Workflow for Tenant: {tenant_id} ---")
    start_time = time.time()

    product_collection_name = f"real_products_{tenant_id}"
    complementary_collection_name = f"complementary_products_{tenant_id}"

    # --- 1. Kontrola existence dat a rozhodnutí o krocích ---
    logger.info("Step 1: Checking existing data and determining workflow steps...")
    (
        product_collection_exists,
        complementary_collection_exists,
        relationships_exist,
        # contexts_exist # Odstraněno
    ) = await asyncio.gather(
        qdrant_ops.collection_exists(qdrant_client, product_collection_name),
        qdrant_ops.collection_exists(qdrant_client, complementary_collection_name),
        state_store.check_category_relationships_exist(tenant_id),
        # state_store.check_functional_contexts_exist(tenant_id) # Odstraněno
    )

    # Kontrola, zda potřebujeme správné LLM klienty
    need_anthropic = (not skip_relationships or use_llm_complementary) and llm_provider == 'anthropic'
    need_openai_complementary = use_llm_complementary and llm_provider == 'openai'
    
    # Kontrola dostupnosti požadovaných klientů
    if need_anthropic and not anthropic_client:
         logger.error("Anthropic API client is required for selected steps but not provided.")
         logger.warning("Skipping all steps requiring Anthropic LLM.")
         skip_relationships = True
         use_llm_complementary = False if llm_provider == 'anthropic' else use_llm_complementary
    
    if need_openai_complementary and not openai_client:
         logger.error("OpenAI API client is required for complementary generation but not provided.")
         logger.warning("Skipping OpenAI complementary generation.")
         use_llm_complementary = False

    run_load_upload = not product_collection_exists or force_update
    run_embeddings = True
    run_relationships = (anthropic_client is not None) and ((not skip_relationships and not relationships_exist) or (force_update and not skip_relationships))
    # run_contexts = False # Vždy False
    run_llm_complementary = use_llm_complementary and (not complementary_collection_exists or force_update)

    logger.info(f"Workflow steps decision for tenant {tenant_id}:")
    logger.info(f"- Load/Upload Products: {run_load_upload}")
    logger.info(f"- Update Embeddings: {run_embeddings}")
    logger.info(f"- Generate Category Relationships: {run_relationships}")
    # logger.info(f"- Generate/Assign Functional Contexts: {run_contexts}") # Odstraněno
    logger.info(f"- Generate LLM Complementary Products: {run_llm_complementary} (using: {llm_provider})")
    if use_arango:
        logger.info(f"- Using ArangoDB for category relationships")

    # --- 2. Načtení a transformace feedu ---
    logger.info("Step 2: Loading and transforming feed...")
    products_raw: List[Product] = await load_and_transform_feed(tenant_settings)
    if not products_raw:
        logger.error(f"No products loaded from feed for tenant {tenant_id}. Aborting.")
        return False

    if sample_size is not None and len(products_raw) > sample_size:
        logger.info(f"Using sample of {sample_size} products out of {len(products_raw)}.")
        products_raw = products_raw[:sample_size]

    products_internal: List[ProductInternal] = []
    unique_qdrant_ids = set()
    for p_raw in products_raw:
        try:
            q_id = create_qdrant_id(p_raw.id)
            if q_id in unique_qdrant_ids:
                 logger.warning(f"Duplicate Qdrant ID '{q_id}' generated for product '{p_raw.id}'. Skipping.")
                 continue
            unique_qdrant_ids.add(q_id)
            # Vytvoření ProductInternal bez functional_contexts
            p_internal = ProductInternal(**p_raw.model_dump(), qdrant_id=q_id, embedding=None)
            products_internal.append(p_internal)
        except Exception as e:
            logger.error(f"Failed to create internal product model for {p_raw.id}: {e}")

    if not products_internal:
         logger.error(f"No products available after internal model creation. Aborting.")
         return False
    logger.info(f"Prepared {len(products_internal)} unique products for processing.")

    # --- 3. Vytvoření / Smazání Qdrant kolekcí ---
    logger.info("Step 3: Managing Qdrant collections...")
    # ... (Logika pro create/delete product_collection a complementary_collection zůstává stejná) ...
    if run_load_upload:
        if product_collection_exists:
            logger.info(f"Force update enabled, deleting existing collection: {product_collection_name}")
            try:
                await qdrant_ops.delete_collection(qdrant_client, product_collection_name)
                product_collection_exists = False
            except Exception:
                 logger.error(f"Failed to delete existing product collection {product_collection_name}. Aborting.")
                 return False
        if not product_collection_exists:
             try:
                 await qdrant_ops.create_product_collection(qdrant_client, product_collection_name)
                 product_collection_exists = True
             except Exception:
                 logger.error(f"Failed to create product collection {product_collection_name}. Aborting.")
                 return False

    if run_llm_complementary:
         if complementary_collection_exists:
              logger.info(f"Force update enabled, deleting existing collection: {complementary_collection_name}")
              try:
                  await qdrant_ops.delete_collection(qdrant_client, complementary_collection_name)
                  complementary_collection_exists = False
              except Exception:
                   logger.warning(f"Failed to delete existing complementary collection {complementary_collection_name}. Proceeding may lead to duplicates.")
         if not complementary_collection_exists:
              try:
                  await qdrant_ops.create_complementary_collection(qdrant_client, complementary_collection_name)
                  complementary_collection_exists = True
              except Exception:
                  logger.error(f"Failed to create complementary collection {complementary_collection_name}. LLM complementary generation cannot proceed.")
                  run_llm_complementary = False

    # --- 3.5 Kontrola existence embeddingů (pokud není force_update) ---
    if not force_update and run_embeddings:
        logger.info("Checking if embeddings already exist for all products...")
        all_embeddings_exist = True
        product_qdrant_ids = [p.qdrant_id for p in products_internal if p.qdrant_id]
        batch_size_check = 200
        
        if not product_qdrant_ids:
            logger.warning("No valid product Qdrant IDs to check embeddings for.")
            all_embeddings_exist = False # Nemůžeme potvrdit, raději je vygenerujeme
        else:
            try:
                checked_ids = 0
                total_ids = len(product_qdrant_ids)
                while checked_ids < total_ids:
                    batch_ids_to_check = product_qdrant_ids[checked_ids : checked_ids + batch_size_check]
                    if not batch_ids_to_check:
                        break
                        
                    logger.debug(f"Checking embeddings for batch of {len(batch_ids_to_check)} IDs starting from index {checked_ids}...")
                    
                    # Použijeme scroll s filtrem na ID a with_vector=True
                    scroll_filter = Filter(must=[HasIdCondition(has_id=batch_ids_to_check)])
                    points_batch, next_offset = await qdrant_client.scroll(
                        collection_name=product_collection_name,
                        scroll_filter=scroll_filter,
                        limit=len(batch_ids_to_check), # Limit na velikost dávky
                        with_payload=False,
                        with_vectors=True # Chceme vektor pro kontrolu
                    )

                    # Musíme ověřit, že jsme našli všechny ID z dávky
                    found_ids = {point.id for point in points_batch}
                    if len(found_ids) != len(batch_ids_to_check):
                        missing_in_batch = set(batch_ids_to_check) - found_ids
                        logger.info(f"Some products missing in Qdrant (batch check). Count: {len(missing_in_batch)}. Example: {list(missing_in_batch)[:5]}")
                        all_embeddings_exist = False
                        break

                    # Zkontrolujeme vektory nalezených bodů
                    for point in points_batch:
                        has_valid_vector = False
                        if point.vector:
                            vector_data = point.vector.get('default', point.vector) if isinstance(point.vector, dict) else point.vector
                            if isinstance(vector_data, list) and vector_data and vector_data[0] != 0.1:
                                has_valid_vector = True
                                
                        if not has_valid_vector:
                            logger.info(f"Found product (ID: {point.id}) without a valid embedding vector. Embeddings need to be generated/updated.")
                            all_embeddings_exist = False
                            break
                    
                    if not all_embeddings_exist:
                        break # Ukončíme vnější while smyčku
                    
                    checked_ids += len(batch_ids_to_check)
                    # Scroll s ID filtrem nevrací next_offset, takže nepotřebujeme logiku s offsetem

                # Zbytek logiky pro přeskočení a načtení zůstává stejný
                if all_embeddings_exist:
                    logger.info("All products seem to have valid embeddings in Qdrant. Skipping embedding generation/update.")
                    run_embeddings = False
                    logger.info("Loading existing embeddings into memory...")
                    # Efektivnější by bylo načítat dávkově a přímo mapovat
                    all_points_with_vectors = []
                    offset = None
                    while True:
                        points_batch, next_offset = await qdrant_client.scroll(
                            collection_name=product_collection_name,
                            limit=500, # Větší dávka pro načítání
                            offset=offset,
                            with_payload=["original_id"], # Potřebujeme original_id pro mapování
                            with_vectors=True
                        )
                        if not points_batch:
                            break
                        all_points_with_vectors.extend(points_batch)
                        offset = next_offset
                        if offset is None: break
                    
                    # Vytvoříme mapu qdrant_id -> embedding
                    qdrant_id_to_embedding = {}
                    for point in all_points_with_vectors:
                         vector_data = point.vector.get('default', point.vector) if isinstance(point.vector, dict) else point.vector
                         qdrant_id_to_embedding[point.id] = vector_data
                         
                    # Přiřadíme načtené embeddingy k produktům v paměti
                    loaded_count = 0
                    for p in products_internal:
                        if p.qdrant_id in qdrant_id_to_embedding:
                             p.embedding = qdrant_id_to_embedding[p.qdrant_id]
                             loaded_count += 1
                        else:
                             logger.warning(f"Could not find embedding for product qdrant_id {p.qdrant_id} even after scroll. Assigning dummy.")
                             p.embedding = [0.1] * settings.embedding_dimensions
                    logger.info(f"Successfully loaded {loaded_count} existing embeddings into memory.")

            except Exception as e:
                 logger.warning(f"Error checking existing embeddings: {e}. Proceeding with embedding generation/update just in case.", exc_info=True)
                 run_embeddings = True

    # --- 4. Generování a update Embeddings ---
    logger.info("Step 4: Handling product embeddings...")
    # Zde už proměnná run_embeddings rozhodne, zda se kód provede
    if run_embeddings:
        embeddings_map = await embedding_service.generate_and_update_embeddings(
            openai_client,
            products_internal
        )
        for p in products_internal:
             if p.id in embeddings_map:
                  p.embedding = embeddings_map[p.id]
             elif p.embedding is None:
                 logger.warning(f"Product {p.id} still has no embedding after generation step, assigning dummy.")
                 p.embedding = [0.1] * settings.embedding_dimensions

        if run_load_upload:
             logger.info("Uploading products with embeddings...")
             products_to_upload = [p for p in products_internal if p.embedding]
             if not products_to_upload:
                  logger.error("No products have embeddings, cannot upload. Aborting.")
                  return False
             success = await qdrant_ops.upsert_product_points(
                 qdrant_client, product_collection_name, products_to_upload, settings.embedding_batch_size
             )
             if not success:
                  logger.error("Failed to upload products with embeddings. Aborting.")
                  return False
        else:
             logger.info("Updating existing product embeddings in Qdrant...")
             updates = {p.qdrant_id: p.embedding for p in products_internal if p.embedding and p.qdrant_id}
             if updates:
                  await qdrant_ops.update_product_embeddings_batch(
                      qdrant_client, product_collection_name, updates
                  )
             else:
                  logger.info("No embeddings to update in Qdrant.")
    else:
        logger.info("Skipping embedding generation/update as they seem to exist.")


    # --- 5. Generování vztahů kategorií (pouze když je Anthropic) ---
    if run_relationships:
        logger.info("Step 5: Generating category relationships...")

        # Extrahujeme unikátní kategorie (cca 50-300 dle běžných e-shopů)
        all_categories = sorted({p.category for p in products_internal if p.category})
        if not all_categories:
            logger.error("No categories found in products. Cannot generate relationships.")
        else:
            logger.info(f"Found {len(all_categories)} unique categories for relationship generation.")
            try:
                relationships = await llm_service.generate_category_relationships(anthropic_client, all_categories)
                if relationships:
                    logger.info(f"Generated {len(relationships)} relationships between categories.")
                    # Uložíme do state úložiště
                    await state_store.save_category_relationships(tenant_id, relationships)
                    logger.info(f"Saved category relationships to state store.")
                    
                    # Pokud používáme ArangoDB, importujeme do ní vztahy
                    if use_arango:
                        try:
                            # Import do ArangoDB se provádí samostatně
                            from arangodb_integration import ArangoGraphStore
                            arango_store = ArangoGraphStore()
                            # Převedeme na list of dicts pro ArangoDB
                            rel_dicts = [rel.model_dump() for rel in relationships]
                            arango_store.import_relationships(rel_dicts, tenant_id)
                            logger.info(f"Imported {len(relationships)} relationships to ArangoDB.")
                        except Exception as e:
                            logger.error(f"Failed to import relationships to ArangoDB: {e}", exc_info=True)
                else:
                    logger.warning("No relationships generated from LLM.")
            except Exception as e:
                logger.error(f"Error during relationship generation: {e}", exc_info=True)
    else:
        logger.info("Skipping category relationships generation.")
    
    # --- 7. Generování LLM komplementárních produktů ---
    if run_llm_complementary:
        logger.info(f"Step 7: Generating LLM complementary product suggestions...")
        # Inicializace arango_store pokud ještě nebyl inicializován
        if use_arango and not locals().get('arango_store'):
            try:
                from arangodb_integration import ArangoGraphStore
                arango_store = ArangoGraphStore()
            except Exception as e:
                logger.error(f"Failed to initialize ArangoGraphStore: {e}", exc_info=True)
                arango_store = None
        else:
            arango_store = locals().get('arango_store') if use_arango else None
        
        result = await llm_service.generate_llm_complementary_relationships(
            qdrant_client=qdrant_client,
            collection_name=f"real_products_{tenant_id}",
            products=products_internal[:max_products],
            settings=settings,
            tenant_id=tenant_id,
            openai_client=openai_client,
            anthropic_client=anthropic_client,
            use_arango=use_arango,
            arango_store=arango_store,
            use_qdrant_categories=use_qdrant_categories,
            llm_provider=llm_provider,
            openai_model=openai_model,
            state_store=state_store
        )
        if result:
            logger.info(f"Successfully generated and stored LLM complementary products.")
        else:
            logger.warning(f"Failed to generate or store LLM complementary products.")
    else:
        logger.info("Skipping LLM complementary product generation.")
    
    # --- 8. Závěr ---
    end_time = time.time()
    total_time = end_time - start_time
    logger.info(f"--- Initialization Workflow for Tenant: {tenant_id} finished in {total_time:.2f} seconds ---")
    
    return True # Vrátíme úspěch (pokud jsme nedošli k fatální chybě dříve)

async def run_update_workflow(
    tenant_settings: TenantSettings,
    sample_size: Optional[int],
    settings: Settings,
    qdrant_client: AsyncQdrantClient,
    openai_client: openai.AsyncOpenAI,
    # Anthropic nepotřebujeme pro similarity-based update
    # anthropic_client: Optional[anthropic.AsyncAnthropic],
    state_store: StateStore # Může se hodit pro budoucí rozšíření
):
    """Hlavní funkce orchestrace synchronizačního workflow."""
    tenant_id = tenant_settings.tenant_id
    logger.info(f"--- Starting Update Workflow for Tenant: {tenant_id} ---")
    start_time = time.time()

    product_collection_name = f"real_products_{tenant_id}"
    complementary_collection_name = f"complementary_products_{tenant_id}"

    # --- 1. Načtení aktuálního feedu ---
    logger.info("Step 1: Loading and transforming feed...")
    products_raw: List[Product] = await load_and_transform_feed(tenant_settings)
    if not products_raw:
        logger.error(f"No products loaded from feed for tenant {tenant_id}. Aborting update.")
        return False
    # Vytvoření mapy a setu ID pro rychlé porovnání
    feed_products_map: Dict[str, Product] = {p.id: p for p in products_raw}
    feed_product_ids = set(feed_products_map.keys())
    logger.info(f"Loaded {len(feed_product_ids)} products from feed.")

    # --- 2. Načtení ID z Qdrantu ---
    logger.info("Step 2: Fetching existing product IDs from Qdrant...")
    qdrant_product_qdrant_ids: set[Union[int, str]] = await qdrant_ops.get_all_product_qdrant_ids(
        qdrant_client, product_collection_name
    )
    # Potřebujeme mapování Qdrant ID zpět na originální ID, pokud se liší
    # Pro jednoduchost předpokládejme, že můžeme získat originální ID z Qdrant ID
    # nebo že máme konzistentní mapování. Zde získáme originální ID z payloadu.
    # (Toto je třeba vylepšit pro efektivitu - načíst jen ID a original_id payload)
    qdrant_product_original_ids = set()
    if qdrant_product_qdrant_ids:
         logger.info("Fetching original IDs for existing Qdrant points...")
         # Efektivnější by bylo načíst jen payload['original_id']
         # Zde pro jednoduchost načteme všechny body (neefektivní pro velké DB!)
         try:
              points = await qdrant_client.retrieve(collection_name=product_collection_name, ids=list(qdrant_product_qdrant_ids), with_payload=["original_id"])
              qdrant_product_original_ids = {p.payload['original_id'] for p in points if p.payload and 'original_id' in p.payload}
              logger.info(f"Found {len(qdrant_product_original_ids)} original IDs in Qdrant.")
         except Exception as e:
              logger.error(f"Failed to retrieve original_ids from Qdrant: {e}. Cannot reliably determine updates/deletes.")
              return False

    # --- 3. Identifikace změn ---
    logger.info("Step 3: Identifying changes...")
    new_original_ids = feed_product_ids - qdrant_product_original_ids
    deleted_original_ids = qdrant_product_original_ids - feed_product_ids
    existing_original_ids = feed_product_ids.intersection(qdrant_product_original_ids)

    logger.info(f"Changes identified: New={len(new_original_ids)}, Deleted={len(deleted_original_ids)}, Existing={len(existing_original_ids)}")

    # --- 4. Zpracování smazaných produktů ---
    if deleted_original_ids:
        logger.info("Step 4: Processing deleted products...")
        # Potřebujeme Qdrant ID pro smazání
        # Znovu, neefektivní - měli bychom si držet mapování original_id -> qdrant_id
        # Zde znovu načteme body (velmi neefektivní!)
        qdrant_ids_to_delete = []
        try:
             points_to_delete = await qdrant_client.scroll(
                 collection_name=product_collection_name,
                 scroll_filter=Filter(must=[FieldCondition(key="original_id", match=MatchValue(value=list(deleted_original_ids)))])
             )
             if points_to_delete and points_to_delete[0]:
                  qdrant_ids_to_delete = [p.id for p in points_to_delete[0]]
        except Exception as e:
             logger.error(f"Failed to get Qdrant IDs for deletion: {e}")
        
        if qdrant_ids_to_delete:
            await qdrant_ops.delete_products_by_id(qdrant_client, product_collection_name, qdrant_ids_to_delete)
            # Smazání z complementary kolekce (zatím vynecháno pro KISS)
            # await qdrant_ops.delete_products_by_id(qdrant_client, complementary_collection_name, qdrant_ids_to_delete)
        else:
             logger.warning("Could not map deleted original IDs to Qdrant IDs. Deletion skipped.")
    else:
        logger.info("Step 4: No products to delete.")

    # --- 5. Zpracování existujících produktů ---
    payload_updates: Dict[Union[int, str], Dict[str, Any]] = {}
    if existing_original_ids:
        logger.info("Step 5: Checking existing products for updates (price, availability)...")
        # Znovu, potřeba mapování original_id -> qdrant_id a payload
        # Načteme relevantní data pro existující produkty (velmi neefektivní!)
        existing_qdrant_points: Dict[str, Tuple[Union[int, str], Dict]] = {}
        try:
             points_to_check = await qdrant_client.scroll(
                  collection_name=product_collection_name,
                  scroll_filter=Filter(must=[FieldCondition(key="original_id", match=MatchValue(value=list(existing_original_ids)))]),
                  with_payload=["price", "availability", "original_id"]
             )
             if points_to_check and points_to_check[0]:
                  for p in points_to_check[0]:
                       if p.payload and 'original_id' in p.payload:
                            existing_qdrant_points[p.payload['original_id']] = (p.id, p.payload)
        except Exception as e:
             logger.error(f"Failed to retrieve existing product data for update check: {e}")

        update_count = 0
        for original_id in existing_original_ids:
             if original_id not in existing_qdrant_points: continue
             qdrant_id, current_payload = existing_qdrant_points[original_id]
             feed_product = feed_products_map[original_id]
             
             update_payload = {}
             if feed_product.price != current_payload.get("price"):
                  update_payload["price"] = feed_product.price
             if feed_product.availability != current_payload.get("availability"):
                  update_payload["availability"] = feed_product.availability
             
             if update_payload:
                  payload_updates[qdrant_id] = update_payload
                  update_count += 1
        
        if payload_updates:
            logger.info(f"Found {update_count} products with updated price/availability. Applying updates...")
            await qdrant_ops.update_product_payloads_batch(qdrant_client, product_collection_name, payload_updates)
        else:
             logger.info("No updates found for existing products.")
    else:
         logger.info("Step 5: No existing products to check for updates.")

    # --- 6. Zpracování nových produktů ---
    new_complementary_records: List[ComplementaryProductRecord] = []
    if new_original_ids:
        logger.info("Step 6: Processing new products...")
        new_products_internal: List[ProductInternal] = []
        unique_qdrant_ids_new = set()
        for original_id in new_original_ids:
             p_raw = feed_products_map[original_id]
             try:
                  q_id = create_qdrant_id(p_raw.id)
                  if q_id in unique_qdrant_ids_new or q_id in qdrant_product_qdrant_ids:
                       logger.warning(f"Generated Qdrant ID '{q_id}' for new product '{p_raw.id}' already exists. Skipping.")
                       continue
                  unique_qdrant_ids_new.add(q_id)
                  p_internal = ProductInternal(**p_raw.model_dump(), qdrant_id=q_id, embedding=None)
                  new_products_internal.append(p_internal)
             except Exception as e:
                  logger.error(f"Failed to create internal model for new product {p_raw.id}: {e}")
        
        if new_products_internal:
            # a) Generovat embeddingy a nahrát do Qdrantu
            logger.info(f"Generating embeddings for {len(new_products_internal)} new products...")
            embeddings_map_new = await embedding_service.generate_and_update_embeddings(
                openai_client, new_products_internal
            )
            for p in new_products_internal:
                 if p.id in embeddings_map_new:
                      p.embedding = embeddings_map_new[p.id]
                 else:
                      logger.warning(f"New product {p.id} missing embedding, assigning dummy.")
                      p.embedding = [0.1] * settings.embedding_dimensions
            
            logger.info(f"Uploading {len(new_products_internal)} new products to Qdrant...")
            await qdrant_ops.upsert_product_points(
                qdrant_client, product_collection_name, new_products_internal, settings.embedding_batch_size
            )

            # b) Najít podobné a zkopírovat komplementy
            logger.info("Assigning complementary products based on similarity...")
            new_product_map = {p.id: p for p in new_products_internal}
            copied_complement_count = 0
            for new_p in new_products_internal:
                 if not new_p.embedding or new_p.embedding[0] == 0.1: # Přeskočíme, pokud nemá validní embedding
                      continue
                 
                 similar_qdrant_id = await qdrant_ops.find_similar_product(
                     qdrant_client, product_collection_name, new_p.embedding, top_k=1
                 )
                 
                 if similar_qdrant_id and similar_qdrant_id != new_p.qdrant_id:
                      # Našli jsme podobný produkt, zkusíme načíst jeho komplementy
                      neighbor_complement_record = await qdrant_ops.get_complementary_record_by_id(
                          qdrant_client, complementary_collection_name, similar_qdrant_id
                      )
                      
                      if neighbor_complement_record and neighbor_complement_record.complementary_product_ids:
                           # Vytvoříme nový záznam pro nový produkt s komplementy souseda
                           new_record = ComplementaryProductRecord(
                                main_product_id=new_p.id,
                                main_product_name=new_p.name,
                                main_product_url=new_p.product_url,
                                main_product_category=new_p.category,
                                complementary_product_ids=neighbor_complement_record.complementary_product_ids,
                                complementary_product_reasons=neighbor_complement_record.complementary_product_reasons,
                                tenant_id=tenant_id,
                                relationship_type="similarity_copied" # Označíme původ
                           )
                           new_complementary_records.append(new_record)
                           copied_complement_count += 1
                      # else: # Log pokud soused nemá komplementy
                      #      logger.debug(f"Similar product {similar_qdrant_id} found for {new_p.id}, but it has no complementary products.")
                 # else: # Log pokud nebyl nalezen podobný
                 #     logger.debug(f"No similar product found for new product {new_p.id}.")
                 
            logger.info(f"Prepared {len(new_complementary_records)} complementary records for new products based on similarity.")
            if new_complementary_records:
                 logger.info(f"Saving {len(new_complementary_records)} copied complementary records to Qdrant...")
                 await qdrant_ops.upsert_complementary_records(
                     qdrant_client, complementary_collection_name, new_complementary_records
                 )
    else:
        logger.info("Step 6: No new products to process.")

    # --- Konec ---
    end_time = time.time()
    logger.info(f"--- Tenant {tenant_id} update workflow finished in {end_time - start_time:.2f} seconds ---")
    return True