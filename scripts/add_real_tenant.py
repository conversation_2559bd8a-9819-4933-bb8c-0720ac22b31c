#!/usr/bin/env python3
"""
Skript pro přidání reálného tenanta s použitím veřejného XML feedu.
"""

import os
import sys
import subprocess
import tempfile
import yaml
import requests
from pathlib import Path

# Cesta k projektu
PROJECT_ROOT = Path(__file__).resolve().parent.parent

# Ukázkový XML feed
DEFAULT_FEED_URL = "https://www.zbozi.cz/static/beng/export-products.xml"

def add_real_tenant():
    """
    Přidá nového tenanta pomocí onboard_tenant.py
    s reálným XML feedem.
    """
    print("🚀 === PŘIDÁNÍ NOVÉHO TENANTA ===")
    
    # Tenant ID
    tenant_id = input("Zadej ID nového tenanta (např. 'zbozi_cz'): ").strip() or "zbozi_cz"
    
    # Feed URL
    feed_url = input(f"Zadej URL feedu (výchozí: {DEFAULT_FEED_URL}): ").strip() or DEFAULT_FEED_URL
    
    print(f"\n📋 Konfigurace:")
    print(f"  • Tenant ID: {tenant_id}")
    print(f"  • Feed URL: {feed_url}")
    
    if not input("\nPokračovat? (Enter pro pokračování, cokoliv jiného pro zrušení): "):
        # Spuštění onboard_tenant.py
        onboard_script = PROJECT_ROOT / "scripts" / "onboard_tenant.py"
        
        # Vytvoření inputu pro onboard_tenant.py
        inputs = f"{tenant_id}\n{feed_url}\n"
        
        # Spuštění příkazu
        print(f"\n🔄 Spouštím onboarding pro tenanta '{tenant_id}'...")
        try:
            process = subprocess.run(
                [sys.executable, str(onboard_script)],
                input=inputs,
                text=True,
                capture_output=True,
                timeout=300  # 5 minut timeout
            )
            
            # Výpis výstupu
            print("\n📊 === VÝSLEDKY ONBOARDINGU ===")
            print("\nSTDOUT:")
            print(process.stdout)
            
            if process.stderr:
                print("\nSTDERR:")
                print(process.stderr)
            
            # Kontrola úspěšnosti
            if process.returncode == 0:
                print("\n✅ Onboarding ÚSPĚŠNÝ!")
            else:
                print("\n❌ Onboarding SELHAL!")
                
            # Zkusíme alternativní přístup
            if process.returncode != 0:
                print("\n⚠️ Zkusíme použít alternativní přístup s demo skriptem...")
                
                # Spuštění demo testu se stejným feedem
                demo_script = PROJECT_ROOT / "scripts" / "demo_connector_test_custom.py"
                
                # Kopírování a úprava demo skriptu pro vlastní feed
                create_custom_demo_script(demo_script, tenant_id, feed_url)
                
                demo_process = subprocess.run(
                    [sys.executable, str(demo_script)],
                    capture_output=True,
                    text=True,
                    timeout=300
                )
                
                print("\n📊 === VÝSLEDKY ALTERNATIVNÍHO PŘÍSTUPU ===")
                print("\nSTDOUT:")
                print(demo_process.stdout)
                
                if demo_process.stderr:
                    print("\nSTDERR:")
                    print(demo_process.stderr)
                
                if demo_process.returncode == 0:
                    print("\n✅ Alternativní přístup ÚSPĚŠNÝ!")
                else:
                    print("\n❌ Alternativní přístup SELHAL!")
            
        except subprocess.TimeoutExpired:
            print("❌ Proces překročil časový limit (5 minut)")
        except Exception as e:
            print(f"❌ Chyba při spouštění: {e}")
    else:
        print("❌ Operace zrušena uživatelem.")

def create_custom_demo_script(script_path, tenant_id, feed_url):
    """Vytvoří upravený demo skript pro konkrétní tenant a feed."""
    demo_template = PROJECT_ROOT / "scripts" / "demo_connector_test.py"
    
    # Načtení původního skriptu
    with open(demo_template, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Úprava pro vlastní tenant a feed
    modified_content = content.replace(
        'tenant_id = "demo_test"',
        f'tenant_id = "{tenant_id}"'
    ).replace(
        'feed_path = create_test_feed()',
        'feed_path = None'
    ).replace(
        'feed_url = f"file://{feed_path}"',
        f'feed_url = "{feed_url}"'
    ).replace(
        'with open(feed_path, \'rb\') as f:',
        'try:'
    ).replace(
        '    content = f.read()',
        '    response = requests.get(feed_url, timeout=30)\n'
        '    response.raise_for_status()\n'
        '    content = response.content'
    ).replace(
        'feed_info = detect_csv_format(content)',
        'if feed_url.endswith(".xml"):\n'
        '        feed_info = {"format": "xml", "subformat": "generic"}\n'
        '    else:\n'
        '        feed_info = detect_csv_format(content)'
    )
    
    # Uložení upraveného skriptu
    with open(script_path, 'w', encoding='utf-8') as f:
        f.write(modified_content)
        
    print(f"✅ Vytvořen vlastní demo skript: {script_path}")
    return script_path

if __name__ == "__main__":
    add_real_tenant() 