# Načtení produktů z feedu pomocí příslušného konektoru
connector_class = connector_factory.get_connector(tenant_config["feed"]["type"])
connector = connector_class(tenant_config["feed"]["feed_url"])
raw_products_data = connector.load_data()

# # OMEZENÍ PRO TESTOVÁNÍ - Zpracovat pouze prvních N produktů
# N = 20
# raw_products_data = raw_products_data[:N]
# logger.warning(f"!!! TESTOVACÍ OMEZENÍ: Zpracovávám pouze prvních {N} produktů !!!")

# Načtení existujících produktů z DB pro efektivní porovnání
existing_product_ids, existing_products_map = product_service._get_all_products_from_db(tenant) 