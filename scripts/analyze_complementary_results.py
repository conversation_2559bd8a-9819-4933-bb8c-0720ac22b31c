import os
import random
import requests
import logging
from typing import List, Dict, Optional, Any
from dotenv import load_dotenv
from qdrant_client import QdrantClient, models

# --- Configuration ---
load_dotenv(dotenv_path=os.path.join(os.path.dirname(__file__), '..', '.env'))

QDRANT_URL = os.getenv("QDRANT_URL")
QDRANT_API_KEY = os.getenv("QDRANT_API_KEY")
API_BASE_URL = os.getenv("API_BASE_URL", "http://127.0.0.1:8000")
TENANT_ID = "jabkolevne"
JABKOLEVNE_API_KEY = os.getenv("JABKOLEVNE_API_KEY") # Assumes this is the env var name
NUM_PRODUCTS_TO_ANALYZE = 20

PRODUCTS_COLLECTION = f"real_products_{TENANT_ID}"

# --- Logging Setup ---
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# --- Helper Functions ---
def get_all_product_ids(client: QdrantClient, collection_name: str) -> List[str]:
    """Fetches all product IDs from the specified Qdrant collection."""
    all_ids = []
    next_offset = None
    logging.info(f"Fetching all product IDs from '{collection_name}'...")
    try:
        while True:
            points, next_offset = client.scroll(
                collection_name=collection_name,
                limit=1000, # Fetch in batches
                offset=next_offset,
                with_payload=["product_id", "original_id"], # Only fetch necessary fields
                with_vectors=False
            )
            batch_ids = [str(p.payload.get('product_id') or p.payload.get('original_id')) for p in points if p.payload]
            all_ids.extend(batch_ids)
            if next_offset is None:
                break
        logging.info(f"Fetched {len(all_ids)} product IDs.")
        return list(set(all_ids)) # Return unique IDs
    except Exception as e:
        logging.error(f"Failed to fetch all product IDs from '{collection_name}': {e}")
        return []

def get_product_name(client: QdrantClient, collection_name: str, product_id: str) -> Optional[str]:
    """Fetches the name of a single product by its ID using scroll/filter."""
    try:
        # Primary method: Scroll/filter by payload field (more robust for string IDs)
        # Try product_id first
        scroll_result, _ = client.scroll(
            collection_name=collection_name,
            scroll_filter=models.Filter(
                must=[models.FieldCondition(key="product_id", match=models.MatchValue(value=product_id))]
            ),
            limit=1,
            with_payload=["name"]
        )
        if scroll_result and scroll_result[0].payload:
             return scroll_result[0].payload.get("name")

        # Fallback: Try original_id
        scroll_result, _ = client.scroll(
            collection_name=collection_name,
            scroll_filter=models.Filter(
                must=[models.FieldCondition(key="original_id", match=models.MatchValue(value=product_id))]
            ),
            limit=1,
            with_payload=["name"]
        )
        if scroll_result and scroll_result[0].payload:
             return scroll_result[0].payload.get("name")

        logging.warning(f"Product name not found for ID {product_id} in {collection_name} using payload fields.")
        return None
    except Exception as e:
        logging.error(f"Error fetching name for product ID {product_id}: {e}")
        return None

def get_complementary_from_api(product_id: str) -> Optional[List[Dict[str, Any]]]:
    """Calls the API to get complementary products."""
    if not JABKOLEVNE_API_KEY:
        logging.error("Jabkolevne API Key (JABKOLEVNE_API_KEY) not found in environment.")
        return None

    api_url = f"{API_BASE_URL}/complementary-products/{TENANT_ID}/by-product-id/{product_id}"
    headers = {
        "accept": "application/json",
        "X-API-Key": JABKOLEVNE_API_KEY
    }
    try:
        response = requests.get(api_url, headers=headers, timeout=15)
        response.raise_for_status() # Raise HTTPError for bad responses (4xx or 5xx)
        
        # Check for 'Not Found' detail specifically
        if response.status_code == 404 and response.json().get("detail") == "Not Found":
             logging.warning(f"API returned 404 Not Found for product ID {product_id}")
             return [] # Return empty list to indicate not found in cache
             
        # Check for other potential 404s (e.g., incorrect endpoint path)
        if response.status_code == 404:
             logging.error(f"API returned 404 for URL {api_url}. Check endpoint path.")
             return None
             
        return response.json()
    except requests.exceptions.HTTPError as http_err:
        logging.error(f"HTTP error occurred calling API for {product_id}: {http_err} - Response: {response.text}")
        return None
    except requests.exceptions.RequestException as req_err:
        logging.error(f"Request error occurred calling API for {product_id}: {req_err}")
        return None
    except Exception as e:
        logging.error(f"An unexpected error occurred calling API for {product_id}: {e}")
        return None

# --- Main Execution ---
if __name__ == "__main__":
    if not QDRANT_URL or not JABKOLEVNE_API_KEY:
        logging.error("QDRANT_URL or JABKOLEVNE_API_KEY environment variables not set.")
        exit(1)

    try:
        qdrant_client = QdrantClient(url=QDRANT_URL, api_key=QDRANT_API_KEY, timeout=20)
        qdrant_client.get_collections() # Test connection
        logging.info(f"Successfully connected to Qdrant at {QDRANT_URL}")
    except Exception as e:
        logging.error(f"Failed to connect to Qdrant: {e}")
        exit(1)

    all_ids = get_all_product_ids(qdrant_client, PRODUCTS_COLLECTION)
    if not all_ids:
        logging.error("Could not retrieve any product IDs. Exiting.")
        exit(1)

    if len(all_ids) < NUM_PRODUCTS_TO_ANALYZE:
        logging.warning(f"Found only {len(all_ids)} products, analyzing all of them.")
        selected_ids = all_ids
    else:
        selected_ids = random.sample(all_ids, NUM_PRODUCTS_TO_ANALYZE)

    logging.info(f"Analyzing complementary products for {len(selected_ids)} randomly selected products...")
    print("\n" + "="*80)
    print(f"Analysis Results for Tenant: {TENANT_ID}")
    print("="*80 + "\n")

    analysis_results = []

    for i, product_id in enumerate(selected_ids):
        print(f"Processing product {i+1}/{len(selected_ids)}: ID = {product_id}")
        main_product_name = get_product_name(qdrant_client, PRODUCTS_COLLECTION, product_id)
        if not main_product_name:
            main_product_name = f"(Name not found for ID: {product_id})"
        
        print(f"  -> Main Product: {main_product_name}")
        
        complementary_products = get_complementary_from_api(product_id)
        
        current_result = {
             "main_product_id": product_id,
             "main_product_name": main_product_name,
             "complementary": []
        }

        if complementary_products is None:
            print("      ERROR fetching complementary products from API.")
            current_result["error"] = "API Error"
        elif not complementary_products:
            print("      No complementary products found via API (or cache empty/invalid).")
            current_result["status"] = "Not Found/Empty"
        else:
            print(f"      Found {len(complementary_products)} complementary products:")
            comp_details = []
            for comp_prod in complementary_products:
                comp_id = comp_prod.get('product_id', 'N/A')
                comp_name = comp_prod.get('name', 'N/A')
                print(f"        - ID: {comp_id}, Name: {comp_name}")
                comp_details.append({"id": comp_id, "name": comp_name})
            current_result["complementary"] = comp_details
            
        analysis_results.append(current_result)
        print("-"*50)

    print("\n" + "="*80)
    print("Analysis Complete.")
    print("="*80)

    # Optionally, save results to a file (e.g., JSON)
    # import json
    # with open("complementary_analysis_results.json", "w", encoding="utf-8") as f:
    #     json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    # logging.info("Analysis results saved to complementary_analysis_results.json")
