#!/usr/bin/env python3
"""
Demo skript pro onboarding tenanta s XML feedem.
"""

import os
import sys
import subprocess
import tempfile
import yaml
import requests
import xml.etree.ElementTree as ET
import pandas as pd
from pathlib import Path
from typing import Dict, List, Any, Optional

# Základní konfigurace
PROJECT_ROOT = Path(__file__).resolve().parent.parent
CONNECTORS_DIR = PROJECT_ROOT / "connectors"
CONFIG_DIR = PROJECT_ROOT / "config" / "tenants"
LOG_DIR = PROJECT_ROOT / "logs"

# Zajistíme existenci adresářů
CONFIG_DIR.mkdir(parents=True, exist_ok=True)
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Ukázkový XML feed
XML_FEED_URL = "https://www.zbozi.cz/static/beng/export-products.xml"

def create_xml_connector(tenant_id: str, feed_url: str) -> str:
    """Vytvoří XML konektor pro daného tenanta."""
    connector_name = f"{tenant_id}_connector"
    connector_file = CONNECTORS_DIR / f"{connector_name}.py"
    
    # Kód konektoru
    connector_code = f"""from typing import List, Dict, Any
import requests
import xml.etree.ElementTree as ET
from .base_connector import BaseConnector


class {tenant_id.capitalize()}Connector(BaseConnector):
    \"\"\"Automaticky generovaný XML konektor pro {tenant_id}\"\"\"
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")
        
        # Namespace pro XML feed
        self.ns = {{'g': 'http://base.google.com/ns/1.0'}}

    def get_feed(self) -> List[Dict[str, Any]]:
        \"\"\"Získá a zpracuje XML feed\"\"\"
        self.logger.info(f"Načítám XML feed z {{self.feed_url}}")
        try:
            response = requests.get(self.feed_url, timeout=60)
            response.raise_for_status()
            
            # Parsování XML
            self.logger.info("Parsování XML...")
            root = ET.fromstring(response.content)
            
            # Hledáme produktové položky s různými možnými namespace
            items = []
            
            # Zkusíme najít různé možné formáty produktových položek
            for path in ['.//item', './/entry', './/*[local-name()="item"]', './/*[local-name()="entry"]', './/SHOPITEM']:
                found_items = root.findall(path)
                if found_items:
                    items = found_items
                    self.logger.info(f"Nalezeno {{len(items)}} produktů pomocí xpath: {{path}}")
                    break
            
            if not items:
                self.logger.warning("Nenalezeny žádné produktové položky v XML feedu!")
                return []
            
            products = []
            
            for item in items:
                try:
                    def safe_get_text(element_name, default=""):
                        # Zkusíme najít element přímo
                        elem = item.find(element_name)
                        
                        # Pokud nenajdeme, zkusíme s namespace
                        if elem is None and ':' in element_name:
                            if element_name.startswith('g:'):
                                ns_tag = element_name.replace('g:', '{{http://base.google.com/ns/1.0}}')
                                elem = item.find(ns_tag)
                        
                        # Pokud stále nenajdeme, zkusíme bez namespace
                        if elem is None and ':' in element_name:
                            elem = item.find(element_name.split(':')[1])
                        
                        # Ještě zkusíme case-insensitive
                        if elem is None:
                            for child in item:
                                if child.tag.lower() == element_name.lower() or (
                                    ':' in element_name and child.tag.lower() == element_name.split(':')[1].lower()
                                ):
                                    elem = child
                                    break
                        
                        return elem.text.strip() if elem is not None and elem.text else default
                    
                    # Zkusíme běžné názvy polí pro různé typy XML feedů
                    product = {{
                        'ID': (
                            safe_get_text('g:id') or safe_get_text('id') or safe_get_text('ID') or
                            safe_get_text('ITEM_ID') or safe_get_text('CODE')
                        ),
                        'Name': (
                            safe_get_text('title') or safe_get_text('g:title') or safe_get_text('name') or
                            safe_get_text('PRODUCTNAME') or safe_get_text('NAME')
                        ),
                        'Description': (
                            safe_get_text('description') or safe_get_text('g:description') or 
                            safe_get_text('DESCRIPTION') or safe_get_text('desc')
                        ),
                        'Category': (
                            safe_get_text('g:product_type') or safe_get_text('category') or 
                            safe_get_text('CATEGORY') or safe_get_text('CATEGORYTEXT')
                        ),
                        'Price': (
                            safe_get_text('g:price') or safe_get_text('price') or 
                            safe_get_text('PRICE') or safe_get_text('PRICE_VAT')
                        ),
                        'Brand': (
                            safe_get_text('g:brand') or safe_get_text('brand') or 
                            safe_get_text('MANUFACTURER') or safe_get_text('BRAND')
                        ),
                        'Availability': (
                            safe_get_text('g:availability') or safe_get_text('availability') or 
                            safe_get_text('AVAILABILITY') or safe_get_text('DELIVERY_DATE')
                        ),
                        'ImageUrl': (
                            safe_get_text('g:image_link') or safe_get_text('image') or 
                            safe_get_text('IMGURL') or safe_get_text('IMAGE')
                        ),
                        'ProductUrl': (
                            safe_get_text('link') or safe_get_text('g:link') or 
                            safe_get_text('URL') or safe_get_text('url')
                        )
                    }}
                    
                    # Kontrola povinných polí
                    if product['ID'] and product['Name']:
                        products.append(product)
                    else:
                        self.logger.warning(f"Přeskakuji produkt bez ID nebo názvu")
                        
                except Exception as e:
                    self.logger.warning(f"Chyba při zpracování produktu: {{e}}")
                    continue
            
            self.logger.info(f"Načteno {{len(products)}} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání feedu: {{e}}")
            raise
"""
    
    # Zápis konektoru
    with open(connector_file, 'w', encoding='utf-8') as f:
        f.write(connector_code)
    
    print(f"✅ Vytvořen XML konektor: {connector_file}")
    return connector_name

def update_connector_factory(tenant_id: str, connector_name: str):
    """Aktualizuje connector_factory.py o nový konektor."""
    factory_file = CONNECTORS_DIR / "connector_factory.py"
    
    try:
        with open(factory_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Přidání importu
        import_line = f"from .{connector_name} import {tenant_id.capitalize()}Connector"
        if import_line not in content:
            # Najdeme místo pro import
            import_section_end = content.find('\n\nclass ConnectorFactory:')
            if import_section_end != -1:
                content = content[:import_section_end] + f"\n{import_line}" + content[import_section_end:]
        
        # Přidání do _connectors slovníku
        connector_entry = f"        '{tenant_id}': {tenant_id.capitalize()}Connector,"
        connectors_start = content.find('_connectors = {')
        if connectors_start != -1:
            connectors_end = content.find('}', connectors_start)
            if connectors_end != -1:
                before_closing = content[:connectors_end].rstrip()
                if not before_closing.endswith(','):
                    before_closing += ','
                content = before_closing + f"\n{connector_entry}\n" + content[connectors_end:]
        
        with open(factory_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Aktualizován connector_factory.py pro konektor {tenant_id}")
        
    except Exception as e:
        print(f"❌ Chyba při aktualizaci connector_factory.py: {e}")

def create_config(tenant_id: str, feed_url: str) -> Path:
    """Vytvoří konfigurační soubor pro tenanta."""
    config_data = {
        'feed': {
            'type': tenant_id,
            'format': 'xml',
            'feed_url': feed_url
        },
        'recommender': {
            'similar_products_count': 5,
            'min_similarity': 0.3
        },
        'api': {
            'enabled': True,
            'key_env_prefix': tenant_id.upper()
        },
        'cache': {
            'postgres': {
                'max_age_days': 7,
                'cleanup_interval_hours': 24
            },
            'redis': {
                'host': 'redis',
                'port': 6379,
                'db': 0,
                'ttl': 3600,
                'listener': {
                    'enabled': True,
                    'batch_size': 100,
                    'retry_delay': 5
                },
                'keys': {
                    'recommendations': f"{tenant_id}:{{product_id}}:recommendations",
                    'embeddings': f"{tenant_id}:{{product_id}}:embeddings",
                    'metadata': f"{tenant_id}:{{product_id}}:metadata"
                }
            }
        }
    }
    
    config_file_path = CONFIG_DIR / f"{tenant_id}.yaml"
    with open(config_file_path, 'w', encoding='utf-8') as f:
        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ Vytvořena konfigurace: {config_file_path}")
    return config_file_path

def test_connector(tenant_id: str, config_path: Path):
    """Testuje vytvořený konektor."""
    try:
        # Import modulu
        sys.path.insert(0, str(PROJECT_ROOT))
        from connectors.connector_factory import ConnectorFactory
        
        # Načtení konfigurace
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Vytvoření konektoru
        print(f"Vytvářím konektor pro {tenant_id}...")
        connector = ConnectorFactory.create_connector(tenant_id, config['feed'])
        
        # Test načtení dat
        print(f"Načítám data z feedu {config['feed']['feed_url']}...")
        products = connector.get_feed()
        
        if products:
            print(f"✅ Načteno {len(products)} produktů")
            print("\nUkázka prvního produktu:")
            for key, value in products[0].items():
                print(f"  {key}: {value}")
            return True
        else:
            print("❌ Žádné produkty nenačteny")
            return False
    
    except Exception as e:
        print(f"❌ Chyba při testování konektoru: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """
    Hlavní funkce pro demo XML onboardingu.
    """
    print("🚀 === DEMO XML ONBOARDING ===\n")
    
    # Tenant ID
    tenant_id = input(f"Zadej ID tenanta (výchozí: xml_tenant): ").strip() or "xml_tenant"
    
    # Feed URL
    feed_url = input(f"Zadej URL XML feedu (výchozí: {XML_FEED_URL}): ").strip() or XML_FEED_URL
    
    print(f"\n📋 Konfigurace:")
    print(f"  • Tenant ID: {tenant_id}")
    print(f"  • Feed URL: {feed_url}")
    
    # Potvrzení
    if input("\nPokračovat? (Enter pro pokračování, cokoliv jiného pro zrušení): "):
        print("❌ Operace zrušena uživatelem.")
        return
    
    try:
        # 1. Vytvoření konektoru
        print("\n🔧 1. Vytváření XML konektoru...")
        connector_name = create_xml_connector(tenant_id, feed_url)
        
        # 2. Aktualizace connector_factory
        print("\n📝 2. Aktualizace connector_factory...")
        update_connector_factory(tenant_id, connector_name)
        
        # 3. Vytvoření konfigurace
        print("\n📋 3. Vytváření konfigurace...")
        config_path = create_config(tenant_id, feed_url)
        
        # 4. Test konektoru
        print("\n🧪 4. Testování konektoru...")
        success = test_connector(tenant_id, config_path)
        
        if success:
            print("\n🎉 === XML ONBOARDING ÚSPĚŠNÝ! ===")
            print("✅ Tento tenant byl úspěšně přidán!")
            print("\n📊 Další kroky:")
            print("1. Spusť synchronizaci produktů:")
            print(f"   python sync_products.py --tenant {tenant_id}")
            print("2. Spusť výpočet komplementárních produktů:")
            print(f"   python batch_scripts/compute_complementary.py --tenant {tenant_id}")
        else:
            print("\n❌ === XML ONBOARDING SELHAL ===")
            print("❌ Test konektoru selhal, zkontrolujte logy a feed URL")
    
    except Exception as e:
        print(f"\n❌ Chyba při onboardingu: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main() 