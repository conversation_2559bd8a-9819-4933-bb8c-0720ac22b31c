#!/usr/bin/env python3
"""
Testovací skript pro ově<PERSON><PERSON><PERSON> onboarding procesu.
Spustí kompletní test s fiktivním tenantem.
"""

import sys
import subprocess
import tempfile
import json
from pathlib import Path

def create_test_feed():
    """Vytvoří testovací CSV feed pro účely testování."""
    test_data = """ID,Name,Description,Category,Price,Brand,Availability,ImageUrl,ProductUrl
1,Test Produkt 1,Popis prvního testovacího produktu,Testovací kategorie,100.50,TestBrand,in stock,https://example.com/img1.jpg,https://example.com/product1
2,Test Produkt 2,Popis druhého testovacího produktu,<PERSON><PERSON> kategorie,250.00,TestBrand,in stock,https://example.com/img2.jpg,https://example.com/product2
3,Test Produkt 3,Popis třetího testovac<PERSON>ho produktu,<PERSON>ova<PERSON><PERSON> kategorie,75.25,<PERSON><PERSON><PERSON>,out of stock,https://example.com/img3.jpg,https://example.com/product3
"""
    
    # Vytvoření dočasného souboru
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(test_data)
        return f.name

def test_onboarding():
    """Spustí test onboarding procesu."""
    print("🧪 === TEST ONBOARDING PROCESU ===")
    
    # Vytvoření testovacího feedu
    test_feed_path = create_test_feed()
    test_feed_url = f"file://{test_feed_path}"
    tenant_id = "test_tenant_001"
    
    print(f"📝 Testovací tenant ID: {tenant_id}")
    print(f"🌐 Testovací feed URL: {test_feed_url}")
    
    # Simulace inputů pro onboarding skript
    inputs = f"{tenant_id}\n{test_feed_url}\n"
    
    try:
        # Spuštění onboarding skriptu
        onboard_script = Path(__file__).parent / "onboard_tenant.py"
        process = subprocess.run(
            [sys.executable, str(onboard_script)],
            input=inputs,
            text=True,
            capture_output=True,
            timeout=300  # 5 minut timeout
        )
        
        print("\n📊 === VÝSLEDKY TESTU ===")
        print(f"Return code: {process.returncode}")
        print("\nSTDOUT:")
        print(process.stdout)
        
        if process.stderr:
            print("\nSTDERR:")
            print(process.stderr)
        
        if process.returncode == 0:
            print("\n✅ Test onboarding procesu ÚSPĚŠNÝ!")
        else:
            print("\n❌ Test onboarding procesu SELHAL!")
        
        return process.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("❌ Test překročil časový limit (5 minut)")
        return False
    except Exception as e:
        print(f"❌ Chyba při spouštění testu: {e}")
        return False
    finally:
        # Úklid dočasného souboru
        try:
            Path(test_feed_path).unlink()
        except:
            pass

if __name__ == "__main__":
    success = test_onboarding()
    sys.exit(0 if success else 1) 