#!/usr/bin/env python3
import os
import sys
import subprocess
import yaml
import re
import datetime
import requests
import xml.etree.ElementTree as ET
import pandas as pd
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse

# --- Konfigurace ---
PROJECT_ROOT = Path(__file__).resolve().parent.parent
TENANT_CONFIG_DIR = PROJECT_ROOT / "config" / "tenants"
CONNECTORS_DIR = PROJECT_ROOT / "connectors"
LOG_DIR = PROJECT_ROOT / "logs"

# Zajistíme existenci adresářů
TENANT_CONFIG_DIR.mkdir(parents=True, exist_ok=True)
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Cesty k hlavním skriptům
SYNC_SCRIPT_PATH = PROJECT_ROOT / "sync_products.py"
COMPLEMENTARY_SCRIPT_PATH = PROJECT_ROOT / "batch_scripts" / "compute_complementary.py"

# --- Automatick<PERSON> detekce formátu feedu ---

def detect_feed_format(url: str) -> Dict[str, Any]:
    """
    Automaticky detekuje formát feedu a vytvoří odpovídající konfiguraci.
    
    Returns:
        Dict s informacemi o formátu, typu konektoru a vzorku dat
    """
    print(f"Analyzuji feed na URL: {url}")
    
    try:
        response = requests.get(url, timeout=30, headers={
            'User-Agent': 'Mozilla/5.0 (compatible; FeedAnalyzer/1.0)'
        })
        response.raise_for_status()
        content = response.content
        
        # Detekce podle Content-Type
        content_type = response.headers.get('content-type', '').lower()
        
        # Detekce XML feedu
        if 'xml' in content_type or content.strip().startswith(b'<?xml'):
            return _analyze_xml_feed(content, url)
        
        # Detekce CSV feedu
        elif 'csv' in content_type or url.lower().endswith('.csv'):
            return _analyze_csv_feed(content, url)
        
        # Detekce JSON feedu
        elif 'json' in content_type or content.strip().startswith(b'{'):
            return _analyze_json_feed(content, url)
        
        # Pokus o detekci podle obsahu
        else:
            content_str = content.decode('utf-8', errors='ignore')[:1000]
            if content_str.strip().startswith('<?xml') or '<rss' in content_str or '<feed' in content_str:
                return _analyze_xml_feed(content, url)
            elif content_str.startswith('{') or content_str.startswith('['):
                return _analyze_json_feed(content, url)
            else:
                # Zkusíme CSV
                return _analyze_csv_feed(content, url)
    
    except Exception as e:
        print(f"Chyba při analýze feedu: {e}")
        return {
            'format': 'unknown',
            'error': str(e)
        }

def _analyze_xml_feed(content: bytes, url: str) -> Dict[str, Any]:
    """Analyzuje XML feed a detekuje jeho strukturu."""
    try:
        root = ET.fromstring(content)
        
        # Detekce typu XML feedu
        if root.tag.lower() == 'rss':
            # RSS feed
            items = root.findall('.//item')
            if items:
                sample_item = items[0]
                return {
                    'format': 'xml',
                    'subformat': 'rss',
                    'item_count': len(items),
                    'sample_fields': [elem.tag for elem in sample_item],
                    'connector_type': 'xml_rss'
                }
        
        elif 'feed' in root.tag.lower():
            # Atom feed
            entries = root.findall('.//{http://www.w3.org/2005/Atom}entry')
            if not entries:
                # Zkusíme bez namespace
                entries = root.findall('.//entry')
            
            if entries:
                sample_entry = entries[0]
                return {
                    'format': 'xml',
                    'subformat': 'atom',
                    'item_count': len(entries),
                    'sample_fields': [elem.tag for elem in sample_entry],
                    'connector_type': 'xml_atom'
                }
        
        # Google Shopping feed nebo jiný XML
        items = root.findall('.//item')
        if not items:
            # Zkusíme entry
            items = root.findall('.//entry')
        
        if items:
            sample_item = items[0]
            return {
                'format': 'xml',
                'subformat': 'generic',
                'item_count': len(items),
                'sample_fields': [elem.tag for elem in sample_item],
                'connector_type': 'xml_generic'
            }
        
        return {
            'format': 'xml',
            'subformat': 'unknown',
            'error': 'Nepodařilo se najít produktové položky'
        }
        
    except ET.ParseError as e:
        return {
            'format': 'xml',
            'error': f'Chyba při parsování XML: {e}'
        }

def _analyze_csv_feed(content: bytes, url: str) -> Dict[str, Any]:
    """Analyzuje CSV feed."""
    try:
        # Zkusíme různá kódování
        for encoding in ['utf-8', 'utf-8-sig', 'cp1252', 'latin1']:
            try:
                content_str = content.decode(encoding)
                break
            except UnicodeDecodeError:
                continue
        else:
            raise UnicodeDecodeError("Nepodařilo se dekódovat CSV")
        
        # Zkusíme různé oddělovače
        for separator in [',', ';', '\t']:
            try:
                df = pd.read_csv(pd.io.common.StringIO(content_str), sep=separator, nrows=5)
                if len(df.columns) > 1:
                    return {
                        'format': 'csv',
                        'separator': separator,
                        'encoding': encoding,
                        'columns': df.columns.tolist(),
                        'sample_data': df.head(2).to_dict('records'),
                        'connector_type': 'csv_generic'
                    }
            except:
                continue
        
        return {
            'format': 'csv',
            'error': 'Nepodařilo se parsovat CSV s žádným oddělovačem'
        }
        
    except Exception as e:
        return {
            'format': 'csv',
            'error': f'Chyba při analýze CSV: {e}'
        }

def _analyze_json_feed(content: bytes, url: str) -> Dict[str, Any]:
    """Analyzuje JSON feed."""
    try:
        data = json.loads(content.decode('utf-8'))
        
        if isinstance(data, list):
            return {
                'format': 'json',
                'structure': 'array',
                'item_count': len(data),
                'sample_item': data[0] if data else {},
                'connector_type': 'json_array'
            }
        elif isinstance(data, dict):
            # Hledáme pole s produkty
            for key, value in data.items():
                if isinstance(value, list) and value:
                    return {
                        'format': 'json',
                        'structure': 'object',
                        'products_key': key,
                        'item_count': len(value),
                        'sample_item': value[0],
                        'connector_type': 'json_object'
                    }
            
            return {
                'format': 'json',
                'structure': 'single_object',
                'keys': list(data.keys()),
                'connector_type': 'json_single'
            }
        
    except json.JSONDecodeError as e:
        return {
            'format': 'json',
            'error': f'Chyba při parsování JSON: {e}'
        }

# --- Generování konektoru ---

def generate_connector(tenant_id: str, feed_info: Dict[str, Any], feed_url: str) -> str:
    """
    Generuje nový konektor na základě analýzy feedu.
    
    Returns:
        Název nového konektoru
    """
    connector_name = f"{tenant_id}_connector"
    connector_file = CONNECTORS_DIR / f"{connector_name}.py"
    
    print(f"Generuji konektor: {connector_name}")
    
    if feed_info['format'] == 'xml':
        connector_code = _generate_xml_connector(tenant_id, feed_info, feed_url)
    elif feed_info['format'] == 'csv':
        connector_code = _generate_csv_connector(tenant_id, feed_info, feed_url)
    elif feed_info['format'] == 'json':
        connector_code = _generate_json_connector(tenant_id, feed_info, feed_url)
    else:
        raise ValueError(f"Nepodporovaný formát feedu: {feed_info['format']}")
    
    # Zápis konektoru
    with open(connector_file, 'w', encoding='utf-8') as f:
        f.write(connector_code)
    
    # Aktualizace connector_factory.py
    _update_connector_factory(tenant_id, connector_name)
    
    print(f"Konektor {connector_name} byl vytvořen: {connector_file}")
    return connector_name

def _generate_xml_connector(tenant_id: str, feed_info: Dict[str, Any], feed_url: str) -> str:
    """Generuje XML konektor."""
    return f'''from typing import List, Dict, Any
import requests
import xml.etree.ElementTree as ET
from .base_connector import BaseConnector


class {tenant_id.title()}Connector(BaseConnector):
    """Automaticky generovaný konektor pro {tenant_id}"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")

    def get_feed(self) -> List[Dict[str, Any]]:
        """Získá a zpracuje XML feed"""
        self.logger.info(f"Načítám XML feed z {{self.feed_url}}")
        try:
            response = requests.get(self.feed_url, timeout=30)
            response.raise_for_status()
            
            root = ET.fromstring(response.content)
            products = []
            
            # Hledáme produktové položky s různými možnými namespace
            items = []
            
            # Zkusíme najít různé možné formáty produktových položek
            for path in ['.//item', './/entry', './/*[local-name()="item"]', './/*[local-name()="entry"]']:
                found_items = root.findall(path)
                if found_items:
                    items = found_items
                    self.logger.info(f"Nalezeno {{len(items)}} produktů pomocí xpath: {{path}}")
                    break
            
            self.logger.info(f"Celkem nalezeno {{len(items)}} produktových položek")
            
            # Příprava namespace pro Google feed
            ns = {{'g': 'http://base.google.com/ns/1.0'}}
            
            for item in items:
                try:
                    def safe_get_text(element_name, default=""):
                        elem = item.find(element_name)
                        if elem is None and ':' in element_name:
                            # Zkusíme s namespace
                            if element_name.startswith('g:'):
                                ns_tag = element_name.replace('g:', '{{http://base.google.com/ns/1.0}}')
                                elem = item.find(ns_tag)
                        return elem.text.strip() if elem is not None and elem.text else default
                    
                    # Mapování základních polí
                    product = {{
                        'ID': safe_get_text('g:id') or safe_get_text('id'),
                        'Name': safe_get_text('title') or safe_get_text('g:title'),
                        'Description': safe_get_text('description') or safe_get_text('g:description'),
                        'Category': safe_get_text('g:product_type') or safe_get_text('category'),
                        'Price': safe_get_text('g:price') or safe_get_text('price'),
                        'Brand': safe_get_text('g:brand') or safe_get_text('brand'),
                        'Availability': safe_get_text('g:availability') or safe_get_text('availability'),
                        'ImageUrl': safe_get_text('g:image_link') or safe_get_text('image_url'),
                        'ProductUrl': safe_get_text('link') or safe_get_text('g:link')
                    }}
                    
                    # Kontrola povinných polí
                    if product['ID'] and product['Name']:
                        products.append(product)
                    else:
                        self.logger.warning(f"Přeskakuji produkt bez ID nebo názvu")
                        
                except Exception as e:
                    self.logger.warning(f"Chyba při zpracování produktu: {{e}}")
                    continue
            
            self.logger.info(f"Načteno {{len(products)}} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání feedu: {{e}}")
            raise
'''

def _generate_csv_connector(tenant_id: str, feed_info: Dict[str, Any], feed_url: str) -> str:
    """Generuje CSV konektor."""
    separator = feed_info.get('separator', ',')
    encoding = feed_info.get('encoding', 'utf-8')
    
    return f'''from typing import List, Dict, Any
import requests
import pandas as pd
from io import StringIO
from .base_connector import BaseConnector


class {tenant_id.title()}Connector(BaseConnector):
    """Automaticky generovaný konektor pro {tenant_id}"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")

    def get_feed(self) -> List[Dict[str, Any]]:
        """Získá a zpracuje CSV feed"""
        self.logger.info(f"Načítám CSV feed z {{self.feed_url}}")
        try:
            response = requests.get(self.feed_url, timeout=30)
            response.raise_for_status()
            
            # Dekódování obsahu
            content = response.content.decode('{encoding}')
            
            # Načtení CSV
            df = pd.read_csv(StringIO(content), sep='{separator}')
            
            # Standardizace názvů sloupců
            column_mapping = {{}}
            for col in df.columns:
                col_lower = col.lower()
                if 'id' in col_lower:
                    column_mapping[col] = 'ID'
                elif 'name' in col_lower or 'title' in col_lower:
                    column_mapping[col] = 'Name'
                elif 'desc' in col_lower:
                    column_mapping[col] = 'Description'
                elif 'categ' in col_lower:
                    column_mapping[col] = 'Category'
                elif 'price' in col_lower or 'cena' in col_lower:
                    column_mapping[col] = 'Price'
                elif 'brand' in col_lower or 'znacka' in col_lower:
                    column_mapping[col] = 'Brand'
                elif 'avail' in col_lower or 'dostup' in col_lower:
                    column_mapping[col] = 'Availability'
                elif 'image' in col_lower or 'foto' in col_lower:
                    column_mapping[col] = 'ImageUrl'
                elif 'url' in col_lower or 'link' in col_lower:
                    column_mapping[col] = 'ProductUrl'
            
            # Přejmenování sloupců
            df = df.rename(columns=column_mapping)
            
            # Konverze na seznam slovníků
            products = df.to_dict('records')
            
            # Vyčištění dat
            for product in products:
                for key, value in product.items():
                    if pd.isna(value):
                        product[key] = ""
                    else:
                        product[key] = str(value)
            
            self.logger.info(f"Načteno {{len(products)}} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání feedu: {{e}}")
            raise
'''

def _generate_json_connector(tenant_id: str, feed_info: Dict[str, Any], feed_url: str) -> str:
    """Generuje JSON konektor."""
    products_key = feed_info.get('products_key', '')
    
    return f'''from typing import List, Dict, Any
import requests
import json
from .base_connector import BaseConnector


class {tenant_id.title()}Connector(BaseConnector):
    """Automaticky generovaný konektor pro {tenant_id}"""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")

    def get_feed(self) -> List[Dict[str, Any]]:
        """Získá a zpracuje JSON feed"""
        self.logger.info(f"Načítám JSON feed z {{self.feed_url}}")
        try:
            response = requests.get(self.feed_url, timeout=30)
            response.raise_for_status()
            
            data = response.json()
            
            # Extrakce produktů
            if isinstance(data, list):
                raw_products = data
            elif isinstance(data, dict) and '{products_key}':
                raw_products = data.get('{products_key}', [])
            else:
                raw_products = [data]  # Jeden produkt
            
            products = []
            for raw_product in raw_products:
                try:
                    # Mapování polí
                    product = {{
                        'ID': str(raw_product.get('id', raw_product.get('ID', ''))),
                        'Name': raw_product.get('name', raw_product.get('title', '')),
                        'Description': raw_product.get('description', ''),
                        'Category': raw_product.get('category', ''),
                        'Price': raw_product.get('price', ''),
                        'Brand': raw_product.get('brand', ''),
                        'Availability': raw_product.get('availability', 'in stock'),
                        'ImageUrl': raw_product.get('image_url', raw_product.get('image', '')),
                        'ProductUrl': raw_product.get('url', raw_product.get('link', ''))
                    }}
                    
                    if product['ID'] and product['Name']:
                        products.append(product)
                    
                except Exception as e:
                    self.logger.warning(f"Chyba při zpracování produktu: {{e}}")
                    continue
            
            self.logger.info(f"Načteno {{len(products)}} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání feedu: {{e}}")
            raise
'''

def _update_connector_factory(tenant_id: str, connector_name: str):
    """Aktualizuje connector_factory.py o nový konektor."""
    factory_file = CONNECTORS_DIR / "connector_factory.py"
    
    try:
        with open(factory_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Přidání importu
        import_line = f"from .{connector_name} import {tenant_id.title()}Connector"
        if import_line not in content:
            # Najdeme místo pro import
            import_section_end = content.find('\n\nclass ConnectorFactory:')
            if import_section_end != -1:
                content = content[:import_section_end] + f"\n{import_line}" + content[import_section_end:]
        
        # Přidání do _connectors slovníku
        connector_entry = f"        '{tenant_id}': {tenant_id.title()}Connector,"
        connectors_start = content.find('_connectors = {')
        if connectors_start != -1:
            connectors_end = content.find('}', connectors_start)
            if connectors_end != -1:
                before_closing = content[:connectors_end].rstrip()
                if not before_closing.endswith(','):
                    before_closing += ','
                content = before_closing + f"\n{connector_entry}\n" + content[connectors_end:]
        
        with open(factory_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"Aktualizován connector_factory.py pro konektor {tenant_id}")
        
    except Exception as e:
        print(f"Chyba při aktualizaci connector_factory.py: {e}")

# --- Testování kompatibility ---

def test_connector_compatibility(tenant_id: str, connector_name: str) -> bool:
    """
    Testuje kompatibilitu nového konektoru se sync_products.py.
    """
    print(f"Testuji kompatibilitu konektoru {connector_name}...")
    
    try:
        # Import konektoru
        sys.path.insert(0, str(CONNECTORS_DIR))
        from connectors.connector_factory import ConnectorFactory
        
        # Test načtení konfigurace tenanta
        config_file = TENANT_CONFIG_DIR / f"{tenant_id}.yaml"
        with open(config_file, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Test vytvoření konektoru
        connector = ConnectorFactory.create_connector(tenant_id, config['feed'])
        
        # Test načtení dat (pouze první 5 produktů)
        print("Testuji načtení dat z konektoru...")
        products = connector.get_feed()[:5]
        
        if not products:
            print("❌ Konektor nevrací žádné produkty")
            return False
        
        # Test struktury dat
        required_fields = ['ID', 'Name']
        for i, product in enumerate(products):
            for field in required_fields:
                if field not in product or not product[field]:
                    print(f"❌ Produkt {i+1} nemá povinné pole '{field}'")
                    return False
        
        print(f"✅ Konektor úspěšně načetl {len(products)} produktů")
        print("Ukázkový produkt:")
        for key, value in products[0].items():
            print(f"  {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ Chyba při testování konektoru: {e}")
        return False

# --- Vytvoření konfigurace ---

def create_tenant_config(tenant_id: str, feed_url: str, connector_name: str) -> Path:
    """Vytvoří konfigurační soubor pro tenanta."""
    config_data = {
        'feed': {
            'type': tenant_id,
            'format': 'auto-detected',
            'feed_url': feed_url
        },
        'force_recalculation': False,
        'recommender': {
            'similar_products_count': 5,
            'min_similarity': 0.3
        },
        'api': {
            'enabled': True,
            'key_env_prefix': tenant_id.upper()
        },
        'cache': {
            'postgres': {
                'max_age_days': 7,
                'cleanup_interval_hours': 24
            },
            'redis': {
                'host': 'redis',
                'port': 6379,
                'db': 0,
                'ttl': 3600,
                'listener': {
                    'enabled': True,
                    'batch_size': 100,
                    'retry_delay': 5
                },
                'keys': {
                    'recommendations': f"{tenant_id}:{{product_id}}:recommendations",
                    'embeddings': f"{tenant_id}:{{product_id}}:embeddings",
                    'metadata': f"{tenant_id}:{{product_id}}:metadata"
                }
            }
        },
        'recommendations': {
            'visual_top_k': 5,
            'cross_sell_top_k': 4,
            'validate_recommendations': True,
            'count': 5,
            'candidates': 10,
            'force_real_time': False
        },
        'processing': {
            'batch_size': 10,
            'max_workers': 3,
            'retry_count': 3,
            'retry_multiplier': 2,
            'retry_initial_delay': 1
        },
        'custom': {
            'brand': tenant_id.upper(),
            'language': 'cs'
        }
    }
    
    config_file_path = TENANT_CONFIG_DIR / f"{tenant_id}.yaml"
    with open(config_file_path, 'w', encoding='utf-8') as f:
        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ Vytvořena konfigurace: {config_file_path}")
    return config_file_path

# --- Spuštění skriptů ---

def run_command(command: list, log_file_path: Path) -> bool:
    """Spustí externí příkaz a loguje výstup."""
    print(f"\n🚀 Spouštím: {' '.join(command)}")
    print(f"📝 Log: {log_file_path}")
    
    try:
        with open(log_file_path, 'w', encoding='utf-8') as log_file:
            process = subprocess.run(
                command,
                stdout=log_file,
                stderr=subprocess.STDOUT,
                text=True,
                check=False,
                encoding='utf-8'
            )
        
        if process.returncode == 0:
            print(f"✅ Úspěšně dokončeno (kód: {process.returncode})")
            return True
        else:
            print(f"❌ Selhalo (kód: {process.returncode}). Zkontroluj log: {log_file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při spouštění: {e}")
        return False

def test_recommendations(tenant_id: str) -> bool:
    """Testuje, zda fungují doporučení pro nového tenanta."""
    print(f"🧪 Testuji doporučení pro tenanta {tenant_id}...")
    
    try:
        # Jednoduchý test - načteme jeden produkt a zkusíme najít doporučení
        test_script = f"""
import sys
sys.path.append('.')
from core.clients import get_qdrant_async_client
import asyncio

async def test():
    client = get_qdrant_async_client()
    try:
        collection_name = "real_products_{tenant_id}"
        results = await client.scroll(
            collection_name=collection_name,
            limit=1,
            with_payload=True,
            with_vectors=False
        )
        
        if results[0]:
            print(f"✅ Nalezen testovací produkt: {{results[0][0].payload.get('name', 'N/A')}}")
            return True
        else:
            print("❌ Žádné produkty nenalezeny")
        return False
    except Exception as e:
        print(f"❌ Chyba: {{e}}")
        return False
    finally:
        await client.close()

result = asyncio.run(test())
"""
        
        # Spuštění testu
        process = subprocess.run(
            [sys.executable, "-c", test_script],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if process.returncode == 0 and "✅" in process.stdout:
            print("✅ Test doporučení prošel")
            return True
        else:
            print(f"❌ Test doporučení selhal: {process.stdout} {process.stderr}")
            return False
            
    except Exception as e:
        print(f"❌ Chyba při testování doporučení: {e}")
        return False

# --- Hlavní funkce ---

def main():
    print("🚀 === AUTOMATIZOVANÝ ONBOARDING NOVÉHO TENANTA ===")
    print()

    # 1. Získání základních informací
    while True:
        tenant_id = input("📝 Zadej unikátní ID nového tenanta (malá písmena, čísla, podtržítka): ").strip()
        if not tenant_id:
            print("❌ ID tenanta nesmí být prázdné.")
            continue
        if not re.match(r"^[a-z0-9_]+$", tenant_id):
            print("❌ ID tenanta obsahuje nepovolené znaky.")
            continue
            break

    feed_url = input("🌐 Zadej URL feedu: ").strip()
    if not feed_url:
        print("❌ URL feedu je povinné")
        return
    
    print(f"\n🔍 Analyzuji feed pro tenanta '{tenant_id}'...")
    
    # 2. Analýza feedu
    feed_info = detect_feed_format(feed_url)
    
    if 'error' in feed_info:
        print(f"❌ Chyba při analýze feedu: {feed_info['error']}")
        return
    
    print(f"✅ Detekován formát: {feed_info['format']}")
    if 'item_count' in feed_info:
        print(f"📊 Počet položek: {feed_info['item_count']}")
    
    # 3. Generování konektoru
    try:
        connector_name = generate_connector(tenant_id, feed_info, feed_url)
    except Exception as e:
        print(f"❌ Chyba při generování konektoru: {e}")
        return
    
    # 4. Vytvoření konfigurace
    try:
        config_file = create_tenant_config(tenant_id, feed_url, connector_name)
    except Exception as e:
        print(f"❌ Chyba při vytváření konfigurace: {e}")
        return
    
    # 5. Test kompatibility
    if not test_connector_compatibility(tenant_id, connector_name):
        print("❌ Konektor není kompatibilní. Onboarding přerušen.")
        return

    print("✅ Konektor je kompatibilní!")
    
    # 6. Spuštění sync_products.py
    print(f"\n📦 Spouštím synchronizaci produktů...")
    sync_log_file = LOG_DIR / f"sync_products_{tenant_id}_onboarding.log"
    sync_command = [
        sys.executable,
        str(SYNC_SCRIPT_PATH),
        "--tenant", tenant_id,
        "--force"
    ]
    
    if not run_command(sync_command, sync_log_file):
        print("❌ Synchronizace produktů selhala. Zkontroluj log.")
        return
    
    # 7. Spuštění compute_complementary.py
    print(f"\n🔄 Spouštím výpočet komplementárních produktů...")
    comp_log_file = LOG_DIR / f"compute_complementary_{tenant_id}_onboarding.log"
    comp_command = [
        sys.executable,
        str(COMPLEMENTARY_SCRIPT_PATH),
        "--tenant", tenant_id
    ]
    
    if not run_command(comp_command, comp_log_file):
        print("⚠️ Výpočet komplementárních produktů selhal, ale onboarding pokračuje.")
    
    # 8. Test doporučení
    if test_recommendations(tenant_id):
        print("✅ Doporučení fungují správně!")
    else:
        print("⚠️ Test doporučení selhal, ale tenant byl úspěšně přidán.")
    
    # 9. Shrnutí
    print(f"\n🎉 === ONBOARDING TENANTA '{tenant_id}' DOKONČEN ===")
    print(f"✅ Konektor vytvořen: {connector_name}")
    print(f"✅ Konfigurace: {config_file}")
    print(f"✅ Produkty synchronizovány")
    print(f"📝 Logy v: {LOG_DIR}")
    print()
    print("🔧 Pro pravidelnou synchronizaci přidej do crontabu:")
    print(f"0 2 * * * cd {PROJECT_ROOT} && python {SYNC_SCRIPT_PATH} --tenant {tenant_id}")
    print(f"0 3 * * * cd {PROJECT_ROOT} && python {COMPLEMENTARY_SCRIPT_PATH} --tenant {tenant_id}")

if __name__ == "__main__":
    main() 