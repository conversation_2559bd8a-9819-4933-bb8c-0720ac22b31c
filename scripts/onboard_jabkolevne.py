#!/usr/bin/env python3
"""
Neinteraktivnu00ed verze onboard_tenant.py specifikovanu00e1 pro tenant jabkolevne
"""

import os
import sys
import subprocess
import yaml
import requests
import xml.etree.ElementTree as ET
from pathlib import Path

# Cesta k projektu
PROJECT_ROOT = Path(__file__).resolve().parent.parent
CONNECTORS_DIR = PROJECT_ROOT / "connectors"
CONFIG_DIR = PROJECT_ROOT / "config" / "tenants"
LOG_DIR = PROJECT_ROOT / "logs"

# Tenant a feed info
TENANT_ID = "jabkolevne"
FEED_URL = "https://www.jabkolevne.cz/google.xml"

# Zajistime existenci adresaru
CONFIG_DIR.mkdir(parents=True, exist_ok=True)
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Cesty k hlavnu00edm skriptu016fm
SYNC_SCRIPT_PATH = PROJECT_ROOT / "sync_products.py"
COMPLEMENTARY_SCRIPT_PATH = PROJECT_ROOT / "batch_scripts" / "compute_complementary.py"

def run_command(command, log_file_path=None):
    """Spustu00ed externu00ed pu0159u00edkaz a loguje vu00fdstup."""
    try:
        print(f"Spouu0161tu00edm pu0159u00edkaz: {' '.join(command)}")
        process = subprocess.run(
            command,
            cwd=PROJECT_ROOT,
            check=False,
            capture_output=True,
            text=True,
            timeout=300
        )
        
        output = f"Stdout:\n{process.stdout}\nStderr:\n{process.stderr}"
        
        if log_file_path:
            with open(log_file_path, 'w', encoding='utf-8') as f:
                f.write(output)
        
        if process.returncode == 0:
            print(f"Příkaz úspěšně dokončen")
            return True
        else:
            print(f"Příkaz selhal s návratovým kódem {process.returncode}")
            return False
    
    except Exception as e:
        print(f"Chyba při spuštění příkazu: {e}")
        return False

def main():
    print(f"=== ONBOARDING TENANT {TENANT_ID} (NEINTERAKTIVNÍ) ===\n")
    
    # 1. Test, zda konektor jablkolevne existuje a funguje
    print("Testování konektoru...")
    test_command = [
        sys.executable,
        str(PROJECT_ROOT / "test_jabkolevne_connector.py")
    ]
    
    if not run_command(test_command):
        print("Test konektoru selhal, zkontrolujte logy.")
        return
    
    # 2. Spuštění sync_products.py
    print(f"\nSpouštím synchronizaci produktů...")
    sync_log_file = LOG_DIR / f"sync_products_{TENANT_ID}.log"
    sync_command = [
        sys.executable,
        str(SYNC_SCRIPT_PATH),
        "--tenant", TENANT_ID,
        "--force"
    ]
    
    if not run_command(sync_command, sync_log_file):
        print("Synchronizace produktů selhala. Zkontroluj log.")
        return
    
    # 3. Spuštění compute_complementary.py
    print(f"\nSpouštím výpočet komplementárních produktů...")
    comp_log_file = LOG_DIR / f"compute_complementary_{TENANT_ID}.log"
    comp_command = [
        sys.executable,
        str(COMPLEMENTARY_SCRIPT_PATH),
        "--tenant", TENANT_ID
    ]
    
    if not run_command(comp_command, comp_log_file):
        print("Výpočet komplementárních produktů selhal, ale onboarding pokračuje.")
    
    # 4. Shrnutí
    print(f"\n=== ONBOARDING TENANTA '{TENANT_ID}' DOKONČEN ===")
    print(f"✅ Produkty synchronizovány")
    print(f"Logy v: {LOG_DIR}")
    print()
    print("Pro pravidelnou synchronizaci přidej do crontabu:")
    print(f"0 2 * * * cd {PROJECT_ROOT} && python {SYNC_SCRIPT_PATH} --tenant {TENANT_ID}")
    print(f"0 3 * * * cd {PROJECT_ROOT} && python {COMPLEMENTARY_SCRIPT_PATH} --tenant {TENANT_ID}")

if __name__ == "__main__":
    main()
