#!/usr/bin/env python3
"""
Jednoduchý demo skript pro ověření principu automatického onboardingu a konektorů.
"""

import os
import sys
import subprocess
import tempfile
import json
import yaml
from pathlib import Path
import requests
import xml.etree.ElementTree as ET
import pandas as pd
from typing import Dict, List, Any, Optional

# Základní konfigurace
PROJECT_ROOT = Path(__file__).resolve().parent.parent
CONNECTORS_DIR = PROJECT_ROOT / "connectors"
CONFIG_DIR = PROJECT_ROOT / "config" / "tenants"
LOG_DIR = PROJECT_ROOT / "logs"

# Zajistíme existenci adresářů
CONFIG_DIR.mkdir(parents=True, exist_ok=True)
LOG_DIR.mkdir(parents=True, exist_ok=True)

def detect_csv_format(content: bytes) -> Dict[str, Any]:
    """Detekuje formát CSV."""
    try:
        # <PERSON><PERSON><PERSON><PERSON> různá kódování
        for encoding in ['utf-8', 'utf-8-sig', 'cp1252', 'latin1']:
            try:
                content_str = content.decode(encoding)
                break
            except UnicodeDecodeError:
                continue
        else:
            raise UnicodeDecodeError("Nepodařilo se dekódovat CSV")
        
        # Zkusíme různé oddělovače
        for separator in [',', ';', '\t']:
            try:
                df = pd.read_csv(pd.io.common.StringIO(content_str), sep=separator, nrows=5)
                if len(df.columns) > 1:
                    return {
                        'format': 'csv',
                        'separator': separator,
                        'encoding': encoding,
                        'columns': df.columns.tolist(),
                        'sample_data': df.head(2).to_dict('records')
                    }
            except:
                continue
        
        return {
            'format': 'csv',
            'error': 'Nepodařilo se parsovat CSV s žádným oddělovačem'
        }
        
    except Exception as e:
        return {
            'format': 'csv',
            'error': f'Chyba při analýze CSV: {e}'
        }

def create_connector(tenant_id: str, feed_format: Dict[str, Any], feed_url: str) -> str:
    """Vytvoří CSV konektor."""
    connector_name = f"{tenant_id}_connector"
    connector_file = CONNECTORS_DIR / f"{connector_name}.py"
    
    separator = feed_format.get('separator', ',')
    encoding = feed_format.get('encoding', 'utf-8')
    
    # Kód konektoru
    connector_code = f"""from typing import List, Dict, Any
import requests
import pandas as pd
import os
from io import StringIO
from .base_connector import BaseConnector
import urllib.parse


class {tenant_id.capitalize()}Connector(BaseConnector):
    \"\"\"Automaticky generovaný CSV konektor pro {tenant_id}\"\"\"
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.feed_url = config.get('feed_url')
        if not self.feed_url:
            raise ValueError("feed_url není nastaven v konfiguraci")

    def get_feed(self) -> List[Dict[str, Any]]:
        \"\"\"Získá a zpracuje CSV feed\"\"\"
        self.logger.info(f"Načítám CSV feed z {{self.feed_url}}")
        try:
            # Kontrola, zda se jedná o lokální soubor nebo URL
            if self.feed_url.startswith('file://'):
                # Lokální soubor - zpracujeme přímo
                file_path = self.feed_url[7:]  # Odstraníme 'file://'
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='{encoding}') as f:
                        content = f.read()
                else:
                    raise FileNotFoundError(f"Soubor {{file_path}} neexistuje")
            else:
                # Běžné URL - použijeme requests
                response = requests.get(self.feed_url, timeout=30)
                response.raise_for_status()
                content = response.content.decode('{encoding}')
            
            # Načtení CSV
            df = pd.read_csv(StringIO(content), sep='{separator}')
            
            # Standardizace názvů sloupců
            column_mapping = {{}}
            for col in df.columns:
                col_lower = col.lower()
                if 'id' in col_lower:
                    column_mapping[col] = 'ID'
                elif 'name' in col_lower or 'title' in col_lower:
                    column_mapping[col] = 'Name'
                elif 'desc' in col_lower:
                    column_mapping[col] = 'Description'
                elif 'categ' in col_lower:
                    column_mapping[col] = 'Category'
                elif 'price' in col_lower or 'cena' in col_lower:
                    column_mapping[col] = 'Price'
                elif 'brand' in col_lower or 'znacka' in col_lower:
                    column_mapping[col] = 'Brand'
                elif 'avail' in col_lower or 'dostup' in col_lower:
                    column_mapping[col] = 'Availability'
                elif 'image' in col_lower or 'foto' in col_lower:
                    column_mapping[col] = 'ImageUrl'
                elif 'url' in col_lower or 'link' in col_lower:
                    column_mapping[col] = 'ProductUrl'
            
            # Přejmenování sloupců
            df = df.rename(columns=column_mapping)
            
            # Konverze na seznam slovníků
            products = df.to_dict('records')
            
            # Vyčištění dat
            for product in products:
                for key, value in product.items():
                    if pd.isna(value):
                        product[key] = ""
                    else:
                        product[key] = str(value)
            
            self.logger.info(f"Načteno {{len(products)}} produktů")
            return products
            
        except Exception as e:
            self.logger.error(f"Chyba při načítání feedu: {{e}}")
            raise
"""
    
    # Zápis konektoru
    with open(connector_file, 'w', encoding='utf-8') as f:
        f.write(connector_code)
    
    print(f"✅ Vytvořen konektor: {connector_file}")
    return connector_name

def update_connector_factory(tenant_id: str, connector_name: str):
    """Aktualizuje connector_factory.py o nový konektor."""
    factory_file = CONNECTORS_DIR / "connector_factory.py"
    
    try:
        with open(factory_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Přidání importu
        import_line = f"from .{connector_name} import {tenant_id.capitalize()}Connector"
        if import_line not in content:
            # Najdeme místo pro import
            import_section_end = content.find('\n\nclass ConnectorFactory:')
            if import_section_end != -1:
                content = content[:import_section_end] + f"\n{import_line}" + content[import_section_end:]
        
        # Přidání do _connectors slovníku
        connector_entry = f"        '{tenant_id}': {tenant_id.capitalize()}Connector,"
        connectors_start = content.find('_connectors = {')
        if connectors_start != -1:
            connectors_end = content.find('}', connectors_start)
            if connectors_end != -1:
                before_closing = content[:connectors_end].rstrip()
                if not before_closing.endswith(','):
                    before_closing += ','
                content = before_closing + f"\n{connector_entry}\n" + content[connectors_end:]
        
        with open(factory_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ Aktualizován connector_factory.py pro konektor {tenant_id}")
        
    except Exception as e:
        print(f"❌ Chyba při aktualizaci connector_factory.py: {e}")

def create_config(tenant_id: str, feed_url: str) -> Path:
    """Vytvoří konfigurační soubor pro tenanta."""
    config_data = {
        'feed': {
            'type': tenant_id,
            'format': 'csv',
            'feed_url': feed_url
        },
        'recommender': {
            'similar_products_count': 5,
            'min_similarity': 0.3
        }
    }
    
    config_file_path = CONFIG_DIR / f"{tenant_id}.yaml"
    with open(config_file_path, 'w', encoding='utf-8') as f:
        yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True)
    
    print(f"✅ Vytvořena konfigurace: {config_file_path}")
    return config_file_path

def create_test_feed():
    """Vytvoří testovací CSV feed pro účely testování."""
    test_data = """ID,Name,Description,Category,Price,Brand,Availability,ImageUrl,ProductUrl
1,Test Produkt 1,Popis prvního testovacího produktu,Testovací kategorie,100.50,TestBrand,in stock,https://example.com/img1.jpg,https://example.com/product1
2,Test Produkt 2,Popis druhého testovacího produktu,Jiná kategorie,250.00,TestBrand,in stock,https://example.com/img2.jpg,https://example.com/product2
3,Test Produkt 3,Popis třetího testovacího produktu,Testovací kategorie,75.25,OtherBrand,out of stock,https://example.com/img3.jpg,https://example.com/product3
"""
    
    # Vytvoření dočasného souboru
    with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
        f.write(test_data)
        return f.name

def test_connector(tenant_id: str, config_path: Path):
    """Testuje vytvořený konektor."""
    try:
        # Import modulu
        sys.path.insert(0, str(PROJECT_ROOT))
        from connectors.connector_factory import ConnectorFactory
        
        # Načtení konfigurace
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        
        # Vytvoření konektoru
        connector = ConnectorFactory.create_connector(tenant_id, config['feed'])
        
        # Test načtení dat
        products = connector.get_feed()
        
        print(f"✅ Načteno {len(products)} produktů")
        print("\nUkázka prvního produktu:")
        for key, value in products[0].items():
            print(f"  {key}: {value}")
            
        return True
    
    except Exception as e:
        print(f"❌ Chyba při testování konektoru: {e}")
        return False

def main():
    """
    Hlavní funkce pro demonstraci automatického onboardingu.
    """
    print("🚀 === DEMO AUTOMATICKÉHO ONBOARDINGU ===\n")
    
    # Vytvoření testovacího tenant ID
    tenant_id = "demo_test"
    
    # Vytvoření testovacího feedu
    feed_path = create_test_feed()
    feed_url = f"file://{feed_path}"
    
    print(f"📊 Testovací feed: {feed_path}")
    
    try:
        # 1. Analýza feedu
        print("\n🔍 1. Analýza formátu feedu...")
        with open(feed_path, 'rb') as f:
            content = f.read()
        
        feed_info = detect_csv_format(content)
        if 'error' in feed_info:
            print(f"❌ Chyba při analýze feedu: {feed_info['error']}")
            return
        
        print(f"✅ Detekován formát: {feed_info['format']}")
        print(f"✅ Oddělovač: '{feed_info['separator']}'")
        print(f"✅ Kódování: {feed_info['encoding']}")
        
        # 2. Vytvoření konektoru
        print("\n🔧 2. Vytváření konektoru...")
        connector_name = create_connector(tenant_id, feed_info, feed_url)
        
        # 3. Aktualizace connector_factory
        print("\n📝 3. Aktualizace connector_factory...")
        update_connector_factory(tenant_id, connector_name)
        
        # 4. Vytvoření konfigurace
        print("\n📋 4. Vytváření konfigurace...")
        config_path = create_config(tenant_id, feed_url)
        
        # 5. Test konektoru
        print("\n🧪 5. Testování konektoru...")
        success = test_connector(tenant_id, config_path)
        
        if success:
            print("\n🎉 === DEMO ONBOARDING ÚSPĚŠNÝ! ===")
            print("✅ Tento úspěšný test demonstruje princip automatického onboardingu")
            print("✅ Konektor byl vytvořen, otestován a funguje správně")
        else:
            print("\n❌ === DEMO ONBOARDING SELHAL ===")
            print("❌ Test konektoru selhal, zkontrolujte logy")
    
    except Exception as e:
        print(f"\n❌ Chyba při běhu dema: {e}")
    finally:
        # Úklid dočasného souboru
        try:
            Path(feed_path).unlink()
        except:
            pass

if __name__ == "__main__":
    main() 