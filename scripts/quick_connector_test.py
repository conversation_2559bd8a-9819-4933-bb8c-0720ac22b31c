#!/usr/bin/env python3
"""
Rychlý test konektoru - testuje pouze vytvoření konektoru a načtení dat
bez spouštění kompletního onboarding procesu.
"""

import sys
import requests
from pathlib import Path

# Přidáme cestu k projektu
sys.path.insert(0, str(Path(__file__).parent.parent))

def quick_test_feed(url: str):
    """Rychlý test feedu - načte první 5 produktů."""
    print(f"🔍 Testuji feed: {url}")
    
    try:
        # Import funkcí z onboard_tenant
        from scripts.onboard_tenant import detect_feed_format, generate_connector, create_tenant_config
        
        # Analýza feedu
        print("📊 Analyzuji formát feedu...")
        feed_info = detect_feed_format(url)
        
        if 'error' in feed_info:
            print(f"❌ Chyba: {feed_info['error']}")
            return False
        
        print(f"✅ Formát: {feed_info['format']}")
        if 'item_count' in feed_info:
            print(f"📈 Počet položek: {feed_info['item_count']}")
        
        # Test generování konektoru
        test_tenant_id = "quick_test"
        print(f"\n🔧 Generuji testovací konektor pro '{test_tenant_id}'...")
        
        try:
            connector_name = generate_connector(test_tenant_id, feed_info, url)
            print(f"✅ Konektor vytvořen: {connector_name}")
        except Exception as e:
            print(f"❌ Chyba při generování konektoru: {e}")
            return False
        
        # Test konfigurace
        print("\n📝 Vytvářím testovací konfiguraci...")
        try:
            config_file = create_tenant_config(test_tenant_id, url, connector_name)
            print(f"✅ Konfigurace vytvořena: {config_file}")
        except Exception as e:
            print(f"❌ Chyba při vytváření konfigurace: {e}")
            return False
        
        # Test načtení dat
        print("\n📦 Testuji načtení dat...")
        try:
            from connectors.connector_factory import ConnectorFactory
            import yaml
            
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            
            connector = ConnectorFactory.create_connector(test_tenant_id, config['feed'])
            products = connector.get_feed()[:5]  # Pouze prvních 5
            
            if products:
                print(f"✅ Načteno {len(products)} produktů")
                print("\n📋 Ukázka prvního produktu:")
                for key, value in products[0].items():
                    print(f"  {key}: {value}")
                return True
            else:
                print("❌ Žádné produkty nenačteny")
                return False
                
        except Exception as e:
            print(f"❌ Chyba při testování načtení dat: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Obecná chyba: {e}")
        return False

def main():
    print("⚡ === RYCHLÝ TEST KONEKTORU ===\n")
    
    if len(sys.argv) > 1:
        url = sys.argv[1]
    else:
        url = input("🌐 Zadej URL feedu pro test: ").strip()
    
    if not url:
        print("❌ URL feedu je povinné")
        return
    
    success = quick_test_feed(url)
    
    if success:
        print("\n✅ Rychlý test ÚSPĚŠNÝ!")
        print("💡 Pro kompletní onboarding spusť: python scripts/onboard_tenant.py")
    else:
        print("\n❌ Rychlý test SELHAL!")
        print("🔧 Zkontroluj URL feedu a jeho formát")

if __name__ == "__main__":
    main() 