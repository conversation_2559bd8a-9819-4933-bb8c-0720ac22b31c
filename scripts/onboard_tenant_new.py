#!/usr/bin/env python3
import os
import sys
import subprocess
import yaml
import re
import datetime
import requests
import xml.etree.ElementTree as ET
import pandas as pd
import json
from pathlib import Path
from typing import Dict, List, Any, Optional
from urllib.parse import urlparse

# --- Konfigurace ---
PROJECT_ROOT = Path(__file__).resolve().parent.parent
TENANT_CONFIG_DIR = PROJECT_ROOT / "config" / "tenants"
CONNECTORS_DIR = PROJECT_ROOT / "connectors"
LOG_DIR = PROJECT_ROOT / "logs"

# Zajistíme existenci adresářů
TENANT_CONFIG_DIR.mkdir(parents=True, exist_ok=True)
LOG_DIR.mkdir(parents=True, exist_ok=True)

# Cesty k hlavním skriptům
SYNC_SCRIPT_PATH = PROJECT_ROOT / "sync_products.py"
COMPLEMENTARY_SCRIPT_PATH = PROJECT_ROOT / "batch_scripts" / "compute_complementary.py"

# --- Automatick<PERSON> detekce formátu feedu ---

def detect_feed_format(url: str) -> Dict[str, Any]:
    """
    Automaticky detekuje formát feedu a vytvoří odpovídající konfiguraci.
    
    Returns:
        Dict s informacemi o formátu, typu konektoru a vzorku dat
    """
    print(f"Analyzuji feed na URL: {url}")
    
    try:
        response = requests.get(url, timeout=30, headers={
            'User-Agent': 'Mozilla/5.0 (compatible; FeedAnalyzer/1.0)'
        })
        response.raise_for_status()
        content = response.content
        
        # Detekce podle Content-Type
        content_type = response.headers.get('content-type', '').lower()
        
        # Detekce XML feedu
        if 'xml' in content_type or content.strip().startswith(b'<?xml'):
            return _analyze_xml_feed(content, url)
        
        # Detekce CSV feedu
        elif 'csv' in content_type or url.lower().endswith('.csv'):
            return _analyze_csv_feed(content, url)
        
        # Detekce JSON feedu
        elif 'json' in content_type or content.strip().startswith(b'{'):
            return _analyze_json_feed(content, url)
        
        # Pokus o detekci podle obsahu
        else:
            content_str = content.decode('utf-8', errors='ignore')[:1000]
            if content_str.strip().startswith('<?xml') or '<rss' in content_str or '<feed' in content_str:
                return _analyze_xml_feed(content, url)
            elif content_str.startswith('{') or content_str.startswith('['):
                return _analyze_json_feed(content, url)
            else:
                # Zkusíme CSV
                return _analyze_csv_feed(content, url)
    
    except Exception as e:
        print(f"Chyba při analýze feedu: {e}")
        return {
            'format': 'unknown',
            'error': str(e)
        }
