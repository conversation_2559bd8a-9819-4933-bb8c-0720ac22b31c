# 🚀 Automatizovaný Onboarding Nových Tenantů

Tento systém umožňuje kompletně automatizovaný onboarding nových tenantů včetně:
- ✅ Automatické detekce formátu feedu (XML, CSV, JSON)
- ✅ Generování vlastního konektoru pro tenant
- ✅ Testování kompatibility se sync_products.py
- ✅ Vytvoření konfigurace tenanta
- ✅ Kompletní synchronizace produktů a výpočet embeddingů
- ✅ Výpočet komplementárních produktů
- ✅ Testování doporučení

## 🎯 Hlavní skript: `onboard_tenant.py`

### Použití:
```bash
cd /path/to/project
python scripts/onboard_tenant.py
```

### Co skript dělá:

1. **Analýza feedu** - automaticky detekuje formát (XML/CSV/JSON) a strukturu
2. **Generování konektoru** - vyt<PERSON><PERSON><PERSON> vlastní konektor specifický pro tenant
3. **Testování kompatibility** - o<PERSON><PERSON><PERSON><PERSON>, že konektor funguje se sync_products.py
4. **Konfigurace** - vytvoří kompletní konfigurační YAML soubor
5. **Synchronizace** - spustí sync_products.py pro načtení všech produktů
6. **Embeddingy** - automaticky vygeneruje embeddingy pro všechny produkty
7. **Komplementy** - spustí compute_complementary.py pro výpočet doporučení
8. **Test** - ověří, že doporučení fungují správně

### Výstup:
- Nový konektor v `connectors/{tenant_id}_connector.py`
- Aktualizovaný `connectors/connector_factory.py`
- Konfigurační soubor `config/tenants/{tenant_id}.yaml`
- Logy v `logs/`
- Instrukce pro cron nastavení

## ⚡ Rychlý test: `quick_connector_test.py`

Pro rychlé otestování konektoru bez plného onboarding procesu:

```bash
python scripts/quick_connector_test.py https://example.com/feed.xml
```

nebo

```bash
python scripts/quick_connector_test.py
# Pak zadej URL interaktivně
```

## 🧪 Testování: `test_onboarding.py`

Pro automatické testování celého onboarding procesu s fiktivními daty:

```bash
python scripts/test_onboarding.py
```

## 📊 Podporované formáty feedů

### XML formáty:
- **RSS feeds** - standardní RSS s produktovými položkami
- **Atom feeds** - Atom XML s entries
- **Google Shopping feeds** - s g: namespace
- **Obecné XML** - s item nebo entry elementy

### CSV formáty:
- Automatická detekce oddělovače (`,`, `;`, `\t`)
- Automatická detekce kódování (UTF-8, CP1252, Latin1)
- Inteligentní mapování sloupců na standardní pole

### JSON formáty:
- **Array formát** - `[{product1}, {product2}, ...]`
- **Object formát** - `{"products": [{...}], "meta": {...}}`
- **Single object** - jeden produkt jako objekt

## 🎛️ Automatické mapování polí

Skript automaticky mapuje běžné názvy polí:

| Detekované názvy | Standardní pole |
|------------------|-----------------|
| `id`, `ID`, `product_id` | `ID` |
| `name`, `title`, `Name` | `Name` |
| `description`, `desc` | `Description` |
| `category`, `categ` | `Category` |
| `price`, `cena` | `Price` |
| `brand`, `znacka` | `Brand` |
| `availability`, `dostup` | `Availability` |
| `image`, `foto`, `image_url` | `ImageUrl` |
| `url`, `link`, `product_url` | `ProductUrl` |

## 🔧 Řešení problémů

### Chyba při analýze feedu:
- Zkontroluj, zda je URL dostupné
- Ověř formát feedu (XML musí být validní, CSV musí mít správný oddělovač)
- Zkus rychlý test: `python scripts/quick_connector_test.py URL`

### Konektor není kompatibilní:
- Zkontroluj, zda feed obsahuje povinná pole `ID` a `Name`
- Ověř, zda se data načítají správně v quick testu

### Sync_products.py selhal:
- Zkontroluj log v `logs/sync_products_{tenant_id}_onboarding.log`
- Ověř, zda jsou správně nastavené API klíče (OpenAI, Qdrant)
- Zkontroluj připojení k databázi

### Compute_complementary.py selhal:
- Toto je méně kritické - tenant funguje i bez komplementů
- Zkontroluj log v `logs/compute_complementary_{tenant_id}_onboarding.log`
- Komplementy lze spustit později ručně

## 🔄 Pravidelná synchronizace

Po úspěšném onboarding přidej do crontabu:

```bash
# Editace crontabu
crontab -e

# Přidej tyto řádky (nahraď cestu a tenant_id):
0 2 * * * cd /path/to/project && python sync_products.py --tenant TENANT_ID
0 3 * * * cd /path/to/project && python batch_scripts/compute_complementary.py --tenant TENANT_ID
```

## 📁 Struktura výstupních souborů

```
connectors/
  ├── {tenant_id}_connector.py      # Nový konektor
  └── connector_factory.py          # Aktualizovaná továrna

config/tenants/
  └── {tenant_id}.yaml              # Konfigurace tenanta

logs/
  ├── sync_products_{tenant_id}_onboarding.log
  └── compute_complementary_{tenant_id}_onboarding.log
```

## 🎯 Příklad použití

```bash
# Spustí plný onboarding
python scripts/onboard_tenant.py

# Zadej:
# Tenant ID: novyshop
# URL: https://novyshop.cz/feed.xml

# Výsledek:
# ✅ Konektor: novyshop_connector
# ✅ Konfigurace: config/tenants/novyshop.yaml  
# ✅ Produkty synchronizovány
# ✅ Embeddingy vygenerovány
# ✅ Komplementy vypočteny
# ✅ Doporučení testována
```

## 🚨 Důležité poznámky

1. **API klíče** - ujisti se, že máš nastavené správné API klíče pro OpenAI a Qdrant
2. **Výkon** - pro velké feedy (>10k produktů) může onboarding trvat i hodiny
3. **Disk space** - embeddingy zabírají značné místo, zkontroluj dostupný prostor
4. **Backup** - před spuštěním si zazálohuj stávající konfiguraci

---

*Pokud máš problémy, zkontroluj logy nebo spusť quick test pro diagnostiku.* 