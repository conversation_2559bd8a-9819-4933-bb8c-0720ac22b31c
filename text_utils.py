import re
import logging
from typing import Optional, Tuple

logger = logging.getLogger(__name__)

def normalize_text(text: str) -> str:
    """
    Základní normalizace textu.
    Odstraní přebytečné mezery a vyčistí speciální znaky.
    """
    if not text:
        return ""
    
    try:
        # Základní čištění
        text = text.strip()
        
        # Nahrazení non-breaking space normálním
        text = text.replace('\xa0', ' ')
        
        # Odstranění nadbytečných mezer
        text = ' '.join(text.split())
        
        return text
    except Exception as e:
        logger.warning(f"Chyba při normalizaci textu: {e}")
        return text

def extract_price_range_from_query(query: str) -> Tuple[Optional[float], Optional[float], Optional[float]]:
    """
    Extrahuje cenové rozsahy z vyhledávacího dotazu.
    
    Args:
        query: Vyhledávací dotaz
        
    Returns:
        Tuple (min_price, max_price, target_price)
    """
    if not query:
        return None, None, None
    
    query = query.lower()
    min_price = None
    max_price = None
    target_price = None
    
    try:
        # Hledání vzorů pro cenové rozsahy
        # "do 5000", "pod 1000", "max 2000"
        max_patterns = [
            r'(?:do|pod|max|maximálně|nejvíce)\s+(\d+)',
            r'(?:pod|do)\s+(\d+)\s*(?:kč|czk|korun)?',
        ]
        
        for pattern in max_patterns:
            match = re.search(pattern, query)
            if match:
                max_price = float(match.group(1))
                break
        
        # "od 2000", "min 1000", "alespoň 500"  
        min_patterns = [
            r'(?:od|min|minimálně|alespoň)\s+(\d+)',
            r'(?:od|minimálně)\s+(\d+)\s*(?:kč|czk|korun)?',
        ]
        
        for pattern in min_patterns:
            match = re.search(pattern, query)
            if match:
                min_price = float(match.group(1))
                break
        
        # "kolem 2000", "asi 1500", "přibližně 3000"
        target_patterns = [
            r'(?:kolem|asi|přibližně|okolo|cca|zhruba)\s+(\d+)',
            r'(?:kolem|asi|přibližně|okolo|cca|zhruba)\s+(\d+)\s*(?:kč|czk|korun)?',
        ]
        
        for pattern in target_patterns:
            match = re.search(pattern, query)
            if match:
                target_price = float(match.group(1))
                break
        
        # Rozsah "1000-5000", "2000 až 8000"
        range_patterns = [
            r'(\d+)\s*(?:-|až)\s*(\d+)',
            r'(\d+)\s+až\s+(\d+)',
        ]
        
        for pattern in range_patterns:
            match = re.search(pattern, query)
            if match:
                min_price = float(match.group(1))
                max_price = float(match.group(2))
                break
        
        if min_price or max_price or target_price:
            logger.debug(f"Extrakované ceny z dotazu '{query}': min={min_price}, max={max_price}, target={target_price}")
        
    except Exception as e:
        logger.warning(f"Chyba při extrakci cen z dotazu '{query}': {e}")
    
    return min_price, max_price, target_price 