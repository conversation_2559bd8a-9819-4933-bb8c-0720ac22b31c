#!/usr/bin/env python3
"""
Test skript pro ověření komplementárn<PERSON>ch kategorií a vztahů pro jabkolevne.cz
Tento skript testuje funkčnost vytvořených kategorií a komplementárních vztahů.

Autor: <PERSON>: 2025-01-17
"""

from neo4j import GraphDatabase
import os
from dotenv import load_dotenv
import logging

# Nastavení loggingu
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Načtení proměnných prostředí
load_dotenv()

# Konfigurace
TENANT_ID = "jabkolevne"
CATEGORY_LABEL = f"Category_{TENANT_ID}"
NEO4J_URI = os.getenv("NEO4J_URI", "bolt://localhost:7687")
NEO4J_USER = os.getenv("NEO4J_USER", "neo4j")  
NEO4J_PASSWORD = os.getenv("NEO4J_PASSWORD", "Graph2025Secure!")

def connect_to_neo4j():
    """Připojí se k Neo4j databázi."""
    try:
        driver = GraphDatabase.driver(NEO4J_URI, auth=(NEO4J_USER, NEO4J_PASSWORD))
        return driver
    except Exception as e:
        logger.error(f"❌ Chyba při připojení k Neo4j: {e}")
        return None

def test_categories_exist(driver):
    """Testuje, zda byly vytvořeny očekávané kategorie."""
    expected_categories = [
        "iPhone", "iPad", "MacBook", "Apple Watch", "AirPods",
        "Nabíječky (Apple)", "Kabely (Apple)", "Pouzdra pro iPhone",
        "Ochranná skla pro iPhone", "Apple Pencil", "AirTag"
    ]
    
    try:
        with driver.session() as session:
            for category in expected_categories:
                result = session.run(
                    f"MATCH (c:{CATEGORY_LABEL} {{name: $name}}) RETURN count(c) as count",
                    {"name": category}
                )
                count = result.single()["count"]
                
                if count == 1:
                    logger.info(f"✅ Kategorie '{category}' existuje")
                else:
                    logger.error(f"❌ Kategorie '{category}' neexistuje")
                    return False
            
            logger.info("✅ Všechny očekávané kategorie existují")
            return True
            
    except Exception as e:
        logger.error(f"❌ Chyba při testování kategorií: {e}")
        return False

def test_complementary_relationships(driver):
    """Testuje komplementární vztahy."""
    test_cases = [
        ("iPhone", "Pouzdra pro iPhone", 1.0),
        ("iPhone", "Ochranná skla pro iPhone", 1.0),
        ("iPhone", "AirPods", 0.8),
        ("iPad", "Apple Pencil", 0.9),
        ("Apple Watch", "Řemínky pro Apple Watch", 1.0)
    ]
    
    try:
        with driver.session() as session:
            for source, target, expected_weight in test_cases:
                result = session.run(f"""
                    MATCH (s:{CATEGORY_LABEL} {{name: $source}})
                    -[r:COMPLEMENTARY_TO]->
                    (t:{CATEGORY_LABEL} {{name: $target}})
                    WHERE r.created_by = 'jabkolevne_script'
                    RETURN r.weight as weight
                """, {"source": source, "target": target})
                
                record = result.single()
                if record and record["weight"] == expected_weight:
                    logger.info(f"✅ Vztah '{source}' -> '{target}' má správnou váhu ({expected_weight})")
                else:
                    logger.error(f"❌ Vztah '{source}' -> '{target}' má nesprávnou váhu nebo neexistuje")
                    return False
            
            logger.info("✅ Všechny testované komplementární vztahy jsou správné")
            return True
            
    except Exception as e:
        logger.error(f"❌ Chyba při testování komplementárních vztahů: {e}")
        return False

def test_bidirectional_relationships(driver):
    """Testuje, zda jsou vztahy obousměrné."""
    try:
        with driver.session() as session:
            # Testujeme iPhone -> Pouzdra pro iPhone a zpětný vztah
            result1 = session.run(f"""
                MATCH (s:{CATEGORY_LABEL} {{name: 'iPhone'}})
                -[r:COMPLEMENTARY_TO]->
                (t:{CATEGORY_LABEL} {{name: 'Pouzdra pro iPhone'}})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN count(r) as count
            """)
            
            result2 = session.run(f"""
                MATCH (s:{CATEGORY_LABEL} {{name: 'Pouzdra pro iPhone'}})
                -[r:COMPLEMENTARY_TO]->
                (t:{CATEGORY_LABEL} {{name: 'iPhone'}})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN count(r) as count
            """)
            
            count1 = result1.single()["count"]
            count2 = result2.single()["count"]
            
            if count1 == 1 and count2 == 1:
                logger.info("✅ Vztahy jsou správně obousměrné")
                return True
            else:
                logger.error(f"❌ Vztahy nejsou obousměrné ({count1}, {count2})")
                return False
                
    except Exception as e:
        logger.error(f"❌ Chyba při testování obousměrných vztahů: {e}")
        return False

def test_query_complementary_products(driver):
    """Testuje dotaz na komplementární produkty pro iPhone."""
    try:
        with driver.session() as session:
            result = session.run(f"""
                MATCH (source:{CATEGORY_LABEL} {{name: 'iPhone'}})
                -[r:COMPLEMENTARY_TO]->
                (target:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN target.name as category, r.weight as weight
                ORDER BY r.weight DESC
                LIMIT 5
            """)
            
            products = list(result)
            
            if len(products) >= 3:  # Očekáváme alespoň 3 komplementární kategorie pro iPhone
                logger.info("✅ Dotaz na komplementární produkty pro iPhone:")
                for product in products:
                    logger.info(f"   - {product['category']} (váha: {product['weight']})")
                return True
            else:
                logger.error(f"❌ Nedostatek komplementárních produktů pro iPhone ({len(products)})")
                return False
                
    except Exception as e:
        logger.error(f"❌ Chyba při testování dotazu na komplementární produkty: {e}")
        return False

def generate_statistics(driver):
    """Vygeneruje statistiky pro přehled."""
    try:
        with driver.session() as session:
            # Počet kategorií
            result = session.run(f"MATCH (c:{CATEGORY_LABEL}) RETURN count(c) as count")
            cat_count = result.single()["count"]
            
            # Počet komplementárních vztahů
            result = session.run(f"""
                MATCH (c1:{CATEGORY_LABEL})-[r:COMPLEMENTARY_TO]->(c2:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                RETURN count(r) as count
            """)
            rel_count = result.single()["count"]
            
            # Kategorie s nejvíce komplementárními produkty
            result = session.run(f"""
                MATCH (source:{CATEGORY_LABEL})-[r:COMPLEMENTARY_TO]->(target:{CATEGORY_LABEL})
                WHERE r.created_by = 'jabkolevne_script'
                WITH source, count(target) as complement_count
                RETURN source.name as category, complement_count
                ORDER BY complement_count DESC
                LIMIT 3
            """)
            
            top_categories = list(result)
            
            logger.info("📊 === STATISTIKY KOMPLEMENTÁRNÍCH KATEGORIÍ ===")
            logger.info(f"📂 Celkem kategorií: {cat_count}")
            logger.info(f"🔗 Celkem komplementárních vztahů: {rel_count}")
            logger.info("🏆 Kategorie s nejvíce komplementárními produkty:")
            
            for cat in top_categories:
                logger.info(f"   - {cat['category']}: {cat['complement_count']} komplementárních produktů")
                
    except Exception as e:
        logger.error(f"❌ Chyba při generování statistik: {e}")

def main():
    """Hlavní testovací funkce."""
    logger.info("🧪 === TESTOVÁNÍ KOMPLEMENTÁRNÍCH KATEGORIÍ JABKOLEVNE ===")
    
    # Připojení k Neo4j
    driver = connect_to_neo4j()
    if not driver:
        logger.error("❌ Nepodařilo se připojit k Neo4j. Ukončuji.")
        return False
    
    try:
        all_passed = True
        
        # Test 1: Existence kategorií
        logger.info("🔍 Test 1: Kontrola existence kategorií")
        if not test_categories_exist(driver):
            all_passed = False
        
        # Test 2: Komplementární vztahy
        logger.info("🔍 Test 2: Kontrola komplementárních vztahů")
        if not test_complementary_relationships(driver):
            all_passed = False
        
        # Test 3: Obousměrné vztahy
        logger.info("🔍 Test 3: Kontrola obousměrných vztahů")
        if not test_bidirectional_relationships(driver):
            all_passed = False
        
        # Test 4: Dotaz na komplementární produkty
        logger.info("🔍 Test 4: Testování dotazu na komplementární produkty")
        if not test_query_complementary_products(driver):
            all_passed = False
        
        # Statistiky
        generate_statistics(driver)
        
        if all_passed:
            logger.info("🎉 === VŠECHNY TESTY PROŠLY ÚSPĚŠNĚ ===")
        else:
            logger.error("❌ === NĚKTERÉ TESTY SELHALY ===")
        
        return all_passed
        
    finally:
        driver.close()

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1) 