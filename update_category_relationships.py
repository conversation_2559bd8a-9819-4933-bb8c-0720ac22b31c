#!/usr/bin/env python3
import json
import os
import logging
from typing import Dict, List, Set, Tuple
import argparse

# Nastavení loggeru
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("update_category_relationships")

def load_json_file(file_path: str) -> Dict:
    """Načte JSON soubor."""
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
        return data
    except Exception as e:
        logger.error(f"Chyba při načítání souboru {file_path}: {e}")
        return {}

def save_json_file(data: Dict, file_path: str) -> bool:
    """Uloží data do JSON souboru."""
    try:
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(data, file, ensure_ascii=False, indent=2)
        logger.info(f"Data byla úspěšně uložena do souboru {file_path}")
        return True
    except Exception as e:
        logger.error(f"Chyba při ukládání do souboru {file_path}: {e}")
        return False

def convert_clustered_to_simple(clustered_file: str, output_file: str) -> bool:
    """
    Převede strukturovaný formát kategoriálních vztahů na jednoduchý formát.
    
    Args:
        clustered_file: Cesta k souboru se strukturovanými vztahy
        output_file: Cesta k výstupnímu souboru
    
    Returns:
        bool: True, pokud se konverze podařila, jinak False
    """
    clustered_data = load_json_file(clustered_file)
    if not clustered_data:
        return False
    
    simple_data = {}
    for category, relationships in clustered_data.get("relationships", {}).items():
        simple_data[category] = {}
        for rel in relationships:
            complementary_category = rel.get("complementary_category")
            strength = rel.get("strength")
            if complementary_category and strength:
                simple_data[category][complementary_category] = strength
    
    return save_json_file(simple_data, output_file)

def extract_categories_from_products(products_file: str) -> Set[str]:
    """
    Extrahuje unikátní kategorie z produktů.
    
    Args:
        products_file: Cesta k souboru s produkty
    
    Returns:
        Set[str]: Množina unikátních kategorií
    """
    products = load_json_file(products_file)
    if not products:
        return set()
    
    categories = set()
    for product in products:
        category = product.get("category")
        if category:
            categories.add(category)
    
    return categories

def update_category_relationships(relationships_file: str, products_file: str = None, output_file: str = None) -> bool:
    """
    Aktualizuje kategoriální vztahy tak, aby obsahovaly plné hierarchické cesty.
    
    Args:
        relationships_file: Cesta k souboru s kategoriálními vztahy
        products_file: Cesta k souboru s produkty (volitelné)
        output_file: Cesta k výstupnímu souboru (volitelné)
    
    Returns:
        bool: True, pokud se aktualizace podařila, jinak False
    """
    relationships = load_json_file(relationships_file)
    if not relationships:
        return False
    
    # Pokud není zadán výstupní soubor, přepíšeme vstupní soubor
    if not output_file:
        output_file = relationships_file
    
    # Vytvoříme mapu zkrácených názvů kategorií na plné názvy
    category_map = {}
    
    # Pokud máme soubor s produkty, použijeme kategorie z něj
    if products_file:
        product_categories = extract_categories_from_products(products_file)
        for category in product_categories:
            short_name = category.split(">")[-1].strip()
            category_map[short_name] = category
    
    # Aktualizujeme vztahy
    updated_relationships = {}
    for category, related in relationships.items():
        # Najdeme plný název kategorie
        full_category = category
        short_name = category.split(">")[-1].strip() if ">" in category else category
        if short_name in category_map:
            full_category = category_map[short_name]
        
        # Vytvoříme nový slovník souvisejících kategorií
        updated_related = {}
        for related_category, strength in related.items():
            # Najdeme plný název související kategorie
            full_related_category = related_category
            short_related_name = related_category.split(">")[-1].strip() if ">" in related_category else related_category
            if short_related_name in category_map:
                full_related_category = category_map[short_related_name]
            
            updated_related[full_related_category] = strength
        
        updated_relationships[full_category] = updated_related
    
    return save_json_file(updated_relationships, output_file)

def main():
    """Hlavní funkce."""
    parser = argparse.ArgumentParser(description='Aktualizace kategoriálních vztahů')
    parser.add_argument('--relationships', type=str, default='category_relationships_avenberg.json',
                        help='Cesta k souboru s kategoriálními vztahy')
    parser.add_argument('--products', type=str, help='Cesta k souboru s produkty (volitelné)')
    parser.add_argument('--output', type=str, help='Cesta k výstupnímu souboru (volitelné)')
    parser.add_argument('--convert-clustered', type=str, help='Cesta k souboru se strukturovanými vztahy (volitelné)')
    args = parser.parse_args()
    
    if args.convert_clustered:
        output = args.output or 'category_relationships_simple.json'
        if convert_clustered_to_simple(args.convert_clustered, output):
            logger.info(f"Konverze strukturovaných vztahů byla úspěšná. Výstup: {output}")
        else:
            logger.error("Konverze strukturovaných vztahů selhala.")
    else:
        if update_category_relationships(args.relationships, args.products, args.output):
            logger.info("Aktualizace kategoriálních vztahů byla úspěšná.")
        else:
            logger.error("Aktualizace kategoriálních vztahů selhala.")

if __name__ == "__main__":
    main()
