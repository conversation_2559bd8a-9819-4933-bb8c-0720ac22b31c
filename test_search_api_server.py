#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import asyncio
import logging
import os
import sys
import time
import uuid
from typing import Dict, Any, List, Optional

# Nastavenu00ed logovu00e1nu00ed
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger("test_search_api_server")

# Importy z projektu
from dotenv import load_dotenv
from core.clients import get_qdrant_async_client, get_openai_client, get_cohere_client
from semantic import search_semantic_products

# Nau010dtenu00ed promu011bnnu00fdch z .env souboru
load_dotenv()

# Konstanta pro UUID namespace
QUERY_CACHE_NAMESPACE = uuid.UUID('f8db26a4-a1a8-4f9e-8f1a-b9e8a2c9d0c7')

async def test_api_endpoint_no_availability_filter(tenant_id: str, query: str, limit: int = 10, enable_reranking: bool = True):
    """Simulace API endpointu /search/{tenant_id} bez filtrace podle availability."""
    logger.info(f"=== TEST API ENDPOINT BEZ FILTRACE AVAILABILITY ===\nTenant: {tenant_id}\nQuery: {query}\nLimit: {limit}\nReranking: {enable_reranking}")
    
    # Zu00edsku00e1nu00ed klientu016f
    qdrant_client = get_qdrant_async_client()
    openai_client = get_openai_client()
    cohere_client = get_cohere_client()
    
    if cohere_client is None and enable_reranking:
        logger.warning("Cohere client is None, reranking will be skipped.")
    
    # Mu011bu0159enu00ed u010dasu
    t_start = time.time()
    
    # Volu00e1nu00ed funkce search_semantic_products
    products_collection_name = f"real_products_{tenant_id}"
    cache_collection_name = f"semantic_queries_{tenant_id}"
    query_uuid = uuid.uuid5(QUERY_CACHE_NAMESPACE, query)
    point_id = str(query_uuid)
    
    logger.info(f"Collection name: {products_collection_name}")
    logger.info(f"Cache collection name: {cache_collection_name}")
    logger.info(f"Query UUID: {point_id}")
    
    # Kontrola cache
    try:
        cached_results = await qdrant_client.retrieve(
            collection_name=cache_collection_name,
            ids=[point_id],
            with_payload=True
        )
        
        if cached_results:
            cached_payload = cached_results[0].payload
            if cached_payload and "results" in cached_payload and isinstance(cached_payload["results"], list):
                cached_results_with_scores = cached_payload["results"]
                logger.info(f"Cache hit pro dotaz '{query}'. Nalezeno {len(cached_results_with_scores)} vu00fdsledku016f v cache.")
                
                # Vypsat prvnu00edch 5 vu00fdsledku016f z cache
                for i, item in enumerate(cached_results_with_scores[:5]):
                    logger.info(f"  Cache item {i+1}: {item}")
                
                # Ovu011bu0159enu00ed dostupnosti produktu016f
                cached_product_ids = [item["product_id"] for item in cached_results_with_scores]
                qdrant_ids_to_check = [f"{pid}" for pid in cached_product_ids]
                
                try:
                    product_details_points = await qdrant_client.retrieve(
                        collection_name=products_collection_name,
                        ids=qdrant_ids_to_check,
                        with_payload=True
                    )
                    
                    logger.info(f"Retrieved {len(product_details_points)} product details from Qdrant.")
                    
                    # Vypsat informace o prvnu00edch 5 produktech
                    for i, p in enumerate(product_details_points[:5]):
                        logger.info(f"  Product {i+1}: ID={p.id}, Payload keys: {list(p.payload.keys())}")
                        if "availability" in p.payload:
                            logger.info(f"    Availability: {p.payload['availability']}")
                        else:
                            logger.info(f"    Availability field missing!")
                    
                except Exception as retrieve_err:
                    logger.error(f"Chyba pu0159i nau010du00edtu00e1nu00ed detailu016f: {retrieve_err}", exc_info=True)
            else:
                logger.warning(f"Cache pro dotaz '{query}' mu00e1 neplatnu00fd formu00e1t payloadu.")
        else:
            logger.info(f"Cache miss pro dotaz '{query}'.")
    
    except Exception as cache_err:
        logger.error(f"Chyba pu0159i u010dtenu00ed z cache: {cache_err}", exc_info=True)
    
    # Pu0159u00edmu00e9 vyhledu00e1vu00e1nu00ed bez cache
    try:
        results = await search_semantic_products(
            client=qdrant_client,
            collection_name=products_collection_name,
            query_text=query,
            openai_client=openai_client,
            cohere_client=cohere_client,
            limit=limit,
            enable_reranking=enable_reranking
        )
        
        t_end = time.time()
        logger.info(f"Search completed in {t_end - t_start:.4f}s")
        
        # Vu00fdpis vu00fdsledku016f pu0159ed filtracu00ed
        logger.info(f"Found {len(results)} results before filtering:")
        
        # Kontrola, jaku00e9 hodnoty availability se vyskytuju00ed
        availability_values = set(r.get("availability") for r in results)
        logger.info(f"Unique availability values: {availability_values}")
        
        # Vu00fdpis vu0161ech vu00fdsledku016f bez filtrace
        for i, result in enumerate(results[:10]):  # Zobrazu00edme max 10 vu00fdsledku016f
            product_id = result.get("product_id", "N/A")
            name = result.get("name", "N/A")
            score = result.get("score", 0.0)
            availability = result.get("availability", "N/A")
            logger.info(f"  {i+1}. {product_id} - {name} (score: {score:.4f}, availability: {availability})")
        
        # Simulace filtrace podle availability == "in stock" jako v API endpointu
        filtered_results = [r for r in results if r.get("availability") == "in stock"]
        
        logger.info(f"After filtering by 'in stock': {len(filtered_results)} results")
        for i, result in enumerate(filtered_results[:5]):  # Zobrazu00edme max 5 vu00fdsledku016f
            product_id = result.get("product_id", "N/A")
            name = result.get("name", "N/A")
            score = result.get("score", 0.0)
            logger.info(f"  {i+1}. {product_id} - {name} (score: {score:.4f})")
        
        return results
    except Exception as e:
        logger.error(f"Error during search: {e}", exc_info=True)
        return []

async def main():
    # Parametry testu
    tenant_id = "avenberg"  # Tenant ID
    query = "sprcha"        # Vyhledu00e1vacu00ed dotaz
    limit = 10              # Pou010det vu00fdsledku016f
    enable_reranking = True # Zapnout/vypnout reranking
    
    # Test API endpointu bez filtrace podle availability
    await test_api_endpoint_no_availability_filter(tenant_id, query, limit, enable_reranking)

if __name__ == "__main__":
    asyncio.run(main())
