#!/usr/bin/env python3
# generate_all_multi_embeddings.py
import asyncio
import logging
import sys
import argparse
import time
import os
from typing import Dict, List, Any, Optional

from qdrant_client.http.models import PointStruct
from core.clients import get_qdrant_async_client, get_openai_client, close_clients
from embedding_strategies import MultiEmbeddingStrategy
from utils import process_batches_concurrently

# Nastavení loggingu
LOG_DIR = "logs"
os.makedirs(LOG_DIR, exist_ok=True)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(os.path.join(LOG_DIR, "multi_embeddings.log"), encoding='utf-8')
    ]
)
logger = logging.getLogger("generate_all_multi_embeddings")

async def process_product_batch(batch, tenant_id, embedding_strategy, collection_name, qdrant_client):
    """
    Zpracuje dávku produktů - vygeneruje multi-embeddingy a uloží je do Qdrantu.

    Args:
        batch: Seznam produktů ke zpracování
        tenant_id: ID tenanta
        embedding_strategy: Instance MultiEmbeddingStrategy
        collection_name: Název kolekce v Qdrantu
        qdrant_client: Klient pro Qdrant

    Returns:
        Slovník se statistikami zpracování
    """
    batch_start_time = time.time()
    batch_size = len(batch)

    logger.info(f"Zpracovávám dávku {batch_size} produktů")

    # Statistiky
    successful = 0
    failed = 0
    points_to_upsert = []

    for point in batch:
        product = point.payload
        product_id = product.get("product_id") or str(point.id)

        try:
            # Generování multi-embeddingů
            embeddings = await embedding_strategy.create_embeddings(product)

            # Vytvoření bodu pro Qdrant
            # Převedení ID na celé číslo
            try:
                point_id = int(point.id)
            except ValueError:
                # Pokud ID není celé číslo, použijeme hash
                point_id = hash(str(point.id)) % (2**31)

            point_struct = PointStruct(
                id=point_id,
                vector=embeddings.get("combined"),  # Použijeme kombinovaný embedding jako hlavní vektor
                payload={
                    **product,  # Zachováme všechny původní atributy
                    "embeddings_generated_at": time.time(),
                    # Uložíme všechny embeddingy do payloadu
                    "embeddings": {
                        "combined": embeddings.get("combined"),
                        "category": embeddings.get("category_hierarchy"),
                        "description": embeddings.get("pure_description"),
                        "name_brand": embeddings.get("name_brand"),
                        "brand_category": embeddings.get("brand_category")
                    }
                }
            )

            points_to_upsert.append(point_struct)
            successful += 1

        except Exception as e:
            logger.error(f"Chyba při generování embeddingů pro produkt {product_id}: {e}")
            failed += 1

    # Nahrání bodů do Qdrantu
    if points_to_upsert:
        try:
            await qdrant_client.upsert(
                collection_name=collection_name,
                points=points_to_upsert
            )
            logger.info(f"Nahráno {len(points_to_upsert)} bodů do Qdrantu")
        except Exception as e:
            logger.error(f"Chyba při nahrávání bodů do Qdrantu: {e}")
            failed += len(points_to_upsert)
            successful -= len(points_to_upsert)

    # Výpočet statistik
    batch_time = time.time() - batch_start_time
    logger.info(f"Dávka zpracována za {batch_time:.2f}s")

    return {
        "processed": batch_size,
        "successful": successful,
        "failed": failed,
        "time": batch_time
    }

async def generate_all_multi_embeddings(
    tenant_id: str,
    batch_size: int = 10,
    max_products: Optional[int] = None,
    workers: int = 5
):
    """
    Generuje multi-embeddingy pro všechny produkty v kolekci.

    Args:
        tenant_id: ID tenanta
        batch_size: Velikost dávky pro generování embeddingů
        max_products: Maximální počet produktů ke zpracování (None = všechny)
        workers: Počet paralelních workerů pro generování embeddingů
    """
    collection_name = f"real_products_{tenant_id}"

    # Inicializace klientů
    qdrant_client = get_qdrant_async_client()
    openai_client = get_openai_client()

    # Inicializace multi-embedding strategie
    embedding_strategy = MultiEmbeddingStrategy(openai_client)

    try:
        # Načtení produktů z kolekce po dávkách
        logger.info(f"Načítám produkty z kolekce {collection_name}")

        # Omezení počtu produktů, pokud je zadáno
        limit = max_products if max_products else None  # None pro "všechny"

        # Načtení produktů po dávkách
        products = []
        offset = None
        scroll_limit = 1000  # Velikost dávky pro načítání produktů
        total_loaded = 0

        while True:
            try:
                scroll_result = await qdrant_client.scroll(
                    collection_name=collection_name,
                    limit=scroll_limit,
                    offset=offset,
                    with_payload=True
                )

                batch = scroll_result[0]
                offset = scroll_result[1]

                if not batch:
                    break

                products.extend(batch)
                total_loaded += len(batch)
                logger.info(f"Načteno {total_loaded} produktů")

                if limit and total_loaded >= limit:
                    products = products[:limit]
                    break

                if offset is None:
                    break

            except Exception as e:
                logger.error(f"Chyba při načítání produktů: {e}")
                if total_loaded > 0:
                    logger.info(f"Pokračuji se zpracováním {total_loaded} načtených produktů")
                    break
                else:
                    raise

        total_products = len(products)
        logger.info(f"Celkem načteno {total_products} produktů")

        if not products:
            logger.warning(f"Kolekce {collection_name} neobsahuje žádné produkty")
            return

        # Statistiky
        start_time = time.time()

        # Paralelní zpracování produktů
        logger.info(f"Spouštím paralelní zpracování s {workers} workery a velikostí dávky {batch_size}")

        # Vytvoření funkce pro zpracování dávky
        async def process_batch(batch):
            return await process_product_batch(
                batch=batch,
                tenant_id=tenant_id,
                embedding_strategy=embedding_strategy,
                collection_name=collection_name,
                qdrant_client=qdrant_client
            )

        # Zpracování všech produktů paralelně
        results = await process_batches_concurrently(
            items=products,
            process_func=process_batch,
            batch_size=batch_size,
            concurrency_limit=workers
        )

        # Zpracování výsledků
        processed_count = sum(result.get("processed", 0) for result in results)
        successful_count = sum(result.get("successful", 0) for result in results)
        failed_count = sum(result.get("failed", 0) for result in results)

        # Shrnutí
        total_time = time.time() - start_time
        logger.info(f"Zpracování dokončeno za {total_time:.2f}s")
        logger.info(f"Úspěšně zpracováno: {successful_count}/{total_products} produktů ({successful_count/total_products*100:.1f}%)")
        logger.info(f"Selhalo: {failed_count}/{total_products} produktů ({failed_count/total_products*100:.1f}%)")

    except Exception as e:
        logger.error(f"Chyba při generování multi-embeddingů: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # Uzavření klientů
        await close_clients()
        logger.info("Klienti byli ukončeni")

async def main():
    """Hlavní funkce pro generování multi-embeddingů pro všechny produkty."""

    # Zpracování argumentů
    parser = argparse.ArgumentParser(description="Generování multi-embeddingů pro všechny produkty")
    parser.add_argument("--tenant", type=str, default="filsonstore",
                        help="ID tenanta (výchozí: filsonstore)")
    parser.add_argument("--batch-size", type=int, default=5,
                        help="Velikost dávky pro generování embeddingů (výchozí: 5)")
    parser.add_argument("--max-products", type=int, default=None,
                        help="Maximální počet produktů ke zpracování (výchozí: všechny)")
    parser.add_argument("--workers", type=int, default=5,
                        help="Počet paralelních workerů pro generování embeddingů (výchozí: 5)")
    args = parser.parse_args()

    # Generování multi-embeddingů
    await generate_all_multi_embeddings(
        tenant_id=args.tenant,
        batch_size=args.batch_size,
        max_products=args.max_products,
        workers=args.workers
    )

if __name__ == "__main__":
    asyncio.run(main())
