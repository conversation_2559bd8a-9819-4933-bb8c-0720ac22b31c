# fulltext.py
import logging
import re
from typing import Dict, List, Any, Optional, Union, Tuple
from rank_bm25 import BM25Okapi
import nltk
from nltk.corpus import stopwords
from qdrant_client import AsyncQdrantClient
from qdrant_client.http.models import Filter, FieldCondition, MatchValue
from text_utils import normalize_text

logger = logging.getLogger(__name__)

# Ensure stopwords are downloaded
try:
    nltk.data.find('corpora/stopwords')
except LookupError:
    logger.info("Downloading stopwords for NLTK...")
    nltk.download('stopwords', quiet=True)

class CzechFulltextRetriever:
    """
    Implementace fulltextového vyhledávače pomocí BM25 pro české texty.
    Zahrnuje podporu pro stop slova a základní normalizaci textu.
    """
    def __init__(self):
        # Příprava stop slov pro češtinu
        try:
            self.stop_words = set(stopwords.words('czech'))
        except Exception as e:
            logger.warning(f"Nepodařilo se načíst česk<PERSON> stop slova, použív<PERSON><PERSON> prázdnou množinu: {e}")
            self.stop_words = set()
            
        self.documents = []
        self.document_ids = []
        self.bm25 = None
        self.tokenized_docs = []
    
    def _preprocess_text(self, text: str) -> List[str]:
        """
        Předzpracování textu - normalizace, odstranění interpunkce, stop slov.
        """
        if not text:
            return []
            
        # Normalizace a odstranění interpunkce
        text = normalize_text(text.lower())
        text = re.sub(r'[^\w\s]', ' ', text)
        
        # Tokenizace a odstranění stop slov
        tokens = [token for token in text.split() if token not in self.stop_words]
        
        return tokens
    
    def add_documents(self, documents: List[Dict[str, Any]], id_field: str = "product_id"):
        """
        Přidá dokumenty do indexu a inicializuje BM25.
        
        Args:
            documents: Seznam dokumentů k indexování
            id_field: Název pole, které obsahuje ID dokumentu
        """
        if not documents:
            logger.warning("Nebyly poskytnuty žádné dokumenty pro indexaci BM25.")
            return
            
        self.documents = []
        self.document_ids = []
        document_texts = []
        
        for doc in documents:
            doc_id = doc.get(id_field)
            if not doc_id:
                logger.warning(f"Dokument bez ID pole '{id_field}', přeskakuji: {doc}")
                continue
                
            # Připravíme text pro vyhledávání (kombinace relevantních polí)
            name = doc.get("name", "")
            description = doc.get("description", "")
            category = doc.get("category", "")
            brand = doc.get("brand", "")
            product_code = doc.get("product_code", "") or doc.get("sku", "") or doc.get("code", "") or ""
            
            # Důležité: přidáváme produktový kód do textu s velkým opakováním pro zvýšení váhy
            combined_text = f"{name} {category} {brand} {description} {product_code} {product_code} {product_code}".strip()
            combined_text = " ".join(combined_text.split())  # Odstranění nadbytečných mezer
            
            self.documents.append(doc)
            self.document_ids.append(doc_id)
            document_texts.append(combined_text)
            
        # Tokenizace a předzpracování dokumentů
        self.tokenized_docs = [self._preprocess_text(doc) for doc in document_texts]
        
        # Inicializace BM25
        self.bm25 = BM25Okapi(self.tokenized_docs)
        logger.info(f"BM25 index byl inicializován s {len(self.tokenized_docs)} dokumenty.")
    
    def search(self, query: str, top_k: int = 10) -> List[Dict[str, Any]]:
        """
        Vyhledává v indexu pomocí BM25 algoritmu s prioritizací pro produktové kódy.
        
        Args:
            query: Vyhledávací dotaz
            top_k: Počet nejlepších výsledků k vrácení
            
        Returns:
            Seznam dokumentů s přidanými skóre a metodou vyhledávání
        """
        if not self.bm25 or not self.tokenized_docs:
            logger.warning("BM25 index není inicializován nebo neobsahuje žádné dokumenty.")
            return []
            
        # Kontrola, zda dotaz může být produktový kód (pouze čísla a písmena, bez mezer)
        normalized_query = query.strip()
        is_product_code = bool(re.match(r'^[\w\d]+$', normalized_query)) and len(normalized_query) >= 4
        
        # Pokud se zdá, že jde o produktový kód, zkusit přímé vyhledání
        exact_matches = []
        if is_product_code:
            logger.info(f"Dotaz '{query}' vypadá jako produktový kód, zkouším přímé vyhledání")
            
            # Hledání přesné shody v produktových kódech
            for i, doc in enumerate(self.documents):
                product_code = doc.get("product_code", "") or doc.get("sku", "") or doc.get("code", "") or ""
                
                # Pokud je shoda, přidáme s velmi vysokým skóre
                if product_code and product_code.lower() == normalized_query.lower():
                    exact_matches.append({
                        **doc,
                        "score": 100.0,  # Velmi vysoké skóre pro přesnou shodu
                        "method": "exact_match"
                    })
        
        # Standardní BM25 vyhledávání
        query_tokens = self._preprocess_text(query)
        if not query_tokens and not exact_matches:
            logger.warning(f"Dotaz '{query}' neobsahuje po předzpracování žádné tokeny a není produktový kód.")
            return []
        
        # Pokud máme produktový kód a přesné shody, vrátíme jen je
        if exact_matches:
            logger.info(f"Nalezeno {len(exact_matches)} přesných shod pro produktový kód '{query}'")
            return exact_matches[:top_k]
            
        # Získání skóre pro každý dokument
        scores = self.bm25.get_scores(query_tokens)
        
        # Získání top-k výsledků
        top_indices = sorted(range(len(scores)), key=lambda i: scores[i], reverse=True)[:top_k]
        
        results = [
            {
                **self.documents[idx],  # Kopírujeme celý dokument
                "score": float(scores[idx]),  # Převedeme numpy float na Python float
                "method": "fulltext"
            }
            for idx in top_indices if scores[idx] > 0
        ]
        
        return results

async def load_products_from_qdrant(client: AsyncQdrantClient, collection_name: str, batch_size: int = 1000) -> List[Dict[str, Any]]:
    """
    Načte všechny produkty z Qdrantu po dávkách.
    
    Args:
        client: AsyncQdrantClient instance
        collection_name: Název kolekce
        batch_size: Velikost dávky pro dotaz
        
    Returns:
        Seznam všech produktů z kolekce
    """
    all_products = []
    offset = None
    total_loaded = 0
    
    while True:
        try:
            # Použití scroll API pro postupné načítání dokumentů
            results, offset = await client.scroll(
                collection_name=collection_name,
                limit=batch_size,
                offset=offset,
                with_payload=True,
                with_vectors=False
            )
            
            if not results:
                break
                
            # Extrakce payloadu z výsledků
            batch_products = [point.payload for point in results]
            all_products.extend(batch_products)
            total_loaded += len(batch_products)
            
            logger.info(f"Načteno {total_loaded} produktů z kolekce {collection_name}")
            
            # Pokud není další offset, končíme
            if not offset:
                break
                
        except Exception as e:
            logger.error(f"Chyba při načítání produktů z kolekce {collection_name}: {e}", exc_info=True)
            break
            
    logger.info(f"Celkem načteno {len(all_products)} produktů z kolekce {collection_name}")
    return all_products
